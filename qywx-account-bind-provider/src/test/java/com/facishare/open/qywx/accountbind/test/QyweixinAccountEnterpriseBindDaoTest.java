package com.facishare.open.qywx.accountbind.test;

import com.facishare.open.qywx.accountbind.config.ConfigCenter;
import com.facishare.open.qywx.accountbind.dao.QyweixinAccountEnterpriseBindDao;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinAccountEnterpriseBindDaoTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinAccountEnterpriseBindDao qyweixinAccountEnterpriseBindDao;

    @Test
    public void queryEaMappingFromOutEa() {
        List<QyweixinAccountEnterpriseMapping> mapping = qyweixinAccountEnterpriseBindDao.queryEaMappingFromOutEa("qywx",
                "wpwx1mDAAAwwbHZg0MCuybKcqNoGd7rw",
                null,
                -1,
                ConfigCenter.crm_domain);
        System.out.println(mapping);
    }

    @Test
    public void updateEnterpriseBindStatus() {
        Integer mapping = qyweixinAccountEnterpriseBindDao.updateEnterpriseBindStatus("88102",0,null);
        System.out.println(mapping);
    }
}
