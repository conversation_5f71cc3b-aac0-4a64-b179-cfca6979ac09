package com.facishare.open.qywx.accountbind.test;

import com.facishare.open.qywx.accountbind.dao.QyweixinAccountDepartmentBindDao;
import com.facishare.open.qywx.accountbind.dao.QyweixinAccountEmployeeBindDao;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinAccountDepartmentBindDaoTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinAccountDepartmentBindDao qyweixinAccountDepartmentBindDao;

    @Test
    public void updateEmployeeBindStatus() {
        int count = qyweixinAccountDepartmentBindDao.batchUpdateFsDepBindStatus("86244",
                Lists.newArrayList("1008","1009"),0,"wx88a141937dd6f838",null);
        System.out.println(count);
    }

    @Test
    public void getQywxDepartmentMapping() {
        QyweixinAccountDepartmentMapping mapping = qyweixinAccountDepartmentBindDao.getQywxDepartmentMapping("84883",
                "1049","wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA","1",null);
        System.out.println(mapping);
    }
}
