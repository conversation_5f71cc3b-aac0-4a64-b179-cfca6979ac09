package com.facishare.open.qywx.accountbind.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.EnterpriseModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by fengyh on 2018/3/3.
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
@Slf4j
public class AccountBindTest extends AbstractJUnit4SpringContextTests {

    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    private static String source = "qywx";
    private static String fs_ea="fs_ea_test";

    @Test
    @Ignore
    public void testDeleteEa() {

    }

    @Test
    public void bindAccountEmployeeMapping() {
        QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
        employeeMapping.setSource("qywx");
        employeeMapping.setFsAccount("E.jjj7351.1001");
        employeeMapping.setOutAccount("kgdwbb");
        employeeMapping.setOutEa("wpwx1mDAAA949qpVShKEx6WNwLXh2b3A");
        employeeMapping.setStatus(0);
        Result<Boolean> ret = qyweixinAccountBindService.bindAccountEmployeeMapping(Lists.newArrayList(employeeMapping));
        System.out.println("testBindkis return: " + ret);
    }

    @Test
    @Ignore
    public void testDeleteDepartment() {
    }

    @Test
    public void fsAccountToOutAccountBatch2() {
        Result<List<EmployeeAccountMatchResult>> result = qyweixinAccountBindService.fsAccountToOutAccountBatch2("qywx",
                "wx88a141937dd6f838",
                Lists.newArrayList("E.90562.1000"),
                null);
        System.out.println("result: "+result);
    }

    @Test
    public void fsAccountToOutAccount2() {
        Result<List<EmployeeAccountMatchResult>> result = qyweixinAccountBindService.fsAccountToOutAccount2("qywx",
                Lists.newArrayList("E.90562.1000"),
                "wpwx1mDAAAGJS40Vzl75pp8r_HiH0jMA");
        System.out.println("result: "+result);
    }

    @Test
    public void testFsEaToOutEa() {
        Result<String> outEa = qyweixinAccountBindService.fsEaToOutEa("qywx", "74860");
        System.out.println("outea: "+outEa);
    }

    @Test
    public void fsEaToOutEaResult2() {
        Result<QyweixinAccountEnterpriseMapping> outEa = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
                "90562",
                "wpwx1mDAAAFtBrQCfiHtmRaUHt5hRo7A");
        System.out.println("outea: "+outEa);
    }

    @Test
    public void fsEaToOutEaAllResult2() {
        Result<QyweixinAccountEnterpriseMapping> outEa = qyweixinAccountBindService.fsEaToOutEaAllResult2("qywx",
                "90562",
                "wpwx1mDAAAFtBrQCfiHtmRaUHt5hRo7A");
        System.out.println("outea: "+outEa);
    }

    @Test
    public void fsEaToOutEaResultList() {
        Result<List<QyweixinAccountEnterpriseMapping>> outEa = qyweixinAccountBindService.fsEaToOutEaResultList("qywx","74860");
        System.out.println("outea: "+outEa);
    }

    @Test
    public void testfsAccountToOutAccount() {
        for(int i = 0; i<10; i++){
            Result<Map<String, String>> userMap = qyweixinAccountBindService.fsAccountToOutAccountBatch(source, Lists.newArrayList("E.74342.1000", "E.74342.1001"));
            System.out.println("fsAccountToOutAccountBatch ret:"+userMap);
        }
    }

    @Test
    public void testBindEa() {
        QyweixinAccountEnterpriseMapping accountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
        accountEnterpriseMapping.setFsEa("********");
        accountEnterpriseMapping.setOutEa("ww4ba39487c1f49492");
        accountEnterpriseMapping.setSource(source);
        accountEnterpriseMapping.setStatus(0);
        Result<Boolean> ret = qyweixinAccountBindService.bindAccountEnterpriseMapping(accountEnterpriseMapping);
        System.out.println("bindAccountEnterpriseMapping ret:"+ret);
    }

    @Test
    public void testBindEmployee() {
        List<QyweixinAccountEmployeeMapping>  accountEmployeeMappingList = new ArrayList<>();
        QyweixinAccountEmployeeMapping accountEmployeeMapping1 = new QyweixinAccountEmployeeMapping();
        accountEmployeeMapping1.setSource(source);
        accountEmployeeMapping1.setFsAccount("E."+fs_ea+".********");
        accountEmployeeMapping1.setOutAccount("yunzhijia_employee_test1");
        accountEmployeeMapping1.setOutEa("*********");
        QyweixinAccountEmployeeMapping accountEmployeeMapping2 = new QyweixinAccountEmployeeMapping();
        accountEmployeeMapping2.setSource(source);
        accountEmployeeMapping2.setFsAccount("E."+fs_ea+".********");
        accountEmployeeMapping2.setOutAccount("yunzhijia_employee_test2");
        accountEmployeeMapping2.setOutEa("*********");

        accountEmployeeMappingList.add(accountEmployeeMapping1);
        accountEmployeeMappingList.add(accountEmployeeMapping2);
        Result<Boolean> ret = qyweixinAccountBindService.bindAccountEmployeeMapping(accountEmployeeMappingList);
        System.out.println("bindAccountEmployeeMapping ret:"+ret);
    }

    @Test
    @Ignore
    public void testBindDepartment() {
        QyweixinAccountDepartmentMapping accountDepartmentMapping1 = new QyweixinAccountDepartmentMapping();
        accountDepartmentMapping1.setSource(source);
        accountDepartmentMapping1.setFsEa("fs_ea_test");
        accountDepartmentMapping1.setOutEa("yunzhijia_ea_test");
        accountDepartmentMapping1.setFsDepartmentId(101);
        accountDepartmentMapping1.setOutDepartmentId("yunzhijia_department_1");
        accountDepartmentMapping1.setStatus(0);
        QyweixinAccountDepartmentMapping accountDepartmentMapping2 = new QyweixinAccountDepartmentMapping();
        accountDepartmentMapping2.setSource(source);
        accountDepartmentMapping2.setFsEa("fs_ea_test");
        accountDepartmentMapping2.setOutEa("yunzhijia_ea_test");
        accountDepartmentMapping2.setFsDepartmentId(102);
        accountDepartmentMapping2.setOutDepartmentId("yunzhijia_department_2");
        accountDepartmentMapping2.setStatus(0);

        Result<Boolean> ret1 = qyweixinAccountBindService.bindAccountDepartmentMapping(Lists.newArrayList(accountDepartmentMapping1, accountDepartmentMapping2));
        System.out.println("bindAccountDepartmentMapping ret:"+ret1);
    }


    @Test
    public void testOutEaToFsEa(){
        Result<String> ret3 = qyweixinAccountBindService.outEaToFsEa(source, "ww4ba39487c1f49492",null);
        System.out.println("outEaToFsEa ret:"+ret3);
    }

    @Test
    public void selectEnterpriseBind(){
        Result<List<QyweixinAccountEnterpriseMapping>> ret3 = qyweixinAccountBindService.selectEnterpriseBind(source, "ww4ba39487c1f49492");
        System.out.println("outEaToFsEa ret:"+ret3);
    }

    @Test
    public void selectAllEnterpriseBind(){
        Result<List<QyweixinAccountEnterpriseMapping>> ret3 = qyweixinAccountBindService.selectAllEnterpriseBind(source, "ww4ba39487c1f49492");
        System.out.println("outEaToFsEa ret:"+ret3);
    }

    @Test
    public void outAccountToFsAccountBatch(){
        Result<Map<String, String>> fsuser = qyweixinAccountBindService.outAccountToFsAccountBatch(source, "61257",Lists.newArrayList("ChenXiangFei"));
        System.out.println(fsuser.getData().get("ChenXiangFei"));
        System.out.println("outEaToFsEa ret:"+ JSONObject.toJSONString(fsuser));
    }
//
    @Test
    public void queryDepartmentBindByOutDepartment(){
        Result<List<QyweixinAccountDepartmentMapping>> fsuser = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(source, "wxa2f5cab3f5ade255", Lists.newArrayList());
        System.out.println("outEaToFsEa ret:"+fsuser);
    }

    @Test
    public void fsEaToOuEa(){
        Result<String> ret2 = qyweixinAccountBindService.fsEaToOutEa(source, "58512");
        System.out.println("fsEaToOutEa ret:"+ret2);
    }

    @Test
    public void testQuery() {
        Result<Map<String, String>> fsuser = qyweixinAccountBindService.outAccountToFsAccountBatch(source,"", Lists.newArrayList("yunzhijia_employee_test12", "yunzhijia_employee_test22"));
        System.out.println("fsuser ret:"+fsuser);
        Result<Map<String, String>> outuser = qyweixinAccountBindService.fsAccountToOutAccountBatch(source, Lists.newArrayList("E.fs_ea_test.1000", "E.fs_ea_test.1001"));
        System.out.println("outuser ret:"+outuser);
        Result<String> ret2 = qyweixinAccountBindService.fsEaToOutEa(source, "58512");
        System.out.println("fsEaToOutEa ret:"+ret2);
        Result<String> ret3 = qyweixinAccountBindService.outEaToFsEa(source, "ww17222766be033966",null);
        System.out.println("outEaToFsEa ret:"+ret3);
        Result<List<QyweixinAccountDepartmentMapping>> accountDepartmentMappingList = qyweixinAccountBindService.queryDepartmentBindByFsDepartment(source, fs_ea, Lists.newArrayList(101, 102));
        System.out.println("queryDepartmentBindByFsDepartment ret:"+accountDepartmentMappingList);
        accountDepartmentMappingList = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(source, "yunzhijia_ea_test" , Lists.newArrayList("yunzhijia_department_1", "yunzhijia_department_2"));
        System.out.println("queryDepartmentBindByOutDepartment ret:"+accountDepartmentMappingList);
    }

    @Test
    public void testQueryFsAccount(){
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountEmployeeMappingList = qyweixinAccountBindService.queryFsAccountBindByOldOutAccount(SourceTypeEnum.QYWX.getSourceType(), Lists.newArrayList("YongHeng"), "wwbd02a00abf156207");
        if(null != accountEmployeeMappingList) {
            String fsAccount = accountEmployeeMappingList.getData().get(0).getFsAccount();
            System.out.println("testQuery2 fsAccount:" + fsAccount);
//            int result = qyweixinAccountBindService.updateByNewOutAccount("CAF","ChenAFei","wxbbe44d073ff6c715");
//            System.out.println("testQuery2  result!:" +result);
        }else
            System.out.println("testQuery2 fsAccount error!:" );
    }

    @Test
    public void getFsEaList() {
        com.facishare.open.qywx.accountsync.result.Result<List<EnterpriseModel>> result = qyweixinAccountBindInnerService.getFsEaList("wpwx1mDAAAH-IDsqaKUveaASb10BN06Q",
                "wowx1mDAAAMdB1vuVmKYTplHe-rEsDFQ");
        System.out.println(result);

        com.facishare.open.qywx.accountsync.result.Result<List<EnterpriseModel>> result1 = qyweixinAccountBindInnerService.getFsEaList("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA",
                "wowx1mDAAAfPMspC9iKzWLx16d_zjcRA");
        System.out.println(result1);
    }

    @Test
    public void outAccountToFsAccount() {
        com.facishare.open.qywx.accountsync.result.Result<String> result = qyweixinAccountBindInnerService.outAccountToFsAccount("qywx",
                "wpwx1mDAAAZ44huF602PhT3IxdnRhToQ",
                "wowx1mDAAAIwzemRR_ybbW5uF4ItIsMg",
                "");
        System.out.println(result);
    }

    @Test
    public void testDeleteEmp() {
        qyweixinAccountBindInnerService.deleteAccountBind("1000", Lists.newArrayList("E.56677.1001", "E.56677.1002"), "wx88a141937dd6f838", null);
    }

    @Test
    public void queryEnterpriseMappingByBindType() {
        Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindService.queryEnterpriseMappingByBindType(1);
        System.out.println(result);
    }

    @Test
    public void batchGetEmployeeMapping2() {
        Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.batchGetEmployeeMapping2("84883", null, null);
        System.out.println(result);
    }

    @Test
    public void updateEmployeeBindStatus() {
        Result<Integer> result = qyweixinAccountBindService.updateEmployeeBindStatus("E.************.1000", 0);
        System.out.println(result);
    }

    @Test
    public void updateEnterpriseBindStatus() {
        Result<Integer> result = qyweixinAccountBindService.updateEnterpriseBindStatus("8888888", 0);
        System.out.println(result);
    }

    @Test
    public void getFsEaList1() {
        com.facishare.open.qywx.accountsync.result.Result<List<String>> result = qyweixinAccountBindInnerService.getFsEaList("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        System.out.println(result);
    }
}
