package com.facishare.open.qywx.accountbind.test.service

import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class QyweixinAccountBindServiceTest extends Specification {

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    def "findEmployeeBinds"() {
        given:
        List<QyweixinAccountEmployeeMapping> employeeMappings = new LinkedList<>();
        QyweixinAccountEmployeeMapping mapping = new QyweixinAccountEmployeeMapping();
        mapping.setOutEa(outEa);
        mapping.setAppId(appId);
        mapping.setFsAccount(fsAccount);
        mapping.setOutAccount(outAccount);
        mapping.setSource("qywx");
        mapping.setStatus(status);
        employeeMappings.add(mapping);
        expect:
        def result = qyweixinAccountBindService.findEmployeeBinds(employeeMappings);
        println result;
        where:
        outEa       |            appId         |      fsAccount       |        outAccount      |     status
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "test"                 |        "E.84883.1021"  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"  |        0
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        "E.84883.1021"  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"  |        0
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        "E.84883.1021"  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"  |        1
    }

    def "saveEmployeeBinds"() {
        given:
        List<QyweixinAccountEmployeeMapping> employeeMappings = new LinkedList<>();
        QyweixinAccountEmployeeMapping mapping = new QyweixinAccountEmployeeMapping();
        mapping.setOutEa(outEa);
        mapping.setAppId(appId);
        mapping.setFsAccount(fsAccount);
        mapping.setOutAccount(outAccount);
        mapping.setSource("qywx");
        mapping.setStatus(status);
        employeeMappings.add(mapping);
        expect:
        def result = qyweixinAccountBindService.saveEmployeeBinds(employeeMappings);
        println result;
        where:
        outEa       |            appId         |      fsAccount       |        outAccount      |     status
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        "E.84883.10000"  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ1"  |        0
    }

    def "updateEmployeeBinds"() {
        given:
        List<QyweixinAccountEmployeeMapping> employeeMappings = new LinkedList<>();
        QyweixinAccountEmployeeMapping mapping = new QyweixinAccountEmployeeMapping();
        mapping.setOutEa(outEa);
        mapping.setAppId(appId);
        mapping.setFsAccount(fsAccount);
        mapping.setOutAccount(outAccount);
        mapping.setSource("qywx");
        mapping.setStatus(status);
        employeeMappings.add(mapping);
        expect:
        def result = qyweixinAccountBindService.updateEmployeeBinds(employeeMappings);
        println result;
        where:
        outEa       |            appId         |      fsAccount       |        outAccount      |     status
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        "E.84883.10000"  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ2"  |        0
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        "E.84883.10000"  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ2"  |        1
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        "E.84883.10000"  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ1"  |        0
    }

    def "findDepartmentBinds"() {
        given:
        List<QyweixinAccountDepartmentMapping> departmentMappings = new LinkedList<>();
        QyweixinAccountDepartmentMapping mapping = new QyweixinAccountDepartmentMapping();
        mapping.setOutEa(outEa);
        mapping.setAppId(appId);
        mapping.setOutDepartmentId(outDepartmentId);
        mapping.setFsDepartmentId(fsDepartmentId);
        mapping.setSource("qywx");
        mapping.setStatus(status);
        mapping.setFsEa(fsEa);
        departmentMappings.add(mapping);
        expect:
        def result = qyweixinAccountBindService.findDepartmentBinds(departmentMappings);
        println result;
        where:
        outEa       |            appId         |      fsDepartmentId    |        outDepartmentId      |     status   |     fsEa
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        1  |   "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ2"  |        0    |   "84883"
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  |  "wx88a141937dd6f838"   |        1  |   "test"  |        0    |   "84883"
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  |  "wx88a141937dd6f838"   |        1000  |   "2"  |        0    |   "djt6721"
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  |  "wx88a141937dd6f838"   |        1001  |   "3"  |        0    |   "djt6721"
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  |  "wx88a141937dd6f838"   |        1001  |   "30000"  |        0    |   "djt6721"
    }

    def "saveDepartmentBinds"() {
        given:
        List<QyweixinAccountDepartmentMapping> departmentMappings = new LinkedList<>();
        QyweixinAccountDepartmentMapping mapping = new QyweixinAccountDepartmentMapping();
        mapping.setOutEa(outEa);
        mapping.setAppId(appId);
        mapping.setOutDepartmentId(outDepartmentId);
        mapping.setFsDepartmentId(fsDepartmentId);
        mapping.setSource("qywx");
        mapping.setStatus(status);
        mapping.setFsEa(fsEa);
        departmentMappings.add(mapping);
        expect:
        def result = qyweixinAccountBindService.saveDepartmentBinds(departmentMappings);
        println result;
        where:
        outEa       |            appId         |      fsDepartmentId    |        outDepartmentId      |     status   |     fsEa
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  |  "wx88a141937dd6f838"   |        10000  |   "30000"  |        0    |   "djt6721"
    }

    def "updateDepartmentBinds"() {
        given:
        List<QyweixinAccountDepartmentMapping> departmentMappings = new LinkedList<>();
        QyweixinAccountDepartmentMapping mapping = new QyweixinAccountDepartmentMapping();
        mapping.setOutEa(outEa);
        mapping.setAppId(appId);
        mapping.setOutDepartmentId(outDepartmentId);
        mapping.setFsDepartmentId(fsDepartmentId);
        mapping.setSource("qywx");
        mapping.setStatus(status);
        mapping.setFsEa(fsEa);
        departmentMappings.add(mapping);
        expect:
        def result = qyweixinAccountBindService.updateDepartmentBinds(departmentMappings);
        println result;
        where:
        outEa       |            appId         |      fsDepartmentId    |        outDepartmentId      |     status   |     fsEa
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  |  "wx88a141937dd6f838"   |        10000  |   "30000"  |        0    |   "djt6721"
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  |  "wx88a141937dd6f838"   |        10000  |   "300001"  |        1    |   "djt6721"
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  |  "wx88a141937dd6f838"   |        10000  |   "30000"  |        0    |   "djt6721"
    }
    def "queryRepeatEmployees"() {
        expect:
        def result = qyweixinAccountBindService.queryRepeatEmployees();
        println result;
    }

}
