package com.facishare.open.qywx.accountbind.test;

import com.facishare.open.qywx.accountbind.dao.QyweixinAccountEmployeeBindDao;
import com.facishare.open.qywx.accountbind.dao.QyweixinAccountEnterpriseBindDao;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinAccountEmployeeBindDaoTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinAccountEmployeeBindDao qyweixinAccountEmployeeBindDao;

    @Test
    public void updateEmployeeBindStatus() {
        Integer mapping = qyweixinAccountEmployeeBindDao.updateEmployeeBindStatus("E.88102.1000",0,null);
        System.out.println(mapping);
    }

    @Test
    public void batchUpdateFsEmpBindStatus() {
        Integer mapping = qyweixinAccountEmployeeBindDao.batchUpdateFsEmpBindStatus(Lists.newArrayList("E.74860.1050","E.74860.1048"),
                0,"wx88a141937dd6f838",null);
        System.out.println(mapping);
    }

    @Test
    public void getQywxEmployeeMapping2() {
        QyweixinAccountEmployeeMapping mapping = qyweixinAccountEmployeeBindDao.getQywxEmployeeMapping2("E.81243.1072",
                "wpwx1mDAAAsJuqgPFlcfGWtLOewfEiNw",
                "wowx1mDAAA0Ou5xip4toV7W4cvRmepcw");
        System.out.println(mapping);
    }
}
