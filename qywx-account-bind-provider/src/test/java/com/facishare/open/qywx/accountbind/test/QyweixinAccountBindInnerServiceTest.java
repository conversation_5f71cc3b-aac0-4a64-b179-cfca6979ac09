package com.facishare.open.qywx.accountbind.test;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.AccountSyncConfigModel;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.model.EnterpriseModel;
import com.facishare.open.qywx.accountsync.result.Result;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/6/19 16:49
 * @<NAME_EMAIL>
 * @version 1.0 
 */

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")

public class QyweixinAccountBindInnerServiceTest extends AbstractJUnit4SpringContextTests {

    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;

    @ReloadableProperty("crmAppId")
    private String crmAppId;

    @Test
    public void queryAccountBind(){
        Result<List<QyweixinAccountEmployeeMapping>> listResult = qyweixinAccountBindInnerService.queryAccountBind2(
                Lists.newArrayList("E.58566.1000","E.58566.1001", "E.58566.1002", "E.58566.1003"
                        , "E.58566.********"),crmAppId,null);
        System.out.println(JSONObject.toJSONString(listResult));
    }

    @Test
    public void updateQyweixinAccountEmployee(){
        QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping = new QyweixinAccountEmployeeMapping();
        qyweixinAccountEmployeeMapping.setId(21118059L);
        qyweixinAccountEmployeeMapping.setFsAccount("E.56677.1001");
        qyweixinAccountEmployeeMapping.setOutAccount("********");
        qyweixinAccountEmployeeMapping.setOutEa("nichuia");
        int i = qyweixinAccountBindInnerService.updateQyweixinAccountEmployee(qyweixinAccountEmployeeMapping, "212");
        System.out.println(i);
    }


    @Test
    public void bindAccountEmployeeMapping() {
        List<QyweixinAccountEmployeeMapping>  accountEmployeeMappingList = new ArrayList<>();
        QyweixinAccountEmployeeMapping accountEmployeeMapping1 = new QyweixinAccountEmployeeMapping();
        accountEmployeeMapping1.setSource("qywx");
        accountEmployeeMapping1.setFsAccount("E.58566.********");
        accountEmployeeMapping1.setOutAccount("***********");
        accountEmployeeMapping1.setOutEa("wxa2f5cab3f5ade255");

        accountEmployeeMappingList.add(accountEmployeeMapping1);
        com.facishare.open.qywx.accountbind.result.Result<Boolean> ret = qyweixinAccountBindService.bindAccountEmployeeMapping(accountEmployeeMappingList);
        System.out.println("bindAccountEmployeeMapping ret:"+ret);
    }

//    @Test
//    public void deleteAccountBind(){
//        System.out.println(qyweixinAccountBindInnerService.deleteAccountBind(Lists.newArrayList("E.58566.********")));
//    }


    @Test
    public void queryAccountBindByFsEa2(){
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2("81961",crmAppId,null);
        System.out.println(JSONObject.toJSONString(qyweixinAccountEmployeeMappings));
    }

    @Test
    public void test() {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> mapping
                = qyweixinAccountBindService.getFsEmployeeMapping2("wwb3811da6336e7a1c", "WuBeiBei");
        System.out.println(mapping);
    }

    @Test
    public void fsAccountToOutAccount() {

        com.facishare.open.qywx.accountbind.result.Result<List<EmployeeAccountMatchResult>> result = qyweixinAccountBindService.fsAccountToOutAccountByIsv2("qywx",
                Lists.newArrayList("E.81961.1027"),null);
        System.out.println(result);
    }

    @Test
    public void getEmployeeMapping(){
        Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindInnerService.getEmployeeMapping("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g",
                "wowx1mDAAAIlE9l25FtHdbyv2kedv9Pg",0,null);
        System.out.println(result);
    }

    @Test
    public void test1() {
        com.facishare.open.qywx.accountbind.result.Result<List<EmployeeAccountMatchResult>> mapping
                = qyweixinAccountBindService.fsAccountToOutAccountBatchByIsv2("qywx", "wx88a141937dd6f838",
                Lists.newArrayList("E.81961.1028", "E.81961.1027"),null);
        System.out.println(mapping);
    }

    @Test
    public void testOutEaToFsEa(){
        com.facishare.open.qywx.accountbind.result.Result<String> ret3 = qyweixinAccountBindService.outEaToFsEa("qywx", "ww7e3b4b1681c29d47",null);
        System.out.println("outEaToFsEa ret:"+ret3);
    }

    @Test
    public void testBindEa() {
        QyweixinAccountEnterpriseMapping accountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
        accountEnterpriseMapping.setFsEa("8888888");
        accountEnterpriseMapping.setOutEa("ww4ba39487c1f49492");
        accountEnterpriseMapping.setDepId("2");
        accountEnterpriseMapping.setSource("qywx");
        accountEnterpriseMapping.setStatus(0);
        accountEnterpriseMapping.setBindType(0);
        com.facishare.open.qywx.accountbind.result.Result<Boolean> ret = qyweixinAccountBindService.bindAccountEnterpriseMapping(accountEnterpriseMapping);
        System.out.println("bindAccountEnterpriseMapping ret:"+ret);
    }

    @Test
    public void testEmployee() {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.employeeToOutAccount("qywx", "wpwx1mDAAAw6T0Q8oWs-URPoxRynraSQ", "wx88a141937dd6f838", Lists.newArrayList("wowx1mDAAARq8TB9E2AdttLwVew88NaA"));
        System.out.println(result);
    }

    @Test
    public void updateAccountSyncConfig2() {
        String json = "[\n" +
                "    {\n" +
                "        \"syncType\": \"SYNC_TO_LEADS\",\n" +
                "        \"autoSync\": false,\n" +
                "        \"allowSyncIfNoPhoneNumber\": false,\n" +
                "        \"setting\": {\n" +
                "            \"byPhone\": false,\n" +
                "            \"byMembership\": true\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"syncType\": \"SYNC_TO_CONTACT\",\n" +
                "        \"autoSync\": false,\n" +
                "        \"allowSyncIfNoPhoneNumber\": true,\n" +
                "        \"setting\": {\n" +
                "            \"byPhone\": true,\n" +
                "            \"byMembership\": true\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        \"syncType\": \"SYNC_TO_ACCOUNT\",\n" +
                "        \"autoSync\": false,\n" +
                "        \"allowSyncIfNoPhoneNumber\": false,\n" +
                "        \"setting\": {\n" +
                "            \"byPhone\": false,\n" +
                "            \"byMembership\": true\n" +
                "        }\n" +
                "    }\n" +
                "]";
        List<AccountSyncConfigModel> list = JSONArray.parseArray(json,AccountSyncConfigModel.class);

        Result result = qyweixinAccountBindInnerService.updateAccountSyncConfig2("74860",list,null);
        System.out.println(result);
    }

    @Test
    public void getEnterpriseMapping() {
        Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindInnerService.getEnterpriseMapping("74860");
        System.out.println(result);
    }

    @Test
    public void queryEnterpriseBindByOutEa() {
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa("wwf7677c24edc709df111");
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            System.out.println(enterpriseMappingList);
        }
        System.out.println(enterpriseMappingList);
    }

    @Test
    public void queryCorpBindByOutEa() {
        Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindInnerService.queryCorpBindByOutEa("ww4ba39487c1f49492", null);
        System.out.println(enterpriseMappingResult);
    }

    @Test
    public void deleteQYWXBindByOutEa() {
        int count = qyweixinAccountBindInnerService.deleteQYWXBindByOutEa(Lists.newArrayList("ww4ba39487c1f494921"), crmAppId);
        System.out.println(count);
    }

    @Test
    public void getEnterpriseMapping2() {
        Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindInnerService.getEnterpriseMapping2("********", "ww4ba39487c1f494921");
        System.out.println(enterpriseMappingResult);
    }

    @Test
    public void deleteOrResumeEmployee() {
        qyweixinAccountBindService.deleteOrResumeEmployee("qywx","82335",crmAppId,
                Lists.newArrayList("***********"),false);
    }

    @Test
    public void testSelectDepartmentBind() {
        com.facishare.open.qywx.accountbind.result.Result<Integer> mapping2 = qyweixinAccountBindService.batchUpdateDepartmentBind("test", "ww1dc427ba799b9ba6");
        System.out.println(mapping2);
    }

    @Test
    public void testIsManualBinding() {
        com.facishare.open.qywx.accountbind.result.Result<Boolean> binding = qyweixinAccountBindService.isManualBinding("82777", "wpwx1mDAAAH-IDsqaKUveaASb10BN06Q");
        if(binding.getData()) {
            System.out.println("1");
        }
        System.out.println("2");
    }

    @Test
    public void testGetDepartment() {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountDepartmentMapping>> listResult = qyweixinAccountBindService.queryDepartmentBindByOutDepartment("qywx", "wpwx1mDAAA3fRMFTGuFvJa2LXoZ8U9_A", "wx88a141937dd6f838", Lists.newArrayList("5", "3", "4", "2"));
        System.out.println(listResult);
    }

    @Test
    public void testDeleteQYWXAccountBind() {
        qyweixinAccountBindInnerService.deleteQYWXAccountBind("81243",
                crmAppId,
                "wpwx1mDAAACigpu1Ro4u89tCMaZns7ag");
    }

    @Test
    public void updateEnterpriseOutInfo() {
        Result<Integer> result = qyweixinAccountBindInnerService.updateEnterpriseOutInfo(1093,
                "wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w",
                "3");
        System.out.println(result);
    }

    @Test
    public void updateExtend2() {
        Result<Integer> result = qyweixinAccountBindInnerService.updateExtend2("yddcsq5277",
                GlobalValue.enterprise_extend,
                "wpwx1mDAAA4u47AvqhvCTQZRrMVkQWpQ");
        System.out.println(result);
    }

    @Test
    public void queryEnterpriseMappingListFromFsEa() {
        Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindInnerService.queryEnterpriseMappingListFromFsEa("qywx",
                "74860",
                0);
        System.out.println(result);
        queryEmployeeMappingListFromFsUserId();
    }

    @Test
    public void queryEmployeeMappingListFromFsUserId() {
        Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindInnerService.queryEmployeeMappingListFromFsUserId("qywx",
                "74860",
                "1050");
        System.out.println(result);
    }
}
