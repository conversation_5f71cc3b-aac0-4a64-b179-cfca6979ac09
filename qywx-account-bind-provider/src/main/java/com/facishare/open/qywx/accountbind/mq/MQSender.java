package com.facishare.open.qywx.accountbind.mq;

import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.model.OaconnectorEventDateChangeProto;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * MQ发送专用工具
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class MQSender {
    @Resource(name = "outEventDataChangeMQSender")
    private AutoConfRocketMQProducer outEventDataChangeMQSender;
    @Resource(name = "oaconnectorEventDataChangeMQSender")
    private AutoConfRocketMQProducer oaconnectorEventDataChangeMQSender;


    public void sendCloudProxyMQ(Integer ei, CloudMessageProxyProto proto) {
        Message msg = new Message();
        msg.setTags("cloud_proxy_event_tag");
        msg.setBody(proto.toProto());

        //把纷享云的MQ投递到所有的专属云
        TraceContext context = TraceContext.get();
        context.setEi(String.valueOf(ei));

        SendResult sendResult = outEventDataChangeMQSender.send(msg);

        //移除上下文，避免跨云调用混乱
        TraceContext.remove();

        log.info("MQSender.sendCloudProxyMQ,sendResult={}",sendResult);
    }

    public SendResult sendOaconnectorEventDataChangeMQ(String tag, OaconnectorEventDateChangeProto proto, String ei) {
        Message msg = new Message();
        msg.setTopic(oaconnectorEventDataChangeMQSender.getDefaultTopic());
        msg.setTags(tag);
        msg.setBody(proto.toProto());

        TraceContext context = TraceContext.get();
        context.setEi(ei);  // 可以区分是否跨云投递
        context.setTraceId(TraceUtil.get());  // 日记记录，分布式跟踪需要

        SendResult sendResult = oaconnectorEventDataChangeMQSender.send(msg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("MQSender.sendOaconnectorEventDataChangeMQ.ei={},tag={},sendResult={}", ei, tag, sendResult);
        return sendResult;
    }
}
