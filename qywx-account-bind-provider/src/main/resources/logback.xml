<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <!-- monitorLog-->
    <appender name="runLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.base}/logs/run.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.base}/logs/run-%d{yyyy-MM-dd}.%i.log.zip</FileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>[%d{yyyy/MM/dd-HH:mm:ss.SSS}]-[%level]-[%thread]-[%X{traceId}]-[%class:%line] - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.facishare.open.qywx.accountbind" level="info" additivity="false">
        <appender-ref ref="runLog" />
    </logger>


    <logger name="com.facishare.open.qywx.accountbind.dao" level="debug" additivity="false">
        <appender-ref ref="runLog" />
    </logger>

    <root level="error">
        <appender-ref ref="runLog"/>
    </root>

    <conversionRule conversionWord="msg" converterClass="com.fxiaoke.metrics.logback.MaskMessageConverter"/>
</configuration>
