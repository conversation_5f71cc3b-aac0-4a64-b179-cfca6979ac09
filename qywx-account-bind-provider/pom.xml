<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-open-qywx-gateway</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>

    <artifactId>qywx-account-bind-provider</artifactId>

    <dependencies>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.16</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.facishare.open</groupId>-->
<!--            <artifactId>qywx-account-bind-api</artifactId>-->
<!--            <version>1.0.1-SNAPSHOT</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.facishare.open</groupId>-->
<!--            <artifactId>qywx-account-inner-api</artifactId>-->
<!--            <version>1.0.1-SNAPSHOT</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mybatis-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rpc-trace</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>4.1.7.RELEASE</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.cloud</groupId>
            <artifactId>datapersist</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.fxiaoke</groupId>-->
<!--            <artifactId>fs-i18n-api</artifactId>-->
<!--            <version>1.0-SNAPSHOT</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-uc-api</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alibaba.rocketmq</groupId>-->
<!--            <artifactId>rocketmq-client</artifactId>-->
<!--            <version>3.2.6</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.facishare</groupId>-->
<!--            <artifactId>fs-common-mq</artifactId>-->
<!--            <version>1.1.0-SNAPSHOT</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>

        <!--spock单元测试-->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <version>1.3-groovy-2.4</version>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>feishu-sync-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>1.3-groovy-2.4</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>qywx-account-bind-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>qywx-account-inner-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <warName>qywx-account-bind-provider</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>