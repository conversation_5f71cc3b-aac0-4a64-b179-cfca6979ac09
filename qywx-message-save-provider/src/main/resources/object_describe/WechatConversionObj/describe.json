{"api_name": "WechatConversionObj", "display_name": "企业微信会话存档", "is_udef": true, "store_table_name": "biz_wechat_conversion", "description": "存储企业微信会话信息", "define_type": "package", "icon_path": "A_201705_11_a0131af8e55549c68233e12bc53f5a60.png", "icon_index": 5, "config": {"edit": 1, "fields": {"add": 1}, "layout": {"add": 1, "assign": 1}, "record_type": {"add": 1, "assign": 1}, "cascade": {"add": 1}, "layout_rule": {"add": 1}, "rule": {"add": 1}, "button": {"add": 1}}, "is_active": true, "version": 1, "index_version": 1, "package": "CRM", "fields": {"name": {"describe_api_name": "WechatConversionObj", "prefix": "{yyyy}{mm}{dd}-", "is_index": true, "is_active": true, "pattern": "", "description": "", "is_unique": true, "label": "会话存档编号", "start_number": 1, "serial_number": 6, "type": "auto_number", "is_need_convert": false, "is_required": false, "api_name": "name", "define_type": "system", "is_index_field": false, "is_single": false, "help_text": "", "postfix": "", "status": "released"}, "fs_ea": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "企业EA", "is_need_convert": false, "api_name": "fs_ea", "is_index_field": false, "help_text": "", "status": "released"}, "msg_id": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "消息ID", "is_need_convert": false, "api_name": "msg_id", "is_index_field": false, "help_text": "", "status": "released"}, "seq": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "number", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "消息序号", "is_need_convert": false, "api_name": "seq", "is_index_field": false, "help_text": "", "status": "released"}, "key_version": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "number", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "密钥版本", "is_need_convert": false, "api_name": "key_version", "is_index_field": false, "help_text": "", "status": "released"}, "from_user_cipher": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "发送方密文用户ID", "is_need_convert": false, "api_name": "from_user_cipher", "is_index_field": false, "help_text": "", "status": "released"}, "from_user": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "发送方明文用户ID", "is_need_convert": false, "api_name": "from_user", "is_index_field": false, "help_text": "", "status": "released"}, "to_list_cipher": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "long_text", "max_length": 2000, "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "接收方密文用户ID列表", "is_need_convert": false, "api_name": "to_list_cipher", "is_index_field": false, "help_text": "", "status": "released"}, "to_list": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "long_text", "max_length": 2000, "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "接收方明文用户ID列表", "is_need_convert": false, "api_name": "to_list", "is_index_field": false, "help_text": "", "status": "released"}, "room_id": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "群ID", "is_need_convert": false, "api_name": "room_id", "is_index_field": false, "help_text": "", "status": "released"}, "msg_time": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "number", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "消息时间", "is_need_convert": false, "api_name": "msg_time", "is_index_field": false, "help_text": "", "status": "released"}, "msg_type": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "消息类型", "is_need_convert": false, "api_name": "msg_type", "is_index_field": false, "help_text": "", "status": "released"}, "content": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "long_text", "max_length": 10000, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "消息内容", "is_need_convert": false, "api_name": "content", "is_index_field": false, "help_text": "", "status": "released"}, "md5sum": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "媒体资源的MD5", "is_need_convert": false, "api_name": "md5sum", "is_index_field": false, "help_text": "", "status": "released"}, "sdk_file_id": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "max_length": 1024, "label": "媒体资源的id信息", "is_need_convert": false, "api_name": "sdk_file_id", "is_index_field": false, "help_text": "", "status": "released"}, "file_size": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "number", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "媒体资源的文件大小", "is_need_convert": false, "api_name": "file_size", "is_index_field": false, "help_text": "", "status": "released"}, "image": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "image", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "图片", "is_need_convert": false, "api_name": "image", "is_index_field": false, "help_text": "单个图片不得超过20M", "support_file_types": ["jpg", "gif", "jpeg", "png"], "file_amount_limit": 1, "file_size_limit": 20971520, "status": "released"}, "attachment": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "file_attachment", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "附件", "is_need_convert": false, "api_name": "attachment", "is_index_field": false, "help_text": "单个文件不得超过100M", "support_file_types": [], "file_amount_limit": 1, "file_size_limit": 104857600, "status": "released"}, "npath": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "文件的npath", "is_need_convert": false, "api_name": "npath", "is_index_field": false, "help_text": "", "status": "released"}, "file_name": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "文件名", "is_need_convert": false, "api_name": "file_name", "is_index_field": false, "help_text": "", "status": "released"}, "file_ext": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "文件扩展名", "is_need_convert": false, "api_name": "file_ext", "is_index_field": false, "help_text": "", "status": "released"}, "qywx_customer_id": {"describe_api_name": "WechatConversionObj", "description": "", "is_unique": false, "type": "object_reference", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "企业微信客户ID", "target_api_name": "WechatWorkExternalUserObj", "target_related_list_name": "wechat_customer_conversion_list", "target_related_list_label": "企业微信客户", "action_on_target_delete": "set_null", "is_need_convert": false, "api_name": "qywx_customer_id", "is_index_field": false, "status": "new"}, "qywx_group_id": {"describe_api_name": "WechatConversionObj", "description": "", "is_unique": false, "type": "object_reference", "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "企业微信群ID", "target_api_name": "WechatGroupObj", "target_related_list_name": "wechat_group_conversion_list", "target_related_list_label": "企业微信群", "action_on_target_delete": "set_null", "is_need_convert": false, "api_name": "qywx_group_id", "is_index_field": false, "status": "new"}, "_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "api_name": "_id", "description": "_id", "status": "new", "is_extend": false}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "status": "new", "is_extend": false}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "new", "label": "创建人", "is_active": true, "index_name": "crt_by"}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "new", "is_active": true, "index_name": "md_by", "label": "最后修改人"}, "record_type": {"describe_api_name": "WechatConversionObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "r_type", "config": {}, "status": "new"}, "owner": {"describe_api_name": "WechatConversionObj", "is_index": true, "is_active": true, "description": "负责人", "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "index_name": "owner", "status": "new"}, "life_status": {"describe_api_name": "WechatConversionObj", "is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "审核中", "value": "under_review", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "正常", "value": "normal", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "变更中", "value": "in_change", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "作废", "value": "invalid", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "s_5", "config": {}, "status": "new"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "new", "index_name": "crt_time"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time"}, "lock_status": {"describe_api_name": "WechatConversionObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "锁定", "value": "1", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "s_4", "config": {}, "status": "new"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "new", "index_name": "is_del"}, "out_tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "外部企业", "is_active": true, "api_name": "out_tenant_id", "description": "out_tenant_id", "status": "new", "index_name": "o_ei", "config": {"display": 0}}, "out_owner": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "out_owner", "index_name": "o_owner", "status": "new", "label": "外部负责人", "is_active": true, "config": {"display": 1}}, "data_own_department": {"type": "department", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "data_own_department", "status": "new", "label": "归属部门", "is_active": true, "index_name": "data_owner_dept_id"}, "lock_user": {"is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_index_field": false, "is_single": true, "index_name": "a_1", "status": "new"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "new", "index_name": "api_name"}, "extend_obj_data_id": {"describe_api_name": "WechatConversionObj", "default_is_expression": false, "pattern": "", "description": "extend_obj_data_id", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "system", "is_extend": false, "is_single": false, "index_name": "t_3", "max_length": 100, "is_index": false, "is_active": true, "default_value": "", "label": "extend_obj_data_id", "api_name": "extend_obj_data_id", "is_index_field": false, "help_text": "", "status": "new"}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "new", "index_name": "version"}, "lock_rule": {"describe_api_name": "WechatConversionObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "s_1", "status": "new"}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "is_active": true, "api_name": "package", "description": "package", "status": "new", "index_name": "pkg"}, "life_status_before_invalid": {"describe_api_name": "WechatConversionObj", "is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "t_4", "max_length": 256, "status": "new"}, "relevant_team": {"describe_api_name": "WechatConversionObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_unique": false, "description": "成员员工", "label": "成员员工", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "define_type": "package", "is_single": false, "index_name": "stringList_2", "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_unique": false, "description": "成员角色", "label": "成员角色", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"value": "1", "label": "负责人"}, {"value": "4", "label": "普通成员"}], "define_type": "package", "index_name": "string_4", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_unique": false, "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"value": "1", "label": "只读"}, {"value": "2", "label": "读写"}], "define_type": "package", "index_name": "string_5", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "a_team", "help_text": "相关团队", "status": "released"}, "owner_department": {"describe_api_name": "WechatConversionObj", "pattern": "", "description": "负责人主属部门", "is_unique": false, "type": "text", "is_required": false, "define_type": "package", "is_single": false, "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "status": "released"}, "order_by": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "order_by", "api_name": "order_by", "description": "order_by", "status": "released", "index_name": "l_by"}}}