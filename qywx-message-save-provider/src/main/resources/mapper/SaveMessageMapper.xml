<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.facishare.open.qywx.save.dao.SaveMessageDao">

    <resultMap id="baseResultMap" type="qywxMessagePo">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="fs_ea" property="fsEa" javaType="java.lang.String"/>
        <result column="message_id" property="messageId" javaType="java.lang.String"/>
        <result column="key_version" property="keyVersion" javaType="java.lang.Integer"/>
        <result column="seq" property="seq" javaType="java.lang.Long"/>
        <result column="from_user" property="fromUser" javaType="java.lang.String"/>
        <result column="from_encryption_user" property="fromEncryptionUser" javaType="java.lang.String"/>
        <result column="to_list" property="toList" typeHandler="com.facishare.open.qywx.save.typeHandler.ListTypeHandler"/>
        <result column="to_encryption_list" property="toEncryptionList" typeHandler="com.facishare.open.qywx.save.typeHandler.ListTypeHandler"/>
        <result column="room_id" property="roomId" javaType="java.lang.String"/>
        <result column="message_time" property="messageTime" javaType="java.lang.Long"/>
        <result column="message_type" property="messageType" javaType="java.lang.String"/>
        <result column="content" property="content" javaType="java.lang.String"/>
        <result column="md5sum" property="md5sum" javaType="java.lang.String"/>
        <result column="sdk_file_id" property="sdkFileId" javaType="java.lang.String"/>
        <result column="npath" property="npath" javaType="java.lang.String"/>
        <result column="file_name" property="fileName" javaType="java.lang.String"/>
        <result column="file_ext" property="fileExt" javaType="java.lang.String"/>
        <result column="file_size" property="fileSize" javaType="java.lang.Long"/>
    </resultMap>

    <sql id="baseColumn">
        id,fs_ea,message_id,key_version,seq,from_user,from_encryption_user,to_list,to_encryption_list,room_id,message_time,message_type,content,md5sum
        ,sdk_file_id,npath,file_name,file_ext,file_size
    </sql>

    <insert id="batchSaveMessage">
        INSERT IGNORE INTO qyweixin_message_save(id,fs_ea,message_id,key_version,seq,from_user,from_encryption_user,to_list,to_encryption_list,room_id,message_time,message_type,content,md5sum,sdk_file_id,file_name,file_ext,file_size)
        VALUES
        <foreach collection="messageLists" item="item" separator="," index="index">
            (#{item.id}, #{item.fsEa}, #{item.messageId}, #{item.keyVersion}, #{item.seq},#{item.fromUser},#{item.fromEncryptionUser},#{item.toList},#{item.toEncryptionList},#{item.roomId},
            #{item.messageTime}, #{item.messageType}, #{item.content}, #{item.md5sum}
            ,#{item.sdkFileId},#{item.fileName},#{item.fileExt},#{item.fileSize})
        </foreach>
    </insert>

    <update id="batchUpdateMessageIds">
        update qyweixin_message_save
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="npath=case" suffix="end,">
                <foreach collection="messageLists" item="item" index="index">
                    <if test="item.npath!=null">
                        when message_id=#{item.messageId} then #{item.npath}
                    </if>
                </foreach>
            </trim>
            <trim prefix="file_size =case" suffix="end,">
                <foreach collection="messageLists" item="item" index="index">
                    <if test="item.fileSize!=null">
                        when message_id=#{item.messageId} then #{item.fileSize}
                    </if>
                </foreach>
            </trim>
            <trim prefix="file_name =case" suffix="end,">
                <foreach collection="messageLists" item="item" index="index">
                    <if test="item.fileName!=null">
                        when message_id=#{item.messageId} then #{item.fileName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="file_ext =case" suffix="end,">
                <foreach collection="messageLists" item="item" index="index">
                    <if test="item.fileExt!=null">
                        when message_id=#{item.messageId} then #{item.fileExt}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="messageLists" separator="or" item="item" index="index">
            message_id=#{item.messageId} and fs_ea=#{ea}
        </foreach>
    </update>


    <update id="updateMessage">
        update qyweixin_message_save
        <trim prefix="set" suffixOverrides=",">
            <if test="npath!=null">npath=#{npath},</if>
            <if test="fileSize!=null">file_size=#{fileSize},</if>
            <if test="fileName!=null">file_name=#{fileName},</if>
            <if test="fileExt!=null">file_ext=#{fileExt},</if>
        </trim>
        WHERE message_id=#{messageId} and fs_ea=#{fsEa}
    </update>


    <delete id="deleteMessageById"></delete>
    <delete id="batchDeleteMessageByIds"></delete>

    <select id="queryMessage" resultMap="baseResultMap">
        select <include refid="baseColumn"></include> from qyweixin_message_save
        <where>
            <if test="fsEa!=null and fsEa!=''">
                AND   fs_Ea=#{fsEa}
            </if>
            AND
            (
            (
            <if test="senderIds!=null ">from_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            OR
            (
            <if test="senderIds!=null ">from_encryption_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_encryption_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            )
            <if test="lastMessageTime!=null and preMessageTime!=null">
                AND   message_time BETWEEN #{preMessageTime}  and  #{lastMessageTime}
            </if>
            <choose>
                <when test="roomId!=null">
                    AND room_id = #{roomId}
                </when>
                <otherwise>
                    AND room_id is null
                </otherwise>
            </choose>
            ORDER BY message_time DESC
        </where>

    </select>

        <select id="queryMessageByNoRoom" resultMap="baseResultMap">
        select <include refid="baseColumn"></include> from qyweixin_message_save
        <where>
            <if test="fsEa!=null and fsEa!=''">
                AND   fs_Ea=#{fsEa}
            </if>
            AND room_id is null
            AND
            (
            (
            <if test="senderIds!=null ">from_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            OR
            (
            <if test="senderIds!=null ">from_encryption_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_encryption_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            )


            <if test="lastMessageTime!=null and preMessageTime!=null">
                AND   message_time BETWEEN #{preMessageTime}  and  #{lastMessageTime}
            </if>
            ORDER BY message_time DESC
        </where>

    </select>

    <select id="queryMessageByRoom" resultMap="baseResultMap">
        select <include refid="baseColumn"></include> from qyweixin_message_save
        <where>
            <if test="queryMessageArg.fsEa!=null and queryMessageArg.fsEa!=''">
                AND   fs_Ea=#{queryMessageArg.fsEa}
            </if>
            AND room_id = #{roomId}
            AND
            (
            (
            <if test="queryMessageArg.senderIds!=null ">
                from_user in
                <foreach collection="queryMessageArg.senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="queryMessageArg.receiveIds!=null ">
                AND (
                <foreach collection="queryMessageArg.senderIds" separator="or" index="index" item="item">
                    to_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            OR
            (
            <if test="queryMessageArg.senderIds!=null "> from_encryption_user in
                <foreach collection="queryMessageArg.senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="queryMessageArg.receiveIds!=null ">
                AND (
                <foreach collection="queryMessageArg.senderIds" separator="or" index="index" item="item">
                    to_encryption_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            )

            <if test="queryMessageArg.lastMessageTime!=null and queryMessageArg.preMessageTime!=null">
                AND   message_time BETWEEN #{queryMessageArg.preMessageTime}  and  #{queryMessageArg.lastMessageTime}
            </if>
            ORDER BY message_time DESC
        </where>

    </select>

    <select id="getIsRoom" resultType="java.lang.Integer">
        select count(*) from qyweixin_message_save
        <where>
            <if test="fsEa!=null and fsEa!=''">
                AND   fs_Ea=#{fsEa}
            </if>
            AND room_id is null
            AND
            (
            (
            <if test="senderIds!=null ">from_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            OR
            (
            <if test="senderIds!=null ">from_encryption_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_encryption_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            )
            <if test="lastMessageTime!=null and preMessageTime!=null">
                AND   message_time BETWEEN #{preMessageTime}  and  #{lastMessageTime}
            </if>
        </where>
    </select>

    <select id="getRoom" resultType="java.lang.String">
        select distinct(room_id) from qyweixin_message_save
        <where>
            <if test="fsEa!=null and fsEa!=''">
                AND   fs_Ea=#{fsEa}
            </if>
            AND room_id is not null
            AND
            (
            (
            <if test="senderIds!=null ">from_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            OR
            (
            <if test="senderIds!=null ">from_encryption_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_encryption_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            )
            <if test="lastMessageTime!=null and preMessageTime!=null">
                AND   message_time BETWEEN #{preMessageTime}  and  #{lastMessageTime}
            </if>
        </where>
    </select>

    <select id="queryMessageById" resultMap="baseResultMap">
        SELECT <include refid="baseColumn"></include> from qyweixin_message_save where message_id=#{messageId} and fs_ea=#{ea}

    </select>
    <select id="queryLastSeq" resultType="java.lang.Long">
        select seq from qyweixin_message_save
        where fs_ea=#{ea} order by message_time desc limit 1
    </select>
    <select id="queryPreMessageTime" resultType="java.lang.Long">
        select message_time from qyweixin_message_save
        <where>
            fs_ea=#{fsEa}
            AND
            (
            (
            <if test="senderIds!=null ">from_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            OR
            (
            <if test="senderIds!=null ">from_encryption_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_encryption_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            )
            <choose>
                <when test="roomId!=null">
                    AND room_id = #{roomId}
                </when>
                <otherwise>
                    AND room_id is null
                </otherwise>
            </choose>
            order by message_time desc limit #{limit} offset 0
        </where>
    </select>

    <select id="queryChat" resultType="java.lang.Integer">
        select count(*) from qyweixin_message_save
        <where>
            <if test="fsEa!=null and fsEa!=''">
                AND   fs_Ea=#{fsEa}
            </if>
            AND
            (
            (
            <if test="senderIds!=null ">from_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            OR
            (
            <if test="senderIds!=null ">from_encryption_user in
                <foreach collection="senderIds" separator="," index="index" open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="receiveIds!=null ">
                AND (
                <foreach collection="senderIds" separator="or" index="index" item="item">
                    to_encryption_list like concat ('%',#{item},'%')
                </foreach>
                )
            </if>
            )
            )
            <if test="lastMessageTime!=null and preMessageTime!=null">
                AND   message_time BETWEEN #{preMessageTime}  and  #{lastMessageTime}
            </if>
        </where>
    </select>

    <select id="batchQueryMessageByIds" resultMap="baseResultMap">
        SELECT <include refid="baseColumn"></include>
        from qyweixin_message_save
        where
        fs_ea=#{ea}
        and message_id in
        <foreach collection="messageIdLists" separator="," index="index" open="(" close=")" item="item">
            #{item}
        </foreach>
        order by message_time
    </select>

    <select id="queryMessageBySeq" resultMap="baseResultMap">
        SELECT <include refid="baseColumn"></include>
        from qyweixin_message_save
        where
        fs_ea=#{ea}
        and seq BETWEEN #{startSeq} and #{endSeq}
        order by message_time
    </select>

    <select id="queryAllMessage" resultMap="baseResultMap">
        select <include refid="baseColumn"></include> from qyweixin_message_save
        <where>
            <if test="fsEa!=null and fsEa!=''">
                AND   fs_Ea=#{fsEa}
            </if>
            ORDER BY message_time
        </where>

    </select>

</mapper>