<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.facishare.open.qywx.save.dao.QyweixinIdToOpenidDao">

    <resultMap id="baseResultMap" type="qyweixinIdToOpenidPo">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="corp_id" property="corpId" javaType="java.lang.String"/>
        <result column="plaintext_id" property="plaintextId" javaType="java.lang.String"/>
        <result column="openid" property="openid" javaType="java.lang.String"/>
        <result column="type" property="type" javaType="java.lang.Integer"/>

    </resultMap>

    <sql id="baseColumn">
        id, corp_id, plaintext_id, openid, type
    </sql>


    <insert id="saveInfo" parameterType="messageGeneratingPo">
        insert  into qyweixin_id_to_openid (corp_id,plaintext_id,openid,type)
        values (#{corpId},#{plaintextId},#{openid},#{type})
        ON DUPLICATE KEY UPDATE openid=#{openid}
    </insert>

    <insert id="batchSaveInfo" parameterType="messageGeneratingPo">
        insert IGNORE into qyweixin_id_to_openid (corp_id,plaintext_id,openid,type)
        values
        <foreach collection="qyweixinIdToOpenidPoList" item="item" separator="," index="index">
        (#{item.corpId},#{item.plaintextId},#{item.openid},#{item.type})
        </foreach>
    </insert>

    <select id="getByExternalIds" resultType="java.lang.String">
        select plaintext_id from qyweixin_id_to_openid
        where corp_id=#{corpId}
        and plaintext_id in
        <foreach collection="externalIds" separator="," index="index" open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getByOpenIds" resultType="QyweixinIdToOpenidPo">
        select <include refid="baseColumn"></include>
        from qyweixin_id_to_openid
        where corp_id=#{corpId}
        and openid in
        <foreach collection="openIds" separator="," index="index" open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getPoByExternalIds" resultType="QyweixinIdToOpenidPo">
        select <include refid="baseColumn"></include>
        from qyweixin_id_to_openid
        where corp_id=#{corpId}
        and plaintext_id in
        <foreach collection="externalIds" separator="," index="index" open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

</mapper>