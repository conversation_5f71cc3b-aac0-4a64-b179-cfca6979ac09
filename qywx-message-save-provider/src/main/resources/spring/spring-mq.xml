<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--发送企业自建应用的secret -->
<!--    <bean id="secretMessageSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-message-save"/>-->
<!--        <constructor-arg index="1" value="QX_WX_NAMESERVER"/>-->
<!--        <constructor-arg index="2" value="QX_WX_GROUP"/>-->
<!--        <constructor-arg index="3" value="QX_WX_MSG_TOPIC"/>-->
<!--    </bean>-->

<!--    <bean id="conversionSettingMessageSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-message-save"/>-->
<!--        <constructor-arg index="1" value="QYWX_CONVERSION_SETTING_NAMESERVER"/>-->
<!--        <constructor-arg index="2" value="QYWX_CONVERSION_SETTING_GROUP"/>-->
<!--        <constructor-arg index="3" value="QYWX_CONVERSION_SETTING_TOPIC"/>-->
<!--    </bean>-->

<!--    <bean id="conversionChangeMessageSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-message-save"/>-->
<!--        <constructor-arg index="1" value="QYWX_CONVERSION_CHANGE_NAMESERVER"/>-->
<!--        <constructor-arg index="2" value="QYWX_CONVERSION_CHANGE_GROUP"/>-->
<!--        <constructor-arg index="3" value="QYWX_CONVERSION_CHANGE_TOPIC"/>-->
<!--    </bean>-->

    <!--发送企业自建应用的secret -->
    <bean id="secretMessageSender" class="com.facishare.open.qywx.save.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX_UPGRADE_NOTICE_EVENT_SECTION"/>
    </bean>

    <bean id="conversionSettingMessageSender" class="com.facishare.open.qywx.save.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX-CONVERSION-SETTING-EVENT-SECTION"/>
    </bean>

    <bean id="conversionChangeMessageSender" class="com.facishare.open.qywx.save.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX-CONVERSION-CHANGE-EVENT-SECTION"/>
    </bean>

    <bean id="outEventDataChangeMQSender" class="com.facishare.open.qywx.save.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="out-event-save-section"/>
    </bean>
</beans>