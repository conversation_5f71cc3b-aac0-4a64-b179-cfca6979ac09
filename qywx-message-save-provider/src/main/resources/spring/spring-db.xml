<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <!-- MySQL数据库配置 -->
    <bean id="openQyexDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="fs-open-qywx-db"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="openQyexDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.qywx.message.save"/>
        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>
        <property name="mapperLocations" value="classpath*:mapper/*.xml"/>
    </bean>
    <!-- scan for mapper and let them be autowired, interface path -->
    <bean id="dbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.qywx.save.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>
    <!--事务管理器-->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="openQyexDB"/>
    </bean>
    <!--使用注解事务 -->
    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true"/>


</beans>