<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <description>dubbo消费者接口</description>
    <dubbo:reference id="accountBindService"
                     interface="com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService"
                     protocol="dubbo"
                     version="1.0"
                     check="false"
                     />
    <dubbo:reference id="qyweixinAccountSyncService"
                     interface="com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService"
                     version="2.0"
                     timeout="300000"
                     check="false"
                     />

    <dubbo:reference id="qyweixinGatewayInnerService"
                     interface="com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />
    <import resource="classpath:spring/qywx-i18n.xml"/>

</beans>