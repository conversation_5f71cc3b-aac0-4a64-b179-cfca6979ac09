<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="useGeneratedKeys" value="false"/>
        <setting name="autoMappingBehavior" value="PARTIAL"/>
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <setting name="defaultStatementTimeout" value="25"/>
        <setting name="safeRowBoundsEnabled" value="false"/>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <setting name="localCacheScope" value="SESSION"/>
        <setting name="logImpl" value="STDOUT_LOGGING"/>
        <setting name="jdbcTypeForNull" value="OTHER"/>
        <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString"/>
    </settings>
    <typeAliases>
        <package name="com.facishare.open.qywx.save.po" />
    </typeAliases>
    <typeHandlers>
        <typeHandler javaType="string" handler="com.github.mybatis.handler.StringTypeUtf8mb4Handler"/>
        <typeHandler  handler="com.facishare.open.qywx.save.typeHandler.ListTypeHandler"/>
    </typeHandlers>
    <plugins>
        <plugin interceptor="com.github.mybatis.interceptor.MasterSlaveInterceptor"/>
        <plugin interceptor="com.github.mybatis.interceptor.PaginationAutoMapInterceptor"/>
        <plugin interceptor="com.github.pagehelper.PageInterceptor">
            <property name="helperDialect" value="mysql"/>
            <property name="reasonable" value="true"/>
            <!-- 当该参数设置为 true 时，如果 pageSize=0 或者 RowBounds.num = 0 就会查询出全部的结果 -->
            <property name="pageSizeZero" value="true"/>
        </plugin>
        <plugin interceptor="com.facishare.open.qywx.save.interceptor.EncryptInterceptor">
        </plugin>
        <plugin interceptor="com.facishare.open.qywx.save.interceptor.DecryptInterceptor">
        </plugin>
    </plugins>

    <!--dao路径-->
    <mappers>
        <package name="com.facishare.open.qywx.save.dao"/>
    </mappers>

</configuration>
