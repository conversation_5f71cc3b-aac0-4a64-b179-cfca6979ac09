<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">


    <!--mongo-->
    <bean id="oaSyncLogMongo" name="oaSyncLogMongo"
          class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-open-qywx-message-save"/>
        <property name="sectionNames" value="messageMongo"/>
    </bean>
    <!--  分发框架的mongo-->


</beans>