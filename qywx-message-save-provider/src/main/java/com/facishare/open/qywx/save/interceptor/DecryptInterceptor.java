package com.facishare.open.qywx.save.interceptor;

import com.facishare.open.qywx.accountinner.annotation.SecurityObj;
import com.facishare.open.qywx.save.utils.SecurityUtil;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.CollectionUtils;

import java.sql.Statement;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2023/4/23 19:13
 * @Version 1.0
 */
@Intercepts({
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
})
public class DecryptInterceptor implements Interceptor {
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //取出查询的结果
        Object resultObject = invocation.proceed();
        if (Objects.isNull(resultObject)) {
            return null;
        }
        //基于selectList
        if (resultObject instanceof ArrayList) {
            ArrayList resultList = (ArrayList) resultObject;
            if (!CollectionUtils.isEmpty(resultList) && needToDecrypt(resultList.get(0))) {
                for (Object result : resultList) {
                    //逐一解密
                    SecurityUtil.decrypt(result);
                }
            }
            //基于selectOne
        } else {
            if (needToDecrypt(resultObject)) {
                SecurityUtil.decrypt(resultObject);
            }
        }
        return resultObject;
    }
    private boolean needToDecrypt(Object object) {
        Class<?> objectClass = object.getClass();
        SecurityObj sensitiveData = AnnotationUtils.findAnnotation(objectClass, SecurityObj.class);
        return Objects.nonNull(sensitiveData);
    }
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
