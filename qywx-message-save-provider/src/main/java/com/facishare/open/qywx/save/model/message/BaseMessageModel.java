package com.facishare.open.qywx.save.model.message;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/17 17:56
 * @Version 1.0
 */
@Data
public class BaseMessageModel implements Serializable {
    private String msgid;
    private String action;//消息动作，目前有send(发送消息)/recall(撤回消息)/switch(切换企业日志)三种类型。String类型
    private String from;
    private List<String> toList;
    private String content;//文本
    private String roomid;//消息群id
    private Long msgtime;//消息发送时间戳
    private String msgType;
    private String sdkfileid;
    private Integer fileSize;
    private String name;//文件名字
    private String fileExt;//扩展名

}
