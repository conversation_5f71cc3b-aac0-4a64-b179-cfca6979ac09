package com.facishare.open.qywx.save.job;


import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/3/29 12:11
 * 企业微信会话存档定时拉取任务
 * @Version 1.0
 */
@JobHander(value = "AutoPullMessageJobHandler")
@Component
@Slf4j
public class AutoPullMessageJobHandler extends IJobHandler {
    @Autowired
    private AutoPullMessageService autoPullMessageService;


    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {

            log.info("scan AutoEndAndTransfer start the job!");
            autoPullMessageService.getCorpIdMessage();
            log.info("scan AutoEndAndTransfer end the job!");
            return new ReturnT(ReturnT.SUCCESS_CODE, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }
}
