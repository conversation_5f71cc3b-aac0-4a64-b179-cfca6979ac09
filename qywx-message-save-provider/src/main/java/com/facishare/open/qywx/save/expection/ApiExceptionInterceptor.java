package com.facishare.open.qywx.save.expection;


import com.facishare.open.qywx.save.enums.ErrorRefer;
import com.facishare.open.qywx.save.result.Result;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

@lombok.extern.slf4j.Slf4j
public class ApiExceptionInterceptor implements MethodInterceptor {

	@Override
	public Object invoke(MethodInvocation invocation) throws Throwable {
		try {
			return invocation.proceed();
		} catch (Exception e) {
			log.error("api error,serviceName:{},arg:{},exception", invocation.getMethod(), invocation.getArguments(), e);
			return new Result<>(ErrorRefer.SYSTEM_ERROR);
		}
	}

}
