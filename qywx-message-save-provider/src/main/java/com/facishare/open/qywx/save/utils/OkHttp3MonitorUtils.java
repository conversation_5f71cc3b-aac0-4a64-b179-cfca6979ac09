package com.facishare.open.qywx.save.utils;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * OkHttp3工具类，基于http-spring-support，会上报蜂眼
 * Created by system on 2018/4/16.
 */
@Component
@Slf4j
public class OkHttp3MonitorUtils {

    private static OkHttp3MonitorUtils okHttp3Utils;

    @Resource
    @Qualifier("okHttpSupport")
    private OkHttpSupport httpSupport;

    @PostConstruct
    public void init() {
        okHttp3Utils = this;
    }

    /**
     * 发送GET Request
     * @param url
     * @param headers
     * @param params
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Get(String url,
                                                     Map<String, String> headers,
                                                     Map<String, Object> params) {
        Request request = UrlUtils.buildGetRequest(params, headers, url);
        return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
    }

    /**
     * 发送Form格式的Post Request
     * @param url
     * @param headers
     * @param params
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Post(String url,
                                                      Map<String, String> headers,
                                                      Map<String, Object> params) {
        Request request = UrlUtils.buildFormPostRequest(params, headers, url);
        return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
    }

    /**
     * 发送json格式的Post Request
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Post(String url, Map<String, String> headers, String json) {
        Request request = UrlUtils.buildJsonPostRequest(json, headers, url);
        return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
    }

    /**
     * 发送json格式的Put Request
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Put(String url, Map<String, String> headers, String json) {
        Request request = UrlUtils.buildJsonPutRequest(json, headers, url);
        return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
    }

    /**
     * 发送Delete Request
     * @param url
     * @param headers
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Delete(String url, Map<String, String> headers) {
        Request request = UrlUtils.buildDeleteRequest(headers, url);
        return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
    }

}
