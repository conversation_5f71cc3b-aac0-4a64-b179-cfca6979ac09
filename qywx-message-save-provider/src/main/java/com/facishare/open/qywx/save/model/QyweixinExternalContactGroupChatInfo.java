package com.facishare.open.qywx.save.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinExternalContactGroupChatInfo implements Serializable {
    private String chatId;
    private String name;
    private String owner;
    private String notice;
    private String createTime;
    private List<Member> memberList;
    private List<AdminList> adminList;

    @Data
    public static class Member implements Serializable {
        private String userId;
        private String type;
        private String joinTime;
        private String joinScene;
        private String groupNickname;
        private String name;
        private Invitor invitor;

        @Data
        public static class Invitor implements Serializable {
            private String userId;
        }
    }

    @Data
    private static class AdminList implements Serializable {
        private String userId;
    }
}
