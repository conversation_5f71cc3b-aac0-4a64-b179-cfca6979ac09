package com.facishare.open.qywx.save.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.qywx.accountsync.model.qyweixin.AutoMessageArg;
import com.facishare.open.qywx.accountsync.model.qyweixin.InnerSearchQueryInfo;
import com.facishare.open.qywx.save.crm.CrmUrlUtils;
import com.facishare.open.qywx.save.limiter.CrmRateLimiter;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.facishare.open.qywx.save.utils.HttpResponseMessage;
import com.facishare.open.qywx.save.utils.OkHttp3MonitorUtils;
import com.facishare.open.qywx.save.vo.AutRetentionVo;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.EmployeeId;
import com.facishare.qixin.api.model.message.out.CreateOuterMergedMessageApiArg;
import com.facishare.qixin.api.model.message.out.OuterMessageInfo;
import com.facishare.qixin.api.model.message.result.CreateMergedMessageResult;
import com.facishare.qixin.api.service.MessageService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.facishare.open.qywx.save.config.ConfigCenter.ACTIVERECORDOBJ_URL;


@Service
@Slf4j
public class ExternalContactManager {

    @Resource(name = "messageService")
    private MessageService messageService;


    /**
     * 根据手机号查询对象数据
     */
    public Map<String,String> queryByPhone(Integer enterpriseId, String filed, String filedValue, String obj){
        if(filedValue == null || filedValue.length() == 0) {
            return null;
        }
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);
        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, String> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", obj);
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return null;
        }
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/" + obj), hearsMap, JSONObject.toJSONString(queryMap));
        System.out.println(httpResponseMessage.getContent());
//        log.info("query emp obj:arg:{},result:{}",queryMap,httpResponseMessage.getContent());
        JSONArray read = (JSONArray) JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList");
        if(read.size()==0){
//            log.warn("query by phone failed,arg:{},result:{}",innerSearchQueryInfo,httpResponseMessage);
            Map<String, String> objectMap = Maps.newHashMap();
            return objectMap;
        }
        //String userID = JSONPath.read(httpResponseMessage, "$.data.dataList[0].user_id").toString();
        String object_id = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0]._id").toString();
        Map<String, String> objectMap = Maps.newHashMap();
        //objectMap.put("user_id",userID);
        objectMap.put("object_id",object_id);
        return objectMap;
    }

    public void saveSales(AutRetentionVo autRetentionVo) {
        CreateOuterMergedMessageApiArg createOuterMergedMessageApiArg = new CreateOuterMergedMessageApiArg();
        List<OuterMessageInfo> outerMessageInfos = new LinkedList<>();
        autRetentionVo.getPagerResult().getData().stream().forEach(item -> {
            OuterMessageInfo outerMessageInfo = new OuterMessageInfo();
            outerMessageInfo.setMessageId(item.getMessageId());
            outerMessageInfo.setTimeStamp(item.getTimeStamp());
            outerMessageInfo.setType(item.getType());
            outerMessageInfo.setContent(item.getContent());
            outerMessageInfo.setSenderId(item.getSenderId());
            outerMessageInfo.setSenderName(item.getSenderName());
            outerMessageInfo.setSenderPortrait(item.getSenderPortrait());
            outerMessageInfo.setFsFullSenderId(item.getFsFullSenderId());
            outerMessageInfos.add(outerMessageInfo);
        });
        createOuterMergedMessageApiArg.setOutMessageInfoList(outerMessageInfos);
        if(!StringUtils.isEmpty(autRetentionVo.getRoomFsId())) {
            //走群聊
            createOuterMergedMessageApiArg.setSessionName(autRetentionVo.getUserName() +"和"+ autRetentionVo.getContactName() + "在" + (autRetentionVo.getRoomName() == null ? "群" : autRetentionVo.getRoomName()));
        } else {
            createOuterMergedMessageApiArg.setSessionName(autRetentionVo.getUserName() +"和"+ autRetentionVo.getContactName());
        }
        AuthInfo authInfo=new AuthInfo();
        EmployeeId employeeId=new EmployeeId(EmployeeId.Type.OUT, autRetentionVo.getEa(), autRetentionVo.getFsId());
        authInfo.setEmployeeId(employeeId);
        createOuterMergedMessageApiArg.setAuthInfo(authInfo);

        CreateMergedMessageResult createMergedMessageResult = messageService.createOuterMergedMessage(createOuterMergedMessageApiArg);
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage:createMergedMessageResult={}", createMergedMessageResult);
        String url = createMergedMessageResult.getLinkUrl();
        String content = createMergedMessageResult.getContent();
        String title = createMergedMessageResult.getTitle();
        AutoMessageArg autoMessageArg = new AutoMessageArg();
        autoMessageArg.setEi(autRetentionVo.getEi());
        autoMessageArg.setEa(autRetentionVo.getEa());
        autoMessageArg.setFsId(autRetentionVo.getFsId());
        if(!StringUtils.isEmpty(autRetentionVo.getRoomFsId())) {
            //走群聊
            autoMessageArg.setCreatorId(autRetentionVo.getRoomFsId());
        } else {
            autoMessageArg.setCreatorId(autRetentionVo.getFsId());
        }
        autoMessageArg.setContent(content);
        autoMessageArg.setTitle(title);
        autoMessageArg.setUrl(url);
        autoMessageArg.setRelatedObject(autRetentionVo.getRelatedObject());

        String result = sendAutoMessage(autoMessageArg, autRetentionVo.getAttempt());
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.noRoom result:{}", result);
        if (JSONPath.read(result, "$.errCode").equals(0)) {
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.noRoom  生成销售记录成功。result={}, userName={}, contactId={}", result, autRetentionVo.getUserName(), autRetentionVo.getContactId());
        } else {
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.noRoom  生成销售记录失败。result={}, userName={}, contactId={}", result, autRetentionVo.getUserName(), autRetentionVo.getContactId());
        }
    }

    private String sendAutoMessage(AutoMessageArg autoMessageArg, Map<String, Object> attempt) {
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.sendAutoMessage: autoMessageArg={}, attempt={}", autoMessageArg, attempt);
        Map<String, String> headerMap = new HashMap<>();
        Map<String, Object> bodyMap = new HashMap<>();
        Map<String, Object> objectData = new HashMap<>();
        Map<String, Object> activeRecordContent = new HashMap<>();
        Map<String, Object> url = new HashMap<>();
        headerMap.put("x-fs-ei", String.valueOf(autoMessageArg.getEi()));
        headerMap.put("x-fs-userInfo", String.valueOf(autoMessageArg.getFsId()));
        headerMap.put("x-tenant-id", autoMessageArg.getEa());
        headerMap.put("x-user-id", String.valueOf(autoMessageArg.getFsId()));
        headerMap.put("x-fs-locale", "zh-CN");
        headerMap.put("Content-Type", "application/json");

        url.put("summary", autoMessageArg.getContent());
        url.put("title", autoMessageArg.getTitle());
        url.put("url", autoMessageArg.getUrl());
        url.put("icon", "https://a9.fspage.com/FSR/weex/avatar/feed/images/chat_history_to_sales_record.png");
        activeRecordContent.put("url", url);
        activeRecordContent.put("text", autoMessageArg.getTitle());
        activeRecordContent.put("attachments", attempt.get("attachments"));

        List<String> list = new LinkedList<>();
        list.add(autoMessageArg.getCreatorId());
        objectData.put("object_describe_api_name", "ActiveRecordObj");
        objectData.put("record_type", "default__c");
        objectData.put("active_record_type", "b9523b9e443d41f993a20cd8e9ded0b9");
        objectData.put("created_by", list);
        objectData.put("active_record_content", activeRecordContent);
        objectData.put("related_object", autoMessageArg.getRelatedObject());
        bodyMap.put("object_data", objectData);
        bodyMap.put("source", "501");
        //String ACTIVERECORDOBJ_URL="http://172.31.101.246:17263/API/v1/inner/object/ActiveRecordObj/action/Add";

        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.sendAutoMessage: ACTIVERECORDOBJ_URL={}, headerMap={}, bodyMap={}", ACTIVERECORDOBJ_URL, headerMap, bodyMap);
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return null;
        }
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(ACTIVERECORDOBJ_URL, headerMap, JSONObject.toJSONString(bodyMap));
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage:httpResponseMessage={}", httpResponseMessage);
        return httpResponseMessage.getContent();

    }


}
