package com.facishare.open.qywx.save.service.impl;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QywxAccessTokenInfo;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountsync.utils.GsonUtil;
import com.facishare.open.qywx.save.arg.ConversionChangeArg;
import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArg;
import com.facishare.open.qywx.save.config.ConfigCenter;
import com.facishare.open.qywx.save.constant.Constant;
import com.facishare.open.qywx.save.constant.FileEnum;
import com.facishare.open.qywx.save.dao.QyweixinIdToOpenidDao;
import com.facishare.open.qywx.save.dao.SaveMessageDao;
import com.facishare.open.qywx.save.enums.ErrorRefer;
import com.facishare.open.qywx.save.manager.ExternalContactManager;
import com.facishare.open.qywx.save.manager.OANewBaseManager;
import com.facishare.open.qywx.save.manager.QYWXFileManager;
import com.facishare.open.qywx.save.mongo.dao.MessageSaveMongoDao;
import com.facishare.open.qywx.save.mq.QiXinMsgSender;
import com.facishare.open.qywx.save.network.ProxyOkHttpClient;
import com.facishare.open.qywx.save.po.QyweixinIdToOpenidPo;
import com.facishare.open.qywx.save.po.QywxMessagePo;
import com.facishare.open.qywx.save.result.*;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.save.utils.HttpResponseMessage;
import com.facishare.open.qywx.save.utils.OkHttp3MonitorUtils;
import com.facishare.open.qywx.save.utils.RSAUtil;
import com.facishare.open.qywx.save.vo.AutRetentionVo;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.open.qywx.save.vo.QywxMessageVo;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.EmployeeId;
import com.facishare.qixin.api.model.message.out.CreateOuterMergedMessageApiArg;
import com.facishare.qixin.api.model.message.out.OuterMessageInfo;
import com.facishare.qixin.api.model.message.result.CreateMergedMessageResult;
import com.facishare.qixin.api.service.MessageService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.client.FindIterable;
import io.protostuff.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.facishare.open.qywx.save.config.ConfigCenter.ACTIVERECORDOBJ_URL;

/**
 * <AUTHOR>
 * @Date 2021/3/29 19:21
 * @Version 1.0
 */
@Service("autoPullMessageServiceImpl")
@Slf4j
public class AutoPullMessageServiceImpl implements AutoPullMessageService {

    @Autowired
    private MessageGeneratingService settingService;
    @Autowired
    private QYWXFileManager qywxFileManager;
    @Autowired
    private SaveMessageService saveMessageService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private MessageGeneratingService generatingService;
    @Autowired
    private ExternalContactManager externalContactManager;
    @Autowired
    private MessageService messageService;
    @Autowired
    private ProxyOkHttpClient proxyOkHttpClient;
    @Autowired
    private SaveMessageDao saveMessageDao;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private MessageSaveMongoDao messageSaveMongoDao;
    @Autowired
    private QyweixinIdToOpenidDao qyweixinIdToOpenidDao;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private QiXinMsgSender qiXinMsgSender;
    @Resource
    private OANewBaseManager oaNewBaseManager;

    private static String OUT_ACCOUNT = "OUT.WX.";

    ExecutorService pullMessagesThreadPool = Executors.newFixedThreadPool(20);
    ExecutorService autoPullMessagesThreadPool = Executors.newFixedThreadPool(5);
    @Override
    public Result<Integer> getCorpIdMessage() {
        Result<List<String>> eaLists = settingService.queryAllSetting();
        eaLists.getData().forEach(item ->{
            Boolean isNewBase = oaNewBaseManager.canRunInNewBaseByFsEa(item);
            if(!isNewBase) {
                Result<GenerateSettingVo> enterpriseResult = settingService.querySetting(item, null, null);
                if(ObjectUtils.isNotEmpty(enterpriseResult.getData())
                        && !StringUtils.isEmpty(enterpriseResult.getData().getSecret())
                        && !StringUtils.isEmpty(enterpriseResult.getData().getPrivateKey())
                        && !StringUtils.isEmpty(enterpriseResult.getData().getPublicKey())) {
                    List<MessageSaveDoc> messageSaveDocs = messageSaveMongoDao.queryLastSeq(eieaConverter.enterpriseAccountToId(item), item);
                    Long seq = CollectionUtils.isNotEmpty(messageSaveDocs) ? messageSaveDocs.get(0).getSeq() : 0L;
                    log.info("ea={} auto task starting,seq={}", item, seq);
                    Long finalSeq = seq;
                    pullMessagesThreadPool.execute(()-> qywxFileManager.saveMessage(enterpriseResult.getData(), finalSeq));
                }
            }
        });
        return null;
    }

    @Override
    public Result<Integer> getAutoSynchronizationMessage() {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);
        if(StringUtils.isNotEmpty(redisDataSource.getRedisClient().get("QYWX_autoSynchronizationMessage"))) {
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage,redis is exist.");
            return new Result<>();
        }
        redisDataSource.getRedisClient().set("QYWX_autoSynchronizationMessage", "QYWX_autoSynchronizationMessage");
        redisDataSource.getRedisClient().expire("QYWX_autoSynchronizationMessage", 7200L);
        //查询留存的企业ea
        Result<List<String>> eaListResult = settingService.queryAllSetting();
        if(!eaListResult.isSuccess() || CollectionUtils.isEmpty(eaListResult.getData())) {
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage:  eaList is not automatic retention");
            //一个企业都没有，直接退出
            return new Result<>();
        }
        List<String> eaList = eaListResult.getData();
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage: eaList={}", eaList);
        for (int i = 0; i < eaList.size(); i++) {
            String ea = eaList.get(i);
            Boolean isNewBase = oaNewBaseManager.canRunInNewBaseByFsEa(ea);
            if(isNewBase) {
                continue;
            }
            //查询开启了自动留存的企业ea
            Result<GenerateSettingVo> enterpriseResult = settingService.querySetting(ea, null, null);
            if(!enterpriseResult.isSuccess() || ObjectUtils.isEmpty(enterpriseResult.getData())) {
                continue;
            }
            MessageStorageArg messageStorageArg = enterpriseResult.getData().getStorageLocation();
            if(messageStorageArg.getSalesRetentionType() == 0) {
                log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage: messageStorageArg={}", messageStorageArg);
                continue;
            }
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage:this is a ea={}", eaList.get(i));
            autoPullMessagesThreadPool.execute(()-> this.getAutoSynchronizationMessage2(ea));
        }
        return null;
    }

    @Override
    public Result<Integer> getMessageByCallBackToken(String qywxCorpId,String appId, String notifyId) {
        log.info("getMessageByCallBackToken.qywxCorpId{}:appId={}，notifyId：{}", qywxCorpId,appId,notifyId);
        com.facishare.open.qywx.accountsync.result.Result<QywxAccessTokenInfo> accessTokenInfo2 = qyweixinGatewayInnerService.getAccessTokenInfo2(qywxCorpId, appId);
        if(accessTokenInfo2.isSuccess()&&ObjectUtils.isNotEmpty(accessTokenInfo2.getData())){
            QywxAccessTokenInfo data = accessTokenInfo2.getData();
            String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,data.getCorpAccessToken());
            //拿到纷享ea
            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> listResult = qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), qywxCorpId);
            if(listResult.isSuccess()&&ObjectUtils.isNotEmpty(listResult.getData())){
                for (QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping : listResult.getData()) {
                    String ea=qyweixinAccountEnterpriseMapping.getFsEa();
                    //根据企业微信给的notifyId，返回对应额数据
                    QywxMessageDataResult callData = getCallData(callUrl, Constant.GET_CALL_BACK_DATA, ConfigCenter.programId, notifyId, "{}");
                    //conversation_new_message

                    if(callData.isSuccess()){
                        //判断对应的eventType
                        JSONObject jsonObject = JSONObject.parseObject(callData.getResponseData());

                        String eventType = jsonObject.getString("event_type");
                        switch (eventType){
                            //"{\"event_type\":\"conversation_new_message\",\"timestamp\":**********,\"conversation_new_message\":{\"token\":\"ENC4npkrg5u6MqmzZqrsdSiswQDJfJMmcz7Uf6GuTRWca1p\"}}"
                            case Constant.CONVERSATION_MESSAGE_TYPE:
                                //存储会话信息
                                Map<String,String> conversationNewMessage = JSONObject.parseObject(jsonObject.get("conversation_new_message").toString(), Map.class);
                                String messageToken=  conversationNewMessage.get("token");;//十分钟有效

                                boolean hasMore=true;
                                String cursor=null;
                                Result<GenerateSettingVo> generateSettingVoResult = generatingService.queryByEaSettingByLastVersion(ea);
                                if(ObjectUtils.isEmpty(generateSettingVoResult.getData())){
                                    log.info("generate setting data is null:{}",ea);
                                    break;
                                }
                                if(generateSettingVoResult.isSuccess()){
                                    String nextCursor = generateSettingVoResult.getData().getStorageLocation().getNextCursor();
                                    if(StringUtils.isNotEmpty(nextCursor)){
                                        cursor=nextCursor;
                                    }
                                }
                                while (hasMore){
                                    List<MessageSaveDoc> messageSaveDocs=new ArrayList<>();
                                    MessageConvertResult messageConvertResult = getConversionMessage(ea, appId, callUrl, messageToken, messageSaveDocs, cursor);
                                    if(messageConvertResult==null&&!messageConvertResult.isSuccess()){
                                        break;
                                    }
                                    if(messageConvertResult!=null&&messageConvertResult.isSuccess()){
                                        cursor=messageConvertResult.getNext_cursor();
                                        hasMore=messageConvertResult.getHas_more()==1?true:false;
                                    }

                                    if(CollectionUtils.isNotEmpty(messageSaveDocs)){
                                        Set<ConversionChangeArg.ChangeModel> changeModelSet = new HashSet<>();

                                        for (MessageSaveDoc messageSaveDoc : messageSaveDocs) {
                                            ConversionChangeArg.ChangeModel changeModel=new ConversionChangeArg.ChangeModel();
                                            changeModel.setIntelligentMessage(true);
                                            if(org.apache.commons.lang.StringUtils.isNotEmpty(messageSaveDoc.getRoomId())) {
                                                changeModel.setRoomId(messageSaveDoc.getRoomId());
                                            } else {
                                                changeModel.setFromCipherId(messageSaveDoc.getFromUser());
                                                changeModel.setToCipherId(messageSaveDoc.getToList().get(0));
                                            }
                                            changeModelSet.add(changeModel);
                                        }
                                        qywxFileManager.saveMessage(messageSaveDocs,generateSettingVoResult.getData(),false);
                                        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(changeModelSet)) {
                                            ConversionChangeArg conversionChangeArg = new ConversionChangeArg();
                                            conversionChangeArg.setEa(ea);
                                            conversionChangeArg.setChangeModelList(new LinkedList<>(changeModelSet));
                                            qiXinMsgSender.sendToConversionChange(conversionChangeArg);
                                        }

                                    }
                                    //存储cursor
                                    MessageStorageArg storageLocation = generateSettingVoResult.getData().getStorageLocation();
                                    storageLocation.setNextCursor(cursor);
                                    generatingService.updateCorpRepSecret(generateSettingVoResult.getData());
                                }

                                break;
                            default:
                        }
                    }
                }
            }

        }


        return null;
    }

    @Override
    public Result<QueryMessageIdResult> conditionQueryMessageData(String qywxCorpId, String appId, QywxQueryMessageArg queryMessageArg) {
        com.facishare.open.qywx.accountsync.result.Result<QywxAccessTokenInfo> accessTokenInfo = qyweixinGatewayInnerService.getAccessTokenInfo2(qywxCorpId, appId);
        log.info("accessTokenInfo{}",accessTokenInfo);
        if(accessTokenInfo.isSuccess()&&ObjectUtils.isNotEmpty(accessTokenInfo.getData())){
            QywxAccessTokenInfo data = accessTokenInfo.getData();
            String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,data.getCorpAccessToken());
            String queryStr= GsonUtil.toJson(queryMessageArg);
            QywxMessageDataResult callData = getCallData(callUrl, Constant.SYNC_SEARCH_MESSAGE, ConfigCenter.programId, null, queryStr);
            if(!callData.isSuccess()){
                log.info("message conditionQueryMessageData get data:{}",callData);
                return new Result(callData.getErrcode(),callData.getErrmsg());
            }
            QueryMessageIdResult queryMessageIdResult = GsonUtil.fromJson(callData.getResponseData(), QueryMessageIdResult.class);
            return new Result<>(queryMessageIdResult);
        }
        return new Result(ErrorRefer.SYSTEM_ERROR.getCode(),accessTokenInfo.getErrorMsg());
    }

    private MessageConvertResult getConversionMessage(String ea,String appId,String url,String messageToken,List<MessageSaveDoc> messageSaveDocs,String nextCursor){
        MessageConvertResult messageConvertResult= null;
        try {
            Map<String,Object> requestData=Maps.newHashMap();
            requestData.put("token",messageToken);
            requestData.put("limit",100);
            if(StringUtils.isNotEmpty(nextCursor)){
                requestData.put("cursor",nextCursor);
            }
            QywxMessageDataResult getMessage = getCallData(url, Constant.SYNC_MESSAGE, ConfigCenter.programId, null, JSONObject.toJSONString(requestData));
            if(!getMessage.isSuccess()){
                log.info("message save get data:{}",JSONObject.toJSONString(getMessage));
                return null;
            }
            //存储数据
             messageConvertResult=JSONObject.parseObject(getMessage.getResponseData(),MessageConvertResult.class);
            //兼容之前的数据格式
            List<MessageConvertResult.MessageData> dataList = messageConvertResult.getMsg_list();
            log.info("message save get data list :{}",dataList.size());
            //因为消息是根据不同密钥区分的。所以需要根据消息指定的版本密钥解密消息
            Result<List<GenerateSettingVo>> listResult = settingService.queryByEaSetting(ea);
            Map<Integer, String> versionMap = listResult.getData().stream().collect(Collectors.toMap(GenerateSettingVo::getVersion, GenerateSettingVo::getPrivateKey, (key1, key2) -> key2));
            for (MessageConvertResult.MessageData messageData : dataList) {
                String encryptedKey= messageData.getService_encrypt_info().getEncryptedSecretKey();
                Integer publicKey=messageData.getService_encrypt_info().getPublicKeyVer();
                String privateKey = versionMap.get(publicKey);
                String dataPrivate = null;
                try {
                    dataPrivate = RSAUtil.getPrivateKey(privateKey, encryptedKey);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                messageData.setSecretKey(dataPrivate);
                MessageSaveDoc messageSaveDoc=new MessageSaveDoc();
                messageSaveDoc.setId(new ObjectId());
                messageSaveDoc.setFsEa(ea);
                messageSaveDoc.setDescryKey(dataPrivate);
                messageSaveDoc.setEi(eieaConverter.enterpriseAccountToId(ea));
                messageSaveDoc.setKeyVersion(publicKey);
                messageSaveDoc.setUpdateTime(System.currentTimeMillis());
                messageSaveDoc.setCreateTime(System.currentTimeMillis());
                messageSaveDoc.setMessageTime(messageData.getSend_time());
                messageSaveDoc.setMessageType(messageData.getMsgtype().toString());
                messageSaveDoc.setAppId(appId);
                messageSaveDoc.setMessageId(messageData.getMsgid());
                messageSaveDoc.setFromUser(messageData.getSender().getId());
                messageSaveDoc.setRoomId(messageData.getChatid());
                List<String> dataIds = messageData.getReceiver_list().stream().map(MessageConvertResult.ReceiverList::getId).collect(Collectors.toList());
                messageSaveDoc.setToList(dataIds);
                messageSaveDocs.add(messageSaveDoc);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return messageConvertResult;
        } finally {
        }
        return messageConvertResult;
    }

    public QywxMessageDataResult getCallData(String callUrl,String abilityId,String programId,String notifyId,String requestData ){
        Map<String,String> dataMap=Maps.newHashMap();
        dataMap.put("ability_id",abilityId);
        dataMap.put("program_id",programId);
        dataMap.put("notify_id",notifyId);
        dataMap.put("request_data",requestData);
        log.info("getcall data url:{}：ability_id:{},program_id：{},notify_id：{},request_data:{}",callUrl,abilityId,programId,notifyId,requestData);
        QywxMessageDataResult qywxMessageDataResult = proxyOkHttpClient.postUrl(callUrl,dataMap, Maps.newHashMap() ,new TypeReference<QywxMessageDataResult>(){});
        return qywxMessageDataResult;
    }

    public void getAutoSynchronizationMessage2(String ea) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);
        //ea转为out_ea
        String outEa = qyweixinAccountSyncService.getOutEaByFsEa(ea);
        //ea转为ei
        int ei = qyweixinAccountSyncService.getEiByEa(ea);
        //通过ea找出企业员工id
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinGetMessageUser>> messageUserListResult;
        try {
            messageUserListResult = qyweixinAccountSyncService.AutoGetExternalContactEmployeeId2(ea,outEa);
        } catch (Exception e) {
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage,message={}.", e.getMessage(), e);
            messageUserListResult = qyweixinAccountSyncService.AutoGetExternalContactEmployeeId2(ea,outEa);
        }
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage,ea={},messageUserListResult={}.", ea, messageUserListResult);
        if(!messageUserListResult.isSuccess() || CollectionUtils.isEmpty(messageUserListResult.getData())) {
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage There are no messageUserListResult={} in the ea={}. ", messageUserListResult, ea);
            //以企业为维度，没有员工，退出这个企业
            return;
        }
        for(QyweixinGetMessageUser messageUser : messageUserListResult.getData()) {
            log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage,ea={},messageUser={}.", ea, messageUser);
            String fsId = messageUser.getFsId().substring(messageUser.getFsId().length()-4);
            String userName = saveMessageService.getName(ea, outEa, messageUser.getOpenid());
            List<QyweixinExternalContactInfo> listResult = qyweixinAccountSyncService.queryExternalContactListTwoScheme(ea, messageUser.getOpenid());
            if(CollectionUtils.isEmpty(listResult)) {
                log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage: ea={}, messageUser={}, listResult={} ", ea, messageUser, listResult);
                //以员工为维度，没有员工，退出
                continue;
            }
            for(QyweixinExternalContactInfo qyweixinExternalContactInfo : listResult) {
                log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage,ea={},externalUserid={},contactName={}.", ea, qyweixinExternalContactInfo.getExternalUserid(), qyweixinExternalContactInfo.getName());
                //外部联系人的微信id
                String outUserId = qyweixinExternalContactInfo.getExternalUserid();
                //TODO ISV拿到會話數據
                //转成明文
                List<QyweixinIdToOpenidPo> idToOpenidPos = qyweixinIdToOpenidDao.getByOpenIds(outEa, Lists.newArrayList(outUserId));
                if(CollectionUtils.isEmpty(idToOpenidPos)) {
                    log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage: ea={}, messageUser={}, idToOpenidPos={} ", ea, messageUser, idToOpenidPos);
                    continue;
                }
                String ExternalPlaintextId = idToOpenidPos.get(0).getPlaintextId();
                //参考手动留存
                QueryMessageArg queryMessageArg = new QueryMessageArg();
                queryMessageArg.setFsEa(ea);
                queryMessageArg.setOutEa(outEa);
                queryMessageArg.setReceiveIds(messageUser.getPlaintextId());
                queryMessageArg.setSenderIds(ExternalPlaintextId);
                //先计算时间区间
                Long nowTime = new Date().getTime();
                Long preMessageTime = new Date().getTime() - 1000 * 60 * 60 * 24;
                queryMessageArg.setLastMessageTime(nowTime);
                queryMessageArg.setPreMessageTime(preMessageTime);
                if(CollectionUtils.isEmpty(qyweixinExternalContactInfo.getFollowUserList())
                        || CollectionUtils.isEmpty(qyweixinExternalContactInfo.getFollowUserList().get(0).getRemark_mobiles())) {
                    log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage contactName={} is not mobile", qyweixinExternalContactInfo.getName());
                    //以外部联系人为维度，没有外部联系人电话，退出
                    continue;
                }
                Map<String, Object> relatedObject = new HashMap<>();
                //获取多关联对象的id
                List<String> filed = new LinkedList<>();
                filed.add("tel");
                filed.add("mobile");
                //客户
                String accountObjId = externalContactManager.queryByPhone(ei, "tel", qyweixinExternalContactInfo.getFollowUserList().get(0).getRemark_mobiles().get(0), "AccountObj").get("object_id");
                if(!StringUtils.isEmpty(accountObjId) && accountObjId.length() != 0) {
                    relatedObject.put("AccountObj", Lists.newArrayList(accountObjId));
                }
                if(ObjectUtils.isEmpty(relatedObject)) {
                    for(String file: filed) {
                        //联系人
                        String contactObjId = externalContactManager.queryByPhone(ei, file, qyweixinExternalContactInfo.getFollowUserList().get(0).getRemark_mobiles().get(0), "ContactObj").get("object_id");
                        if(!StringUtils.isEmpty(contactObjId) && contactObjId.length() != 0) {
                            relatedObject.put("ContactObj", Lists.newArrayList(contactObjId));
                        }
                    }
                }
                if(ObjectUtils.isEmpty(relatedObject)) {
                    for(String file: filed) {
                        //线索
                        String leadsObjId = externalContactManager.queryByPhone(ei, file, qyweixinExternalContactInfo.getFollowUserList().get(0).getRemark_mobiles().get(0), "LeadsObj").get("object_id");
                        if(!StringUtils.isEmpty(leadsObjId) && leadsObjId.length() != 0) {
                            relatedObject.put("LeadsObj", Lists.newArrayList(leadsObjId));
                        }
                    }
                }
                if(relatedObject.isEmpty()) {
                    log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage contactName={} is not relate", qyweixinExternalContactInfo.getName());
                    //以外部联系人为维度，没有外部联系人字段，退出
                    continue;
                }
                List<MessageSaveDoc> npathResult = messageSaveMongoDao.queryMessages(ei, queryMessageArg);
                if(CollectionUtils.isNotEmpty(npathResult)) {
                    log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage is wechat");
                    List<FileMessageResult> pagerResult = getConditionQueryMessage(queryMessageArg, npathResult, messageUser, outUserId);
                    Map<String, Object> attempt = npath(npathResult);
                    log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage:attachments={}", attempt);
                    AutRetentionVo autRetentionVo = new AutRetentionVo();
                    autRetentionVo.setEi(ei);
                    autRetentionVo.setEa(ea);
                    autRetentionVo.setFsId(fsId);
                    autRetentionVo.setFsUserName(messageUser.getOpenid());
                    autRetentionVo.setUserName(userName);
                    autRetentionVo.setContactId(outUserId);
                    autRetentionVo.setContactName(qyweixinExternalContactInfo.getName());
                    autRetentionVo.setPagerResult(new Result<>(pagerResult));
                    autRetentionVo.setRelatedObject(relatedObject);
                    autRetentionVo.setAttempt(attempt);
                    externalContactManager.saveSales(autRetentionVo);
                }
                FindIterable<MessageSaveDoc> roomIdDocs = messageSaveMongoDao.queryRoomIds(ei, queryMessageArg);
                Set<String> roomIdSet = new HashSet<>();
                for(MessageSaveDoc roomIdDoc : roomIdDocs) {
                    roomIdSet.add(roomIdDoc.getRoomId());
                }
                for(String id: roomIdSet) {
                    log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.room:roomId={}", id);
                    queryMessageArg.setRoomId(id);
                    List<MessageSaveDoc> npathResultByRoomId = messageSaveMongoDao.queryMessages(ei, queryMessageArg);
                    if(CollectionUtils.isEmpty(npathResultByRoomId)) {
                        continue;
                    }
                    List<FileMessageResult> pagerResult = getConditionQueryMessage(queryMessageArg, npathResultByRoomId, messageUser, outUserId);
                    Map<String, Object> attempt = npath(npathResultByRoomId);
                    //群主id
                    QyweixinGroupChatDetail.GroupChat roomMessage = qyweixinAccountSyncService.getRoomMessage2(ea, id, outEa);
                    log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.room:roomMessage={}", roomMessage);
                    AutRetentionVo autRetentionVo = new AutRetentionVo();
                    if(ObjectUtils.isNotEmpty(roomMessage)) {
                        String roomFsId;
                        if(!StringUtils.isEmpty(roomMessage.getOwner())) {
                            String fsAccount = qyweixinAccountSyncService.getFsAccount(ea, roomMessage.getOwner(), outEa);
                            if(fsAccount != null && fsAccount.length() != 0) {
                                roomFsId = fsAccount.substring(fsAccount.length()-4);
                            } else {
                                roomFsId = "-10000";
                            }
                        } else {
                            roomFsId = "-10000";
                        }
                        if(!StringUtils.isEmpty(roomMessage.getName())) {
                            autRetentionVo.setRoomName(roomMessage.getName());
                        }
                        autRetentionVo.setRoomFsId(roomFsId);
                    }
                    autRetentionVo.setEi(ei);
                    autRetentionVo.setEa(ea);
                    autRetentionVo.setFsId(fsId);
                    autRetentionVo.setFsUserName(messageUser.getOpenid());
                    autRetentionVo.setUserName(userName);
                    autRetentionVo.setContactId(outUserId);
                    autRetentionVo.setContactName(qyweixinExternalContactInfo.getName());
                    autRetentionVo.setPagerResult(new Result<>(pagerResult));
                    autRetentionVo.setRelatedObject(relatedObject);
                    autRetentionVo.setAttempt(attempt);
                    externalContactManager.saveSales(autRetentionVo);
                }
            }
        }
    }

    private List<FileMessageResult> getConditionQueryMessage(QueryMessageArg arg, List<MessageSaveDoc> messageSaveDocs, QyweixinGetMessageUser messageUser, String externalUserId) {
        List<FileMessageResult> results = Lists.newArrayList();
        //获取自建应用密钥
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting(arg.getFsEa(), null, arg.getOutEa());
        if (ObjectUtils.isEmpty(generateSettingVoResult)) return null;
        Map<String, String> nameMap = convertName(arg.getOutEa(), Lists.newArrayList(messageUser.getOpenid(), externalUserId), arg.getFsEa(), generateSettingVoResult.getData().getCorpSecret());
        messageSaveDocs.stream().forEach(item -> {
            String fromUserId;
            if(item.getFromUser().equals(messageUser.getPlaintextId())) {
                fromUserId = messageUser.getOpenid();
            } else {
                fromUserId = externalUserId;
            }
            FileMessageResult fileMessageResult = new FileMessageResult();
            fileMessageResult.setMessageId(item.getMessageId());
            fileMessageResult.setSenderId(OUT_ACCOUNT.concat(fromUserId));
            fileMessageResult.setTimeStamp(item.getMessageTime());
            fileMessageResult.setType(FileEnum.getType(item.getMessageType()).getFsMessageType());
            fileMessageResult.setContent(convertContent(item));
            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount(SourceTypeEnum.QYWX.getSourceType(),
                    arg.getFsEa(), StringUtils.EMPTY, fromUserId);
            if (CollectionUtils.isNotEmpty(accountResult.getData())) {
                fileMessageResult.setFsFullSenderId(accountResult.getData().get(0).getFsAccount());
            }
            //配置名字
            fileMessageResult.setSenderName(nameMap.get(fromUserId));
            results.add(fileMessageResult);
        });
        return results;
    }

    private Map<String, String> convertName(String corpId, List<String> qywxIds, String ea, String appSecret) {
        //外部联系人的前缀wo/wm，但是不靠谱。还是都请求先请求外部联系人接口有则返回，无则请求员工接口
        Map<String, String> nameMap = Maps.newHashMap();
        qywxIds.stream().forEach(item -> {
            String senderName = Strings.EMPTY;
            //外部联系人
            com.facishare.open.qywx.accountsync.result.Result<QyweixinExternalContactInfo> qyweixinExternalContactInfoResult = qyweixinAccountSyncService.queryExternalContactsForSelf(corpId, appSecret, item);
            log.info("convertName  result:{}", qyweixinExternalContactInfoResult);
            if (ObjectUtils.isNotEmpty(qyweixinExternalContactInfoResult.getData())) {
                senderName = qyweixinExternalContactInfoResult.getData().getName();
            } else {
                com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> employeeInfoBatch = qyweixinAccountSyncService.getEmployeeInfoBatch(ea, ConfigCenter.QYWX_REP_APPID, Lists.newArrayList(item));
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(employeeInfoBatch.getData())) {
                    senderName = employeeInfoBatch.getData().get(0).getName();
                }
            }
            nameMap.put(item, senderName);
        });
        return nameMap;
    }

    private String convertContent(MessageSaveDoc messageSaveDoc) {
        if (messageSaveDoc.getMessageType().equals(FileEnum.TEXT_TYPE.getMessageType()) ||
                messageSaveDoc.getMessageType().equals(FileEnum.EXTERNAL_REDPACKET.getMessageType()) ||
                messageSaveDoc.getMessageType().equals(FileEnum.LOCATION.getMessageType()) ||
                messageSaveDoc.getMessageType().equals(FileEnum.WEAPP.getMessageType()) ||
                messageSaveDoc.getMessageType().equals(FileEnum.LINK.getMessageType()))
            return messageSaveDoc.getContent();
        Map<String, Object> contentMap = Maps.newHashMap();
        contentMap.put("path", messageSaveDoc.getNpath());
        contentMap.put("fileSize", messageSaveDoc.getFileSize());
        contentMap.put("name", messageSaveDoc.getFileName());
        contentMap.put("fileExt", messageSaveDoc.getFileExt());
        return JSONObject.toJSON(contentMap).toString();
    }

    private List<QywxMessageVo> poToVo(List<QywxMessagePo> npathResult) {
        List<QywxMessageVo> poList = new ArrayList<>();
        npathResult.stream().forEach(item -> {
            QywxMessageVo po = new QywxMessageVo();
            BeanUtils.copyProperties(item, po);
            poList.add(po);
        });
        return poList;
    }

    public Map<String, Object> npath(List<MessageSaveDoc> npathResults) {
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.attachments");
        Map<String, Object> attachments = new HashMap<>();
        List<Map<String, Object>> list = new LinkedList<>();
        for(MessageSaveDoc npathResult: npathResults) {
            if(!StringUtils.isAnyEmpty(npathResult.getNpath())) {
                Map<String, Object> attach = new HashMap<>();
                attach.put("name", npathResult.getFileName()+"."+npathResult.getFileExt());
                attach.put("filename", npathResult.getFileName()+"."+npathResult.getFileExt());
                attach.put("path", npathResult.getNpath());
                attach.put("ext", npathResult.getFileExt());
                attach.put("size", npathResult.getFileSize());
                attach.put("create_time", npathResult.getMessageTime());
                list.add(attach);
            }
        }
        attachments.put("attachments", list);
        return attachments;
    }

    //临时方法
    public String sendAutoMessage(AutoMessageArg autoMessageArg, Map<String, Object> attempt) {
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.sendAutoMessage: autoMessageArg={}, attempt={}", autoMessageArg, attempt);
        Map<String, String> headerMap = new HashMap<>();
        Map<String, Object> bodyMap = new HashMap<>();
        Map<String, Object> objectData = new HashMap<>();
        Map<String, Object> activeRecordContent = new HashMap<>();
        Map<String, Object> url = new HashMap<>();
        headerMap.put("x-fs-ei", String.valueOf(autoMessageArg.getEi()));
        headerMap.put("x-fs-userInfo", String.valueOf(autoMessageArg.getFsId()));
        headerMap.put("x-tenant-id", autoMessageArg.getEa());
        headerMap.put("x-user-id", String.valueOf(autoMessageArg.getFsId()));
        headerMap.put("x-fs-locale", "zh-CN");
        headerMap.put("Content-Type", "application/json");

        url.put("summary", autoMessageArg.getUrl());
        url.put("title", autoMessageArg.getTitle());
        url.put("url", autoMessageArg.getUrl());
        url.put("icon", "https://a9.fspage.com/FSR/weex/avatar/feed/images/chat_history_to_sales_record.png");
        activeRecordContent.put("url", url);
        activeRecordContent.put("text", autoMessageArg.getTitle());
        activeRecordContent.put("attachments", attempt.get("attachments"));

        List<String> list = new LinkedList<>();
        list.add(autoMessageArg.getCreatorId());
        objectData.put("object_describe_api_name", "ActiveRecordObj");
        objectData.put("record_type", "default__c");
        objectData.put("created_by", list);
        objectData.put("active_record_content", activeRecordContent);
        objectData.put("related_object", autoMessageArg.getRelatedObject());
        bodyMap.put("object_data", objectData);
        bodyMap.put("source", "501");
        //String ACTIVERECORDOBJ_URL="http://172.31.101.246:17263/API/v1/inner/object/ActiveRecordObj/action/Add";

        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage.sendAutoMessage: ACTIVERECORDOBJ_URL={}, headerMap={}, bodyMap={}", ACTIVERECORDOBJ_URL, headerMap, bodyMap);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(ACTIVERECORDOBJ_URL, headerMap, JSONObject.toJSONString(bodyMap));
        log.info("AutoPullMessageServiceImpl.getAutoSynchronizationMessage:httpResponseMessage={}", httpResponseMessage);
        return httpResponseMessage.getContent();

    }




    @Override
    public void test() {
//先计算时间区间
        Long nowTime = new Date().getTime();
        Long preMessageTime = new Date().getTime() - 1000 * 60 * 60 * 24;
        //生成url
        CreateOuterMergedMessageApiArg createOuterMergedMessageApiArg = new CreateOuterMergedMessageApiArg();
        List<OuterMessageInfo> outerMessageInfos = new LinkedList<>();
        OuterMessageInfo outerMessageInfo = new OuterMessageInfo();
        outerMessageInfo.setMessageId("14746004503409650660_1617180959_external");
        outerMessageInfo.setTimeStamp(preMessageTime);
        outerMessageInfo.setType("T");
        outerMessageInfo.setContent("这是测试");
        outerMessageInfo.setSenderId("OUT.WX.wwb3811da6336e7a1c");
        outerMessageInfo.setSenderName("KeNanYing");
        outerMessageInfo.setFsFullSenderId("E.83384.1006");
        outerMessageInfos.add(outerMessageInfo);
        createOuterMergedMessageApiArg.setOutMessageInfoList(outerMessageInfos);
        createOuterMergedMessageApiArg.setSessionName("测试1和测试2的聊天记录");

        AuthInfo authInfo=new AuthInfo();
        EmployeeId employeeId=new EmployeeId(EmployeeId.Type.OUT,"83384",1006);
        authInfo.setEmployeeId(employeeId);
        createOuterMergedMessageApiArg.setAuthInfo(authInfo);
        CreateMergedMessageResult createMergedMessageResult = messageService.createOuterMergedMessage(createOuterMergedMessageApiArg);
        String url = createMergedMessageResult.getLinkUrl();
        String content = createMergedMessageResult.getContent();
        String title = createMergedMessageResult.getTitle();




        //参考手动留存
        QueryMessageArg queryMessageArg = new QueryMessageArg();
        queryMessageArg.setFsEa("81002");
        queryMessageArg.setOutEa("ww986b47d6dead87f3");
        List<String> ids = new LinkedList<>();
        ids.add("KeNanYing");
        ids.add("wmwx1mDAAAGAWZE8aAFfQOflVPyY1gVw");
//        queryMessageArg.setReceiveIds(ids);
//        queryMessageArg.setSenderIds(ids);

        queryMessageArg.setLastMessageTime(nowTime);
        queryMessageArg.setPreMessageTime(preMessageTime);
        //判断单聊
        int room = saveMessageService.getIsRoom(queryMessageArg);
        if (room >= 1) {
            //单聊
            List<QywxMessagePo> messageResult = saveMessageDao.queryMessageByNoRoom(queryMessageArg);
            System.out.println(messageResult);
//            Result<List<FileMessageResult>> pagerResult = saveMessageService.getConditionQueryMessage(queryMessageArg);
//            System.out.println(pagerResult);

        }
    }

    @Override
    public void test1() {
        AutoMessageArg autoMessageArg = new AutoMessageArg();
        autoMessageArg.setContent("U-FSQYWX-chenzongxin:[语音]\n" +
                "U-FSQYWX-chenzongxin:[图片]\n" +
                "U-FSQYWX-chenzongxin:你是个好人\n" +
                "U-FSQYWX-chenzongxin:你真的很好\n" +
                "U-FSQYWX-chenzongxin:你真的好\n" +
                "U-FSQYWX-chenzongxin:你很好\n" +
                "U-FSQYWX-chenzongxin:你好\n" +
                "U-FSQYWX-chenzongxin:[文档]\n" +
                "糕云:…\n" +
                "U-FSQYWX-chenzongxin:[语音]\n" +
                "U-FSQYWX-chenzongxin:[语音]\n" +
                "U-FSQYWX-chenzongxin:拉拉\n");
        autoMessageArg.setEa("83384");
        autoMessageArg.setEi(83384);
        autoMessageArg.setFsId("1000");
        Map<String, Object> map = new HashMap<>();
        List<String> list = new LinkedList<>();
        list.add("61a75444d1836400018df648");
        map.put("AccountObj", list);
        autoMessageArg.setRelatedObject(map);
        autoMessageArg.setTitle("U-FSQYWX-chenzongxin和糕云的聊天记录的聊天记录（外部）");
        autoMessageArg.setCreatorId("1000");
        autoMessageArg.setUrl("https://www.ceshi112.com/fsh5/chat/index.html#/mergedMessage/61cbdc5757b9d100013e8651");
        /**
         *
         *   "filename" -> "mmexport1640616507141.jpg.$.file.fileext"
         * "name" -> "mmexport1640616507141.jpg.$.file.fileext"
         * "create_time" -> {Long@12123} *************
         * "size" -> {Long@12121} 2243047
         * "path" -> "N_202112_28_e19be3140d244cd498374f532e42686f"
         * "ext" -> "$.file.fileext"
         *
         */
        Map<String, Object> attachments = new HashMap<>();
        List<Map<String, Object>> list1 = new LinkedList<>();
        Map<String, Object> attach1 = new HashMap<>();
        attach1.put("name", "4b6d424223d37ac80085741da8a9c809");
        attach1.put("path", "N_202112_28_9705ef8cdb9e494c98bfee6d459b11e8");
        attach1.put("size", "1467");
        attach1.put("filename", "mmexport1640616507141.jpg.$.file.fileext");
        attach1.put("create_time", "*************");
        attach1.put("ext", "$.file.fileext");
        list1.add(attach1);
        Map<String, Object> attach2 = new HashMap<>();
        attach2.put("name", "92d11292593d27698813d8fbfcdf3e5a.amr");
        attach2.put("path", "N_202112_29_914bdc759b5147059f116786f512d737");
        attach2.put("size", "944");
        attach2.put("filename", "92d11292593d27698813d8fbfcdf3e5a.amr");
        attach2.put("create_time", "1640748987057");
        attach2.put("ext", "amr");
        list1.add(attach2);
        attachments.put("attachments", list1);

        String s = sendAutoMessage(autoMessageArg, attachments);
        log.info("result");

    }
}
