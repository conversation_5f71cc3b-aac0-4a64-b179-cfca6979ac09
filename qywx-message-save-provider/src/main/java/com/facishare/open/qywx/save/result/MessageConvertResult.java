package com.facishare.open.qywx.save.result;

import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.save.utils.HttpRequestUtils;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:21
 * 数据智能会话
 * @Version 1.0
 */
@Data
public class MessageConvertResult implements Serializable {

    private List<MessageData> msg_list;
    private Integer errcode;
    private String errmsg;
    private Integer has_more;//0-否；1-是。
    private String next_cursor;//下次调用带上该值，则从当前的位置继续往后拉，以实现增量拉取

    public boolean isSuccess() {
        if (HttpRequestUtils.CRM_SUCCESS == errcode) {
            return true;
        }
        return false;
    }
    @Data
    public static class MessageData implements Serializable{
        private String msgid;
        private Sender sender;
        private List<ReceiverList> receiver_list;
        private ServiceEncryInfo service_encrypt_info;
        private String chatid;
        private Long send_time;
        private Integer msgtype;
        //前端组件展示会话需要，已经是解密的key
        // https://developer.work.weixin.qq.com/document/path/99587
        private String secretKey;
    }
    @Data
    public static class Sender implements Serializable{
        //消息发送者的id，当消息发送者为员工时，该字段为员工的userid
        private String id;
        //消息发送者身份类型。1：员工；2：外部联系人; 3：机器人
        private String type;
    }
    @Data
    public static class ReceiverList implements Serializable{
        //消息接收者的身份类型。1：员工；2：外部联系人; 3：机器人
        private String type;
        //当接收者身份类型为员工时，该字段为员工userid
        private String id;
    }
    @Data
    public static class ServiceEncryInfo implements Serializable{
        //加密后的密钥
        private String encryptedSecretKey;
        //公钥版本号
        private Integer publicKeyVer;
    }

    public  static List<MessageSaveDoc> convertDoc(List<MessageData> messageData){
        List<MessageSaveDoc> messageSaveDocs= Lists.newArrayList();
        for (MessageData messageDatum : messageData) {
            MessageSaveDoc messageSaveDoc=new MessageSaveDoc();
            messageSaveDoc.setMessageId(messageDatum.getMsgid());
            messageSaveDoc.setMessageTime(messageDatum.getSend_time());
            messageSaveDoc.setKeyVersion(messageDatum.getService_encrypt_info().getPublicKeyVer());
            messageSaveDoc.setRoomId(messageDatum.getChatid());
//            //对消息进行解密key
//            messageSaveDoc.setDescryKey(dataMap.get(messageDatum.getMsgid()));

        }
        return messageSaveDocs;
    }
}
