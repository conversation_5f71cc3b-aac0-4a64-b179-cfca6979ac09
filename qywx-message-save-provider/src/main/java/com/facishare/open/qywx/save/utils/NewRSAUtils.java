package com.facishare.open.qywx.save.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @Date 2021/4/13 11:36
 * @Version 1.0
 */
public class NewRSAUtils {

    /**
     * RSA加解密工具类，实现公钥加密私钥解密和私钥解密公钥解密
     */

        private static final String src = "abcdefghijklmnopqrstuvwxyz";

        public static void main(String[] args) throws Exception {
            System.out.println("\n");
            RSAKeyPair keyPair = generateKeyPair();
            System.out.println("公钥：" + keyPair.getPublicKey());
            System.out.println("私钥：" + keyPair.getPrivateKey());
            System.out.println("\n");
            test1(keyPair, src);
            System.out.println("\n");
            test2(keyPair, src);
            System.out.println("\n");
        }

        /**
         * 公钥加密私钥解密
         */
        private static void test1(RSAKeyPair keyPair, String source) throws Exception {
            System.out.println("***************** 公钥加密私钥解密开始 *****************");
            String text1 = encryptByPublicKey(keyPair.getPublicKey(), source);
            String text2 = decryptByPrivateKey(keyPair.getPrivateKey(), text1);
            System.out.println("加密前：" + source);
            System.out.println("加密后：" + text1);
            System.out.println("解密后：" + text2);
            if (source.equals(text2)) {
                System.out.println("解密字符串和原始字符串一致，解密成功");
            } else {
                System.out.println("解密字符串和原始字符串不一致，解密失败");
            }
            System.out.println("***************** 公钥加密私钥解密结束 *****************");
        }

        /**
         * 私钥加密公钥解密
         *
         * @throws Exception
         */
        private static void test2(RSAKeyPair keyPair, String source) throws Exception {
            System.out.println("***************** 私钥加密公钥解密开始 *****************");
            String text1 = encryptByPrivateKey(keyPair.getPrivateKey(), source);
            String text2 = decryptByPublicKey(keyPair.getPublicKey(), text1);
            System.out.println("加密前：" + source);
            System.out.println("加密后：" + text1);
            System.out.println("解密后：" + text2);
            if (source.equals(text2)) {
                System.out.println("解密字符串和原始字符串一致，解密成功");
            } else {
                System.out.println("解密字符串和原始字符串不一致，解密失败");
            }
            System.out.println("***************** 私钥加密公钥解密结束 *****************");
        }

        /**
         * 公钥解密
         *
         * @param publicKeyText
         * @param text
         * @return
         * @throws Exception
         */
        public static String decryptByPublicKey(String publicKeyText, String text) throws Exception {
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyText));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
            byte[] result = cipher.doFinal(Base64.decodeBase64(text));
            return new String(result);
        }

        /**
         * 私钥加密
         *
         * @param privateKeyText
         * @param text
         * @return
         * @throws Exception
         */
        public static String encryptByPrivateKey(String privateKeyText, String text) throws Exception {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyText));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            byte[] result = cipher.doFinal(text.getBytes());
            return Base64.encodeBase64String(result);
        }

        /**
         * 私钥解密
         *
         * @param privateKeyText
         * @param text
         * @return
         * @throws Exception
         */
        public static String decryptByPrivateKey(String privateKeyText, String text) throws Exception {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyText));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] result = cipher.doFinal(Base64.decodeBase64(text));
            return new String(result);
        }

        /**
         * 公钥加密
         *
         * @param publicKeyText
         * @param text
         * @return
         */
        public static String encryptByPublicKey(String publicKeyText, String text) throws Exception {
            X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyText));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] result = cipher.doFinal(text.getBytes());
            return Base64.encodeBase64String(result);
        }

        /**
         * 构建RSA密钥对
         *
         * @return
         * @throws NoSuchAlgorithmException
         */
        public static RSAKeyPair generateKeyPair() throws NoSuchAlgorithmException {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(1024);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
            RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
            String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
            String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
            RSAKeyPair rsaKeyPair = new RSAKeyPair(publicKeyString, privateKeyString);
            return rsaKeyPair;
        }


        /**
         * RSA密钥对对象
         */
        public static class RSAKeyPair {

            private String publicKey;
            private String privateKey;

            public RSAKeyPair(String publicKey, String privateKey) {
                this.publicKey = publicKey;
                this.privateKey = privateKey;
            }

            public String getPublicKey() {
                return publicKey;
            }

            public String getPrivateKey() {
                return privateKey;
            }

        }


}
