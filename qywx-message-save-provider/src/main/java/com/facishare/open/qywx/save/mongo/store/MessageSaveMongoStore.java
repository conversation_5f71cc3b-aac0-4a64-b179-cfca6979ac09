package com.facishare.open.qywx.save.mongo.store;

import com.facishare.open.qywx.save.mongo.dao.MessageSaveMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 会话存档mongo store
 * <AUTHOR>
 * @date 2022/12/28
 */
@Repository
@Slf4j
public class MessageSaveMongoStore {

    @Getter
    private final DatastoreExt store;

    private final static String CollectionPrefix = "message_save_";
    private final String dbName;
    private final Set<String> collectionCache = Sets.newConcurrentHashSet();

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(MessageSaveDoc.class)
                        .automatic(true).build()));
    }


    public MessageSaveMongoStore(@Qualifier("saveMessageMongoStore") DatastoreExt store) {
        this.store = store;
        this.dbName = ConfigFactory.getInstance().getConfig("fs-open-qywx-message-save")
                .get("mongo.dbName", "fs-open-qywx");
        store.getMongo().getDatabase(dbName)
                .listCollectionNames().iterator().forEachRemaining(v -> {
                    if (v.startsWith(CollectionPrefix)) {
                        collectionCache.add(v);
                    }
                });
    }

    private String getCollectionName(Integer ei) {
        return CollectionPrefix + ei;
    }

    /**
     * 创建集合，检查索引
     * 这里不移除索引，另外使用批量接口移除
     * 也不根据名称更新索引，同样，更新索引需要走批量接口更新
     */
    public synchronized MongoCollection<MessageSaveDoc> getOrCreateCollection(Integer ei) {
        //dbName会从配置文件的mongo.servers解析
        String collectionName = getCollectionName(ei);
        MongoCollection<MessageSaveDoc> collection = getDatabase()
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(collectionName, MessageSaveDoc.class);
        if (!collectionCache.add(collectionName)) {
            return collection;
        }

        List<IndexModel> indexList = Lists.newArrayList();
        //过期自动清理时间,365天
        Bson expireTimeBson = Indexes.descending(MessageSaveMongoDao.f_createTime);
        indexList.add(new IndexModel(expireTimeBson, new IndexOptions()
                .name("index_expire_time")
                .expireAfter(365L, TimeUnit.DAYS)
                .background(true)));

        //根据fsEa字段创建索引,索引名称=index_fsEa
        Bson fsEaBson = Indexes.ascending(MessageSaveMongoDao.f_fsEa);
        indexList.add(new IndexModel(fsEaBson, new IndexOptions()
                .name("index_fsEa")
                .background(true)));

        Bson fsEaRoomIdFromUserToListBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_fsEa),
                Indexes.ascending(MessageSaveMongoDao.f_roomId),
                Indexes.ascending(MessageSaveMongoDao.f_fromUser),
                Indexes.ascending(MessageSaveMongoDao.f_toList));
        indexList.add(new IndexModel(fsEaRoomIdFromUserToListBson, new IndexOptions()
                .name("index_fsEa_roomId_fromUser_toList")
                .background(true)));
        Bson fsMessageIdTimeBson = Indexes.compoundIndex(
                Indexes.descending(MessageSaveMongoDao.f_messageId));
        indexList.add(new IndexModel(fsMessageIdTimeBson, new IndexOptions()
                .name("index_fs_message")
                .background(true)));

        Bson fsEaRoomIdFromUserToListMessageTimeBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_fsEa),
                Indexes.ascending(MessageSaveMongoDao.f_roomId),
                Indexes.ascending(MessageSaveMongoDao.f_fromUser),
                Indexes.ascending(MessageSaveMongoDao.f_toList),
                Indexes.ascending(MessageSaveMongoDao.f_messageTime));
        indexList.add(new IndexModel(fsEaRoomIdFromUserToListMessageTimeBson, new IndexOptions()
                .name("index_fsEa_roomId_fromUser_toList_messageTime")
                .background(true)));

        Bson fsEaRoomIdIsNullFromUserToListMessageTimeBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_fsEa),
                Indexes.ascending(MessageSaveMongoDao.f_fromUser),
                Indexes.ascending(MessageSaveMongoDao.f_toList),
                Indexes.ascending(MessageSaveMongoDao.f_messageTime));

        Bson roomIdIsNull = Filters.eq(MessageSaveMongoDao.f_roomId, null);

        indexList.add(new IndexModel(fsEaRoomIdIsNullFromUserToListMessageTimeBson, new IndexOptions()
                .name("index_fsEa_roomIdIsNull_fromUser_toList_messageTime")
                .partialFilterExpression(roomIdIsNull)
                .background(true)));

        Bson fsEaMessageTimeBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_fsEa),
                Indexes.ascending(MessageSaveMongoDao.f_messageTime));
        indexList.add(new IndexModel(fsEaMessageTimeBson, new IndexOptions()
                .name("index_fsEa_messageTime")
                .background(true)));

        Bson fsEaRoomIdMessageTimeBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_fsEa),
                Indexes.ascending(MessageSaveMongoDao.f_roomId),
                Indexes.ascending(MessageSaveMongoDao.f_messageTime));
        indexList.add(new IndexModel(fsEaRoomIdMessageTimeBson, new IndexOptions()
                .name("index_fsEa_roomId_messageTime")
                .background(true)));

        Bson roomIdSeqBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_roomId),
                Indexes.ascending(MessageSaveMongoDao.f_seq));
        indexList.add(new IndexModel(roomIdSeqBson, new IndexOptions()
                .name("index_roomId_seq")
                .background(true)));

        Bson fromUserToListSeqBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_fromUser),
                Indexes.ascending(MessageSaveMongoDao.f_toList),
                Indexes.ascending(MessageSaveMongoDao.f_seq));
        indexList.add(new IndexModel(fromUserToListSeqBson, new IndexOptions()
                .name("index_fromUser_toList_seq")
                .background(true)));

        Bson fromUserToListBson = Indexes.compoundIndex(
                Indexes.ascending(MessageSaveMongoDao.f_fromUser),
                Indexes.ascending(MessageSaveMongoDao.f_toList));
        indexList.add(new IndexModel(fromUserToListBson, new IndexOptions()
                .name("index_fromUser_toList")
                .background(true)));

                Bson roomIdBson = Indexes.compoundIndex(
                        Indexes.ascending(MessageSaveMongoDao.f_roomId));
        indexList.add(new IndexModel(roomIdBson, new IndexOptions()
                .name("index_roomId")
                .background(true)));

        List<String> created = collection.createIndexes(indexList);
        log.info("created indexes: {}, wanted: {}, created: {}", created, indexList, created);

        return collection;
    }

    public MongoDatabase getDatabase() {
        return store.getMongo().getDatabase(dbName);
    }
}
