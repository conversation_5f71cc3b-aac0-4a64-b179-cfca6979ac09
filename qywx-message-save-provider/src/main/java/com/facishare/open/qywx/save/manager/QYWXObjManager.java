package com.facishare.open.qywx.save.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinGroupChatDetail;
import com.facishare.open.qywx.save.Filter.ObjectDataFilter;
import com.facishare.open.qywx.save.arg.FileMessageArg;
import com.facishare.open.qywx.save.constant.FileEnum;
import com.facishare.open.qywx.save.constant.QYWXApiAndObjectEnum;
import com.facishare.open.qywx.save.model.QyweixinExternalContactGroupChatInfo;
import com.facishare.open.qywx.save.po.QywxMessagePo;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ObjectDataCreateResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

@Service
@Slf4j
public class QYWXObjManager {
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayServiceNormal;
    @Resource
    private EIEAConverter eieaConverter;

    @ReloadableProperty("QYWX_REP_APPID")
    private String QYWX_REP_APPID;

    public void createWeChatConversionObj(List<QywxMessagePo> messageList) {
        for(QywxMessagePo messagePo : messageList) {
            HeaderObj headerObj = HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(messagePo.getFsEa()), CrmConstants.SYSTEM_USER);
            ObjectData objectData = new ObjectData();
            objectData.put(QYWXApiAndObjectEnum.OWNER.getName(), Lists.newArrayList(String.valueOf(CrmConstants.SYSTEM_USER)));
            objectData.put(QYWXApiAndObjectEnum.FS_EA.getName(), messagePo.getFsEa());
            objectData.put(QYWXApiAndObjectEnum.SEQ.getName(), messagePo.getSeq());
            objectData.put(QYWXApiAndObjectEnum.MSG_ID.getName(), messagePo.getMessageId());
            objectData.put(QYWXApiAndObjectEnum.KEY_VERSION.getName(), messagePo.getKeyVersion());
            objectData.put(QYWXApiAndObjectEnum.FROM_USER_CIPHER.getName(), messagePo.getFromEncryptionUser());
            objectData.put(QYWXApiAndObjectEnum.FROM_USER.getName(), messagePo.getFromUser());
            objectData.put(QYWXApiAndObjectEnum.TO_LIST_CIPHER.getName(), messagePo.getToEncryptionList());
            objectData.put(QYWXApiAndObjectEnum.TO_LIST.getName(), messagePo.getToList());
            objectData.put(QYWXApiAndObjectEnum.ROOM_ID.getName(), messagePo.getRoomId());
            objectData.put(QYWXApiAndObjectEnum.MSG_TIME.getName(), messagePo.getMessageTime());
            objectData.put(QYWXApiAndObjectEnum.MSG_TYPE.getName(), messagePo.getMessageType());
            objectData.put(QYWXApiAndObjectEnum.CONTENT.getName(), messagePo.getContent());
            objectData.put(QYWXApiAndObjectEnum.MD5SUM.getName(), messagePo.getMd5sum());
            objectData.put(QYWXApiAndObjectEnum.SDK_FILE_ID.getName(), messagePo.getSdkFileId());
            objectData.put(QYWXApiAndObjectEnum.FILE_SIZE.getName(), messagePo.getFileSize());
            //图片展示
            if(StringUtils.isNotEmpty(messagePo.getNpath())) {
                FileMessageArg fileMessageArg = new FileMessageArg();
                fileMessageArg.setPath(messagePo.getNpath());
                fileMessageArg.setExt(messagePo.getFileExt());
                fileMessageArg.setFilename(messagePo.getFileName());
                fileMessageArg.setSize(messagePo.getFileSize());
                if (messagePo.getMessageType().equals(FileEnum.IMAGE_TYPE.getMessageType())) {
                    objectData.put(QYWXApiAndObjectEnum.IMAGE.getName(), Lists.newArrayList(fileMessageArg));
                } else {
                    //除图片外，需要上传至服务器的消息的展示
                    objectData.put(QYWXApiAndObjectEnum.ATTACHMENT.getName(), Lists.newArrayList(fileMessageArg));
                }
            }
            objectData.put(QYWXApiAndObjectEnum.NPATH.getName(), messagePo.getNpath());
            objectData.put(QYWXApiAndObjectEnum.FILE_NAME.getName(), messagePo.getFileName());
            objectData.put(QYWXApiAndObjectEnum.FILE_EXT.getName(), messagePo.getFileExt());
            //对于客户关联，先确定哪个id是客户的才能关联上去，群聊关联不了，除非是客户发消息的才关联上去
            if (StringUtils.isEmpty(messagePo.getRoomId())) {
                String externalId = this.getExternalIdBySingleChat(messagePo.getFromUser(), messagePo.getFromEncryptionUser(), messagePo.getToList(), messagePo.getToEncryptionList(), headerObj);
                log.info("QYWXObjManager.createWeChatConversionObj,singleChat,ea={},externalId={}.", messagePo.getFsEa(), externalId);
                if (StringUtils.isNotEmpty(externalId)) {
                    objectData.put(QYWXApiAndObjectEnum.QYWX_CUSTOMER_ID.getName(), externalId);
                }
            } else {
                String externalId = this.getExternalIdByGroupChat(messagePo.getFromUser(), messagePo.getFromEncryptionUser(), messagePo.getFsEa(), messagePo.getRoomId(), headerObj);
                log.info("QYWXObjManager.createWeChatConversionObj,CroupChat,ea={},externalId={}.", messagePo.getFsEa(), externalId);
                if (StringUtils.isNotEmpty(externalId)) {
                    objectData.put(QYWXApiAndObjectEnum.QYWX_CUSTOMER_ID.getName(), externalId);
                }
                String roomObjectId = this.getRoomObjectIdByGroupChat(messagePo.getRoomId(), headerObj);
                log.info("QYWXObjManager.createWeChatConversionObj,CroupChat,ea={},roomObjectId={}.", messagePo.getFsEa(), roomObjectId);
                if (StringUtils.isNotEmpty(roomObjectId)) {
                    objectData.put(QYWXApiAndObjectEnum.QYWX_GROUP_ID.getName(), roomObjectId);
                }
            }
            log.info("QYWXObjManager.createWeChatConversionObj,headerObj={},apiName={},objectData={}.", headerObj, QYWXApiAndObjectEnum.WECHAT_CONVERSION_OBJ.getName(), objectData);
            Result<ObjectDataCreateResult> result = null;
            try {
                result = objectDataService.create(headerObj, QYWXApiAndObjectEnum.WECHAT_CONVERSION_OBJ.getName(), false, false, false, objectData);
            } catch (Exception e) {
                log.info("QYWXObjManager.createWeChatConversionObj,ea={},message={}.", messagePo.getFsEa(), e.getMessage(), e);
            }
            log.info("QYWXObjManager.createWeChatConversionObj,ea={},result={}.", messagePo.getFsEa(), result);
        }
    }

    private Result<QueryBySearchTemplateResult> queryBySearchTemplate(List<ObjectDataFilter> filters, String apiName, HeaderObj headerObj) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setPermissionType(0);
        for(ObjectDataFilter filter : filters) {
            searchTemplateQuery.addFilter(filter.getFieldName(), filter.getFieldValues(), QYWXApiAndObjectEnum.OPERATOR.getName());
        }
        Result<QueryBySearchTemplateResult> queryBySearchTemplateResult = objectDataService.queryBySearchTemplate(headerObj, apiName, searchTemplateQuery);
        return queryBySearchTemplateResult;
    }

    private String getExternalIdBySingleChat(String fromUser, String fromEncryptionUser, List<String> toList, List<String> toEncryptionList, HeaderObj headerObj) {
        String externalId = null;
        //对于关联的客户ID，可以通过查询预设对象是否存在
        if (fromUser.length() == 32) {
            List<ObjectDataFilter> filters = new LinkedList<>();
            ObjectDataFilter externalUserIdFilter = new ObjectDataFilter();
            externalUserIdFilter.setFieldName(QYWXApiAndObjectEnum.EXTERNAL_USER_ID.getName());
            externalUserIdFilter.setFieldValues(Lists.newArrayList(fromEncryptionUser));
            filters.add(externalUserIdFilter);
            Result<QueryBySearchTemplateResult> queryBySearchTemplateResult = this.queryBySearchTemplate(filters, QYWXApiAndObjectEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), headerObj);
            if (queryBySearchTemplateResult.isSuccess() && ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData()) &&
                    ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult()) &&
                    CollectionUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult().getData())) {
                externalId = queryBySearchTemplateResult.getData().getQueryResult().getData().get(0).getId();
            }
        }
        if (StringUtils.isEmpty(externalId) && toList.get(0).length() == 32) {
            List<ObjectDataFilter> filters = new LinkedList<>();
            ObjectDataFilter externalUserIdFilter = new ObjectDataFilter();
            externalUserIdFilter.setFieldName(QYWXApiAndObjectEnum.EXTERNAL_USER_ID.getName());
            externalUserIdFilter.setFieldValues(toEncryptionList);
            filters.add(externalUserIdFilter);
            Result<QueryBySearchTemplateResult> queryBySearchTemplateResult = this.queryBySearchTemplate(filters, QYWXApiAndObjectEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), headerObj);
            if (queryBySearchTemplateResult.isSuccess() && ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData()) &&
                    ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult()) &&
                    CollectionUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult().getData())) {
                externalId = queryBySearchTemplateResult.getData().getQueryResult().getData().get(0).getId();
            }
        }
        return externalId;
    }

    private String getExternalIdByGroupChat(String fromUser, String fromEncryptionUser, String fsEa, String roomId, HeaderObj headerObj) {
        String externalId = null;
        if (fromUser.length() == 32) {
            List<ObjectDataFilter> filters = new LinkedList<>();
            ObjectDataFilter externalUserIdFilter = new ObjectDataFilter();
            externalUserIdFilter.setFieldName(QYWXApiAndObjectEnum.EXTERNAL_USER_ID.getName());
            externalUserIdFilter.setFieldValues(Lists.newArrayList(fromEncryptionUser));
            filters.add(externalUserIdFilter);
            Result<QueryBySearchTemplateResult> queryBySearchTemplateResult = this.queryBySearchTemplate(filters, QYWXApiAndObjectEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), headerObj);
            if (queryBySearchTemplateResult.isSuccess() && ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData()) &&
                    ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult()) &&
                    CollectionUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult().getData())) {
                externalId = queryBySearchTemplateResult.getData().getQueryResult().getData().get(0).getId();
            }
        }

        //群里客户发消息没有预设上去，或者是员工发的消息，直接从群里找一个已预设上去的客户
        if (StringUtils.isEmpty(externalId)) {
            com.facishare.open.qywx.accountsync.result.Result<QyweixinGroupChatDetail> groupChatJSONObjectResult =
                    qyweixinGatewayServiceNormal.getGroupChatDetail2(fsEa, roomId, QYWX_REP_APPID,null);
            if (groupChatJSONObjectResult.isSuccess()
                    && ObjectUtils.isNotEmpty(groupChatJSONObjectResult.getData())) {
                //找出群客户
                String groupChat = ObjectUtils.isEmpty(JSONPath.read(JSON.toJSONString(groupChatJSONObjectResult.getData()),"$.group_chat")) ? null : JSONPath.read(JSON.toJSONString(groupChatJSONObjectResult.getData()),"$.group_chat").toString();
                QyweixinExternalContactGroupChatInfo groupChatInfo = JSONObject.parseObject(groupChat, QyweixinExternalContactGroupChatInfo.class);
                if(ObjectUtils.isNotEmpty(groupChatInfo) && CollectionUtils.isNotEmpty(groupChatInfo.getMemberList())) {
                    List<QyweixinExternalContactGroupChatInfo.Member> memberList = groupChatInfo.getMemberList();
                    for(QyweixinExternalContactGroupChatInfo.Member member : memberList) {
                        if(member.getType().equals("2")) {
                            List<ObjectDataFilter> filters = new LinkedList<>();
                            ObjectDataFilter externalUserIdFilter = new ObjectDataFilter();
                            externalUserIdFilter.setFieldName(QYWXApiAndObjectEnum.EXTERNAL_USER_ID.getName());
                            externalUserIdFilter.setFieldValues(Lists.newArrayList(member.getUserId()));
                            filters.add(externalUserIdFilter);
                            Result<QueryBySearchTemplateResult> queryBySearchTemplateResult = this.queryBySearchTemplate(filters, QYWXApiAndObjectEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), headerObj);
                            if (queryBySearchTemplateResult.isSuccess() && ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData()) &&
                                    ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult()) &&
                                    CollectionUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult().getData())) {
                                externalId = queryBySearchTemplateResult.getData().getQueryResult().getData().get(0).getId();
                                break;
                            }
                        }
                    }
                }
            }
        }
        return externalId;
    }

    private String getRoomObjectIdByGroupChat(String roomId, HeaderObj headerObj) {
        String roomObjectId = null;
        List<ObjectDataFilter> filters = new LinkedList<>();
        ObjectDataFilter externalUserIdFilter = new ObjectDataFilter();
        //关联群
        externalUserIdFilter.setFieldName(QYWXApiAndObjectEnum.CHAT_ID.getName());
        //TODO 这里的群id为明文，但是crm获取到的群id为密文，需要做转换
        externalUserIdFilter.setFieldValues(Lists.newArrayList(roomId));
        filters.add(externalUserIdFilter);
        Result<QueryBySearchTemplateResult> queryBySearchTemplateResult = this.queryBySearchTemplate(filters, QYWXApiAndObjectEnum.WECHAT_GROUP_OBJ.getName(), headerObj);
        if (queryBySearchTemplateResult.isSuccess() && ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData()) &&
                ObjectUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult()) &&
                CollectionUtils.isNotEmpty(queryBySearchTemplateResult.getData().getQueryResult().getData())) {
            roomObjectId = queryBySearchTemplateResult.getData().getQueryResult().getData().get(0).getId();
        }
        return roomObjectId;
    }
}
