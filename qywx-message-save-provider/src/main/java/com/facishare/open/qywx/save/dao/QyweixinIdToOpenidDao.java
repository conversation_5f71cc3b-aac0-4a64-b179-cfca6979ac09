package com.facishare.open.qywx.save.dao;

import com.facishare.open.qywx.save.po.QyweixinIdToOpenidPo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QyweixinIdToOpenidDao {
    int saveInfo(QyweixinIdToOpenidPo qyweixinIdToOpenidPo);

    int batchSaveInfo(@Param("qyweixinIdToOpenidPoList") List<QyweixinIdToOpenidPo> qyweixinIdToOpenidPoList);

    List<String> getByExternalIds(@Param("corpId")String corpId, @Param("externalIds")List<String> externalIds);

    List<QyweixinIdToOpenidPo> getByOpenIds(@Param("corpId")String corpId, @Param("openIds")List<String> openIds);

    List<QyweixinIdToOpenidPo> getPoByExternalIds(@Param("corpId")String corpId, @Param("externalIds")List<String> externalIds);
}
