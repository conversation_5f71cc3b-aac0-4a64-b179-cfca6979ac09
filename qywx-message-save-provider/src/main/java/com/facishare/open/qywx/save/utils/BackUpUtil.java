/*
 * work_wx
 * wuhen 2020/1/16.
 * Copyright (c) 2020  jianfeng<PERSON><EMAIL> All Rights Reserved.
 */

package com.facishare.open.qywx.save.utils;

public class BackUpUtil {
    private static String RSA = "RSA";

//    static String priKey =
//            "-----BEGIN RSA PRIVATE KEY-----\n" +
//                    "MIICXAIBAAKBgQCtX0eWgh9AD2hPuiNGMSH24ocwB3bpIYruuKk8jUeARb/qfjEx\n" +
//                    "WYNRtdGFs79Pm9L7Sfl29JjuBCpg70iTrF0pIgXd9pjLWdwmeF7IF9e3c5IgjA4I\n" +
//                    "Nml1qe4g/8baXynfhX90vUGdy3UfpFgzT//Danxa/W8jg8Bfj0irgqyIGQIDAQAB\n" +
//                    "AoGAEgqsRHleDyiLTmCscw2B31NLhjAAq9oVvynwUqDRJAQeKKThMaWDCOnG2AcQ\n" +
//                    "jZRFrGjSURK7J2m/jz7XaqaxOv651Nf7xckc8Wr+CVKWcQp0Ekv6kbF6+gbsje9R\n" +
//                    "bP0MUJLtrd4SmNZgOItz7IC+kYuhd/SChDoX447G4HV49AECQQDU59b5PjqYmY81\n" +
//                    "T/bJl6kP2rT55KVVPGOh38EMGy3VZua9HFx4QihxTYOTpmc9cxyfuwMcppyGnm19\n" +
//                    "mgQhdBC5AkEA0HbuJbhXNyxGL2i+XR+jc5ccSqjMR9noXpgG62/BA+6ahOL9ZxBQ\n" +
//                    "1HwIVUs/qQf8cXR71sfmKKu33L0/KjbCYQJAFrYQgY/40jR3SVmZWtHZz/4llg6k\n" +
//                    "8F27xxXGUxNHJV+Pt5ah6pYsGEILiiGTG8P+xq89Wr4PLnER/vcB/8uQyQJAV6or\n" +
//                    "6+DhjGop+bXql+6+JdXeJ+dkQLL6bQ0xm8CbQrQMduWd+sF5vGGMf5Hta3/YQT3i\n" +
//                    "9ieKOoA8Ca/r6CyvAQJBAImOZakR3BndNXJNqQT/a5OPaGTT5D5P1i4GyzixxV7n\n" +
//                    "yZgoxrg+Us6rm30byXCyqF8JC68O/CXaGLlHGPRgiLA=\n" +
//                    "-----END RSA PRIVATE KEY-----";


//    static String priKey_PKCS8 = "-----BEGIN PRIVATE KEY-----\n" +
//            "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAK1fR5aCH0APaE+6\n" +
//            "I0YxIfbihzAHdukhiu64qTyNR4BFv+p+MTFZg1G10YWzv0+b0vtJ+Xb0mO4EKmDv\n" +
//            "SJOsXSkiBd32mMtZ3CZ4XsgX17dzkiCMDgg2aXWp7iD/xtpfKd+Ff3S9QZ3LdR+k\n" +
//            "WDNP/8NqfFr9byODwF+PSKuCrIgZAgMBAAECgYASCqxEeV4PKItOYKxzDYHfU0uG\n" +
//            "MACr2hW/KfBSoNEkBB4opOExpYMI6cbYBxCNlEWsaNJRErsnab+PPtdqprE6/rnU\n" +
//            "1/vFyRzxav4JUpZxCnQSS/qRsXr6BuyN71Fs/QxQku2t3hKY1mA4i3PsgL6Ri6F3\n" +
//            "9IKEOhfjjsbgdXj0AQJBANTn1vk+OpiZjzVP9smXqQ/atPnkpVU8Y6HfwQwbLdVm\n" +
//            "5r0cXHhCKHFNg5OmZz1zHJ+7AxymnIaebX2aBCF0ELkCQQDQdu4luFc3LEYvaL5d\n" +
//            "H6NzlxxKqMxH2ehemAbrb8ED7pqE4v1nEFDUfAhVSz+pB/xxdHvWx+Yoq7fcvT8q\n" +
//            "NsJhAkAWthCBj/jSNHdJWZla0dnP/iWWDqTwXbvHFcZTE0clX4+3lqHqliwYQguK\n" +
//            "IZMbw/7Grz1avg8ucRH+9wH/y5DJAkBXqivr4OGMain5teqX7r4l1d4n52RAsvpt\n" +
//            "DTGbwJtCtAx25Z36wXm8YYx/ke1rf9hBPeL2J4o6gDwJr+voLK8BAkEAiY5lqRHc\n" +
//            "Gd01ck2pBP9rk49oZNPkPk/WLgbLOLHFXufJmCjGuD5SzqubfRvJcLKoXwkLrw78\n" +
//            "JdoYuUcY9GCIsA==\n" +
//            "-----END PRIVATE KEY-----";


//    static String priKey_PKCS8 = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAK1fR5aCH0APaE+6\n" +
//            "I0YxIfbihzAHdukhiu64qTyNR4BFv+p+MTFZg1G10YWzv0+b0vtJ+Xb0mO4EKmDv\n" +
//            "SJOsXSkiBd32mMtZ3CZ4XsgX17dzkiCMDgg2aXWp7iD/xtpfKd+Ff3S9QZ3LdR+k\n" +
//            "WDNP/8NqfFr9byODwF+PSKuCrIgZAgMBAAECgYASCqxEeV4PKItOYKxzDYHfU0uG\n" +
//            "MACr2hW/KfBSoNEkBB4opOExpYMI6cbYBxCNlEWsaNJRErsnab+PPtdqprE6/rnU\n" +
//            "1/vFyRzxav4JUpZxCnQSS/qRsXr6BuyN71Fs/QxQku2t3hKY1mA4i3PsgL6Ri6F3\n" +
//            "9IKEOhfjjsbgdXj0AQJBANTn1vk+OpiZjzVP9smXqQ/atPnkpVU8Y6HfwQwbLdVm\n" +
//            "5r0cXHhCKHFNg5OmZz1zHJ+7AxymnIaebX2aBCF0ELkCQQDQdu4luFc3LEYvaL5d\n" +
//            "H6NzlxxKqMxH2ehemAbrb8ED7pqE4v1nEFDUfAhVSz+pB/xxdHvWx+Yoq7fcvT8q\n" +
//            "NsJhAkAWthCBj/jSNHdJWZla0dnP/iWWDqTwXbvHFcZTE0clX4+3lqHqliwYQguK\n" +
//            "IZMbw/7Grz1avg8ucRH+9wH/y5DJAkBXqivr4OGMain5teqX7r4l1d4n52RAsvpt\n" +
//            "DTGbwJtCtAx25Z36wXm8YYx/ke1rf9hBPeL2J4o6gDwJr+voLK8BAkEAiY5lqRHc\n" +
//            "Gd01ck2pBP9rk49oZNPkPk/WLgbLOLHFXufJmCjGuD5SzqubfRvJcLKoXwkLrw78\n" +
//            "JdoYuUcY9GCIsA==";

    static String priKey_PKCS8 ="MIIEowIBAAKCAQEAz/Hl7Ay0SE+zzdGMctJcQotAkCH2tT5/C4ISI4mR3N2KicE+\n" +
            "EdJCICxS6DrDVs6YOyvKR9XX2c31vy/qGM+rZxMjeIzHdQvFaY1cRPtVyLpAax++\n" +
            "3HnczJsFHV77evrK0GkaBNUtL5+05NTdBRcj66HY2aO/+4ttAk59hSS/8qtOGqm6\n" +
            "I5yfpQsKXE++8daQMTVUHMOA7aTmzCnn+FT9enXkjkcI/bCpgpEtBRh8plupIxXM\n" +
            "ui6PdT7RfGt5h0mWVHFRvlHC+N0bpYoNqzj43Y8AZSRBK8EP4TFP4Rlk7hAuFV4a\n" +
            "Ts3if5nv/63iMJ3YqG+y+PyEpZlnzfNCZE76SwIDAQABAoIBAHAzk940VKqX5urt\n" +
            "YJ0sCIAXZzTePqI5II/zFRp7xmqoV3JRBM7U5r05bVrFKlWSj+2NiU4NgrSRP0Jz\n" +
            "9hqBI3kwiHkpbQ4o1dJIZjsKapUuekfTD0cjshHsq2vXrlYDMKAXteRZqlICGLdI\n" +
            "bCGtBMLFx55XjuWJq74M4AmRdMjY0QSdnAgz+dcMtj3FxIn8VMfipV33FFobmRQC\n" +
            "YN6ItMW0uC9HXXNWQSwhaghmhSnm1EnxWFbny3mKY3qJnhasjUR6EJaFUSqN5I9Z\n" +
            "Nbz0VFFgjAxbtvfH7fqW9urEht1B61YGsK5o/+5BrTAY2+Vg0vgovs5hVRi3pCcM\n" +
            "APCg8IECgYEA5s6xnxDDDLS6bN0EoD0jfEfB1Nm8YNELtoQvpg9CbbYBnsgk0TFy\n" +
            "tQnKGkrfkkAP7AfVZIaflNyvqY959BRGt/AGVvaGuuLyufiEsYXsLW9Vd6mOmIYy\n" +
            "QEmoPUCQkn7HIezEvbxp3TTW7+fW6REQImkiUqrwx/fgR9axPzlj1aECgYEA5qRf\n" +
            "TlvlPlxLP6vFtbjDA2726Yvhy/d5nb5uxtHDwiaUsQENHfZoDkF6kN5FROsJp+7N\n" +
            "5Fr/cpYzPK51m2Wkgrvf8j+ImuMlKFD3XZPyVhT04TTNkP3xzeicKM+B/ZyzISc8\n" +
            "5Q5CYkfILF75TW6EDVDWkgvCxH8u0R+qx/z2sGsCgYAlQrECEN6sKnD+KiAZDkWw\n" +
            "RpVQG2aB6r2NVYGruULsGznfvEfVTbpK562s2PGG1ri7TfhxJhqVGZtyMCtr7+oK\n" +
            "v8EGQP43JXQx+aDSV+Bs5VBS5RiUHvX10u5KFSZBwB29qE+KoeQlReZ9DFxxe8Oz\n" +
            "Cm30EoyUe7vFXS94GXe4gQKBgQCjnh/uWsq5/odzV8weKkBOAz4uWAmKxLkF6r5z\n" +
            "VQPmi7AYEYLYqqEO2+yzMLs7NPHYrFRrlxJ4m40lky3jW6vlAprQI7opBtKpUybo\n" +
            "v7e+0YcW7HqYTU5ooIeHfA3feHarIkbUx9TYG8wpjgaVo70SJTLS0H0PIJp5yFlD\n" +
            "HnVymQKBgHyGRiQWCRiyrbamHcsWQlM0wj7+iIfyZxRLsMvr2BJ5C+1emn7uwSLB\n" +
            "E0tadmGdhvzHezn8xSfO9RWb4TWKqGvbwtVqAVBkKJV+EgtbQro+WWbTAP3eCb7l\n" +
            "ua0jXMGuJ2NwicusVr2YmkOId5QF8siEpEz/AERY0WviFi+qrINH";


    /**
     * 用私钥解密
     *
     * @param encryptedData
     *            经过encryptedData()加密返回的byte数据
     *            私钥
     * @return
     */
    public static String decryptData(String encryptedData)
    {
        try
        {
            return RSAUtil.decryptByPriKey(encryptedData, priKey_PKCS8);
        } catch (Exception e)
        {
            e.printStackTrace();
            return "";
        }
    }







}
