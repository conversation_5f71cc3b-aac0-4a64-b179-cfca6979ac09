package com.facishare.open.qywx.save.constant;

public enum QYWXApiAndObjectEnum {

    WECHAT_CONVERSION_OBJ("WechatConversionObj", "企业微信会话存档"),
    NAME("name", "名称"),
    FS_EA("fs_ea", "企业EA"),
    MSG_ID("msg_id", "消息ID"),
    SEQ("seq", "消息序号"),
    KEY_VERSION("key_version", "密钥版本"),
    FROM_USER_CIPHER("from_user_cipher", "发送方密文用户ID"),
    FROM_USER("from_user", "发送方明文用户ID"),
    TO_LIST_CIPHER("to_list_cipher", "接收方密文用户ID列表"),
    TO_LIST("to_list", "接收方明文用户ID列表"),
    ROOM_ID("room_id", "群ID"),
    MSG_TIME("msg_time", "消息时间"),
    MSG_TYPE("msg_type", "消息类型"),
    CONTENT("content", "消息内容"),
    MD5SUM("md5sum", "媒体资源的MD5"),
    SDK_FILE_ID("sdk_file_id", "媒体资源的id信息"),
    FILE_SIZE("file_size", "媒体资源的文件大小"),
    IMAGE("image", "图片"),
    ATTACHMENT("attachment", "文件"),
    NPATH("npath", "文件的npath"),
    FILE_NAME("file_name", "文件名"),
    FILE_EXT("file_ext", "文件扩展名"),
    QYWX_CUSTOMER_ID("qywx_customer_id", "企业微信客户ID"),
    QYWX_GROUP_ID("qywx_group_id", "企业微信群ID"),
    WECHAT_WORK_EXTERNAL_USER_OBJ("WechatWorkExternalUserObj", "企业微信客户"),
    WECHAT_GROUP_OBJ("WechatGroupObj", "企业微信客户群"),
    EXTERNAL_USER_ID("external_user_id", "外部联系人ID"),
    CHAT_ID("chat_id", "群ID"),
    OWNER("owner", "负责人"),
    OPERATOR("EQ", "操作者"),
    ;



    private String name;
    private String description;

    public String getName() {
        return name;
    }

    public void getName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    QYWXApiAndObjectEnum(String name, String description){
        this.name = name;
        this.description = description;
    }

}
