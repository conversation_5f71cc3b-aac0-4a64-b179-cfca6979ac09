package com.facishare.open.qywx.save.manager;

import com.facishare.open.qywx.save.constant.CreateObjectEnum;
import com.fxiaoke.crmrestapi.arg.CreateObjectArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.CreateObjectResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.crmrestapi.service.ObjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/16
 */
@Component
@Slf4j
public class CrmObjectSupportManager {
    @Resource
    private ObjectService objectService;
    @Resource
    private ObjectDescribeService objectDescribeService;

    /**
     * 判断对象是否已存在
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    public boolean isObjCreate(Integer tenantId, String objApiName) {
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, -10000);
        Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(headerObj, objApiName);
        return describeResult.isSuccess();
    }

    /**
     * 创建预定义对象。
     * 存在则不创建，不存在时才会创建。
     *
     * @param tenantId
     * @param objApiName
     * @return true：创建。false：不创建
     */
    public boolean createDefineObject(Integer tenantId, String objApiName) {
        if (isObjCreate(tenantId, objApiName)) {
            return true;
        }
        try {
            CreateObjectEnum createObjectEnum = CreateObjectEnum.valueOf(objApiName);
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, -10000);
            CreateObjectArg createObjectArg = new CreateObjectArg();
            createObjectArg.setJsonData(createObjectEnum.getDescribe());
            createObjectArg.setIncludeLayout(true);
            createObjectArg.setJsonLayout(createObjectEnum.getDetailLayout());
            createObjectArg.setJsonListLayout(createObjectEnum.getMobileListLayout());
            createObjectArg.setLayoutType("detail");
            createObjectArg.setActive(true);
            InnerResult<CreateObjectResult> createResult = objectService.createObject(headerObj, createObjectArg);
            return createResult.isSuccess();
        } catch (Exception e) {
            log.error("create Obj failed", e);
            return false;
        }
    }

}
