package com.facishare.open.qywx.save.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.facishare.open.qywx.accountinner.annotation.SecurityField;
import com.facishare.open.qywx.save.config.ConfigCenter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/4/23 17:38
 * @Version 1.0
 */
@Slf4j
public class SecurityUtil {
    /**
     * aes用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(ConfigCenter.BASE64_SECRET);

    /**
     * 根据这个秘钥得到一个aes对象
     */
    @Getter
    private final static AES aes = SecureUtil.aes(SECRET_BYTES);

    /**
     * 对字符串进行加密处理
     * @param str
     * @return
     * @throws Exception
     */
    public static String encryptStr(String str) {
        if(StringUtils.isEmpty(str)) {
            return str;
        }
        //加密  这里我使用自定义的AES加密工具、
        String encryptValue=str;
        try {
            encryptValue = aes.encryptBase64(str);
        } catch (Exception e) {
            log.debug("encrypt value fail:{}",e.getMessage());
        }
        return encryptValue;
    }

    /**
     * 对字符串进行解密处理
     * @param str
     * @return
     * @throws Exception
     */
    public static String decryptStr(String str) {
        if(StringUtils.isEmpty(str)) {
            return str;
        }
        //解密
        String decryptValue=str;
        try {
            decryptValue = aes.decryptStr(str);
        } catch (Exception e) {
            log.debug("decrypt value fail:{}",e.getMessage());
        }
        return decryptValue;
    }

    /**
     * 加密
     *
     * @param declaredFields paramsObject所声明的字段
     * @param paramsObject   mapper中paramsType的实例
     * @return T
     * @throws IllegalAccessException 字段不可访问异常
     */
   public static  <T> T encrypt(Field[] declaredFields, T paramsObject) throws Exception {
        for (Field field : declaredFields) {
            SecurityField sensitiveField = field.getAnnotation(SecurityField.class);
            if (!Objects.isNull(sensitiveField)) {
                field.setAccessible(true);
                Object object = field.get(paramsObject);
                //暂时只实现String类型的加密
                if (ObjectUtils.isNotEmpty(object) && object instanceof String) {
                    //加密  这里我使用自定义的AES加密工具、
                    String encryptValue=object.toString();
                    try {
                        encryptValue = aes.encryptBase64(object.toString());
                    } catch (Exception e) {
                        log.debug("encrypt value fail:{}",e.getMessage());
                    }
                    field.set(paramsObject, encryptValue);
                    System.out.println(field);
                }
            }
        }
        return paramsObject;
    }

    /**
     * 解密
     *
     * @param result resultType的实例
     * @return T
     * @throws IllegalAccessException 字段不可访问异常
     */
    public static  <T> T decrypt(T result) throws IllegalAccessException {
        //取出resultType的类
        Class<?> resultClass = result.getClass();
        Field[] declaredFields = resultClass.getDeclaredFields();
        for (Field field : declaredFields) {
            SecurityField sensitiveField = field.getAnnotation(SecurityField.class);
            if (!Objects.isNull(sensitiveField)) {
                field.setAccessible(true);
                Object object = field.get(result);
                //暂时只实现String类型的加密
                if (ObjectUtils.isNotEmpty(object) && object instanceof String) {
                    //解密
                    String encryptValue=object.toString();
                    try {
                        encryptValue = aes.decryptStr(object.toString());
                    } catch (Exception e) {
                        log.debug("decrypt value fail:{}",e.getMessage());
                    }
                    field.set(result, encryptValue);
                    System.out.println(field);
                }
            }
        }
        return result;
    }

    public static void main(String[] args) {
        System.out.println(SecurityUtil.decryptStr("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")
        );
    }
}
