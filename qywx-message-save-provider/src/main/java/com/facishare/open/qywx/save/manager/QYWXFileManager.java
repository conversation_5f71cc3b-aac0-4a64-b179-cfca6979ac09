package com.facishare.open.qywx.save.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.save.arg.ConversionChangeArg;
import com.facishare.open.qywx.save.config.ConfigCenter;
import com.facishare.open.qywx.save.dao.MessageGeneratingDao;
import com.facishare.open.qywx.save.dao.SaveMessageDao;
import com.facishare.open.qywx.save.model.ChatDatas;
import com.facishare.open.qywx.save.model.Qychat;
import com.facishare.open.qywx.save.mongo.dao.MessageSaveMongoDao;
import com.facishare.open.qywx.save.mq.QiXinMsgSender;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.save.utils.RSAUtil;
import com.facishare.open.qywx.save.utils.deepCopyUtils;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.google.common.collect.Lists;
import com.google.common.primitives.Bytes;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.bulk.BulkWriteResult;
import com.robert.vesta.service.intf.IdService;
import com.tencent.wework.Finance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/22 19:50
 * @Version 1.0
 */
@Service
@Slf4j
public class QYWXFileManager implements Serializable {

    @Autowired
    private SaveMessageService saveMessageService;
    @Autowired
    private IdService idService;
    @Autowired
    private MessageGeneratingService settingService;
    @Autowired
    MessageGeneratingDao messageGeneratingDao;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private SaveMessageDao saveMessageDao;
    @Autowired
    private QYWXObjManager qywxObjManager;
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;
    @Autowired
    private QiXinMsgSender qiXinMsgSender;
    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private MessageSaveMongoDao messageSaveMongoDao;
    @Autowired
    private MessageUploadManger messageUploadManger;
    private static String LOCATION_PREFIX="【位置信息】 ";
    private static String RED_PACKET_PREFIX="【红包信息】";
    private static String DEFAULT = "default";
    private static String singleRoomIdModel = "singleChat[{fromUser}@{toList}]";


    //解密数据
    public void saveMessage(GenerateSettingVo generateSettingVo, Long seq) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);
        long sdk = 0;
        long slice = 0;
        long msg = 0;
        try {
            Set<String> externalIds = new HashSet<>();
            log.info("saveMessage message:{} seq:{}",generateSettingVo,seq);
            if(ObjectUtils.isEmpty(generateSettingVo))return;
            List<MessageSaveDoc> voList=new ArrayList<>(100);
            //因为消息是根据不同密钥区分的。所以需要根据消息指定的版本密钥解密消息
            Result<List<GenerateSettingVo>> listResult = settingService.queryByEaSetting(generateSettingVo.getEa());
            Map<Integer, String> versionMap = listResult.getData().stream().collect(Collectors.toMap(GenerateSettingVo::getVersion, GenerateSettingVo::getPrivateKey, (key1, key2) -> key2));
            sdk = Finance.NewSdk();
            Finance.Init(sdk, generateSettingVo.getQywxCorpId(), generateSettingVo.getSecret());
            slice = Finance.NewSlice();
            Map<String, Integer> eaMessagePullMap = new Gson().fromJson(ConfigCenter.MESSAGE_PULL_MAX, new TypeToken<Map<String, Integer>>(){}.getType());
            int limit = eaMessagePullMap.containsKey(generateSettingVo.getEa()) ? eaMessagePullMap.get(generateSettingVo.getEa()) : eaMessagePullMap.get(DEFAULT);
            log.info("QYWXFileManager.saveMessage,ea={}, limit={},proxy={}.", generateSettingVo.getEa(), limit, ConfigCenter.httpProxy);
            int ret = Finance.GetChatData(sdk, seq, limit, ConfigCenter.httpProxy, "", 600, slice);
            if (ret != 0) {
                log.info("QYWXFileManager.saveMessage,ea={}, ret={}.", generateSettingVo.getEa(), ret);
                return;
            } else {
                String content = Finance.GetContentFromSlice(slice);
                JSONObject jsonObject = JSONObject.parseObject(content);
                ChatDatas cdata = JSON.toJavaObject(jsonObject, ChatDatas.class);
                log.info("QYWXFileManager.saveMessage,ea={},cdata.errcode={},cdata.errmsg={}.", generateSettingVo.getEa(), cdata.getErrcode(), cdata.getErrmsg());
                List<Qychat> list = cdata.getChatdata();
//                log.info("query message ei:{},list size:{}",generateSettingVo.getEa(),list.size());
                ConversionChangeArg conversionChangeArg = new ConversionChangeArg();
                conversionChangeArg.setEa(generateSettingVo.getEa());
                Set<ConversionChangeArg.ChangeModel> changeModelSet = new HashSet<>();
                for (Qychat chatData : list) {
//                    log.info("QYWXFileManager.saveMessage,ea={},chatData={}.", generateSettingVo.getEa(), chatData);
                    String privateKey=versionMap.get(chatData.getPublickey_ver());
                    if(StringUtils.isEmpty(privateKey)) {
                        log.info("QYWXFileManager.saveMessage,ea={},publicKeyVer={}.", generateSettingVo.getEa(), chatData.getPublickey_ver());
                        continue;
                    }
                    String msgs = chatData.getEncrypt_chat_msg();
                    String encrypt_key = null;
                    try {
                        log.info("QYWXFileManager.saveMessage,ea={}, privateKey={}, randomKey={}.", generateSettingVo.getEa(), privateKey, chatData.getEncrypt_random_key());
                        encrypt_key = RSAUtil.getPrivateKey(privateKey, chatData.getEncrypt_random_key());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    msg = Finance.NewSlice();
//                    log.info("QYWXFileManager.saveMessage,ea={},sdk={},encrypt_key={},msgs={},msg={}.", generateSettingVo.getEa(), sdk, encrypt_key, msgs, msg);
                    int data = Finance.DecryptData(sdk, encrypt_key, msgs, msg);
                    log.info("QYWXFileManager.saveMessage,ea={},data={}.", generateSettingVo.getEa(), data);
                    String datas = Finance.GetContentFromSlice(msg);
                    MessageSaveDoc messageSaveDoc = convertMessage(datas);
                    if(ObjectUtils.isEmpty(messageSaveDoc)) continue;
                    messageSaveDoc.setMessageData(datas);
                    messageSaveDoc.setSeq(chatData.getSeq());
                    messageSaveDoc.setMessageId(chatData.getMsgid());
                    messageSaveDoc.setFsEa(generateSettingVo.getEa());
                    messageSaveDoc.setKeyVersion(chatData.getPublickey_ver());
                    messageSaveDoc.setId(ObjectId.get());
                    messageSaveDoc.setEi(eieaConverter.enterpriseAccountToId(generateSettingVo.getEa()));
                    messageSaveDoc.setCreateTime(System.currentTimeMillis());
                    messageSaveDoc.setUpdateTime(System.currentTimeMillis());
                    if(messageSaveDoc.getFromUser().length() == 32
                            && (messageSaveDoc.getFromUser().startsWith("wo") || messageSaveDoc.getFromUser().startsWith("wm"))) {
                        externalIds.add(messageSaveDoc.getFromUser());
                    }
                    Set<String> externalToList = messageSaveDoc.getToList().stream()
                            .filter(v -> v.length() == 32 && (v.startsWith("wo") || v.startsWith("wm")))
                            .collect(Collectors.toSet());
                    if(CollectionUtils.isNotEmpty(externalToList)) {
                        externalIds.addAll(externalToList);
                    }
                    ConversionChangeArg.ChangeModel changeModel = new ConversionChangeArg.ChangeModel();
                    if(StringUtils.isNotEmpty(messageSaveDoc.getRoomId())) {
                        changeModel.setRoomId(messageSaveDoc.getRoomId());
                    } else {
                        changeModel.setFromPlaintextId(messageSaveDoc.getFromUser());
                        changeModel.setToPlaintextId(messageSaveDoc.getToList().get(0));
                    }
                    changeModelSet.add(changeModel);
                    voList.add(messageSaveDoc);
                    if(voList.size()>=100) {
                        //多线程处理，可能数据被清空导致另外线程执行失败
                        List<MessageSaveDoc> saveDocs;
                        try {
                            saveDocs = deepCopyUtils.deepCopy(voList);
                        } catch (Exception e) {
                            log.info("QYWXFileManager.saveMessage,saveDocs is null", e);
                            //失败采用原始的方式去深拷贝
                            saveDocs = this.deepCopyMessageSaveDocs(voList);
                        }
                        saveMessage(saveDocs, generateSettingVo,true);
                        voList.clear();
                    }
                }
                if(CollectionUtils.isNotEmpty(voList)){
                    //多线程处理，可能数据被清空导致另外线程执行失败
                    List<MessageSaveDoc> saveDocs;
                    try {
                        saveDocs = deepCopyUtils.deepCopy(voList);
                    } catch (Exception e) {
                        log.info("QYWXFileManager.saveMessage,saveDocs is null", e);
                        //失败采用原始的方式去深拷贝
                        saveDocs = this.deepCopyMessageSaveDocs(voList);
                    }
                    saveMessage(saveDocs, generateSettingVo,true);
                }
                if(CollectionUtils.isNotEmpty(externalIds)) {
                    messageUploadManger.uploadExternalIds(generateSettingVo.getQywxCorpId(), new LinkedList<>(externalIds));
                }
                if(CollectionUtils.isNotEmpty(changeModelSet)) {
                    conversionChangeArg.setChangeModelList(new LinkedList<>(changeModelSet));
                    qiXinMsgSender.sendToConversionChange(conversionChangeArg);
                }
            }
            log.info("QYWXFileManager.saveMessage,ea={} save message completed.", generateSettingVo.getEa());
        } finally {
            Finance.FreeSlice(slice);
            Finance.FreeSlice(msg);
            Finance.DestroySdk(sdk);
        }

    }

    public void saveMessage(List<MessageSaveDoc> voList, GenerateSettingVo generateSettingVo,boolean needUpload) {
        //跨云
        if(ConfigCenter.TEM_CLOUD_EA.contains(generateSettingVo.getEa())) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.conversionMessagePush.name());
            cloudMessageProxyProto.setFsEa(generateSettingVo.getEa());
            cloudMessageProxyProto.setAddConversionMessages(voList);
            //跨云
            qiXinMsgSender.sendCloudProxyMQ(generateSettingVo.getFsTenantId(), cloudMessageProxyProto);
        }
        BulkWriteResult integerResult = messageSaveMongoDao.batchReplace(voList.get(0).getEi(), voList);
        log.info("batch save message count:{},ea:{}",integerResult,generateSettingVo.getEa());
        //解密上传至纷享服务器
        if(needUpload){
            messageUploadManger.getMessageUpload(voList, generateSettingVo.getEa());
        }
    }

    private MessageSaveDoc convertMessage(String datas)  {
//        log.info("message convert datas:{}",datas);
        try {
            Object type=JSONPath.read(datas,"$.msgtype");
            if(ObjectUtils.isEmpty(type))return null;
            MessageSaveDoc messageSaveDoc = new MessageSaveDoc();
            messageSaveDoc.setFromUser(JSONPath.read(datas,"$.from").toString());
            Object roomObject = JSONPath.read(datas, "$.roomid");
            String toList = ObjectUtils.isEmpty(JSONPath.read(datas, "$.tolist"))?null:JSONPath.read(datas, "$.tolist").toString();
            if(StringUtils.isEmpty(toList)){
                //有些解析出来的没有tolist，统一不处理
                return null;
            }
            JSONArray jsonArray = JSONArray.parseArray(toList);
            List<String> toUser = jsonArray.toJavaList(String.class);
            messageSaveDoc.setToList(toUser);
            messageSaveDoc.setMessageTime(Long.valueOf(JSONPath.read(datas,"$.msgtime").toString()));
            messageSaveDoc.setMessageType(type.toString());
            messageSaveDoc.setFromUser(JSONPath.read(datas,"$.from").toString());
            if(roomObject != null && !"".equals(roomObject)) {
                messageSaveDoc.setRoomId(roomObject.toString());
            }
            switch (type.toString()) {
                case "text":
                    messageSaveDoc.setContent(JSONPath.read(datas,"$.text.content").toString());
                    break;
                case "image":
                    messageSaveDoc.setFileSize(Long.parseLong(JSONPath.read(datas,"$.image.filesize").toString()));
                    messageSaveDoc.setSdkFileId(JSONPath.read(datas,"$.image.sdkfileid").toString());
                    messageSaveDoc.setMd5sum(JSONPath.read(datas,"$.image.md5sum").toString());
                    messageSaveDoc.setFileExt("jpg");
                    messageSaveDoc.setFileName(this.getFileName(JSONPath.read(datas,"$.image.md5sum").toString(), "jpg"));
                    break;
                case "voice":
                    messageSaveDoc.setFileSize(Long.parseLong(JSONPath.read(datas,"$.voice.voice_size").toString()));
                    messageSaveDoc.setSdkFileId(JSONPath.read(datas,"$.voice.sdkfileid").toString());
                    messageSaveDoc.setMd5sum(JSONPath.read(datas,"$.voice.md5sum").toString());
                    messageSaveDoc.setFileExt("amr");
                    messageSaveDoc.setFileName(this.getFileName(JSONPath.read(datas,"$.voice.md5sum").toString(), "amr"));
                    break;
                case "video":
                    messageSaveDoc.setFileSize(Long.parseLong(JSONPath.read(datas,"$.video.filesize").toString()));
                    messageSaveDoc.setSdkFileId(JSONPath.read(datas,"$.video.sdkfileid").toString());
                    messageSaveDoc.setMd5sum(JSONPath.read(datas,"$.video.md5sum").toString());
                    messageSaveDoc.setFileExt("mp4");
                    messageSaveDoc.setFileName(this.getFileName(JSONPath.read(datas,"$.video.md5sum").toString(), "mp4"));
                    break;
                case "file":
                    messageSaveDoc.setFileSize(Long.parseLong(JSONPath.read(datas,"$.file.filesize").toString()));
                    messageSaveDoc.setSdkFileId(JSONPath.read(datas,"$.file.sdkfileid").toString());
                    messageSaveDoc.setMd5sum(JSONPath.read(datas,"$.file.md5sum").toString());
                    messageSaveDoc.setFileExt(JSONPath.read(datas,"$.file.fileext").toString());
                    messageSaveDoc.setFileName(JSONPath.read(datas,"$.file.filename").toString());
                    break;
                case "location":
                    //位置信息
                    String locationContent=JSONPath.read(datas,"$.location.address").toString();
                    messageSaveDoc.setContent(LOCATION_PREFIX.concat(locationContent));
                    break;
                case "external_redpacket":
                    //位置信息
                    messageSaveDoc.setContent(RED_PACKET_PREFIX);
                    break;
                case "weapp":
                    //小程序app
                    String weappContent = JSONPath.read(datas,"$.weapp").toString();
                    messageSaveDoc.setContent(weappContent);
                    break;
                case "link":
                    //链接
                    String linkContent = JSONPath.read(datas,"$.link.link_url").toString();
                    messageSaveDoc.setContent(linkContent);
                    break;
                default:
                    messageSaveDoc=null;//不保存其他信息
            }
            return messageSaveDoc;
        } catch (NumberFormatException e) {
            log.info("convert message fail:{},data:{}",e.getMessage(),datas);
            e.printStackTrace();
        }
        return null;
    }


    public  InputStream generateStream(GenerateSettingVo generateSettingVo, String sdkFileid) {
        if(ObjectUtils.isEmpty(generateSettingVo)) return null;
        long sdk = Finance.NewSdk();
        Finance.Init(sdk, generateSettingVo.getQywxCorpId(), generateSettingVo.getSecret());
        long ret = 0;
        String indexbuf = "";
        //sdkFileid 是我们从第一步拉取下来的解密消息 然后通过第三步解密后 的消息内容中 获取到的值（text消息没有   只有文件  图片 语音 视频等消息才有此字段）
        List<Byte> list = Lists.newArrayList();
        while (true) {
            long media_data = Finance.NewMediaData();
            log.info("QYWXFileManager.generateStream,media_data--sdk={}", media_data + "--" + sdk);
            System.out.println(media_data + "--" + sdk);
            ret = Finance.GetMediaData(sdk, indexbuf, sdkFileid, ConfigCenter.httpProxy, null, 3, media_data);
            log.info("QYWXFileManager.generateStream,ret={}", ret);
            if (ret != 0) {
                Finance.DestroySdk(sdk);
                Finance.FreeMediaData(media_data);
                return null;
            }
            try {
                byte[] data = Finance.GetData(media_data);
                list.addAll(Bytes.asList(data));
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (Finance.IsMediaDataFinish(media_data) == 1) {
                // need free media_data
                Finance.FreeMediaData(media_data);
                break;
            } else {
                indexbuf = Finance.GetOutIndexBuf(media_data);
                // need free media_data
                Finance.FreeMediaData(media_data);
            }
        }
        Finance.DestroySdk(sdk);
        byte[] datas = Bytes.toArray(list);
        InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(datas, 0, datas.length));
        log.info("QYWXFileManager.generateStream,sdkFileid={}", sdkFileid);
        return inputStream;
    }

    private String getFileName(String name, String type) {
        return name + "." + type;
    }

    private List<MessageSaveDoc> deepCopyMessageSaveDocs(List<MessageSaveDoc> messageSaveDocs) {
        if(CollectionUtils.isEmpty(messageSaveDocs)) {
            return messageSaveDocs;
        }
        List<MessageSaveDoc> messageSaveDocList = new LinkedList<>();
        for(MessageSaveDoc doc : messageSaveDocs) {
            Gson gson = new Gson();
            MessageSaveDoc saveDoc = gson.fromJson(gson.toJson(doc), MessageSaveDoc.class);
            messageSaveDocList.add(saveDoc);
        }
        return messageSaveDocList;
    }


}
