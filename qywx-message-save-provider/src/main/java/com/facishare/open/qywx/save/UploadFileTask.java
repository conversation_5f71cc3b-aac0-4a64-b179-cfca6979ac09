package com.facishare.open.qywx.save;

import com.facishare.open.qywx.save.arg.UploadFileArg;
import com.facishare.open.qywx.save.manager.FileManager;
import com.facishare.open.qywx.save.model.UploadFileResult;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.save.vo.QywxMessageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @Date 2021/3/29 17:28 处理
 * @Version 1.0
 */
@Slf4j
public class UploadFileTask implements Runnable {
    private CountDownLatch countDownLatch;
    private InputStream inputStream;
    private FileManager fileManager;
    private UploadFileArg uploadFileArg;

    private SaveMessageService saveMessageService;

    public UploadFileTask(CountDownLatch countDownLatch, InputStream inputStream, UploadFileArg uploadFileArg,SaveMessageService saveMessageService,FileManager fileManager){
        this.countDownLatch=countDownLatch;
        this.inputStream=inputStream;
        this.uploadFileArg=uploadFileArg;
        this.saveMessageService=saveMessageService;
        this.fileManager=fileManager;
    }

    @Override
    public void run() {
        try {
            log.info("input stream :{},arg:{}",inputStream,uploadFileArg);
            UploadFileResult uploadFileResult = fileManager.uploadFile(inputStream, uploadFileArg);
            QywxMessageVo qywxMessageVo=new QywxMessageVo();
            qywxMessageVo.setFsEa(uploadFileArg.getEa());
            qywxMessageVo.setMessageId(uploadFileArg.getMessageId());
            qywxMessageVo.setNpath(uploadFileResult.getNpath());
            qywxMessageVo.setFileName(uploadFileArg.getFileName());
            qywxMessageVo.setFileExt(uploadFileArg.getFileExt());
            qywxMessageVo.setFileSize(uploadFileResult.getFileSize());
            //更新
            Result<Integer> messageResult = saveMessageService.updateMessageById(qywxMessageVo);
            log.info("upload file task arg：{},result:{}",qywxMessageVo,messageResult);
        }finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            countDownLatch.countDown();
        }
    }
}
