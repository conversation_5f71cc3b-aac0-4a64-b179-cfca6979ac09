package com.facishare.open.qywx.save.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.save.arg.UploadFileArg;
import com.facishare.open.qywx.save.config.ConfigCenter;
import com.facishare.open.qywx.save.dao.QyweixinIdToOpenidDao;
import com.facishare.open.qywx.save.model.UploadFileResult;
import com.facishare.open.qywx.save.mongo.dao.MessageSaveMongoDao;
import com.facishare.open.qywx.save.mq.QiXinMsgSender;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.mongodb.bulk.BulkWriteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MessageUploadManger {

    @Autowired
    private MessageGeneratingService generatingService;
    @Autowired
    private QYWXFileManager qywxFileManager;
    @Autowired
    private FileManager fileManager;
    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private MessageSaveMongoDao messageSaveMongoDao;
    @Autowired
    public QyweixinIdToOpenidDao qyweixinIdToOpenidDao;
    @Resource
    QyweixinAccountSyncService qyweixinAccountSyncService;

    @Autowired
    private QiXinMsgSender qiXinMsgSender;

    ExecutorService executorService = Executors.newFixedThreadPool(20);

    //获取数据，上传文件
    public void getMessageUpload(List<MessageSaveDoc> messageResult, String ea) {
        executorService.execute(()->{
            this.getMessageUpload2(messageResult, ea);
        });
    }

    //获取数据，上传文件
    private void getMessageUpload2(List<MessageSaveDoc> messageResult, String ea) {
//        log.info("saveMessageServiceImpl.getMessageUpload2,messageResult={},ea={}.", messageResult, ea);
        Integer count = messageResult.size();
        //返回消息中可能包含未解密的。需要多线程解密文件上传到纷享服务器
        Result<List<GenerateSettingVo>> listResult = generatingService.queryByEaSetting(ea);
        Map<Integer, GenerateSettingVo> versionMap = listResult.getData().stream().collect(Collectors.toMap(GenerateSettingVo::getVersion, key1 -> key1, (key1, key2) -> key2));
        Boolean isUpdate = Boolean.FALSE;
        List<MessageSaveDoc> messageSaveDocList = new LinkedList<>();
        for (MessageSaveDoc messageSaveDoc : messageResult) {
            log.info("SaveMessageServiceImpl.getMessageUpload.ea={},messageId={}.", ea, messageSaveDoc.getMessageId());
            //客户消息有大文件，解密会造成服务器oom，为此有两个限制：
            //1、特殊客户不受限制
            //2、普通客户只能解密小于MESSAGE_UPLOAD_MAX的文件
            if(!"null".equals(String.valueOf(messageSaveDoc.getFileSize()))) {
                if(!ConfigCenter.MESSAGE_UPLOAD_EA.contains(ea) &&
                        messageSaveDoc.getFileSize() > Long.parseLong(ConfigCenter.MESSAGE_UPLOAD_MAX)) {
                    log.info("SaveMessageServiceImpl.getMessageUpload.ea={},messageId1={}.", ea, messageSaveDoc.getMessageId());
                    continue;
                }
            }
            if (StringUtils.isEmpty(messageSaveDoc.getNpath())) {
                log.info("SaveMessageServiceImpl.getMessageUpload.ea={},messageId2={}.", ea, messageSaveDoc.getMessageId());
                //说明还未上传到纷享的服务器
                if (ObjectUtils.isEmpty(versionMap.get(messageSaveDoc.getKeyVersion()))) {
                    log.info("SaveMessageServiceImpl.getMessageUpload.ea={},messageId3={}.", ea, messageSaveDoc.getMessageId());
                    continue;
                }
                //获取stream
                GenerateSettingVo data = versionMap.get(messageSaveDoc.getKeyVersion());
                InputStream inputStream = qywxFileManager.generateStream(data, messageSaveDoc.getSdkFileId());
                if (ObjectUtils.isEmpty(inputStream)) {
                    log.info("SaveMessageServiceImpl.getMessageUpload.ea={},messageId4={}.", ea, messageSaveDoc.getMessageId());
                    continue;
                }
                //上传 文件
                log.info("upload file staring....");
                UploadFileArg uploadFileArg = new UploadFileArg();
                uploadFileArg.setEa(messageSaveDoc.getFsEa());
                uploadFileArg.setMessageId(messageSaveDoc.getMessageId());
                uploadFileArg.setMessageType(messageSaveDoc.getMessageType());
                uploadFileArg.setFileName(ObjectUtils.isEmpty(messageSaveDoc.getFileName()) ? messageSaveDoc.getMd5sum() : messageSaveDoc.getFileName());
                uploadFileArg.setFileSize(messageSaveDoc.getFileSize());
                uploadFileArg.setFileExt(messageSaveDoc.getFileExt());
                UploadFileResult uploadFileResult = null;
                try {
                    uploadFileResult = fileManager.uploadFile(inputStream, uploadFileArg);
                    inputStream.close();
                } catch (Exception e) {
                   log.info("SaveMessageServiceImpl.getMessageUpload,uploadFile failed,ea={}", ea);
                }
                if(ObjectUtils.isEmpty(uploadFileResult)) {
                    log.info("SaveMessageServiceImpl.getMessageUpload.ea={},messageId5={}.", ea, messageSaveDoc.getMessageId());
                    continue;
                }
                messageSaveDoc.setMessageId(uploadFileArg.getMessageId());
                messageSaveDoc.setNpath(uploadFileResult.getNpath());
                messageSaveDoc.setFileName(uploadFileArg.getFileName());
                messageSaveDoc.setFileExt(uploadFileArg.getFileExt());
                messageSaveDoc.setFileSize(uploadFileResult.getFileSize());
                messageSaveDocList.add(messageSaveDoc);
                if(isUpdate == Boolean.FALSE) {
                    isUpdate = Boolean.TRUE;
                }
            }
        }
        if(isUpdate) {
            //            log.info("SaveMessageServiceImpl.getMessageUpload.ea:{},messageSaveDocList={}.", messageSaveDocList.get(0).getFsEa(), messageSaveDocList);
            //跨云
            if(ConfigCenter.TEM_CLOUD_EA.contains(ea)) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.conversionMessagePush.name());
                cloudMessageProxyProto.setFsEa(ea);
                cloudMessageProxyProto.setUpdateConversionMessageIds(messageSaveDocList);
                //跨云
                qiXinMsgSender.sendCloudProxyMQ(messageSaveDocList.get(0).getEi(), cloudMessageProxyProto);
            }
            BulkWriteResult integerResult = messageSaveMongoDao.batchUpdate(messageSaveDocList.get(0).getEi(), messageSaveDocList);
            log.info("batch update message count:{},ea:{}", integerResult, ea);
        }
    }

    public void uploadExternalIds(String corpId, List<String> externalIds) {
        //查询表里是否已有了
        //转换
        //保存
        //转换失败的也存进去，每天定时任务删除
        List<String> exitExternalIds = qyweixinIdToOpenidDao.getByExternalIds(corpId, externalIds);
        List<String> updateExternalIds = externalIds.stream()
                .filter(v -> !exitExternalIds.contains(v))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(updateExternalIds)) {
            return;
        }
        qyweixinAccountSyncService.plainToEncryption(corpId, updateExternalIds);
    }

    public void uploadNextMesssage(Integer ei, QueryMessageArg arg) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                log.info("async uploading file....");
                arg.setPageNum(arg.getPageNum() + 1);
                List<MessageSaveDoc> npathMessage = messageSaveMongoDao.pageByFilters(ei, arg);
                //客户消息有大文件，解密会造成服务器oom，为此有两个限制：
                //1、特殊客户不受限制
                //2、普通客户只能解密小于MESSAGE_UPLOAD_MAX的文件
                List<MessageSaveDoc> npathResult = new LinkedList<>();
                for(MessageSaveDoc messageSaveDoc : npathMessage) {
                    if(!"null".equals(String.valueOf(messageSaveDoc.getFileSize()))) {
                        if(!ConfigCenter.MESSAGE_UPLOAD_EA.contains(arg.getFsEa()) &&
                                messageSaveDoc.getFileSize() > Long.parseLong(ConfigCenter.MESSAGE_UPLOAD_MAX)) {
                            log.info("SaveMessageServiceImpl.conditionQueryMessage.ea={},messageId={}.", arg.getFsEa(), messageSaveDoc.getMessageId());
                            continue;
                        }
                    }
                    npathResult.add(messageSaveDoc);
                }
                getMessageUpload(npathResult, arg.getFsEa());
            }
        });
    }


}
