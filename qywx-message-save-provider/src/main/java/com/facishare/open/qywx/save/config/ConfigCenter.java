package com.facishare.open.qywx.save.config;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;


public class ConfigCenter {


    //在企业微信的应用id
    public static String QYWX_CRM_APPID = "wx88a141937dd6f838";
    //公司出网IP列表
    public static List<String> IP_LIST;

    public static String CRM_REST_OBJ_URL="http://**************:17263/API/v1/rest/object";

    public static String ACTIVERECORDOBJ_URL="http://**************:17263/API/v1/inner/object/ActiveRecordObj/action/Add";

    //允许企业会话时解密大文件
    public static Set<String> MESSAGE_UPLOAD_EA= Sets.newHashSet();
    //企业会话解密文件的最大值
    public static String MESSAGE_UPLOAD_MAX;
    //代开发自建应用id
    public static String QYWX_REP_APPID;
    //一次拉取会话最大条数，支持特殊企业定制最大条数
    public static String MESSAGE_PULL_MAX;
    //密钥
    public static String BASE64_SECRET = "aEsva0zDHECg47P8SuPzmw==";

    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    public static String MSG_STORAGE_LOCATION = "{\"testqywx\":[\"salesRetentionType\",\"conversionObjType\"]}";

    public static Set<String> TEM_CLOUD_EA = Sets.newHashSet();

    public static String httpProxy;
    public static String crmAppId;
    public static String programId;

    static {
        ConfigFactory.getInstance().getConfig("fs-open-qywx-message-save", config -> {
            CRM_REST_OBJ_URL = config.get("CRM_REST_OBJ_URL", CRM_REST_OBJ_URL);
            ACTIVERECORDOBJ_URL = config.get("ACTIVERECORDOBJ_URL", ACTIVERECORDOBJ_URL);
            QYWX_CRM_APPID = config.get("QYWX_CRM_APPID", QYWX_CRM_APPID);
            IP_LIST= Splitter.on(",").splitToList(config.get("IP_LIST"));
            MESSAGE_UPLOAD_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("MESSAGE_UPLOAD_EA", "")));
            MESSAGE_UPLOAD_MAX = config.get("MESSAGE_UPLOAD_MAX", MESSAGE_UPLOAD_MAX);
            QYWX_REP_APPID = config.get("QYWX_REP_APPID", QYWX_REP_APPID);
            MESSAGE_PULL_MAX = config.get("MESSAGE_PULL_MAX", MESSAGE_PULL_MAX);
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            MSG_STORAGE_LOCATION = config.get("MSG_STORAGE_LOCATION", MSG_STORAGE_LOCATION);
            TEM_CLOUD_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("TEM_CLOUD_EA", "")));
            httpProxy = config.get("httpProxy", "");
            programId = config.get("programId", "");
        });
        ConfigFactory.getInstance().getConfig("fs-open-qywx-app-config", config -> {
            crmAppId = config.get("crmAppId");

        });
    }
}