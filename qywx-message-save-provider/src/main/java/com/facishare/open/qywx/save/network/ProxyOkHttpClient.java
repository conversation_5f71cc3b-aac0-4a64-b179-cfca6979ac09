package com.facishare.open.qywx.save.network;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class ProxyOkHttpClient extends ProxyHttpClient {


    public ProxyOkHttpClient() {
        super();
    }

    @Override
    public <T> T postUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        return super.postUrl(url, params, headerMap, typeReference);
    }

    @Override
    public <T> T postUrlSerialNull(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        return super.postUrlSerialNull(url, params, headerMap, typeReference);
    }

    @Override
    public <T> T postUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        return super.postUrlByJson(url, paramsJson, headerMap, typeReference);
    }

    @Override
    public <T> T getUrl(String url, Map<String, String> headerMap, TypeReference<T> typeReference) {
        return super.getUrl(url, headerMap, typeReference);
    }

    @Override
    public String postUrl(String url, Object params, Map<String, String> headerMap) {
        String result = super.postUrl(url, params, headerMap);

        return result;
    }

    @Override
    public String postUrlSerialNull(String url, Object params, Map<String, String> headerMap) {
        return super.postUrlSerialNull(url, params, headerMap);
    }

    @Override
    public String getUrl(String url, Map<String, String> headerMap) {
        String result = super.getUrl(url, headerMap);
        return result;
    }
}
