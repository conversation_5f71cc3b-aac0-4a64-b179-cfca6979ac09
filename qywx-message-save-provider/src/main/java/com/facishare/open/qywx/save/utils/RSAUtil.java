/*
 * work_wx
 * wuhen 2020/1/16.
 * Copyright (c) 2020  jianfeng<PERSON><EMAIL> All Rights Reserved.
 */

package com.facishare.open.qywx.save.utils;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * RSA加密和解密工具
 *
 * @Author: syj
 * @CreateDate: 2018/7/20 16:52
 */
public class RSAUtil {

	/**
	 * 数字签名，密钥算法
	 */
	private static final String RSA_KEY_ALGORITHM = "RSA";

	/**
	 * 数字签名签名/验证算法
	 */
	private static final String SIGNATURE_ALGORITHM = "MD5withRSA";

	/**
	 * RSA密钥长度，RSA算法的默认密钥长度是1024密钥长度必须是64的倍数，在512到65536位之间
	 */
	private static final int KEY_SIZE = 2048;

	/**
	 * 生成密钥对
	 */

	private static final String privKeyPEM = "-----BEGIN PRIVATE KEY-----\n"+
			"MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDJNR8R3hyf+YwP75ybxgVHs8dejlWvzIo9p/WylMtIkNbSZFFJ3c2LxWbE2/BB47B/4LUYxBvWcvkRvRmj5666y3j7RD9hoYoeaLzI5qhj4l4grOMapSKWFRepRFNN/uPh470vbPreMhUxpM1ChPQN1UdMEaLphoQFJ7MJerPrOU3vu5QYfaN/crl/n1ewtQ110RqRBUm1nu5LRKMOkEXV5bpAHyhi86pdGxAqeBgQEe1MnoAvmPUfcHre3muDWYw9xl4wX4KpJu0n4LmwFSkoR5Atohlvf1yeQprI5fEpJ8kU+m+BFSVtMkAhN8oRxU4PYIsu4LBCAWDRgfsGVR63AgMBAAECggEBAI5jzIANin5f0JH6hP7sLRJoFMBCxDrr6izl7weZKx9IeO5dVfWLRQoUAb1w1F8crexhbmd/aR/jL4YxJ7MJo2rH0e8nhh8mfaM1hdw695HwQaKoFvPlR0uk06DTHI0Gw+g5DcpBOmwCT2NZeHUJt9kORS19EahnrIglZxtfPXJJUWriNqNlDkwtAyJIen20gv6DomrIJ61HFNKJU+pwIQS8TcOYGWDkl/8AUfUHwY25yh8wwTmX0zdwFOUokGflfUCX6nEsnx4MChV4ibFBHGCLByfN00VvzypkBjXYOiIcKLPYAepo9cXKDfchnPwnWzjJA0MpwoZHR0CyZiId2zECgYEA/EO9q+6nQH83SzDEkJedFcUZm3YUGG6Nw/RZsFMOltYMto4Scj1zItCWSnSmqocTGSE988uI2KsPP9MxdsXOCc9BmImIwxzj05YeqiXsJiEUkFm3BLAb6Z/S9Y+UrN9wa8wXLdeW9fj0yAPKhs3TJwN+R9QjAqbILui2UwB/yK8CgYEAzC/Wnrm3d4LPmHXs6rXarZIzP9uWxWcnlgVNVFsZOyV2hHdQmr20GdzlNTk9ODp1a9XfHR3UWZhmZTFpWkdsUQ6jzF/PfPh7M6iKRCqBE2bN7GvcOh1joi/Ca+9sGpWfQr7sZgSvajf1whKqZ34k54qBX/ZZCG72Mu4+1QiN/HkCgYAYTMoziqyvyNFhu9PjfcdS9oaN9CThaZzcWGhfVNDd9MaKu0rJmGPD4cXobC411Qcg75PRLTUEcg3o/wYPw+QiC8Xs1KrI6LqFgjt39mk2Dw+1C/9WQ0SdD5k5sFgJAwkISUOeVdsj3JRvw/W5YJBLfMmoT6YDtl8oLaCKhEzK3wKBgDDItTRTFtx86nB4rFQfgtG5fnkhU9JyJOkY9zLSWSLifoCDqURvUppjRngC5veKMAfFn3rrZ5LIcJ54wb0KF3z+THBF6+Ll0zmyaOaEaTZjd4um8YUJBIb5djAnkeKAIP7ncr+lGuv71sG5h/EWGGchlmuBBiCXskbU2To4wwOxAoGAErzONYsJIG73OSnq5cgfnf3hIodyCrDpIz57GcXE+VFd93k0A3KoiG54Z7ZOj6MtS8MHO31GgsPctVSUYuzlXRV7pdDhAm8jf24Wc6i7fav34UfHG+eFamO2X/ProgFRxOX5jQlaEDdWSodAMxkDHzDbhbMgXoUzH12Hn2cYhTI=\n"
			+ "-----END PRIVATE KEY-----";




	public static Map<String, String> initKey() throws Exception {
		KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
		keyPairGenerator.initialize(KEY_SIZE);
		KeyPair keyPair = keyPairGenerator.generateKeyPair();
		RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
		RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
		String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
		String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
		NewRSAUtils.RSAKeyPair rsaKeyPair = new NewRSAUtils.RSAKeyPair(publicKeyString, privateKeyString);
		Map<String, String> keyPairMap = new HashMap<>();
		keyPairMap.put("publicKeyString", rsaKeyPair.getPublicKey());
		keyPairMap.put("privateKeyString", rsaKeyPair.getPrivateKey());
		return keyPairMap;
	}

	public static void main(String[] args) throws Exception {
		Map<String, String> stringStringMap = initKey();
		System.out.println("公钥:{}"+stringStringMap.get("publicKeyString"));
		System.out.println("私钥:{}"+stringStringMap.get("privateKeyString"));
	}


	/**
	 * 密钥转成字符串
	 *
	 * @param key
	 * @return
	 */
	public static String encodeBase64String(byte[] key) {
		return Base64.encodeBase64String(key);
	}

	/**
	 * 密钥转成byte[]
	 *
	 * @param key
	 * @return
	 */
	public static byte[] decodeBase64(String key) {
		return Base64.decodeBase64(key);
	}

	/**
	 * 公钥加密
	 *
	 * @param data      加密前的字符串
	 * @param publicKey 公钥
	 * @return 加密后的字符串
	 * @throws Exception
	 */
	public static String encryptByPubKey(String data, String publicKey) throws Exception {
		byte[] pubKey = RSAUtil.decodeBase64(publicKey);
		byte[] enSign = encryptByPubKey(data.getBytes(), pubKey);
		return Base64.encodeBase64String(enSign);
	}

	// 用此方法先获取秘钥
	public static String getPrivateKey(String privateKey,String str) throws Exception {

		String privKeyPEMnew = privateKey.replaceAll("\\n", "").replace("-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "");

		byte[] decoded = java.util.Base64.getDecoder().decode(privKeyPEMnew);
		RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA")
				.generatePrivate(new PKCS8EncodedKeySpec(decoded));
		// 64位解码加密后的字符串
		byte[] inputByte = java.util.Base64.getDecoder().decode(str);

		// RSA解密
		Cipher cipher = Cipher.getInstance("RSA");
		cipher.init(Cipher.DECRYPT_MODE, priKey);
		String outStr = new String(cipher.doFinal(inputByte));
		return outStr;
	}
	/**
	 * 公钥加密
	 *
	 * @param data   待加密数据
	 * @param pubKey 公钥
	 * @return
	 * @throws Exception
	 */
	public static byte[] encryptByPubKey(byte[] data, byte[] pubKey) throws Exception {
		X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(pubKey);
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
		PublicKey publicKey = keyFactory.generatePublic(x509KeySpec);
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.ENCRYPT_MODE, publicKey);
		return cipher.doFinal(data);
	}

	/**
	 * 私钥加密
	 *
	 * @param data       加密前的字符串
	 * @param privateKey 私钥
	 * @return 加密后的字符串
	 * @throws Exception
	 */
	public static String encryptByPriKey(String data, String privateKey) throws Exception {
		byte[] priKey = RSAUtil.decodeBase64(privateKey);
		byte[] enSign = encryptByPriKey(data.getBytes(), priKey);
		return Base64.encodeBase64String(enSign);
	}

	/**
	 * 私钥加密
	 *
	 * @param data   待加密的数据
	 * @param priKey 私钥
	 * @return 加密后的数据
	 * @throws Exception
	 */
	public static byte[] encryptByPriKey(byte[] data, byte[] priKey) throws Exception {
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(priKey);
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
		PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.ENCRYPT_MODE, privateKey);
		return cipher.doFinal(data);
	}

	/**
	 * 公钥解密
	 *
	 * @param data   待解密的数据
	 * @param pubKey 公钥
	 * @return 解密后的数据
	 * @throws Exception
	 */
	public static byte[] decryptByPubKey(byte[] data, byte[] pubKey) throws Exception {
		X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(pubKey);
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
		PublicKey publicKey = keyFactory.generatePublic(x509KeySpec);
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.DECRYPT_MODE, publicKey);
		return cipher.doFinal(data);
	}

	/**
	 * 公钥解密
	 *
	 * @param data      解密前的字符串
	 * @param publicKey 公钥
	 * @return 解密后的字符串
	 * @throws Exception
	 */
	public static String decryptByPubKey(String data, String publicKey) throws Exception {
		byte[] pubKey = RSAUtil.decodeBase64(publicKey);
		byte[] design = decryptByPubKey(Base64.decodeBase64(data), pubKey);
		return new String(design);
	}

	/**
	 * 私钥解密
	 *
	 * @param data   待解密的数据
	 * @param priKey 私钥
	 * @return
	 * @throws Exception
	 */
	public static byte[] decryptByPriKey(byte[] data, byte[] priKey) throws Exception {
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(priKey);
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
		PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.DECRYPT_MODE, privateKey);
		return cipher.doFinal(data);
	}

	/**
	 * 私钥解密
	 *
	 * @param data       解密前的字符串
	 * @param privateKey 私钥
	 * @return 解密后的字符串
	 * @throws Exception
	 */
	public static String decryptByPriKey(String data, String privateKey) throws Exception {
		byte[] priKey = RSAUtil.decodeBase64(privateKey);
		byte[] design = decryptByPriKey(Base64.decodeBase64(data), priKey);
		return new String(design);
	}

	/**
	 * RSA签名
	 *
	 * @param data   待签名数据
	 * @param priKey 私钥
	 * @return 签名
	 * @throws Exception
	 */
	public static String sign(byte[] data, byte[] priKey) throws Exception {
		// 取得私钥
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(priKey);
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
		// 生成私钥
		PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
		// 实例化Signature
		Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
		// 初始化Signature
		signature.initSign(privateKey);
		// 更新
		signature.update(data);
		return Base64.encodeBase64String(signature.sign());
	}

	/**
	 * RSA校验数字签名
	 *
	 * @param data   待校验数据
	 * @param sign   数字签名
	 * @param pubKey 公钥
	 * @return boolean 校验成功返回true，失败返回false
	 */
	public boolean verify(byte[] data, byte[] sign, byte[] pubKey) throws Exception {
		// 实例化密钥工厂
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
		// 初始化公钥
		X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(pubKey);
		// 产生公钥
		PublicKey publicKey = keyFactory.generatePublic(x509KeySpec);
		// 实例化Signature
		Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
		// 初始化Signature
		signature.initVerify(publicKey);
		// 更新
		signature.update(data);
		// 验证
		return signature.verify(sign);
	}



}