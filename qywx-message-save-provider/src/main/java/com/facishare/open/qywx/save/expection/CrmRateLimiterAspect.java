package com.facishare.open.qywx.save.expection;

import com.facishare.open.qywx.save.limiter.CrmRateLimiter;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

/**
 * 调用crm接口限速
 */
@lombok.extern.slf4j.Slf4j
public class CrmRateLimiterAspect implements MethodInterceptor {

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        String fullClassName = invocation.getMethod().getDeclaringClass().getName();
        String methodName = invocation.getMethod().getName();
        Object result = null;
        if (CrmRateLimiter.isAllowed(null)) {
            // 调用接口
            log.info("CrmRimiterAspect.around,{}.{}",
                    fullClassName, methodName);
            result = invocation.proceed();
        } else {
            // 处理超限情况
            log.info("CrmRimiterAspect.around,{}.{} frequent calls",
                    fullClassName, methodName);
            return null;
        }
        return result;
    }
}

