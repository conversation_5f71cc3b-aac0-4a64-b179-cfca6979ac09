package com.facishare.open.qywx.save.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

public class ParallelUtils {
    private static final Logger LOG = LoggerFactory.getLogger(ParallelUtils.class);
    private static ExecutorService executorService;
    private static ExecutorService backgroundExecutorService;

    public static final int MAX_PARALLEL_NUM = 200;

    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("ParallelUtils-%d").setDaemon(true).build();
        executorService = new ThreadPoolExecutor(20, 200, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(50),
                workerFactory);

        ThreadFactory backgroundWorkerFactory = new ThreadFactoryBuilder()
                .setNameFormat("ParallelUtils-Background-%d").setDaemon(true).build();
        backgroundExecutorService = new ThreadPoolExecutor(10, 200, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(500),
                backgroundWorkerFactory);
    }

    public static ParallelTask createParallelTask() {
        return new ParallelTaskImpl(executorService);
    }

    public static ParallelTask createBackgroundTask() {
        return new ParallelTaskImpl(backgroundExecutorService);
    }

    public interface ParallelTask {

        ParallelTask submit(Runnable runnable);

        boolean await(long timeout, TimeUnit timeUnit) throws TimeoutException;

        void run();
    }

    private static class ParallelTaskImpl implements ParallelTask {

        private List<Runnable> runnableList = new ArrayList<>(MAX_PARALLEL_NUM);

        private ExecutorService executor;

        public ParallelTaskImpl(ExecutorService executor) {
            this.executor = executor;
        }

        @Override
        public ParallelTask submit(Runnable runnable) {

            if (runnable != null) {
                if (runnableList.size() <= MAX_PARALLEL_NUM) {
                    runnableList.add(runnable);
                } else {
                    throw new RuntimeException("Max Parallel Task Number:" + MAX_PARALLEL_NUM);
                }
            }

            return this;
        }

        @Override
        public boolean await(long timeout, TimeUnit timeUnit) throws TimeoutException {

            final AtomicBoolean ret = new AtomicBoolean(true);

            if (runnableList.isEmpty()) {
                return true;
            }
            CountDownLatch countDownLatch = new CountDownLatch(runnableList.size());

            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            runnable.run();
                        } catch (Exception e) {
                            ret.compareAndSet(true, false);
                            LOG.error("execute task error", e);
                        } finally {
                            countDownLatch.countDown();
                        }
                    });
                } catch (Exception e) {
                    LOG.error("submit task error!", e);
                    throw e;
                }
            }

            try {
                boolean finished = countDownLatch.await(timeout, timeUnit);
                if (!finished) {
                    throw new TimeoutException("execute task timeout");
                }
            } catch (InterruptedException e) {
                throw new TimeoutException("execute task interrupted");
            }

            return ret.get();
        }


        @Override
        public void run() {
            if (runnableList.isEmpty()) {
                return;
            }
            for (Runnable runnable : runnableList) {
                try {
                    executor.submit(() -> {
                        try {
                            runnable.run();
                        } catch (Exception e) {
                            LOG.error("execute task error", e);
                        }
                    });
                } catch (Exception e) {
                    LOG.error("submit task error!", e);
                }
            }
        }
    }

    public static void main(String[] args) throws Exception {

        boolean ret = createParallelTask()
                .submit(() -> System.out.println("1"))
                .submit(() -> System.out.println("2"))
                .await(1, TimeUnit.MILLISECONDS);

        System.out.println(ret);

        ret = createParallelTask()
                .submit(() -> {
                    System.out.println("1");
                    throw new RuntimeException("test");
                })
                .submit(() -> System.out.println("2"))
                .await(1, TimeUnit.MILLISECONDS);

        System.out.println(ret);
    }
}