package com.fscishare.open.qywx.save.service

import com.facishare.open.qywx.save.arg.MessageStorageArg
import com.facishare.open.qywx.save.manager.OANewBaseManager
import com.facishare.open.qywx.save.service.MessageGeneratingService
import com.facishare.open.qywx.save.vo.GenerateSettingVo
import com.google.common.collect.Lists
import com.google.gson.Gson
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class MessageGeneratingServiceTest extends Specification {

    @Autowired
    private MessageGeneratingService messageGeneratingService
    @Autowired
    private OANewBaseManager oaNewBaseManager;

    def "saveSetting"() {
        given:
        GenerateSettingVo generateSettingVo = new GenerateSettingVo()
        generateSettingVo.setEa("111110")
        generateSettingVo.setAgentId("100000")
        generateSettingVo.setCorpSecret("testCorpSecret")
        generateSettingVo.setFsTenantId(111110)
        generateSettingVo.setIpList(Lists.newArrayList("***********"))
        generateSettingVo.setPrivateKey("testPrivateKey")
        generateSettingVo.setPublicKey("testPublicKey")
        generateSettingVo.setSecret("testSecret")
        generateSettingVo.setVersion(1)
        generateSettingVo.setQywxCorpId("testQywxCorpId")
        MessageStorageArg arg = new MessageStorageArg();
        arg.setSalesRetentionType(1)
        arg.setConversionObjType(0)
        generateSettingVo.setStorageLocation(arg)
        expect:
        def result = messageGeneratingService.saveSetting(generateSettingVo)
        println result
    }

    def "getMessageStorageLocation"() {
        given:
        def fsEa = fsEaCase
        def version = versionCase
        def serviceKey = serviceKeyCase
        expect:
        def result = messageGeneratingService.getMessageStorageLocation(fsEa, version, serviceKey)
        println result
        where:
        fsEaCase  |  versionCase  |  serviceKeyCase
        "84883"   |  4          |  "testqywx"
        "84883"   |  null          |  "testqywx"
        "111110"   |  1          |  "testqywx"
        "111110"   |  null          |  "testqywx"
    }

    def "querySettingByAuto"() {
        given:
        def fsEa = fsEaCase
        def version = versionCase
        expect:
        def result = messageGeneratingService.querySettingByAuto(fsEa, version)
        println result
        where:
        fsEaCase  |  versionCase
        "84883"   |  4
        "84883"   |  null
        "111110"   |  1
        "111110"   |  null
    }

    def "querySetting"() {
        given:
        def fsEa = fsEaCase
        def version = versionCase
        expect:

        Boolean aBoolean = oaNewBaseManager.canRunInNewBaseByFsEa("84883");
        System.out.println(aBoolean);
        Boolean aBoolean1 = oaNewBaseManager.canRunInNewBaseByOutEa("wpwx1mDAAA949qpVShKEx6WNwLXh2b3A");
        System.out.println(aBoolean1);
        String x = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></AuthCorpId><InfoType><![CDATA[change_external_contact]]></InfoType><TimeStamp>1732704344</TimeStamp><ChangeType><![CDATA[add_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAACfqm_NsUY9FcuZ8iBp3ooQ]]></UserID><ExternalUserID><![CDATA[wowx1mDAAAmKNuXtrwlH_nX_bwhneThQ]]></ExternalUserID></xml>";
        Boolean aBoolean2 = oaNewBaseManager.canRunInNewBase("wpwx1mDAAA949qpVShKEx6WNwLXh2b3A", x);
        System.out.println(aBoolean2);
        String x1 = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCode><![CDATA[5tukr7mf0aL8yNq1NyVV_YL56zwJkaUgefeeoYAU5tMwN4SOU2IXUveoDG_0h1K62EQI36TeZ8hotDM0ReSf0DEDZREXV5fvApQ285wsOHs]]></AuthCode><InfoType><![CDATA[create_auth]]></InfoType><TimeStamp>1732698188</TimeStamp></xml>";
        Boolean aBoolean3 = oaNewBaseManager.canRunInNewBase(null, x1);
        System.out.println(aBoolean3);


        def result = messageGeneratingService.querySetting(fsEa, version)
        println result
        where:
        fsEaCase  |  versionCase
        "84883"   |  4
        "84883"   |  null
        "111110"   |  1
        "111110"   |  null
    }
}
