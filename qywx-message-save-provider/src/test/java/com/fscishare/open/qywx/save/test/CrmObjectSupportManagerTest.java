package com.fscishare.open.qywx.save.test;

import com.facishare.open.qywx.save.constant.CreateObjectEnum;
import com.facishare.open.qywx.save.manager.CrmObjectSupportManager;
import com.fscishare.open.qywx.save.test.BaseAbstractTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CrmObjectSupportManagerTest extends BaseAbstractTest {
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;

    @Test
    public void createDefineObject() {
        boolean defineObject = crmObjectSupportManager.createDefineObject(74164, CreateObjectEnum.WechatConversionObj.name());
        System.out.println(defineObject);
    }
}
