package com.fscishare.open.qywx.save.test;

import com.alibaba.fastjson.*;
import com.facishare.open.qywx.accountinner.model.QywxAccessTokenInfo;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinGroupChatDetail;
import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.accountsync.utils.GsonUtil;
import com.facishare.open.qywx.save.arg.FileMessageArg;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArg;
import com.facishare.open.qywx.save.config.ConfigCenter;
import com.facishare.open.qywx.save.constant.Constant;
import com.facishare.open.qywx.save.constant.QYWXApiAndObjectEnum;
import com.facishare.open.qywx.save.dao.QyweixinIdToOpenidDao;
import com.facishare.open.qywx.save.dao.SaveMessageDao;
import com.facishare.open.qywx.save.manager.ExternalContactManager;
import com.facishare.open.qywx.save.manager.QYWXFileManager;
import com.facishare.open.qywx.save.model.QyweixinExternalContactGroupChatInfo;
import com.facishare.open.qywx.save.network.ProxyOkHttpClient;
import com.facishare.open.qywx.save.po.QyweixinIdToOpenidPo;
import com.facishare.open.qywx.save.po.QywxMessagePo;
import com.facishare.open.qywx.save.result.*;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.save.service.impl.AutoPullMessageServiceImpl;
import com.facishare.open.qywx.save.utils.RSAUtil;
import com.facishare.open.qywx.save.vo.AutRetentionVo;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.open.qywx.save.vo.QywxMessageVo;
import com.fscishare.open.qywx.save.test.BaseAbstractTest;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.ObjectDataCreateResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/25 16:00
 * @Version 1.0
 */
@Slf4j
public class ServiceTest extends BaseAbstractTest {
    @Autowired
    private MessageGeneratingService generatingService;

    @Autowired
    private SaveMessageService saveMessageService;
    @Autowired
    private QYWXFileManager qywxFileManager;
    @Autowired
    private AutoPullMessageServiceImpl autoPullMessageService;
    @Autowired
    private ExternalContactManager externalContactManager;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private MessageGeneratingService messageGeneratingService;
    @Autowired
    private ObjectDataService objectDataService;
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayServiceNormal;
    @Autowired
    private SaveMessageDao saveMessageDao;
    public QyweixinIdToOpenidDao qyweixinIdToOpenidDao;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private ProxyOkHttpClient proxyOkHttpClient;
    @Autowired
    private MessageGeneratingService settingService;

    @ReloadableProperty("QYWX_REP_APPID")
    private String QYWX_REP_APPID;

    @Test
    public void saveGenerating() {
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();
        String ea = "jjj7351";
        generateSettingVo.setCorpSecret("GZbKS1I7VMuzsIkrBk2ajsuUxSSvJrb9H_B2pkD1bq0");
        generateSettingVo.setAgentId("1000036");
        generateSettingVo.setQywxCorpId("wpwx1mDAAAZVdwgqOHLomWPL0J6rDUVA");
        generateSettingVo.setSecret("123456789101010101010");
        generateSettingVo.setAgentId("111111111");
        generateSettingVo.setEa(ea);
        generateSettingVo.setSecret("ZWVYRYM11uaiG7xDtw0VUi2r67CXqcO0FPNkjO7P7ms");
        generateSettingVo.setPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCLrlqxctpv1dg8ERgfrsizH9s5I099XKGX0qFYi1NwKXKeXTL1bbWRMEWesHSMiNiQPGTouXRDXKmqyVnKJcMGgIBfMfO4uDQ8U7hUyYHvVRURP4uSOL2ZGXLwnIqlI7WRDtTU1RgKVomyDtKlOK9vtEc0js+hMT7OiriBy91XG67nClzzrYdew284Skx61Jc6CpC4hRxJjIABlq5RX5F+Wp5VzntykY/G6L+vdRu4KBnY+fxEowGmlDk2QxIA4K6S4OfXCYHHRmZanF8oWs+uBUTw1eLh90SEa/5ajwtxdA7HyDC8QVlKxerJClyqcpxv1ptjqFBBbXLvsVhwgXEhAgMBAAECggEATTUdmlIS3ZhFQsZsIC8bbq9gHJAhAvkttN7PIkM45plyaoi3fyOaJduZz+JXOcr2cZuAZ4cC9a0Fd4p+YBdJWpGy42uX/PWMof/gtrT/ZkwQLg2C11sXqcWAW/EbTbaUSM764326IRS+XbaFxp+zkToD1dBOghnXTpEs9Um7WcbxzllZfiJ3rd2UxswWvpB9tQk8li9NphY3ioVxckiCtvQiVnUZaT/0RKoae/hN3VPqG6Uv1ZiZ0kcOiu6KwOQ0nXHEN1uzga5UG87Y17V2JadiEUFWI/5O3KEhRf1rl8cNsod1gf9ObbBHH7AuJCYLvdbuXD3fPHWrHCueUXy9hQKBgQD3pY1uUmHZrzsPkIE3oN0Ls2bUcSZ1cIb6gN0zNOn08D0gSYOgJ76sF32cFtSnhqZueqdu4QvPzqTHeMF6oFh8KhdSVtVlWGDQVC8cczsAOq8wBdyckgSa5Pt7XG+g5kL5aaTDb+6XOqzsrKGdiyMpG8AefLYySIixP669JobaJwKBgQCQZIK8N9fNmP6QUNIUtmlZYdtG+LyUZKiCT3fSeYKZwNtfO2IoQYHJFoP4or+FdeUXVU9eaNaYqxp/VH93ZWbERgPwKWagZDoWQskuyfg3HVz3hZ3lc1CVUear8dtO1TTRissVNJnR+ki3jgsf+sBx0vLniLTZsje/KnCX0YBPdwKBgFv2hYPPYfjlgqgwAFw5B3z93RTNA/weknFaA0qtvqevwvNHeXKy77KWcpXRQJ0JeqqSL7UUKz+7PCO66xZvjwxk0Q5Joqsk26bhbDFDdUiLglzyAE/ARaeDmwPferCkcYCPQ5kz6sUMDAVDwixv69mrLXfk1f/sQZ6YyHoDYZaHAoGBAIXK/HDW9bnmSAsFOIREub0+tWY/2M1Pr+x/IjH+sYsybpMBfWR7vnzLxiE+/GP35/0E6XQ7hI0WDolpjGrfpKe9kKyaUCPSexhhbfVS5BJ9vMUGJFaV0Vdq+mjcxC9502VOS/ssMFOmrHaYwaoyONu/caAkxh7pyknyUz35vADlAoGBAOFTuI8DFp16a0L3+D1tnh/zWr5rsIjjxsKZ4Ov35dlLoKMfHYSIBlV8vDATkfO8KdJHeuncUoKJuGibgXI/dO90rAmxIGjTrd9p1YyEsoHqat9NCPz1aq7KNBxNHwmwu1XZI4JWSJwj4dx4emZGnqnx/sBgUASIIDedjwGUWn==");
        generateSettingVo.setPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi65asXLab9XYPBEYH67Isx/bOSNPfVyhl9KhWItTcClynl0y9W21kTBFnrB0jIjYkDxk6Ll0Q1ypqslZyiXDBoCAXzHzuLg0PFO4VMmB71UVET+Lkji9mRly8JyKpSO1kQ7U1NUYClaJsg7SpTivb7RHNI7PoTE+zoq4gcvdVxuu5wpc862HXsNvOEpMetSXOgqQuIUcSYyAAZauUV+RflqeVc57cpGPxui/r3UbuCgZ2Pn8RKMBppQ5NkMSAOCukuDn1wmBx0ZmWpxfKFrPrgVE8NXi4fdEhGv+Wo8LcXQOx8gwvEFZSsXqyQpcqnKcb9abY6hQQW1y77FYcIFxIQIDAQA=");
        generateSettingVo.setVersion(6);
        generateSettingVo.setSeq(0);
        generateSettingVo.setAutRetention(1);
        generateSettingVo.setFsTenantId(11111);
        Result<Integer> integerResult = generatingService.saveSetting(generateSettingVo);
        log.info("insert:{}",integerResult);
    }

    @Test
    public void saveSecretGenerating() {
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();

        generateSettingVo.setEa("83385");
        generateSettingVo.setFsTenantId(83385);
        generateSettingVo.setQywxCorpId("12323");
        generateSettingVo.setCorpSecret("321651fdsfds");
        generateSettingVo.setAgentId("1000108");
       // String token = qyweixinAccountSyncService.getToken("82234", generateSettingVo.getCorpSecret());
        Result<Integer> integerResult = generatingService.saveSecretSetting(generateSettingVo);
        log.info("insert:{}",integerResult);
    }

    @Test
    public void queryMessageSetting(){
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySettingByAuto("82777", null,null);
        log.info("result:{}",generateSettingVoResult);
    }

    @Test
    public void querySecretSetting(){
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySecretSetting("84263");
        log.info("result:{}",generateSettingVoResult);
    }


    @Test
    public void saveMessage(){
        List<QywxMessageVo> messagePos=new ArrayList<>();
        QywxMessageVo messagePo=new QywxMessageVo();
        messagePo.setId(5521188211211112L);
        messagePo.setFsEa("12121");
        messagePo.setFromUser("wewew");
        messagePo.setToList(Lists.newArrayList("1211","121"));
        messagePo.setFromEncryptionUser("wewewTest");
        messagePo.setToEncryptionList(Lists.newArrayList("1211Test","121Test"));

        messagePo.setKeyVersion(2);
        messagePo.setMessageTime(122112112L);
        messagePo.setMd5sum("12333232323");
        messagePo.setSdkFileId("慢啊哈哈哈哈慢");
        messagePo.setContent("快乐啊");
        messagePo.setMessageId("23210377221361222322232");
        messagePo.setSeq(121L);
        messagePo.setMessageType("I");

        QywxMessageVo messagePo2=new QywxMessageVo();
        messagePo2.setId(11123771117723L);
        messagePo2.setFsEa("12121");
        messagePo2.setFromUser("wewew");
        messagePo2.setToList(Lists.newArrayList("1211","121"));
        messagePo.setFromEncryptionUser("wewewTest");
        messagePo.setToEncryptionList(Lists.newArrayList("1211Test","121Test"));
        messagePo2.setKeyVersion(2);
        messagePo2.setMessageTime(121212112L);
        messagePo2.setMd5sum("12333232323");
        messagePo2.setSdkFileId("212121212");
        messagePo2.setMessageId("23232227711232322211");
        messagePo2.setSeq(121L);
        messagePo2.setMessageType("I");

        messagePos.add(messagePo);
        messagePos.add(messagePo2);
        Result<Integer> integerResult = saveMessageService.batchSaveMessage(messagePos);
        System.out.println(integerResult);
    }

    @Test
    public void BatchUpdate(){
        List<QywxMessageVo> messagePos=new ArrayList<>();
        QywxMessageVo messagePo=new QywxMessageVo();
        messagePo.setMessageId("2321002111361222322232");
        messagePo.setContent("haho哈喽");
        messagePo.setNpath("N_PAJEE");
        messagePo.setFileName("文语件11211");
        messagePo.setFileExt("haho哈喽hhh哈哈哈");
        messagePo.setFileSize(1212L);
        messagePos.add(messagePo);
        //1133121211111
        QywxMessageVo messagePo1=new QywxMessageVo();
        messagePo1.setMessageId("232361222322232");
        messagePo1.setContent("嘟嘟哈喽");
        messagePo1.setNpath("N_P222AJEE");
        messagePo1.setFileName("文语件11211");
        messagePo1.setFileExt("haho哈喽hhh哈哈哈");
        messagePo1.setFileSize(1212L);
        messagePos.add(messagePo1);


        Result<Integer> integerResult = saveMessageService.batchUpdateMessage(messagePos,"12121");
    }

    @Test
    public void queryMessage(){
        Result<QywxMessageVo> messageById = saveMessageService.getMessageById("12121", "2321002111361222322232");
        System.out.println("com.fscishare.open.qywx.save.test.ServiceTest.queryMessage"+messageById);
    }

    @Test
    public void updateMessagee(){
        QywxMessageVo qywxMessagePo=new QywxMessageVo();
        qywxMessagePo.setFsEa("12121");
        qywxMessagePo.setMessageId("2321002111361222322232");
        qywxMessagePo.setNpath("kikiki");
        qywxMessagePo.setFileName("今日文档");
        Result<Integer> count = saveMessageService.updateMessageById(qywxMessagePo);
        System.out.println(count);
    }

    @Test
    public void queryMessageConditory(){
        QueryMessageArg queryMessageArg=new QueryMessageArg();
        queryMessageArg.setFsEa("84883");
        queryMessageArg.setOutEa("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        queryMessageArg.setPageNum(1);
        queryMessageArg.setPageSize(20);
        queryMessageArg.setSenderIds("wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        queryMessageArg.setReceiveIds("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ");
//        queryMessageArg.setRoomId("wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg");
        Result<Pager<FileMessageResult>> pagerResult = saveMessageService.conditionQueryMessage(queryMessageArg);
        System.out.println(pagerResult);
    }

    @Test
    public void testArray(){
        String message="{\"msgid\":\"9619449085594287277_1617018676_external\",\"action\":\"send\",\"from\":\"KeNanYing\",\"tolist\":[\"wowx1mDAAAeWRNqUgWjlPF83-VUcFRTw\",\"wowx1mDAAAXmnq7Rw_dxQsuFqWRFYVZA\",\"18911149036\",\"lifang\"],\"roomid\":\"\",\"msgtime\":1617018676069,\"msgtype\":\"image\",\"image\":{\"md5sum\":\"b40e1bfd24c05cc12556af9b4190e37a\",\"filesize\":9502,\"sdkfileid\":\"CtQBMzA2ODAyMDEwMjA0NjEzMDVmMDIwMTAwMDIwNDIxZTYzYjY0MDIwMzBmNGRmYTAyMDQ1MzcwZmIzYTAyMDQ2MDYxYmYzNDA0MjQ2MzMzMzczMDM1MzkzMDYzMmQzODMyNjYzODJkMzQzOTMwMzkyZDM5MzUzMTM2MmQzNzM4MzEzODYzMzkzMjYxMzEzMjY0MzIwMjAxMDAwMjAyMjUyMDA0MTBiNDBlMWJmZDI0YzA1Y2MxMjU1NmFmOWI0MTkwZTM3YTAyMDEwMTAyMDEwMDA0MDASOE5EZGZNVFk0T0RnMU1EUXlPVEF3TURVME9GOHlNREk0TkRrMk1EYzJYekUyTVRjd01UZzJOelk9GiAzOTMyMzEzNjM4NjUzMjM5MzczNjMwNjI2MTY1Mzc2Ng==\"}}";
        Object read = JSONPath.read(message, "$.action");
        Object toList = JSONPath.read(message, "$.tolist");
        JSONArray jsonArray = JSONArray.parseArray(toList.toString());
        List<String> toUser = jsonArray.toJavaList(String.class);
        System.out.println("message:{}"+read.toString());
        System.out.println("message:{}"+toList.toString());
        System.out.println(toUser);
        System.out.println(toUser.get(0));
    }

    @Test
    public void testMessage(){
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting("84883", 4, null);
        qywxFileManager.saveMessage(generateSettingVoResult.getData(),4397L);
    }

    @Test
    public void testQuerymessage(){
        autoPullMessageService.getCorpIdMessage();
    }

    @Test
    public void testMessageCallBack(){
//        String ea="74860";
//        String data="{\"errcode\":0,\"errmsg\":\"ok\",\"has_more\":0,\"next_cursor\":\"nc#mp_3pJpuwAr4w6p52F2RUbR4M1Urr8fiTIlDtq4BkD-iBH4PsMCqiHxiVMmT-0Pw\",\"msg_list\":[{\"msgid\":\"mAOj165BiL12dJSI92rqDalFg0xNvj1zEbLwg_KzfFzk3\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730869311,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"hepbIEwoOC58mhMVhyPq8OyH844lPjM/FuVenh5jDuBXwX5k6x9ZyM3uLxzjysngoV/HoCs+AqoPhlICSe4zjxiEG1jw7dSNcnJfv3zl/BLGWyCBX1IR8S4FiYwA/6zmtgpO8/hRbcDf3srXcbM7wycFeO4G/gdWerO/ac1mSaRi+jEz4Zn4t2CS7wZIzlbV0Cu+JrQoKMspsAl1J00ROWwQEvturmfhAnpY0skHqc22f7EnsmgAdANwNTs/pQfZNxWFel2B3EDPCKYqFvCfxefjdOp6TOj+QQQelakhGGvpEy0c5j2Ln3oimX1ChV5TUK4pVMYWU1ATGkbXLV+CfQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalH9X-9jaE0rbuervyvtGBlO\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730869315,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"knABZUuFe4h3hXYBsEHSfaoKGGV8iQ51PrXR7lhASA0EW0L49RkA+iK8/IMOvndUHbqH+8+IF2i7l9ZcIAP9MpNPCbE1lZV2c8NFiaPenjuaLQRIAeP7pGZnPTYv9xtjNHef5/DzTY+9pBjeNmjeDHO4a/jI6VZBerRlPZ9RzMTKIaGwq1m8EIVrxfic9cgFQUX/xUyIht/2RHLCIoxyfjltsfv5pitzAGWL0pN4VFQ4WUzxby5RBYLss/elBGoMDDdCD19xCHeTvByX0aS2E2CXjED3MeOhQtGb/hXy/ND5MSPIFPMdo6BpxkBxExP1d54yKvbpLyN8yXIcLubjtA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalE-sjYqQ5hXSs3pymUUXAJL\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730874040,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"TS4uxNnXGXbW81aUBevHMX6Ldj9UTy9OeOefnDXsA5HbbZ1ls5VvKohWtVxQA1vM5+fwXnN8rbe8uYVieFZbHwlM2pHB1lWQbhDwxVEw9nXRihjojOadrP/7y7OjcfbK8wtNJxuvVp4v7N98Ceoi7IwRjoloB3B4ouVFiir2XhhyOQfTo6M8bRk2R7WrIunnKX3qjj8qHganps3OkWVtmApq/GcsxPl9UgYKC0and0h163YDL5D6etJ/PTarDfKfuOqsT6h9KZQa/GwVCUCbtcZs4ZI9dvqKkaMX+IAtd5gq3CSY18ZY3M414iGqLcfPGn17fnEnnjaQMkuBTNqwsQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalG9kpmd1W9mXciL6-AfN5C1\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730874042,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"N0VyKxYnwnDIaDDl62NXnfv5RJixSnMmIbPf1yEY5S+jHxudTb94eRtss9CyBA7zC7LjIv7vxj+BN0rYL9IOwkmVmg79f5xK4vKbFbWwa0ps8o3cTuLDRO7mmopcjF0ihxTAADQYUqIE5v3iMJ24DYnzyxLYqB1Nv1wQgC+XB8opgWAg484u85WuXNz/s+pWhOSYCg0NaBPhCtOBrEd9qoyEybpTHoFl8m0wK7cNC2FIzlUlrZ9qcQD76ouRufuAjnFyOWfQGkhnX2POh4NsQ7vhh1nXRxh3oVdt4ShugQBMyvfhnQlCVz/faU0N97hl8kH4/CiVagza0lotm01z0w==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFnGicEQpksB5zEZWcfWuWt\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730874382,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"X6fY8GIjC/08+sxq12tJj5pyACh5EfK9NFW9SXJEEXxesquXY8RG1fZg48S5O+o1LtjDoThSBlPSIO00ejVtzp3uunlkD0tq94I5lDrzOqdLhNdAiYnsmRpkl2HtbVZ2h6mdlxpQXNmvEZfefyh4yE99ksApHQViYlITcEIlT+o+s1zvYWlDM5d9L4tQB3evE0jnNxy1s4mnTF+IFB/jmBy3sVLJFnidf042Dtbaj2kXgQMypdoV3MpeHR7hzOywj1L1qDOvk0cXxe6Uo1kdxcTJECJD7YY9QOY/iiZeP5dRMUI+sfYgKT4kFq5HkJTyMopld9Gsm/JsR+03MLR67Q==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFuwN779UH-BZoC9bouFut2\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730874383,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"dz1Kcg23GXt6iqKSPcSEMqM9xhdCXX51z03QgE5df1wSTkeWoVdbUFv/Mt1ku/73lYXFNCQEwAloormxHIGnkDPIglo9cxm38eA8Na1IAm/R9g44YxeKLBQ9LXJ1B7VTcQqR10DIQ1ajJ/Xg7XTdVvm+MKXBCuj/hivUOA8EUUH4FTmXrZXUEIMVvw5L8qs1w+0b6fJO7i/F6QQX2DZvQTr3K8jSxAW3p1eicYSyQSFY2n6YnGIE0EmDfTt9DvtXBs8YB4EwcpFHiD+PPtBCOWDzCrhGROQir0Lb1ZUD0VfOFHg1zKoWrComSzEsxVt3XfKNj/rDXvJ9tOnBEUauuw==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalEoZmGp3Jck_-Dm-4ZM8Y6l\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730874665,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"PWjaZITZ3qgTuFdHNCcBkCTwonB3UT8wOl215MrVDIgpo2nfBRBPyZ33ULppQtvkvMQNnPKbuIQJctmu5O6KSape2e8oN6pdGQswTarlB8+PxPei8k1HCuZpUy10d4DltitI5VAdBrP2905bZmV0LoS2pM/SKBCv57q2uzFip93bnZbd9ttSxXU85CDLSuWrPfS7bqDgi88dFbXLxKslSxBbQztXFzEcsUTWDHyTvDCA6u13+kxR/Kb4wkpLO82nnczkFfitjv1fm8O1wki3hNOqfnR+pYL7fFkRzlArQlRJznapDmeBtSqMjidkO6Vfnu0gZwFVqd4aen4Q5kEe2w==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalErMl4Tk2k03HP9KiBrNrSp\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730874666,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"Rjn4hg1iQ4aL/JIQ1xmKBCt5l62ULDeTXsomi+PoAXBCks0YZp8P9RuMHdNW/zFIRIrhDMExszmwVdTzVYMSYmC4Mmcu2EpYunGi2PK4GuRzVfICp3cFQDJG54ohRB9h3OTplopf4EMcpxc0aU9Y8ePoYHbsovvkhSI7ayZ2Uuu/fkNyYD3c7AYzEbj2up/PwSBU5LANu/V+wOL10gi0+nMUY0BhA6TPZzrxwpOemQVGMs4YZHdgU1n2hy8U3lrLxB1zdWXmuuOQtrjtFoM4S54jj1E5ZM0m87lCCIcZdgjs8nMpPdl66AY/2915wbV8kFnCA9xaXdqoDeoCR5g8jQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalE_h-dRt5xZQ9MRxroG_OzX\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730874669,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"nojmARfwxkUg1Tcla2fOPWIWfF5slMyETrsZlo1SdB1971vHlMiiMhLGY4CHlzXKITUqjRgb28QwIEd7sZ9J6meEKhKoHgPFsE3u6KO8Mb/0K5qBl9t7t5Fpqz2o+wea8KFLJfBRtv3WSoOkiOE8QvSl29TdyDz+WLYBQMMiGh8YKW8FcvUt4Kx8rnXHfzzEFq47O1dTKKtVIOkqYNVtjlvGlH5Rnv0egdmuMLGrOvlQW68yykFljA5af9BBiPTStYZJYrAzQPMyWvud5aYzGjVi+apSl7K9mVj36PjKaNJaaZRjBnn6nAIeykaxEb+nto3W43xHX41gaLoavTALCA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalHpTjeDAshzCYcmDHrdyNtZ\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730878721,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"vl/n9zkHI7+GNTXcmjZJTM5io/JoXvA79Ih/spHs6ERukcZKG868ncFqtL9hur7bUuNwHZ/4v9vEog+rSVu2G3xXA/eJTkstvkbCkCfBtDTOLEOxZqNScqdSFFUectEtOGHN7pG5ZpI7nl4MLxTZ/76IZAs7VMsbY4aYOzjLNWUHcFh/n+cSaAMqry6tpqY/ed3Seck6Zd1J0/mRyqzawT+TJDQxDA1cNLQN0UhLW19gHymdChCFhAilaYfbM8uzXmc0xFRbFY0KVNokS9Hj0LpKMEUa5AM1wmSmefSqZ6Hj+xO3eAktp59qA9VFptUq9Nfs81+BQV0mI20KyJizTA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalHZtaf_5jLJpLtGBFGzfZwR\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730881350,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"h9XgH88tjvOsmR5juXWYlyj3eIoQDmzHFwMRcTiRbvPWYNeSyv0cLdZ67D0xT0DhLEz1GLZX4ax9Usvcei0phunW/hq+dE2Ic+Lfh0zipB/HNw5WNUWMFQSy0R+556DQzSufSmrWmMLBgfSMSiGsFGXaHvim1Ln2uGwj1Moz5Vk8eesXC7c2FOGEC9PO0nwznQfzlfRFJ0I8dZxr5O4+m3lTOxYHHAzw5TfCGL6FdZVomrYleqaNPBBg+Vy7nOFQam1hytQ5OEyik+0UzgsfoieFENQOOEM0xwdzeKq/37W436d6/kLrdQZSsMl5TN62Mtozyy7RhGlrZoLp+qBWJg==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFPXFtrMRzTWF1PS1TLfcT3\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730881353,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"lugpb49ENyg+7gygAVw45+owJBya0Ac1OhSqQt2M6QHZNRp58I2+dESqm+XvJZ0MKVbEPrNNG5JvOhU0wQTmb7LLmbfd4Xf1wgzvbknlWPutzEm3YbmKu399wsRqGU8WNQuz84teVxCKc/zvjDtF3VTaJKGp74qFxAxPgoWzTElCWLf9zBjLPUya6hOjuKpPkHLbPzPTxWp1+DTzXQobZN5dPOy8W9cxI8mgwX0uNXEfHQ+yoPJcq0mfUPDkAYNcoqadwQKmhGnnS6SrMZrlT3KPdU7+hU785ISgTVYYuDszUqR2XwNUGY0l05lABVAguFILhB08uqdGGeRHx14YHQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalGRX2XN0sj7hJWfMUbcVsro\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730883987,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"FSsbeClu6cT0QPRV5EdYqYrKBn0ZAcyK6SB/UqNQWdq9uKE7fUjLy+REwZOk4RQZvirHb3w73b3btuI8nZSd2JpaLZvtCOvYw8q5+7Yw8c5okNqKmdBC+vvN8B8eiOPzvkEswfEskyaSJ73VK0RgrVgIMPijMdVV/Xvs7lX8rPifXYaxi90F8sv6a3MZonJgckHOJPbr3HjAMr1sd8Ibhpb7Be0HfXGKi7wBiHcEgIYisVHjQXeFR0LieuJlHUhwnZYjCSQiZRnvfrpYZuvnb4I2O7FbRvMpIBtofX/i5B3sKGJQ0FlX0RMJ7U/Z9Chul2uwYlB+pDacIkGJHYXjEA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalF4RbhmmKJyc3QqgSCVqg2J\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730884473,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"ugvX2e8nVp5t47yGyi4xvwjFxfmz0s9oNwV3w476Il7cLp5G4m5k3F+ofwsSE4/voILTe0ybWI/cQfMp1YG7heSFFIjtMY5R9zGWm6e/1b8o9NOVjSYUARPmNgKOxR0NrjsQD+l0vqkcIx4MF4U7YAalKf+Pd2w6LkjT3FNHVRoNbHVKtnoks8GZmYlAPia9Fo2ySCS1mwzjkZmactXp6W8GiCd1QX4ZOEqXZFyFHRVOksD4z6j7LFTdsyDScr5BPu5g0tfNJNroGPCsYkurqtQg3aluiahBsCqOSNzvIG6xUUj0RFlJOXE72A2sXVrwmO7PXKYKKEpbnoTYFlvArw==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFUT3o4GpVBJcbGWCU_sG6t\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730884476,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"c2hS9iAjXELfvsW94rfZll53OGHDv3tzgnE7r6TDiWdwoFnT6laaQgjZIrw4kZJO9D8kndULii5hyRBYqAFdJV88xPtZGo3PcAXn/9v9fif/1Bf0iH/4TjNESkwxSc7eSiRp8C+BjzaUHHMWRFsf9Xk8mCrgsUxWWyIGbn6ume86g94ppvwvVAbPiZKzgjx+nq8aa+WdhjsMO9UUQ1uf13j+1HeJL8XN3wMHGr5X8TJjgzZ5NlQcnH4/w7kvTw+OmyLUgs8lLO9GlFE7fa5d+tqjknYofKiI9NaPQi5Gczq9l4IYYqDec7NjOwfziFuswh7m01GxAppRh+2obYhXng==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalHAW-ONTkp4h-8Gf5WyKwjx\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730884531,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"IV0msdsdntw4qR6OYo8BftWzLoACWLNrQncQXPcyztD96DAzeB8d/jR05nEEC57SStXHYp4Cn9y6vDbUpZyWM9wakTnCWnpBOR710q/whsSeK3kXaE2XaiTaz8A+nR7k6SXnS6FnJni9uV3o3DJoNbYSP6VAOAgG/cIfzTtpJ5E9qyePZFP3BX8m0cPbculfr4Y3PV7BxRZsqLbuobqWC/qmZ5/tuGTAMtrzg2dP+zv1v2qBZaWwcGinb1JaXe1oLCPwvqyFC1Ywtrq+8+Zv1Z5aw3HytN4BBevqjQ5koweiSK/UamWL72i9MTSTovtJhoKpYx5G7nycVBhBuVpeLA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalENaPwSxfO-L2CIcVd5fkjR\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730884534,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"cKvl3k+GIjpsheuOSXJ+k18WVUbGWkSQ7zW3SgwrEaZckpvhknWaWAxgnK+Eltsjp4KZ1iUMx9rqXlzDLmJtjVQgOgcbuY6vr/pBej8HeJ18D73VUg4hx2l+FtBkXqRQbk8WH5NJo3Yd79JvtFQyevyDt+wagtscTF2Vxyva3FR/SfX7GuFn0gkrRO/elWxPQ/s99Oo8w9w0saSN8Huj/bmBdfhIs0oyFttA6r1TsJ3yi44QuCMRIkr3qXYISEVEP194oiIaMpRzTG3BPcK8NMQaECF5CJpliDssRGT5PKrUDG+E8nsXcSVmn+afZ8bS2BfzdZkcwGj0RclMqmEoSQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalF0V6haY4tyWfhAIUBppkWc\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730885506,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"r08K3aCm0HRw6Mr90jGl5u40gOAb5N3A5bT+sFu9B2AEg73fAFR7NI4QEdPwiG9w+1mUYL1R4fE+rL2wZ3/frhxqJWAtghu+5SCD1MyUhKiTXXJ8JRzUBWJbUMQRwkXYcG8Jd3un4mWBHCvLbat7DuWeduTzpjB/jL3QK1uzW4JFifec1zHMz+BUr9F/j2LKGj8+wbu0LMhcIEarP+Ge0ypzozTgSE+Ao37sk/51A1WZ61HL0LtsDYdICw6rucGxsxcDxFXTzI+WN3SZkUJARUu0OTVf8FJ1b+4FQN7v6797wp2fqbPv79voUNcU5Yk79jJqnFFtLYkx3nyD9gPA7g==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFmOBusQXECSwOVmwgIrgHq\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730885754,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"RAiAYvjuBLbzg1V7rPaf/JvQCucBwRGOgkF8/dzmBiJwJQAFho+jDouyZdDenDU8JQcXtC/x4VzATs3YD2S2WXPLCkkcRfAeLYu+kAWSOQwdeSzCSuayfwXpnSUbUp10yLwXebsn7VkS9j+pv56qfAUn6VwwDxLCgyY+o1hCcu9shge3xCQ1WvNc8fGCXOCcG4kxV0JJfsPQIOeI6LK8SGs+NYjTqWRfCVMfQKQ8lFusgBN7MMPFcD+j3Ch1xiCGOWG9qxGmdm2hF+YxEKDx+Uc82xLY6VRvR8PCSXqNl5Aja5mibnI4PkpB8NY4PFT3wJMi71b7TBJodoF3FJB8wA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalG4IK79OZrqzuk1SrumDWKp\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730886536,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"FPBxD4mg/VGJYDKoVMZ8MuLFhUATNt94//3l5I/LNTNWR0ssVa2OAEVXW9PDGw5X/y5FZx2mDbPgq9YqSsIkfYccz85Ol5NxnUNNLDjWUW9W+E6TkHQSURzpLf73EBRV3gK+B1G4JX1vrpsKgfHExI0b/AELuy1vmmWZzfYqhzeI1th4Jbxsgvj0CQ4U5ykDlpkD/gdOL5ARJvBV60U9CCsNmRgEUzz7yaBrQLWMVl3TbPmnck7ZirMElGEVWSn+/qwFXl9/fqt3+4j594Rzl7P8GX2K6bpj3SPYFzUP48P312vzSVVM+nKvnZ+pGdFNGrZ8OOrMvUoWdx8u6FcmYw==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalHPjBgUwqp7RMBqmt32Etjt\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730886539,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"uV7eomJ/wkfV4e0inQ7PZEYwUgv1S8PSTqvou7sbNEP7X6Z0OmI0FqoSvtNN6cdBd1zjP7iLnRjhJytUi1enO3xF6rCNwCVKKmDioX5fFEHoEJQNm33Rp2bZJrWv3Fg/dI+zH1GuWRjrmPWzMe9DfBMT+1UTRmOdIEkgPt578IKLcwm9VQw69XIS9rvhXGpkfba1uYum9thvgIfMcNtCFB3h+GynbWMgEtb6oYouvMD2B4gwu9iMi22EVO5b6A+la8uOuoW7pg0anN6GGcZDXG5G0tPBvDpI7jKrRyF9E4yI5i83DCWGgsLhVeBLud+irWu7/2A9klqk3rYcG9GA1A==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFjbNlDUZQId8xVv33R14VO\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730886771,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"i/inOkiZIN+8XvOiViivUWsl827t/KDcp9tzZ0fucKeBKxBVL5+YotHrz8fpA0kul79srNSj5r6DAZTLiQW1nikpacFf/kBv+Z8sj7fSMkyUHGbZFAKVQvQs3rXS4E3/m5rYfWde7VtXQhQKt5wzKyDgpgZCJ1Yjfzd72BBJopFB17ERz1Uv+E+7pRiM6syzL9e1IQHI/rCC6jD07NLPKxu4PK6lrKK9wF8QnzLLuedfOWchGh5NubnVUDYYIpVXHufSpykCDT/3HZ4jdeK+fqoRaF9KY1csaYsO+eGrJCTNhy7AWhtd4IK1JKqsx+V5GcDFHIF5yPd8VLyGe1goPA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFCgGo2LFDIHKeu7bPVO746\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730886773,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"TBppj3B4Ju0mTbYxJQY1BP3Tk3a0qtcObS94ac4zEP2H3hjc3cGc+z5EgtOVIoIi3CNSAavuNMRJrWkZ0hYm0MzQk68ez5ksV6x1kzqvBK4DzDk9PaEI101R6t3cQGLQRhCsN3hPOnkhFBd5goAR8kRGKu3Rwfip9o0EYj5DfLS16mUeC9+vGE2onLRIbv4TKlpxYZGqiHiAbt/zBjrfXZTrxYaPoqgXMosvKvVpXz2iozCuAyq/6dSgkdXGsQUloaj5OQwysv+5WfP2WSOXpfV19/rpDEixOmUj3AoBDig4lingLH7MIO4xMGGD04X/sc/rXsQItq9VsVbN5tPnZw==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalHzqxGT5F2u8upCe8zv_0lN\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730886796,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"oVQ2DN9v0okKTWXuBd5lxxR1a/decnNOuSfLIrtNjBL1deEpFMdYSmHF6WPgQc0d9kYyK3OdVcDRQaXXhFU3++RupUuyAINMezuK4+N6d9WodTnsRWzvoAGmp10bCV2pMtnK0itir1MwHb8oJFg/SMl4LA+s9a9+6hxcOh5sqrh4qn32GyRQqDRViLKwjFGIys4w30L0GDt2SzCncom4Nyl+cc3i0a6VihVrI0pktHw4KeUeyWiHHXJD6eRT1jSRHBfZn5B9orU+BMdYP3ln61r9xYveo1hbqhNfsuRAmI8zeV8rTxfWvQrzW9qPSKG1afmdfzPZy/sNYMHh9SreaA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFmBUFrMVLzIKOGfd0-fQPQ\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730886797,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"uZHXZDkz4QPdJ5auA3kEOfebdYRzeg0TXaNM51qXu80kmSR1KzjYOxKSWEuw1d6KuiPpyCkB0a0jR6+9GklgD+BfX3QrjXgeZ4zbbTvYY22JJCkfdicYvqPWrM7uN7nCsPmQDq7MG3uQios5WTmSmFP09fXf/n6SNyp05p0WI4LgHIvafDP/bhbf/v9YuiOEiUqOi8jfzteaWUgUNPxhVtpW2PNypqHsbctLF1HK46Q4FE+7MRR3ZMxaOzX6r8Oip/2V+z/aejPcfLVfS+DHNXjwruRMjJ3SDRLjqWGsI12PRRDSXFMJCJFGl58fiHWGSPR9gH7rkKgLNqKUyrrCKw==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalFeZ11wF-XVWBhe8yoy-T8y\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730891279,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"OQby/RVmz2ms94hPOdvMIxFn7MWvzfpvnalvFYt1BjIiZgpeh7XgCNUQgWfmavnVGPyZxEp5RfCJ3/NS/9tXfPkaaybhfriexF2gRINDlqstJ7V/QAdhL/Lc6zLB0ayVqpfAy81nlNoBYOV4kXZNZj0SQh4XKLMBNd7MN0Fh6k0MEsJ2ZEtAAXW1nU+ryJ1jgN412kMB4tS1ljmwlG8YREUXKGqbilQh8E7orX2DtB7HZ7EcEDkQCTKxxTG92+X0e6PFu8YG1FdLImMb4GbSi/S+oox8ooOkikxhkx8xajgVKpNikS3q8TVOK66Iq/GcCtr8EPQ8dW3ywycFsNEzFg==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalHZ2_-TkQH3QghUsAKGTCaZ\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730891282,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"JtsmQLxbDE/Lptc6SBbkzyKO8euMXT/cvci7M+dmA/XRCQr2i9LqL/bhxlU/eqHja9/5WqhGkTwYPIYHh3ytwY4Cs3Mod/2/RLjwOpIvE2O0F+o65gi9/DJE8nvKOrwVJzJkYsB6KhFc/jMRdCn+oug5TLd/xsp/yE+rnX1aPtUcuDPsRCaltH5PYbhvX5WkRmsWvzfYcX//ujMlvG98Wh0WjGxx98cgAbwiEbwcw+uTUZUJOA5IF3EBBTcIejOeLjtxAHATQhs+unuVxOQXsZ2GTRIbibES7NCYFa59oUpbGg7Ncn2DJeH3AavnHr+YTMKfb9EbYldEqQk9TdFZlg==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalGvhxKdPhM9QH9y0r-abgie\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730891284,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"YwVVyBIY8AW9xVagaPSgPDL+akjFfz+rizC/ejhnS08wPTgEHlcP/Xlc0L5/l1Gl6CTedqHgpji/e3+dwAg8/cFYSEwGrG0RJp3jsv1mUCHHe0r6o+OU45I5pS9wPY8s0blkxO20MmawET5b0EprhyipiklBgGpY2YpD1631ilANMqp3m6Ghlk/E+TCGLtqHARBCbP16Lc25SVzb68FJJJWUI8RSCiSUCi696/TMNFGtJU+CGNpf4225sRG2+qL7ZqGCWp2VOhawUAgol4Z3P8EYj45Bf88buhDLcjmVicrDXw/czPzbuW9lQYlntHTLtFqjDJN9c8J6W2f2XYJm0Q==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalE_VmQ8rZI7xn9dxfCRp4-u\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730891286,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"Hcbr9PWkQZd+yjseyaOrpX/0rAaY9xH+r/sHuRrbxCymiG+A7z4YfjnkECXqoOXrHf2klOkCyYrxbt+DuASjPBFSV9f8faq32rD8bfiOugjqUQkVvjsVzg/d1zhAkP58yp4V3O15baU70csQcoIWr2ecLkoUVP6T/1Pt6AATV0clu3aiqlKmNWiy5imq0c0u7A4aFWmr7xxNSGh2hvV2OWD6CUlPq5duPkbVdD8rry+4aYGerF5hN4x4ZGeqr6KyhfKiyGRNCoI7YbNYxjMMPug2/1hkWemlT0ymaa8ISZb164nc1kqjHCxwe5RbK8DSmQPR3mL50B2wYcOKQDZPUQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalGq8iqjJrfcuEkw60Ct5ng4\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730892114,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"pUnq2JmU3oWV0jr/dQxHj+B6EHOI/m2PsXoZZRFgygLsuKuGLQbQ61IyizbuMtTSSwMW6Nsne7l/Xy6tNw3CUrFx2cAjjb4GyqV8Q46EP/z95qWqaQdTyQwuK1Xw6/vxmkRq59ScMzaggTgiN/Vxj2YVZlrG8LJ6wHIbdrlSLjOQEVklCmXEKGeo+e7L4bGni2/FnHQy5/LMUynsQf3XAdA7bBWs5DnVxRV5jBSQ3VceL2H2mJZroZcEirosvvxQ9epWXMyq72n02Ih4ZBBjGRQihiyGE6w0T6F2e110l8QN0oNBb4klvUJdjL5WGaBAssli6XYjkda7LyxYshnLzA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalG1-v5i2vkUMR_gdqF5HyWb\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730892116,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"ZiuwtnnvyTyyKciXJPF+ub20qfMFcfNXQtKFCrGDWDoXMaaayuMwi7yi0edy+woMOqTFxxHricwM6wBoxx2f2IZuCRzSqM38nbkS9xDqgM3EYnr0yi/f4D6/otXpRF8SrxnWHQyl+6WEo7tMnpK7nl6+aG8c6k+Ra9TXsccmR4lW2OQzYpjRRSHFyVWEqR0JDaUUbgdLRacYx1bi2UkYl0d5CT7R1tVd7aYO9Fp4xFx6ZcDJzXqxBsMcdjAPtdEe+T6oUbvmJtwWguglR8077FYAAm/gLHhzKpJ6NR4j7oObhQCXkb2RpCruicy1jjyySGX51dse9pR7MCXdF/kUUQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalG6OLj35-K923pMBjtoeJhE\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730893406,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"bZHCfSnxSapNIZOo9pmoINhRVLeAgQDfa6PnfXft+DM6aorxpBaACMxMGgBixd+6rO8Q7Fa76YralOV7djQBCHVxMjumrXH7E6rcehYbreMqTf3IPYRj822B3XoegW/Ia62xVQdJ+JSGbQEHF8yAgHU9bsigzmRz+ZLXJBU0rsgIJx1MglQshS+F1V0cG8NDnuAEDeYhzR9gHVKLTlYXUBsanBcScKieiJUdvWAoSl6D+z4hRCYwcMNVtonjbAb7rYkijpVZL+vcIv39ZleqzWmiZHg63aZ2J1Yh4anFdFl2cD7AI+sick5tYTUKEFM9YdkiHoFImwiwHzqaEj3kXQ==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]},{\"msgid\":\"mAOj165BiL12dJSI92rqDalE_9T107MfzzTkTz5xMmAP-\",\"sender\":{\"type\":1,\"id\":\"wowx1mDAAARpHK-VBD1wboem1VuheERg\"},\"send_time\":1730893408,\"msgtype\":1,\"service_encrypt_info\":{\"encrypted_secret_key\":\"fOmOq85t6OS4C2Sk4vgDPNyS6jWs36Za+YvfaqBf6JTge1kzOQZcW7gGnJpfLqxDtSEiF94Y40bCitljUlDGe+qwVxp9x63tQWgv+Gz+b3d2B1Lh2TUStw81fX4RBXnXzUbiN5A3HiTwbathpmtwfa/Sn8P2x5nuvTPNpyTv15UQmdSbBKTsLkkEmBj7ksEstmwSN6yeBdMe/4uYTa1QzgPKteArXz7dl1MsOv9h7VshzRbrAroh9yufpsSFspPFRMnBxXmiY/Ib/61ZHcuYJp7TKTngtTBDJ5Y8su0quUy6n0vjR9toymOB6OrdyHUBE3UT5Wrg5C0vitv1ZooJzA==\",\"public_key_ver\":5},\"receiver_list\":[{\"type\":1,\"id\":\"wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ\"}]}]}";
//
//
//        MessageConvertResult messageConvertResult=JSONObject.parseObject(data,MessageConvertResult.class);
//        List<MessageConvertResult.MessageData> dataList = messageConvertResult.getMsg_list();
//        log.info("message save get data list :{}",dataList.size());
//        //因为消息是根据不同密钥区分的。所以需要根据消息指定的版本密钥解密消息
//        Result<List<GenerateSettingVo>> listResult = settingService.queryByEaSetting(ea);
//        Map<Integer, String> versionMap = listResult.getData().stream().collect(Collectors.toMap(GenerateSettingVo::getVersion, GenerateSettingVo::getPrivateKey, (key1, key2) -> key2));
//        List<MessageSaveDoc> messageSaveDocs=Lists.newArrayList();
//        for (MessageConvertResult.MessageData messageData : dataList) {
//            String encryptedKey= messageData.getService_encrypt_info().getEncryptedSecretKey();
//            Integer publicKey=messageData.getService_encrypt_info().getPublicKeyVer();
//            String privateKey = versionMap.get(publicKey);
//            String dataPrivate = null;
//            try {
//                dataPrivate = RSAUtil.getPrivateKey(privateKey, encryptedKey);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            messageData.setSecretKey(dataPrivate);
//            MessageSaveDoc messageSaveDoc=new MessageSaveDoc();
//            messageSaveDoc.setId(new ObjectId());
//            messageSaveDoc.setFsEa(ea);
//            messageSaveDoc.setDescryKey(dataPrivate);
//            messageSaveDoc.setEi(74860);
//            messageSaveDoc.setKeyVersion(publicKey);
//            messageSaveDoc.setUpdateTime(System.currentTimeMillis());
//            messageSaveDoc.setCreateTime(System.currentTimeMillis());
//            messageSaveDoc.setAppId("wx88a141937dd6f838");
//            messageSaveDoc.setMessageId(messageData.getMsgid());
//            messageSaveDoc.setFromUser(messageData.getSender().getId());
//            List<String> dataIds = messageData.getReceiver_list().stream().map(MessageConvertResult.ReceiverList::getId).collect(Collectors.toList());
//            messageSaveDoc.setToList(dataIds);
//            messageSaveDocs.add(messageSaveDoc);




        autoPullMessageService.getMessageByCallBackToken("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA","dkdf3684b6720635f7","1aabb439e6b6c839a5083868f9ce63b7");

}
    @Test
    public void testMessageCallBack2(){
        String data=JSONObject.toJSONString(Maps.newHashMap());
        com.facishare.open.qywx.accountsync.result.Result<QywxAccessTokenInfo> message = qyweixinGatewayInnerService.getAccessTokenInfo2("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g", "wx88a141937dd6f838");
        System.out.println(message);
    }

    @Test
    public void testAutoMessage(){
        autoPullMessageService.getAutoSynchronizationMessage();
    }
    @Test
    public void testAutoMessageq(){
        List<String> ea = qyweixinAccountSyncService.openAuthorizationByPage(1, 10);
        System.out.println(ea);
    }

    @Test
    public void testAutoMessageq1(){
        List<String> ea = qyweixinAccountSyncService.getAutRetentionCorp();
        System.out.println(ea);
    }

    @Test
    public void testAutoMessage2(){
        qyweixinAccountSyncService.queryByPhone(83838, "phone", "***********", "AccountObj");
    }

    @Test
    public void testAutoMessage4(){
        qyweixinAccountSyncService.queryByPhone(83838, "phone", "***********", "AccountObj");
    }

    @Test
    public void testAutoMessage3(){
        QueryMessageArg queryMessageArg = new QueryMessageArg();
        queryMessageArg.setFsEa("84262");
        queryMessageArg.setOutEa("wpwx1mDAAAZVdwgqOHLomWPL0J6rDUVA");
        List<String> receiveIds=new ArrayList<>();
        receiveIds.add("wowx1mDAAABS8aOmm0Vi97W8jgeU20Mw");
        receiveIds.add("wmwx1mDAAACw7jH5tYISwl6cjEKe8-Ug");
//        queryMessageArg.setReceiveIds(receiveIds);
//        queryMessageArg.setSenderIds(receiveIds);
        int room = saveMessageService.getIsRoom(queryMessageArg);
        List<String> a = saveMessageService.getRoom(queryMessageArg);
        saveMessageService.test(queryMessageArg);
        Result<List<FileMessageResult>> b = saveMessageService.getConditionQueryMessage(queryMessageArg, null);

        System.out.println(room);
    }

    @Test
    public void test() {
        autoPullMessageService.test();
    }

    @Test
    public void test1() {
        //System.out.println(qyweixinAccountSyncService.queryExternalContactListTwoScheme("82236", "***********"));
        qyweixinAccountSyncService.AutoGetExternalContactEmployeeId2("83384",null);
    }

    @Test
    public void sendAutoMessage1() {

        autoPullMessageService.test1();
    }

    @Test
    public void sendAutoMessage2() {

        String userName = saveMessageService.getName("81002", "wxbbe44d073ff6c715", "KeNanYing");
        System.out.println(userName);
    }

    @Test
    public void sendAutoMessage3() {

        //autoPullMessageService.getAutoSynchronizationMessageTest();
    }

    @Test
    public void testRoomHost() {

        QyweixinGroupChatDetail.GroupChat roomMessageId = qyweixinAccountSyncService.getRoomMessage2("83384", "wrwx1mDAAAEbHZ5rpY7Wsyr-xPEXVnfw",null);
        System.out.println(roomMessageId);
    }

    @Test
    public void testMQ() {

        messageGeneratingService.sendMessage("83384");
    }

    @Test
    public void testUpdateCorpRepSecret() {
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();
        generateSettingVo.setQywxCorpId("testRepSecret");
        generateSettingVo.setCorpSecret(null);
        generateSettingVo.setAgentId(null);
        Result<Integer> result = messageGeneratingService.updateCorpRepSecret(generateSettingVo);
        System.out.println(result.getData());
    }


    @Test
    public void testPain() {
        qyweixinAccountSyncService.plainToEncryption("83384", Lists.newArrayList("ChenZongXin"));
    }

    @Test
    public void testSaveSales() {
        AutRetentionVo autRetentionVo = new AutRetentionVo();
//        autRetentionVo.setEi(84262);
//        autRetentionVo.setEa("84262");
//        autRetentionVo.setFsId("1000");
//        autRetentionVo.setFsUserName("chenzongxin");
//        autRetentionVo.setUserName("chenzongxin");
//        autRetentionVo.setContactId(outId);
//        autRetentionVo.setContactName(qyweixinExternalContactInfo.getName());
//        autRetentionVo.setPagerResult(pagerResult);
//        autRetentionVo.setRelatedObject(relatedObject);
//        autRetentionVo.setAttempt(attempt);
        externalContactManager.saveSales(autRetentionVo);
    }

    @Test
    public void testGetMessageUpload() {
        List<QywxMessageVo> messageResult = new LinkedList<>();
        for (long i = 0; i < 10; i++) {
            QywxMessageVo qywxMessageVo = new QywxMessageVo();
            long a = 13000000L + i*1000000L;
            qywxMessageVo.setFileSize(a);
            messageResult.add(qywxMessageVo);
        }
        messageResult.add(new QywxMessageVo());
        String ea = "74860";
        //saveMessageService.getMessageUploadByAuto(messageResult, ea, Boolean.FALSE);
    }

    @Test
    public void testObjectDataServiceCreate() {

        QywxMessagePo messagePo = saveMessageDao.queryMessageById("84883", "7103351322618273246_1665199931325_external");

        HeaderObj headerObj = HeaderObj.newInstance(82777, -10000);
        ObjectData objectData = new ObjectData();
        //objectData.put(QYWXApiAndObjectEnum.NAME.getName(), messagePo.getMessageId());
        objectData.put(QYWXApiAndObjectEnum.OWNER.getName(), Lists.newArrayList("-10000"));
        objectData.put(QYWXApiAndObjectEnum.FS_EA.getName(), messagePo.getFsEa());
        objectData.put(QYWXApiAndObjectEnum.SEQ.getName(), messagePo.getSeq());
        objectData.put(QYWXApiAndObjectEnum.MSG_ID.getName(), "test");
        objectData.put(QYWXApiAndObjectEnum.KEY_VERSION.getName(), messagePo.getKeyVersion());
        objectData.put(QYWXApiAndObjectEnum.FROM_USER_CIPHER.getName(), messagePo.getFromEncryptionUser());
        objectData.put(QYWXApiAndObjectEnum.FROM_USER.getName(), messagePo.getFromUser());
        objectData.put(QYWXApiAndObjectEnum.TO_LIST_CIPHER.getName(), messagePo.getToEncryptionList());
        objectData.put(QYWXApiAndObjectEnum.TO_LIST.getName(), messagePo.getToList());
        //objectData.put(QYWXApiAndObjectEnum.ROOM_ID.getName(), messagePo.getRoomId());
        objectData.put(QYWXApiAndObjectEnum.MSG_TIME.getName(), messagePo.getMessageTime());
        objectData.put(QYWXApiAndObjectEnum.MSG_TYPE.getName(), messagePo.getMessageType());
        objectData.put(QYWXApiAndObjectEnum.CONTENT.getName(), messagePo.getContent());
        //objectData.put(QYWXApiAndObjectEnum.MD5SUM.getName(), messagePo.getMd5sum());
        //objectData.put(QYWXApiAndObjectEnum.SDK_FILE_ID.getName(), messagePo.getSdkFileId());
        //objectData.put(QYWXApiAndObjectEnum.FILE_SIZE.getName(), messagePo.getFileSize());
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataCreateResult> result = null;
        result = objectDataService.create(headerObj, QYWXApiAndObjectEnum.WECHAT_CONVERSION_OBJ.getName(),
                    false,false,false,objectData);
        System.out.println(result);
    }

    @Test
    public void testObjectDataServiceCreateTestMessage__c() {

        HeaderObj headerObj = HeaderObj.newInstance(84883, -10000);
        ObjectData objectData = new ObjectData();
        objectData.put(QYWXApiAndObjectEnum.NAME.getName(), "test010");
        objectData.put(QYWXApiAndObjectEnum.OWNER.getName(), Lists.newArrayList("-10000"));
        FileMessageArg arg = new FileMessageArg();
        arg.setFilename("fe0bd440fc6a46c8489d4f238cf9ef73.jpg");
        arg.setPath("N_202210_19_89ac0f762b30459bbbb674b012a9c029");
        arg.setExt("jpg");
        arg.setSize(1264728L);
        objectData.put("image__c", Lists.newArrayList(arg));
        FileMessageArg arg1 = new FileMessageArg();
        arg1.setFilename("强爽.pdf");
        arg1.setPath("N_202210_20_f43d48b1ab7e4a5183f7cf4f2ba63725");
        arg1.setExt("pdf");
        arg1.setSize(73590L);
        objectData.put("attachment__c", Lists.newArrayList(arg1));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataCreateResult> result = null;
        result = objectDataService.create(headerObj, "testMessage__c",
                false,false,false,objectData);
        System.out.println(result);
    }

    @Test
    public void testObjectDataServiceCreate1() {
        HeaderObj headerObj = HeaderObj.newInstance(82777, -10000);
//        BulkDeleteArg arg = new BulkDeleteArg();
//        arg.setDataIds(Lists.newArrayList("63462acbf7ba6a000165a77d"));
//
//        objectDataService.bulkDelete(headerObj,"WechatGroupObj",
//                arg,true);
        //com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> queryListByIdsResult = objectDataService.queryListByIds(headerObj, "WechatWorkExternalUserObj", Lists.newArrayList("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ"));
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilter(QYWXApiAndObjectEnum.EXTERNAL_USER_ID.getName(), Lists.newArrayList("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ"), QYWXApiAndObjectEnum.OPERATOR.getName());
        //searchTemplateQuery.addFilter("chat_create_time", Lists.newArrayList("1663642641"), "EQ");
        searchTemplateQuery.setPermissionType(0);
        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> oldSpuDataResult = objectDataService.queryBySearchTemplate(headerObj, QYWXApiAndObjectEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), searchTemplateQuery);


        System.out.println(oldSpuDataResult);


//        ObjectData objectData = new ObjectData();
//        objectData.put("name","wmwx1mDAAA6__TC8Z4Ya04gwZhE1epEw");
//        objectData.put("external_user_id","wmwx1mDAAA6__TC8Z4Ya04gwZhE1epEw");
//        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataCreateResult> result = objectDataService.create(headerObj,"WechatWorkExternalUserObj",
//                false,false,false,objectData);
//        System.out.println(result);
    }

    @Test
    public void testGroupInfo() {
        com.facishare.open.qywx.accountsync.result.Result<QyweixinGroupChatDetail> groupChatJSONObject =
                qyweixinGatewayServiceNormal.getGroupChatDetail("84883", "wrQZ1uJQAAbv9IYbAr6BrT-DPF9XU_jg", QYWX_REP_APPID);
        System.out.println(groupChatJSONObject);
        JSONPath.read(JSON.toJSONString(groupChatJSONObject.getData()),"$.group_chat").toString();
        QyweixinExternalContactGroupChatInfo externalContactRsp = JSONObject.parseObject(JSONPath.read(JSON.toJSONString(groupChatJSONObject.getData()),"$.group_chat").toString(), QyweixinExternalContactGroupChatInfo.class);
        System.out.println(externalContactRsp);
    }

    @Test
    public void testBatchQueryMessageByIds() {
        List<QywxMessagePo> newMessageResult = saveMessageDao.batchQueryMessageByIds("84883", Lists.newArrayList("11381565014156652934_1670161999250_external", "11875603809554530463_1669866238854_external", "12349276461183118997_1670223332195_external"));
        System.out.println(newMessageResult);
    }

    @Test
    public void t2() {
        QyweixinIdToOpenidPo a = new QyweixinIdToOpenidPo();
        a.setCorpId("test");
        a.setPlaintextId("pl");
        a.setOpenid("op");
        a.setType(1);
        int s = qyweixinIdToOpenidDao.saveInfo(a);
        System.out.println(s);
    }

    @Test
    public void t3() {
        List<String> s = qyweixinIdToOpenidDao.getByExternalIds("test", Lists.newArrayList("pl"));
        System.out.println(s);
    }

    @Test
    public void t4() {
        List<QyweixinIdToOpenidPo> s = qyweixinIdToOpenidDao.getByOpenIds("test", Lists.newArrayList("pl"));
        System.out.println(s);
    }

    @Test
    public void saveMessageToMongoTest() {
        saveMessageService.saveMessageToMongo("84883", 11L, 20L);
    }

    @Test
    public void saveAllMessageToMongoTest() {
        Result<Void> result = saveMessageService.saveAllMessageToMongo();
        System.out.println(result);
    }

    @Test
    public void saveMessageAllIdsTest() {
        Result<Void> result = saveMessageService.saveMessageAllIds();
        System.out.println(result);
    }

    @Test
    public void getMongoDataByIdTest() {
        Result<QywxMessageVo> result = saveMessageService.getMongoDataById("84883", "63dddb139001a37016b7428e");
        System.out.println(result);
    }

    @Test
    public void updateCorpSetting() {

        Result<Void> result = messageGeneratingService.updateCorpSetting();
        System.out.println(result.getData());
    }

    @Test
    public void insertMongoData() {
        QywxMessageVo message = new QywxMessageVo();

        message.setId(null);
        message.setFsEa("jjj7351");
        message.setMessageId("xxx");
        message.setSeq(50001L);
        message.setKeyVersion(2);
        message.setFromUser("xxx");
        message.setFromEncryptionUser(null);
        message.setToList(Lists.newArrayList("xxx"));
        message.setToEncryptionList(null);
        message.setRoomId(null);
        message.setMessageTime(1714214960133L);
        message.setMessageType("text");
        message.setContent("111");
        message.setMd5sum(null);
        message.setSdkFileId(null);
        message.setFileSize(null);
        message.setNpath(null);
        message.setFileName(null);
        message.setFileExt(null);
        Result<Integer> result = saveMessageService.insertMongoData("jjj7351", message);
        System.out.println(result);
    }

    @Test
    public void testQueryMessage(){
        String corpId="wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA";
        String appId="dkdf3684b6720635f7";
        QywxQueryMessageArg queryMessageArg=new QywxQueryMessageArg();
        QywxQueryMessageArg.ChatInfo chatInfo=new QywxQueryMessageArg.ChatInfo();
        QywxQueryMessageArg.IdInfo idInfo=new QywxQueryMessageArg.IdInfo();
        idInfo.setOpenUserid("open");
        chatInfo.setIdList(Lists.newArrayList(idInfo));
        queryMessageArg.setChatInfo(chatInfo);
        queryMessageArg.setQueryWord("测试测试");
        queryMessageArg.setLimit(1);
//        com.facishare.open.qywx.accountsync.result.Result<QywxAccessTokenInfo> accessTokenInfo = qyweixinGatewayInnerService.getAccessTokenInfo2(corpId, appId);
//        String token=accessTokenInfo.getData().getCorpAccessToken();
//        String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,token);
//
//        autoPullMessageService.getCallData(callUrl, Constant.SYNC_SEARCH_MESSAGE, ConfigCenter.programId,null, GsonUtil.toJson(queryMessageArg));
        Result<QueryMessageIdResult> queryMessageIdResultResult = autoPullMessageService.conditionQueryMessageData(corpId, appId, queryMessageArg);
        System.out.println(queryMessageIdResultResult);

    }
}
