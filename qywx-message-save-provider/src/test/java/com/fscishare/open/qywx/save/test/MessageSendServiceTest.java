package com.fscishare.open.qywx.save.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.save.manager.FileManager;
import com.facishare.open.qywx.save.model.ChatDatas;
import com.facishare.open.qywx.save.model.Qychat;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.utils.RSAUtil;

import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.qixin.api.constant.MessageType;
import com.facishare.qixin.api.exeception.IllegalImageException;
import com.facishare.qixin.api.model.message.content.Document;
import com.facishare.qixin.api.model.message.content.Image;
import com.facishare.stone.sdk.response.StoneFileImageProcessResponse;
import com.facishare.stone.sdk.response.StoneFileImageThumbnailResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fscishare.open.qywx.save.test.BaseAbstractTest;
import com.google.common.collect.Maps;
import com.tencent.wework.Finance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Created by fengyh on 2018/3/7.
 */


@Slf4j
public class MessageSendServiceTest extends BaseAbstractTest {


    @Autowired
    private FileManager fileManager;
    @Autowired
    private MessageGeneratingService settingService;

    private static final String privKeyPEM = "-----BEGIN PRIVATE KEY-----\n"+
            "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDJNR8R3hyf+YwP75ybxgVHs8dejlWvzIo9p/WylMtIkNbSZFFJ3c2LxWbE2/BB47B/4LUYxBvWcvkRvRmj5666y3j7RD9hoYoeaLzI5qhj4l4grOMapSKWFRepRFNN/uPh470vbPreMhUxpM1ChPQN1UdMEaLphoQFJ7MJerPrOU3vu5QYfaN/crl/n1ewtQ110RqRBUm1nu5LRKMOkEXV5bpAHyhi86pdGxAqeBgQEe1MnoAvmPUfcHre3muDWYw9xl4wX4KpJu0n4LmwFSkoR5Atohlvf1yeQprI5fEpJ8kU+m+BFSVtMkAhN8oRxU4PYIsu4LBCAWDRgfsGVR63AgMBAAECggEBAI5jzIANin5f0JH6hP7sLRJoFMBCxDrr6izl7weZKx9IeO5dVfWLRQoUAb1w1F8crexhbmd/aR/jL4YxJ7MJo2rH0e8nhh8mfaM1hdw695HwQaKoFvPlR0uk06DTHI0Gw+g5DcpBOmwCT2NZeHUJt9kORS19EahnrIglZxtfPXJJUWriNqNlDkwtAyJIen20gv6DomrIJ61HFNKJU+pwIQS8TcOYGWDkl/8AUfUHwY25yh8wwTmX0zdwFOUokGflfUCX6nEsnx4MChV4ibFBHGCLByfN00VvzypkBjXYOiIcKLPYAepo9cXKDfchnPwnWzjJA0MpwoZHR0CyZiId2zECgYEA/EO9q+6nQH83SzDEkJedFcUZm3YUGG6Nw/RZsFMOltYMto4Scj1zItCWSnSmqocTGSE988uI2KsPP9MxdsXOCc9BmImIwxzj05YeqiXsJiEUkFm3BLAb6Z/S9Y+UrN9wa8wXLdeW9fj0yAPKhs3TJwN+R9QjAqbILui2UwB/yK8CgYEAzC/Wnrm3d4LPmHXs6rXarZIzP9uWxWcnlgVNVFsZOyV2hHdQmr20GdzlNTk9ODp1a9XfHR3UWZhmZTFpWkdsUQ6jzF/PfPh7M6iKRCqBE2bN7GvcOh1joi/Ca+9sGpWfQr7sZgSvajf1whKqZ34k54qBX/ZZCG72Mu4+1QiN/HkCgYAYTMoziqyvyNFhu9PjfcdS9oaN9CThaZzcWGhfVNDd9MaKu0rJmGPD4cXobC411Qcg75PRLTUEcg3o/wYPw+QiC8Xs1KrI6LqFgjt39mk2Dw+1C/9WQ0SdD5k5sFgJAwkISUOeVdsj3JRvw/W5YJBLfMmoT6YDtl8oLaCKhEzK3wKBgDDItTRTFtx86nB4rFQfgtG5fnkhU9JyJOkY9zLSWSLifoCDqURvUppjRngC5veKMAfFn3rrZ5LIcJ54wb0KF3z+THBF6+Ll0zmyaOaEaTZjd4um8YUJBIb5djAnkeKAIP7ncr+lGuv71sG5h/EWGGchlmuBBiCXskbU2To4wwOxAoGAErzONYsJIG73OSnq5cgfnf3hIodyCrDpIz57GcXE+VFd93k0A3KoiG54Z7ZOj6MtS8MHO31GgsPctVSUYuzlXRV7pdDhAm8jf24Wc6i7fav34UfHG+eFamO2X/ProgFRxOX5jQlaEDdWSodAMxkDHzDbhbMgXoUzH12Hn2cYhTI=\n"
            + "-----END PRIVATE KEY-----";


    @Test
    public void textSendMsg() throws Exception {
        String privateVersion="MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCVZl/fKkWpJQspLBbFKqB93PXoh/3dSG34K9rfu0+cZzfvcLjXPCJCcMg0EEogLt2JWFcJ5lwaqDPJexYBrK6pcnG08WWTsxtUy45T2nT+Rr5lWFf3fBL0sC/RWp7XWYsL6a4GWsg7T8AEl0BEbJtg8IgVReT4KnjUsBwQFtGXjReARW54kudDyc7sCzIZmzMG5qRvkUbzPXW/bychCO8pIl0SuWJt8ArOzqvuyTnT1+vNvHpjxxWL4pyHvnP0JKmJBzuycqUAp7+Ong4tP31cK5DPboGplZsmJ/x6GalLXCzM1vua14ne63gTp3SgfgLklN1E+EN68r998rXNOOyfAgMBAAECggEBAIucdnz6aMyOxGY+QC0SD5ZutRIOe0qdU761ZLzsspGq11eXE/R2ymEiOcSFcG+wu++MMRIcRffXNUXUExWPhEzumJmJd+3VUvYjnqZ3oBq6i1PzgYjRVnl4Z8cRExyVHd4NCkX/8duTRBJGMZV/lJe7Tr/qZ/UM44uF40OZv1UJ3/1VOAzcQiiQeoSg4nUQzRQkZ7zRui9VhO8D4ldvl5yIO3hnhw6TrGPly1Pzi0hn6L7F0VqLNLRB4Agg06+n7LqGjG1xFV/gSX6ACU5dKGWIUP5O/o9ya/ADRTKzgcEvoufRvXrFoDjn+MG6LZA4N8UbVNV7aQDtDiAP8ZoHRHkCgYEA4l9TdkvNr6tUl1BcHxUE2+Hg+GX7rEtvVgpeoUP4GBtWhWI27aKny5RDEm9hG6fHfPieGwGtnZXHwVMMJwDR/3ILDIpw80FXVPiShgDHvxKLFtYZPb95OuvjKnhfIhQlJ2AWj/76Gu//I250gxkWZvJ08LS2tpa6ybHKZy4gJkMCgYEAqPQQGyuLWSQuseZZzO5Pg0DBQM4gCRSDZ2wpJFufMS8csHLR9H2WoKr79o4yQsgIyXY/5oCfy0oCT0g9KLpooQrPw0buzRoTz+lxjkIaErjdKAVllKWB8zFfbgYW9LsJNQ4HUCXYLdOUnQDqB4B7wuYEz/j8lvfdW19okZg70HUCgYEA4Sx9cwB4OzOr01AGMozP5oDLLYsUSxBGUUnDU5quoBwhdlB53uubCTATf7T23XGJR9BuROLodgZVSwJ25h00xmoEf5b+P5pRap5P8ae5CWFCpZaWSQu7ZVbbApX124o6Yu57wwSYeE8edXobv4Qm+oRWulCoJvvMFXEaqoQBSs0CgYEAmNqWZ75xh+hwRor826cPUNQZAyR5Xz2r1LnKTxaFY94Xuptm0Cd/S0TmSB5F4p1PNdVB4qyj5SvEAy58Boj/eBNr/GQEUJMzMXsmPWoYZK/Thg275UwH0ZntmIDx69/7aICUpTJX7r3pSlI1aSwYzC+GUxIUlBETt0MOPArasrECgYBhMiIh6jg6sh7cc/zD+1ylIrZYbJkohlGjPBY7RC+/ovpVwOGuKy/BURnUDz1yS11SXHwpvJRBA33APWhvORsrIB4znRLeMURKUzSFKKKzjGioxRS2Y/KViUd5kYt2Aocl65DONplfMIUmqQWc+3YWGPy9PrTw24EnGhIV5WSV7A==";
        String privKeyPEMnew = privateVersion.replaceAll("\\n", "").replace("-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "");


        Map<String, String> keyMap = RSAUtil.initKey();
        String publicKeyString = keyMap.get("publicKeyString");
        String privateKeyString = keyMap.get("privateKeyString");
        System.out.println(publicKeyString);
        System.out.println("-----------------------------------------------------");
        System.out.println(privateKeyString);
        log.info("HHHHH哈哈哈哈");

    }

    @Test
    public void descryMessage() throws Exception {
        String json="{\n" +
                "    \"errcode\": 0,\n" +
                "    \"errmsg\": \"ok\",\n" +
                "    \"has_more\": 0,\n" +
                "    \"next_cursor\": \"nc#czECs_rGce99VQnGdDMCjQPMPTdfXz2sW7tNnCgApBASrtIM-UKYKLpvQELzrs7z\",\n" +
                "    \"msg_list\": [\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvpFjzVS6ZwipSlotQ9vXHKf\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728872794,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"DSgvjIQYjtH3sg43QlD72d/PiSFr9pvstAlnQae115D+dQJPYMZA2aJzwGr0hrz4bXIOMNk5GpoLViZAQt6ZcAdnvWrKukk0tK1mQMfcukpW2OBXoCXQu60Cru784yB1RqkALc23gXd+pbdQU0s1aD0vjSqnLqjE5s/r0loJAy5Mj6KedcEyDKHYnPfXOv7XPTJm7tbpvZz44usKrZbR9yruuuBQz72I57yRLQUNPHHxa/0Y4Vo+geelpD7Wtett/b1aLqfozolM5BBhQYkoRTarpmIRvjwHkD2F2pMeJmI9McwLtn52Ygrc4y77rrZAOkc18IvcHXHBRP72LpkE/Q==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7Ivqgyi3pIefYyHxhkmZ_VB4z\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728872890,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"ncHj9XVbziqh0K4ResFLaFG+AMqTli5dw3t5HCtuHOsG769wBe4cFkhg8hZ//klJxi/6D8ndjmrYHzUvUv7RxrtfvPKabU51t+EDuFYAD32uutW9+VmijBuChCFv+HiKpFwUhWxgTV4j8xrWPXsTW5YUprxZY3X87pi0gEaxFunEwbC5upF0HqXLg5aGgKPC3jabthnTv2jfjaGLinBSTGVcou/tHrLotqNyrZvH4HRQY1bacoTuMAuKZWX5wAh4yqPGG9B//y9o5AZFvLPuwUi/dQXQ1RIR6GI7ziz1RcY88G8UJXl3m6a8w57ypwYDUoB2dc2wJG1k8OqIpaS61w==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvogIVQ1QhrDgmupjz3XvuUG\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728887302,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"OS08LoIPKoEFvClDw+AN2s8hxiRwmevGqxtVmlHvoYCB4cztrF9L6luITvYu5O4qVyNuNm/NHGiwwA5aaz6EQixaGZPYdCXozbi0xCDwDbrjLclwCJ2TH0/dtnCZe1Y6tP3u3+V9XIvd6Bde8LGrSKyaDosaplfV6NzpGAD+7K1/rBas6PRYexU8kIh0DhnpC8Sd74ilJ6N+W01+auPmgBRHW+9Nu/XU/dg184RqbURwvQlWw1mnZ2kr70wCNXN8AXMFm6QluA3IoKYdD7CsnmWbkxr8CQBkstdnJOBZX8uQqyZNZLdsren8+G4AEVVv7h6L/v2E5BdmGDIniJijWw==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvpqTtV2yue7gIom_2hrdvZA\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728887308,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"nPdcQsBkg2lSfmbUL3PBY7qTRUg2aHgD4UuYF6RjqFrz6ykV8KIbGmxLbiP5lKWWXCPzsZyHI1RoAoBYn4OvcW4h/75XhYA/6wUvea/Xzvn+B/cuz2pfyd/ipJ2Qa1aSVt2bmSwFow1JhOTXWIXXS07FJ6w+9KCNQFCu3s3nsndVWoaOjD3lZgtwTnZiE1f0e7pNJe+WX3QO9UHlQ0e4A96C+v6CAfKXPmRWBR9iKWAT9EmASHt5DQTojunAxkGt4Kc7T5TbZTcqv/pO+7DuywxpSM4dYmg+DRxZWutqwjQrvmszMP5AhO6UjErS2eiWe8+q3zTCz6OuCA/tiy3SJw==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7Ivrh4O1iekGC8ECwNt-M4Eqp\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728887322,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"GKnmZUkOI6EuNFb8yV9K7Lj/TNaa55V448w3l7O07aavfrHwV0VA6ConVlT0fqf21mHKQFevFg8u6F4KB+vsWtHsj/Exda/daMFLmgCABnbyh1LNMJ19423JviscNPV8C/lBGqW20K8jB72O/XszuL3omeDkz0l+5FQM4Zay80yoIkYV2HOPkgb8nC20161HUg5uQcJ5F6OLGJwUx8/pezFmRJ7hGZrCedZdPjemCz/KAeTwbuCXKY/Ks5yAj8i6MS/8Lf8oZGYPnr+MsDah2bAAaY3jcI6tdoTNEt7jgxBcwLVkDAojSioGg+UsYMitCUb41ZqDiCZ/WXibk8y/PA==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvrBFkLZlt0ypbJXi-Me1sr5\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888234,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"UOeBweFMzwZlsSoF8GNC2FpsazEnPFOcPxwovdJJPuSA97GEmd9y7HkLQ4BXXOnn2/7C/thtjtgkGu03d+FWp3vMfb8bBQEs7NHpqxb/PbgJEUOLWioHx2Dm3aFF8k1rYh8YPC/pJyLyQAMguGcMh8oksm4sL9NiL55FrcDgpf8rzONzlbO6J9CvA/YXnqnkwLLzjlp8cqRqlqsqEZiLSxrVNczykqW3SEdMhZSlGaQY8YwzYbGtDH1osLvBi05/TbjQCFEVMjcO+hzkft7Ut/TK69WmHitOSLwrfu9Nh3iCDbDRqupbV1SsZIBjf1kjcqMQjPlQgaz1AaB1cTeLrQ==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvoFO2fYMbGMIWJdXZTDjH0S\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888236,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"f7Fu+kocb5C2EtaKG0q8fgi4xMtkAO0GYfK4OOYmN0MIw1x3h6IsRbYkureiu4XlFtU6jGtnhCt56rQffodlE8uQ/LYnL3x+4nH7H4jpJ33rMu/RTNfpNYyJXPDkn8U53oa5o35n+aUgueRaQm7EYkVT1XncwLLv2l67SH1Tp+1gt/0uko6l6k7gUSCn0M7YOskonscKBq1UG155S5oFSSLX0ciRpeWbbKZDAO1wugCN3xiMyd12kjdlbgCkdvAPMNXfX12YO3jSDVsQ5IGU8ZrrKexsfCmIn2NRmlEXf5zt3GA014tOs3QwVBg6/AuuNbPiY+joDRu7G/fznvugag==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7Ivq_8HI32-bQVZPLNLTvURDP\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888238,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"h9Qs7EQxJmVNJy2DcxrM6I3qoPrjPRJvkXcHN8tYI9x62HHv8moZENZKNk7CUxH3WjHuq+LT9fjsfEaNQWEEOzIH/MZbB+sinNwOVEzaXIyyu21YTyNicgbzGkMDwGa4EuitIaCHFdbbJfSWev1ySyRHVjcJSyJwTmXnECfUqbl7FQdM2UvNYzCTO/JkdlwySMxm8pLcwiQv4VypZ/mjAaUW8SpoTZkFHSJpLvaem268A4/vbQhHnRPAwXObvkCG8D+0h7VWZfKPDpnRiMjaSINSY6ItyOBNau0uDcqWsswpIE/cU8/lYHYtBAO1pmF3gIEBK7VlXJ9WVIpc2jNF0g==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvqNI-U46P5t1Ud4gwkLQ9fr\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888240,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"JFP4f4FIB6GMya7fjtdNI6xwze+/F0aVpX3OyLlcajIuF3ospaOkNa8cSFm2C+WQ/5OgHAuC3k+/YPV619WuPtNvyz9KZD5nhY00+NAMJuPMHE3VMUaKHfhrY+gRv2om8svDYTRLXlvLDhRlWOuVvVhJeozJXGGO/cuvDTWzxg7z2vMaCMSS12pbF2sP3UJqrsE3pWRz4LQG70QMxzbu4ylN1QshMVtOZV+aHgYDF+GCdYvIpYH/+fk5iVmyyy25zn4dsHE8UBh8E6e82jLYlPFB218diPtvAJmqcHnF+jJDdBoy+TyxBvuwfHhAOsi3qu/PKwHO5V7a32Spg2z2cg==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvoSqbMVCxgTFJZ-Ocz5vTEC\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888242,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"CZSWyGjO4wLb20C7v+uI0VoFlrgd3ENPalUoND6gBtbtB7nQKa7WNfVeYF+T1pPtisYCFnU/M+j0V4xDDirSqMgQJxevLc/11jSK5aSQ4ZCfj4c3cxOd1IxOyJqEaXpVhrDbqX8xjtVuFuV3I+UCgXICJvQWqNkK8F8G4v8Feot0h2EO7lz/paaK3aFKaWQ/RWJUG+0OgWTxgkJ0uYh9XBINZ7JbvNn/OIAFV+Tjrylp6nMPiYWJQv2IwxkB5g/HVDOJkv263LZp/SyLmqd/Imuls/Z37DVWwEPppK1RGd/goApJteGVE/OX3rvlzSb2IGeHKFUIS2dAS3MgOiH29g==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvoTvXJWjKyUJavwM7LeC2Wp\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888244,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"uzjnCQBMhMP0oIRaxn82svNxu5IvJSphahAR6tVpX8fLvEBXIdYF/KteiQw0esye4/4oTALYNRu9657ZBg78MBp6TjDlwApRuu1I6RH3vpsOT4ugUgRO92BJ8ttnBQQhh8ktBDRKutrAzuQGnlqFcwUyCw/HfG15i/dYhkw92W7tifdfQt9bSRPpXQ5uSGMSxhVSK7Qw4AkaDfz5Mbgb+rLNTPIkyM9VGWr61ik92lGc8mSt7tUFceqoEibgGlaV3C/QsQnrfc6QtljpT0QUbzcxz7VxCNYQ8ZdJj0YK5O64tRvj2bHQmPEMkwYnvPmuriXUKMCB8wGgnuE2PYRi4Q==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvoAco_p19UnuPgshPvli-h9\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888245,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"jPkDLz4w+wmhzpUHhC3uOd3UPK6m3W+Ac0OyCsj1OteaFAIEbGz6AwyyjuuXMfP6jAUXCn9mF/9Wp/BAK+2T2nPEXeIu9kqZXWTTZ2RfmGNFtwqUBnnENlCrbwTrQ8LGhnU7yhdpIMwcoUMX3Bz2Mhbqz478IRdFhmGaEWQ/6tCakhmSZEQ++HHOqWhkKUeK718diaM+BqbmaxGkshQF1NYms9EIKAKXlwIvs411+SP9+YOR0dOeT9t3KqlSrHuSo8Jj0uBSG3VrfQH/n40dPLKfAnzgZrJBumeVdJJqssMn3pTGEFzimkILwcLB2VQ1GB0X+b8MZdNgk/u31gSzKA==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7Ivrk7xZsHZhbJpJjz8VEdAHX\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888247,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"lcvbufh6Eu4bSTAELrqW2N/qe1Jq2YACPLKXofA266wCBLt5d2q4jO6CiwlmCD7vQAO4sSKPISdTB4uJBGsNjJNvnxZd0ij34iaQ3OgJj1p2Y67gpuMn9C6ETxYY6HC7j3tkzmucFf4sUQttc2EDKTM6QbhnAIOTjdIApSK/tf4cloWF1ufim+ypBNtpSC/LFKiMkmjmXAdHhkYEsBUpXRDlQpeIHGMKz3C4B4WVX+5zs8DJZYSurQ6x0bVp3UB1+EbnKln4eOj++ibicybz7bhMKnzd8N3UmGTjBgxFR6OlbTBVn0nQkUrzNcq0/CnpDmAXIgqDmQmMUuM8HZxOHQ==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"msgid\": \"mALDy8hnfdnXHzPaTzti7IvrPYeTzt86dCs3xRzgLJLLU\",\n" +
                "            \"sender\": {\n" +
                "                \"type\": 1,\n" +
                "                \"id\": \"wowx1mDAAAOP-BculhBRdusb_E007fkQ\"\n" +
                "            },\n" +
                "            \"send_time\": 1728888248,\n" +
                "            \"msgtype\": 1,\n" +
                "            \"service_encrypt_info\": {\n" +
                "                \"encrypted_secret_key\": \"j3gqQlZ0hPQTe/8bX9ZX6f+1FszHAFG75Bwj3BQ3udABBG/4HL8eK7vRhOhJJ0El4tQxrI6GyJJNGiTdFEw/58r4GyfTyjOXD8eiYiIcqfCVFEeeU8LIUHoVSQHHYrr1tWUXA31/Lwql5Q1qs1EDCxxbAsjdWgaAic4mcQL3mJ9EFiZLZj6aQNFQC42qpHsa7aMf5sAp46v81LzX8Wx6aGIRgq05wVweUGGaIccD8kQh+5wfDNdCmcn3cGqUzlVdeQXs88dUR5eQqxygHnfJHH2YRWqsFI4KnAfKM4AlLSPq+i7Ewe/NWg3aB1MBilHUrcqN44ilxkhyhKBCl2PYDw==\",\n" +
                "                \"public_key_ver\": 4\n" +
                "            },\n" +
                "            \"receiver_list\": [\n" +
                "                {\n" +
                "                    \"type\": 2,\n" +
                "                    \"id\": \"wmwx1mDAAAq83MKayMlH5Iw-XlOfSMaA\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        JSONObject jsonObject=JSONObject.parseObject(json);
        List<Map> msgList = JSONArray.parseArray(jsonObject.get("msg_list").toString(), Map.class);
        Map<String,String> messageData= Maps.newHashMap();
        com.facishare.open.qywx.save.result.Result<List<GenerateSettingVo>> listResult = settingService.queryByEaSetting("84883");
        Map<Integer, String> versionMap = listResult.getData().stream().collect(Collectors.toMap(GenerateSettingVo::getVersion, GenerateSettingVo::getPrivateKey, (key1, key2) -> key2));

        for (Map data : msgList) {
            System.out.println(data);
            String msgid = (String) data.get("msgid");
            String publicKeyVer = (String) data.get("public_key_ver");
            Map publicDataMap = (Map) data.get("service_encrypt_info");
            String encryptedKey= (String) publicDataMap.get("encrypted_secret_key");
            Integer publicKey=(Integer) publicDataMap.get("public_key_ver");
            String privateKey = versionMap.get(publicKey);
            String dataPrviate = RSAUtil.getPrivateKey(privateKey, encryptedKey);
            messageData.put(msgid,dataPrviate);
        }
        System.out.println(JSONObject.toJSONString(messageData));
    }


    @Test
    public void initMessage() throws UnsupportedEncodingException {

        long sdk = Finance.NewSdk();
        Finance.Init(sdk, "wwbc77ff71db9569db", "ZWVYRYM11uaiG7xDtw0VUkO1aSs6_AvpoxKulwCRU5w");
        long slice = Finance.NewSlice();
        int ret = Finance.GetChatData(sdk, 79, 100, "", "", 600, slice);
        if (ret != 0) {
            return;
        } else {
            String content = Finance.GetContentFromSlice(slice);
            JSONObject jsonObject = JSONObject.parseObject(content);
            ChatDatas cdata = JSON.toJavaObject(jsonObject, ChatDatas.class);
            List<Qychat> list = cdata.getChatdata();
            for (Qychat ChatData : list) {
                System.out.println("msgid             :" + ChatData.getMsgid());
                System.out.println("Encrypt_random_key:" + ChatData.getEncrypt_random_key());
                String msgs = ChatData.getEncrypt_chat_msg();
                System.out.println("Encrypt_chat_msg  :" + msgs);
                String encrypt_key = null;
                try {
                    encrypt_key = RSAUtil.getPrivateKey(privKeyPEM,ChatData.getEncrypt_random_key());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                System.out.println("encrypt_key  :" + encrypt_key);
                long msg = Finance.NewSlice();
                System.out.println("result " + Finance.DecryptData(sdk, encrypt_key, msgs, msg));
                String datas = Finance.GetContentFromSlice(msg);
                log.info("{}",datas);
            }

        }

    }

    @Test
    public void initMediaData() {
//        long sdk = Finance.NewSdk();
//        Finance.Init(sdk, "wxbbe44d073ff6c715", "ELTTyb30Ny1AF3jUKaM6RNqQIQyCGAuEJSbsMelqp7M");
//        long ret = 0;
//        String sdkFileid = "CtQBMzA2ODAyMDEwMjA0NjEzMDVmMDIwMTAwMDIwNDIxZTYzYjY0MDIwMzBmNGRmYTAyMDQyMjcwZmIzYTAyMDQ2MDViZjE2ZTA0MjQ2NDYxMzM2MTM1NjM2MjM2MmQ2NDYxMzczMjJkMzQzMjMzMzYyZDYxNjYzNTM3MmQ2NTMzMzY2MjM0Mzg2MTMyNjQzNzYzMzQwMjAxMDAwMjAyMTFiMDA0MTBkZTRhMmMwYjU2NDliYzhiODZmOTk3ZDQ2Mjc0ZmM3MjAyMDEwMTAyMDEwMDA0MDASOE5EZGZNVFk0T0RnMU1EUXlPVEF3TURVME9GODNORE0yTmpjeU16QmZNVFl4TmpZek9ETXhPQT09GiAzNTYzMzUzNzM0MzczMDM0NjMzMzMxNjY2MzYxNjUzMg==";
//        String indexbuf = "";
//        //sdkFileid 是我们从第一步拉取下来的解密消息 然后通过第三步解密后 的消息内容中 获取到的值（text消息没有   只有文件  图片 语音 视频等消息才有此字段）
//        List<Byte> list = Lists.newArrayList();
//        while (true) {
//            long media_data = Finance.NewMediaData();
//            System.out.println(media_data + "--" + sdk);
//            ret = Finance.GetMediaData(sdk, indexbuf, sdkFileid, null, null, 3, media_data);
//            System.out.println("getmediadata ret:" + ret);
//            if (ret != 0) {
//                return;
//            }
//            System.out.printf("getmediadata outindex len:%d, data_len:%d, is_finis:%d\n",
//                    Finance.GetIndexLen(media_data), Finance.GetDataLen(media_data),
//                    Finance.IsMediaDataFinish(media_data));
//            try {
//                byte[] data = Finance.GetData(media_data);
//                list.addAll(Bytes.asList(data));
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            if (Finance.IsMediaDataFinish(media_data) == 1) {
//                // need free media_data
//                Finance.FreeMediaData(media_data);
//                break;
//            } else {
//                indexbuf = Finance.GetOutIndexBuf(media_data);
//                // need free media_data
//                Finance.FreeMediaData(media_data);
//            }
//        }
//        byte[] datas = Bytes.toArray(list);
//        InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(datas, 0, datas.length));
//        try {
//            UploadFileArg uploadFileArg=new UploadFileArg();
//            uploadFileArg.setFileName("111111212eee");
//            uploadFileArg.setMessageType("image");
//            uploadFileArg.setMessageId("1111111");
//            uploadFileArg.setEa("zhanghui0916");
//            UploadFileResult result = fileManager.uploadFile(inputStream, uploadFileArg);
//
//            byte[] bytes = fileManager.downloadNFile("zhanghui0916", result.getNpath());
//            FileOutputStream outputStream = new FileOutputStream(new File("D:\\CloudMusic\\aaqa.jpg"), true);
//            outputStream.write(bytes);
//            outputStream.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        //N_202107_27_7d607602f8a74eb48e8261da2853f2f0
        byte[] bytes = fileManager.downloadNFile("81961", "N_202107_27_7d607602f8a74eb48e8261da2853f2f0");
        log.info("bytes");

    }

    @Test
    public void TestFile() {
        long sdk = Finance.NewSdk();
        Finance.Init(sdk, "wxbbe44d073ff6c715", "ELTTyb30Ny1AF3jUKaM6RNqQIQyCGAuEJSbsMelqp7M");
        long ret = 0;
        String sdkFileid = "CpwCMzA4MThiMDIwMTAyMDQ4MTgzMzA4MTgwMDIwMTAwMDIwNDZiYjA3YzYxMDIwMzBmNGRmOTAyMDQ3M2U2NjA3MTAyMDQ2MDU0NTA3YTA0NDQ0ZTQ1NTc0OTQ0MzE1ZjM2NjI2MjMwMzc2MzM2MzEzNzMzNjUzNjM2MzAzNzMxMzYzMDM1MzQzNTMwNjYzMTVmMzU2MTM4MzM2NjM1NjMzOTJkNjQzNjYzMzQyZDM0MzM2MjYxMmQzODYzNjYzNzJkMzEzNDY0MzAzMjM5NjQzNzMwMzAzMDYyMDIwMTAwMDIwMzA4ZjBiMDA0MTAwZTA1ZjczMjNkOTI5NThkYjMxNjdjMDM5YTgyZWMxZTAyMDEwMjAyMDEwMDA0MDASOE5EZGZNVFk0T0RnMU1UWTJOams1TWpJeU5WOHhNemMwTURFeE1UVXdYekUyTVRZeE16ZzBPVFU9GiAyYTJjZjc5YjJmZDk0NThlYWQ4ZjE3NjY0ODdiMjJlMQ==";
        String indexbuf = "";
        //sdkFileid 是我们从第一步拉取下来的解密消息 然后通过第三步解密后 的消息内容中 获取到的值（text消息没有   只有文件  图片 语音 视频等消息才有此字段）


        while (true) {
            long media_data = Finance.NewMediaData();
            System.out.println(media_data + "--" + sdk);
            ret = Finance.GetMediaData(sdk, indexbuf, sdkFileid, null, null, 3, media_data);
            System.out.println("getmediadata ret:" + ret);
            if (ret != 0) {
                return;
            }
            System.out.printf("getmediadata outindex len:%d, data_len:%d, is_finis:%d\n",
                    Finance.GetIndexLen(media_data), Finance.GetDataLen(media_data),
                    Finance.IsMediaDataFinish(media_data));
            try {
                String fileName = "a.jpg";//文件名根据自己需要下载的文件类型自定义
                FileOutputStream outputStream = new FileOutputStream(new File("D:\\CloudMusic\\" + fileName), true);
                outputStream.write(Finance.GetData(media_data));
                outputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (Finance.IsMediaDataFinish(media_data) == 1) {
                // need free media_data
                Finance.FreeMediaData(media_data);
                break;
            } else {
                indexbuf = Finance.GetOutIndexBuf(media_data);
                // need free media_data
                Finance.FreeMediaData(media_data);
            }
        }


    }


    private String getFileName(JSONObject jsonObject, String fileType) {
        String fileName = null;
        String md5sum = jsonObject.getString("md5sum");
        switch (fileType) {
            case "image":
                fileName = md5sum + ".jpg";
                break;
            case "voice":
                fileName = md5sum + ".mp3";
                break;
            case "video":
                fileName = md5sum + ".mp4";
                break;
            case "file":
                fileName = jsonObject.getString("filename");
                break;
            default:
                fileName = "default.jpg";
                break;
        }
        return fileName;
    }


    @Test
    public void testMessage() {

        String privateKey = "MIIEowIBAAKCAQEAz/Hl7Ay0SE+zzdGMctJcQotAkCH2tT5/C4ISI4mR3N2KicE+\n" +
                "EdJCICxS6DrDVs6YOyvKR9XX2c31vy/qGM+rZxMjeIzHdQvFaY1cRPtVyLpAax++\n" +
                "3HnczJsFHV77evrK0GkaBNUtL5+05NTdBRcj66HY2aO/+4ttAk59hSS/8qtOGqm6\n" +
                "I5yfpQsKXE++8daQMTVUHMOA7aTmzCnn+FT9enXkjkcI/bCpgpEtBRh8plupIxXM\n" +
                "ui6PdT7RfGt5h0mWVHFRvlHC+N0bpYoNqzj43Y8AZSRBK8EP4TFP4Rlk7hAuFV4a\n" +
                "Ts3if5nv/63iMJ3YqG+y+PyEpZlnzfNCZE76SwIDAQABAoIBAHAzk940VKqX5urt\n" +
                "YJ0sCIAXZzTePqI5II/zFRp7xmqoV3JRBM7U5r05bVrFKlWSj+2NiU4NgrSRP0Jz\n" +
                "9hqBI3kwiHkpbQ4o1dJIZjsKapUuekfTD0cjshHsq2vXrlYDMKAXteRZqlICGLdI\n" +
                "bCGtBMLFx55XjuWJq74M4AmRdMjY0QSdnAgz+dcMtj3FxIn8VMfipV33FFobmRQC\n" +
                "YN6ItMW0uC9HXXNWQSwhaghmhSnm1EnxWFbny3mKY3qJnhasjUR6EJaFUSqN5I9Z\n" +
                "Nbz0VFFgjAxbtvfH7fqW9urEht1B61YGsK5o/+5BrTAY2+Vg0vgovs5hVRi3pCcM\n" +
                "APCg8IECgYEA5s6xnxDDDLS6bN0EoD0jfEfB1Nm8YNELtoQvpg9CbbYBnsgk0TFy\n" +
                "tQnKGkrfkkAP7AfVZIaflNyvqY959BRGt/AGVvaGuuLyufiEsYXsLW9Vd6mOmIYy\n" +
                "QEmoPUCQkn7HIezEvbxp3TTW7+fW6REQImkiUqrwx/fgR9axPzlj1aECgYEA5qRf\n" +
                "TlvlPlxLP6vFtbjDA2726Yvhy/d5nb5uxtHDwiaUsQENHfZoDkF6kN5FROsJp+7N\n" +
                "5Fr/cpYzPK51m2Wkgrvf8j+ImuMlKFD3XZPyVhT04TTNkP3xzeicKM+B/ZyzISc8\n" +
                "5Q5CYkfILF75TW6EDVDWkgvCxH8u0R+qx/z2sGsCgYAlQrECEN6sKnD+KiAZDkWw\n" +
                "RpVQG2aB6r2NVYGruULsGznfvEfVTbpK562s2PGG1ri7TfhxJhqVGZtyMCtr7+oK\n" +
                "v8EGQP43JXQx+aDSV+Bs5VBS5RiUHvX10u5KFSZBwB29qE+KoeQlReZ9DFxxe8Oz\n" +
                "Cm30EoyUe7vFXS94GXe4gQKBgQCjnh/uWsq5/odzV8weKkBOAz4uWAmKxLkF6r5z\n" +
                "VQPmi7AYEYLYqqEO2+yzMLs7NPHYrFRrlxJ4m40lky3jW6vlAprQI7opBtKpUybo\n" +
                "v7e+0YcW7HqYTU5ooIeHfA3feHarIkbUx9TYG8wpjgaVo70SJTLS0H0PIJp5yFlD\n" +
                "HnVymQKBgHyGRiQWCRiyrbamHcsWQlM0wj7+iIfyZxRLsMvr2BJ5C+1emn7uwSLB\n" +
                "E0tadmGdhvzHezn8xSfO9RWb4TWKqGvbwtVqAVBkKJV+EgtbQro+WWbTAP3eCb7l\n" +
                "ua0jXMGuJ2NwicusVr2YmkOId5QF8siEpEz/AERY0WviFi+qrINH";

        String randomKey = "m1dvasBvTRjKR/P9d5Xh68u4WJkT64YAVPQ3XAE8XUiM8CGG48fqqaABapAxuOVDIVmosndn0ssBamMhIhc7H7i7mkgH3/WTgpI5wgcpefSzwyTSp3sdbVOBJwlsJgOJ6JfvJH5qdof6F4eU9bESl4Xrl7tLXlKTcffTO9jzV/IfBim3iHhffIjU32z4yvC38Xz40W+4kgu467NdbxQegDIDFxVTThTkECQwRgwAuO6q1uIffZX7xJqMB6MWs4AOGXcwG7GlSmGjsgFDDnj8uWGoJTDTMfQzWqmJgH11psJ1EB1pWEiwD4TiHo/IY67t52GPvLzdfSx1AF94Lh8Shg==";
        String bytes = RSAUtil.decodeBase64(randomKey).toString();
        String content = "";
        try {
            content = RSAUtil.decryptByPriKey(bytes, privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(content);

    }


    private String buildContent(StoneFileUploadResponse result, String fileName, String messageType) {
        switch (messageType) {
            case MessageType.IMAGE:
                return createImage(result, fileName).toJson();
            case MessageType.DOCUMENT:
                return createDocument(result, fileName).toJson();
            default:
                return "WebImConstant.UNSUPPORTED_MSG_TYPE";
        }
    }

    private Image createImage(StoneFileUploadResponse response, String fileName) {
        Image image = new Image();
        image.setOriginalName(fileName);
        image.setFileSize(response.getSize());

        StoneFileImageProcessResponse imageProcessResponse = response.getImageProcessResponse();
        if (imageProcessResponse == null) {
            //获取图片列表异常

            return image;
        }
        List<StoneFileImageThumbnailResponse> thumbnailList = imageProcessResponse.getThumbnailList();

        //正常图片
        StoneFileImageThumbnailResponse normalImage = thumbnailList.get(2);
        image.setImage(buildNpath(normalImage.getPath(), normalImage.getExtensionName()));
        image.setImageW(normalImage.getWidth());
        image.setImageH(normalImage.getHeight());
        image.setImageSize(normalImage.getSize());
        //高清原图
        image.setHdImage(buildNpath(response.getPath(), response.getExtensionName()));
        image.setHdSize(response.getSize());
        //缩略图
        StoneFileImageThumbnailResponse smallThumbnail = thumbnailList.get(0);
        image.setThumbnail(buildNpath(smallThumbnail.getPath(), smallThumbnail.getExtensionName()));
        image.setThumbH(smallThumbnail.getHeight());
        image.setThumbW(smallThumbnail.getWidth());
        //中缩略图
        StoneFileImageThumbnailResponse middleThumbnail = thumbnailList.get(1);
        image.setHdThumbnail(buildNpath(middleThumbnail.getPath(), middleThumbnail.getExtensionName()));
        //小缩略图
        StoneFileImageThumbnailResponse tinyThumbnail = thumbnailList.get(3);
        image.setSmallThumbnail(buildNpath(tinyThumbnail.getPath(), tinyThumbnail.getExtensionName()));
        imageCheck(image);
        return image;
    }

    private Document createDocument(StoneFileUploadResponse result, String fileName) {
        Document document = new Document();
        document.setSize(result.getSize().intValue());
        document.setName(fileName);
        document.setFile(buildNpath(result.getPath(), result.getExtensionName()));
        return document;
    }

    private String buildNpath(String npath, String ext) {
        if (StringUtils.isNotBlank(ext)) {
            return npath + "." + ext;
        } else {
            return npath;
        }
    }

    private static void imageCheck(Image image) {
        if (image.getThumbW() <= 0 || image.getThumbH() <= 0) {
            throw new IllegalImageException("illegal image:" + image);
        }
    }


}
