# 企业微信网关项目 - HTTP代理转发机制说明

## 项目概述

这是一个企业微信网关项目，主要功能是作为HTTP代理，将客户端的请求转发到不同的后端服务。项目使用Spring Boot框架，通过拦截器模式实现请求的智能路由和转发。

## 核心功能

### 1. HTTP代理转发机制

项目的核心是 `HttpProxyInterceptor` 类，它是一个Spring MVC拦截器，负责：

- **请求拦截**：拦截所有进入的HTTP请求
- **智能路由**：根据请求参数、Cookie、请求体等信息判断应该转发到哪个后端服务
- **请求转发**：将原始请求转发到目标服务器
- **响应返回**：将后端服务的响应直接返回给客户端

### 2. 转发流程详解

#### 第一步：请求拦截
```java
@Override
public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
```

#### 第二步：构建转发请求
1. **提取请求信息**：
   - URI路径：`request.getRequestURI()`
   - 查询参数：`request.getQueryString()`
   - HTTP方法：`request.getMethod()`
   - 请求头：遍历所有header（排除host和Accept-Encoding）

2. **构建目标URL**：
   ```java
   String url = qywxIpUrl + uri;
   if(StringUtils.isNotEmpty(param)) {
       url = url + "?" + param;
   }
   ```

#### 第三步：判断路由策略
通过 `isNewBase()` 方法判断请求应该转发到哪个环境：

1. **从URL参数获取企业标识**：
   - `ea`、`fsEa`、`fsEnterpriseAccount`（内部企业账号）
   - `outEa`、`corpId`（外部企业账号）

2. **从Cookie获取用户信息**：
   - 解析 `FSAuthXC` Cookie获取用户的企业账号

3. **从请求体获取参数**：
   - 对于POST/PUT请求，解析JSON请求体提取企业标识

#### 第四步：执行转发
如果判断需要转发到新环境：

```java
// 设置请求体
arg.setBody(getParameters(request));

// 执行HTTP转发
String result = httpProxyUtil.httpProxyRequest(arg);

// 直接将结果写入响应流
response.setCharacterEncoding("UTF-8");
ServletOutputStream outputStream = response.getOutputStream();
outputStream.write(result.getBytes(StandardCharsets.UTF_8));
outputStream.flush();

return false; // 阻止继续执行后续处理器
```

### 3. 关键组件说明

#### HttpArg - 请求参数封装类
```java
public class HttpArg {
    private String method;    // HTTP方法
    private String url;       // 目标URL
    private Map<String,String> header;  // 请求头
    private String body;      // 请求体
}
```

#### HttpResponse - 完整响应封装类
```java
public class HttpResponse {
    private int statusCode;           // HTTP状态码
    private String statusMessage;     // 状态消息
    private Map<String,String> headers; // 响应头
    private String body;              // 响应体
    private String contentType;       // Content-Type
    private String charset;           // 字符编码
}
```

#### HttpProxyUtil - 增强的HTTP客户端工具类
现在支持两种转发模式：
- **简单转发**：`httpProxyRequest(HttpArg)` - 只返回响应体字符串（保持向后兼容）
- **完整转发**：`httpProxyRequestWithHeaders(HttpArg)` - 返回包含状态码、响应头的完整响应

### 4. 完整的HTTP响应转发机制

项目现在支持完整的HTTP响应转发，包括状态码、响应头和响应体：

<augment_code_snippet path="qywx-event-handler-web/src/main/java/com/facishare/open/qywx/eventhandler/aop/HttpProxyInterceptor.java" mode="EXCERPT">
````java
// 使用新的完整响应转发方法
HttpResponse httpResponse = httpProxyUtil.httpProxyRequestWithHeaders(arg);

// 设置响应状态码
response.setStatus(httpResponse.getStatusCode());

// 转发响应头（排除一些不应该转发的头）
if (httpResponse.getHeaders() != null) {
    for (Map.Entry<String, String> headerEntry : httpResponse.getHeaders().entrySet()) {
        String headerName = headerEntry.getKey();
        String headerValue = headerEntry.getValue();

        // 排除一些不应该转发的响应头
        if (!shouldSkipResponseHeader(headerName)) {
            response.setHeader(headerName, headerValue);
        }
    }
}

// 设置Content-Type
if (httpResponse.getContentType() != null) {
    response.setContentType(httpResponse.getContentType());
}

// 写入响应体
if (httpResponse.getBody() != null) {
    ServletOutputStream outputStream = response.getOutputStream();
    outputStream.write(httpResponse.getBody().getBytes(StandardCharsets.UTF_8));
    outputStream.flush();
}
````
</augment_code_snippet>

**完整转发的工作原理**：
1. **获取完整响应**：使用`httpProxyRequestWithHeaders`方法获取包含状态码、响应头和响应体的完整响应
2. **转发状态码**：将后端服务的HTTP状态码设置到客户端响应中
3. **智能转发响应头**：转发大部分响应头，但排除代理相关的头部（如transfer-encoding、connection等）
4. **设置Content-Type**：正确设置响应的内容类型，支持各种格式（JSON、HTML、二进制等）
5. **写入响应体**：将后端服务的响应体完整地转发给客户端

### 5. 响应头过滤机制

为了确保代理转发的正确性，系统会过滤一些不应该转发的响应头：

```java
private boolean shouldSkipResponseHeader(String headerName) {
    String lowerHeaderName = headerName.toLowerCase();

    return lowerHeaderName.equals("transfer-encoding") ||  // 传输编码
           lowerHeaderName.equals("connection") ||         // 连接控制
           lowerHeaderName.equals("proxy-authenticate") || // 代理认证
           lowerHeaderName.equals("proxy-authorization") || // 代理授权
           lowerHeaderName.equals("te") ||                // 传输编码扩展
           lowerHeaderName.equals("trailers") ||          // 尾部字段
           lowerHeaderName.equals("upgrade") ||           // 协议升级
           lowerHeaderName.startsWith("x-forwarded-");    // 代理转发相关头
}
```

**过滤原因**：
- **transfer-encoding**：传输编码应该由代理服务器自己决定
- **connection**：连接控制信息不应该转发
- **proxy-***：代理相关的头部不应该转发给客户端
- **x-forwarded-***：代理转发相关的头部由代理服务器统一管理

### 6. 特殊处理机制

#### 请求体缓存
为了避免InputStream只能读取一次的问题，使用了缓存机制：
```java
// 优先从缓存中获取请求体
String cachedBody = (String) request.getAttribute("cachedRequestBody");
```

#### 数据解密
对于特定接口（如`/qyweixin/getFsEaList`），会对加密的企业账号进行解密处理。

#### 白名单机制
通过 `ConfigCenter.qywxNotSupportUrl` 配置不需要代理的URL，直接放行。

## 使用场景

这种代理转发机制主要用于：

1. **服务迁移**：在不影响客户端的情况下，将部分请求路由到新的服务器
2. **灰度发布**：根据企业标识将不同企业的请求路由到不同版本的服务
3. **负载均衡**：根据业务规则分发请求到不同的后端服务
4. **协议转换**：在客户端和后端服务之间进行协议或数据格式转换

## 技术特点

- **透明代理**：客户端无需感知后端服务的变化
- **智能路由**：支持多种路由策略（URL参数、Cookie、请求体）
- **高性能**：直接流式转发，减少内存占用
- **容错处理**：异常情况下自动降级到原有处理流程

## 新增功能亮点

### 1. 完整的HTTP响应转发
- **状态码转发**：正确转发HTTP状态码（200、404、500等）
- **响应头转发**：智能转发响应头，包括Content-Type、Date、Server等
- **内容类型支持**：支持JSON、HTML、二进制等各种内容类型

### 2. 智能响应头过滤
- **自动过滤**：自动过滤不应该转发的代理相关头部
- **安全保障**：避免转发敏感的连接控制信息
- **兼容性**：保持与各种客户端的兼容性

### 3. 增强的错误处理
- **异常捕获**：完善的异常处理机制
- **错误响应**：转发失败时返回有意义的错误信息
- **日志记录**：详细的日志记录便于问题排查

## 使用示例

### 原有的简单转发（保持兼容）
```java
String result = httpProxyUtil.httpProxyRequest(arg);
response.getOutputStream().write(result.getBytes(StandardCharsets.UTF_8));
```

### 新的完整转发
```java
HttpResponse httpResponse = httpProxyUtil.httpProxyRequestWithHeaders(arg);

// 转发状态码
response.setStatus(httpResponse.getStatusCode());

// 转发响应头
for (Map.Entry<String, String> header : httpResponse.getHeaders().entrySet()) {
    if (!shouldSkipResponseHeader(header.getKey())) {
        response.setHeader(header.getKey(), header.getValue());
    }
}

// 转发响应体
response.getOutputStream().write(httpResponse.getBody().getBytes(StandardCharsets.UTF_8));
```

## 注意事项

1. **请求体读取**：由于InputStream只能读取一次，需要使用ContentCachingRequestWrapper进行缓存
2. **字符编码**：统一使用UTF-8编码处理中文字符
3. **请求头过滤**：系统会自动过滤不应该转发的请求头（如Content-Length、Host等），避免协议冲突
4. **响应头过滤**：系统会自动过滤不应该转发的响应头，无需手动处理
5. **异常处理**：转发失败时系统会优雅降级，返回500错误响应
6. **性能监控**：建议添加转发耗时和成功率监控
7. **向后兼容**：新功能完全兼容原有的简单转发方式

## 问题修复记录

### 2024-12-19: 修复Content-Length头冲突问题

**问题描述**：
HTTP代理请求失败，报错：`org.apache.http.ProtocolException: Content-Length header already present`

**问题原因**：
1. 在`HttpProxyInterceptor`中，从原始请求中复制了所有请求头（包括Content-Length）
2. 在`HttpProxyUtil.httpProxyRequestWithHeaders`中，当创建`StringEntity`时，Apache HttpClient会自动计算并设置Content-Length头
3. 这导致了Content-Length头的冲突，引发协议异常

**解决方案**：
1. 在`HttpProxyInterceptor`中添加了`shouldSkipRequestHeader`方法，过滤不应该转发的请求头
2. 过滤的请求头包括：
   - `Content-Length`：由StringEntity自动计算
   - `Host`：由HTTP客户端设置
   - `Accept-Encoding`：避免压缩问题
   - 其他代理相关头部

**修复效果**：
- 解决了POST请求的Content-Length头冲突问题
- 提高了HTTP代理转发的稳定性和兼容性
- 保持了向后兼容性，不影响现有功能

**相关代码修改**：
- `HttpProxyInterceptor.java`：添加请求头过滤逻辑
- `README.md`：更新文档和注意事项
