package com.facishare.open.qywx.accountbind.result;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/24
 */
@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -1L;

    private T data;
    private String errorCode;
    private String errorMsg;

    public Result() {
        errorCode = "s120050000";
        errorMsg = "succ";
    }
    public Result(T t) {
        data = t;
        errorCode = "s120050000";
        errorMsg = "succ";
    }

    public Result(String code, String errorMsg, T data) {
        this.errorCode = code;
        this.errorMsg = errorMsg;
        this.data = data;
    }

    public boolean isSuccess() {
        return ("s120050000".equals(errorCode));
    }
    public Result<T> addError(String code) {
        return this.addErrorCode(code, null);
    }

    public Result<T> addErrorCode(String code , T data) {
        this.errorCode = code;
        this.errorMsg = "";
        this.data = data;
        return this;
    }

    public Result<T> addErrorMsg(String code, String errorMsg, T data) {
        this.errorCode = code;
        this.errorMsg = errorMsg;
        this.data = data;
        return this;
    }
}
