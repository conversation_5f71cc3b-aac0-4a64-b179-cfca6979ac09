package com.facishare.open.qywx.accountbind.service;

import com.facishare.open.qywx.accountbind.arg.BatchGetEmpBindArg;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinEmployeeAccountModel;
import com.facishare.open.qywx.accountbind.result.BatchEmpBindResult;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.result.Result;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/16.
 *
 * source: "qywx"是企业微信
 */
public interface QyweixinAccountBindService {
    /**
     * 绑定员工账号
     * 把一个员工在 纷享和在外部平台的 员工账号对应关系保存下来。
     *
     * @param arg    : 请参考 AccountEmployeeMapping的注释说明
     * @return :true-绑定关系保存成功，false-保存失败。
     *
     * 企业微信：（userid由系统生成时可更改一次）
     *
     * */
    Result<Boolean> bindAccountEmployeeMapping(List<QyweixinAccountEmployeeMapping> arg);

    /**
     * 绑定企业账号,
     * 把一个企业在 纷享和在外部平台的企业账号对应关系保存下来。
     *
     * @param arg    : 请参考 AccountEnterpriseMapping的注释说明
     * @return :true-绑定关系保存成功，false-保存失败。
     * */
    Result<Boolean> bindAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping arg);

    /**
     * 绑定企业下的部门,
     *
     * @param arg    : 请参考 AccountDepartmentMapping的注释说明
     * @return :true-绑定关系保存成功，false-保存失败。
     **/
    Result<Boolean> bindAccountDepartmentMapping(List<QyweixinAccountDepartmentMapping> arg);

    /**
     * 员工外部账号换内部账号。默认CRM appId
     * @param source: "qywx"是企业微信
     * @param fsEnterpriseAccount: 纷享企业账号
     * @return :   <外部账号, 纷享账号>。 企业微信的外部账号是userId，纷享账号格式E.xx.yyy
     * 如果不存在绑定关系，则返回 <外部账号, null>
     *
     * */
    @Deprecated
    Result<Map<String, String>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount, List<String> outAccountList);

    Result<List<QyweixinAccountEmployeeMapping>>  outAccountToFsAccount(String source, String fsEnterpriseAccount, String appId, String outAccount);

    Result<List<QyweixinAccountEmployeeMapping>>  employeeToOutAccount(String source, String outEa, String appId, List<String> outAccounts);

    /**
     * outAccountList为空会查询所有员工
     * 所有状态员工都会查询出来
     *
     * @return Map<outAccount, fsAccount>
     */
    Result<Map<String, String>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount,
                                                           String appId, List<String> outAccountList);

    /**
     * 0-正常 1-停用 -1：全部
     */
    Result<List<QyweixinAccountEmployeeMapping>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount,
                                                           String appId,List<String> outAccountList, int status);
    /**
     * 员工内部账号换外部账号。默认CRM appId
     * @param source: "qywx"是企业微信
     * @return : <纷享账号，外部账号>. 纷享账号格式E.xx.yyy, 企业微信的外部账号是userId
     * */
    @Deprecated
    Result<Map<String, String>> fsAccountToOutAccountBatch(String source, List<String> fsAccountList);

    @Deprecated
    Result<Map<String, String>> fsAccountToOutAccountBatch(String source, String appId, List<String> fsAccountList);

    Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountBatch2(String source, String appId, List<String> fsAccountList, String outEa);

    @Deprecated
    Result<Map<String, String>> fsAccountToOutAccount(String source, List<String> fsAccountList);

    Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccount2(String source, List<String> fsAccountList, String outEa);

    @Deprecated
    Result<Map<String, String>> fsAccountToOutAccount(String source, List<String> fsAccountList, Integer status);
    Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccount2(String source, List<String> fsAccountList, Integer status, String outEa);

    @Deprecated
    Result<Map<String, String>> fsAccountToOutAccountBatchByIsv(String source, String appId, List<String> fsAccountList);

    Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountBatchByIsv2(String source, String appId, List<String> fsAccountList, String outEa);

    @Deprecated
    Result<Map<String, String>> fsAccountToOutAccountByIsv(String source, List<String> fsAccountList);

    Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountByIsv2(String source, List<String> fsAccountList, String outEa);

    @Deprecated
    Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping(String fsEa,String fsUserId);

    Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping2(String fsEa,String fsUserId, String outEa);

    Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping3(String fsEa,String fsUserId, String outEa, String outUserId);

    Result<QyweixinAccountDepartmentMapping> getQywxDepartmentMapping(String fsEa,String fsDepId, String outEa, String outDepId);

    @Deprecated
    Result<QyweixinAccountEmployeeMapping> getFsEmployeeMapping(String fsEa, String qywxUserId);

    Result<List<QyweixinAccountEmployeeMapping>> getFsEmployeeMapping2(String outEa, String qywxUserId);

    /**
     * appId 为空查询全部
     * status = -1查詢全部
     */
    @Deprecated
    Result<List<QyweixinAccountEmployeeMapping>> fsAccountToOutAccountBatchV2(String source, String appId, int status
            , List<String> fsAccountList);

    Result<List<QyweixinAccountEmployeeMapping>> fsAccountToOutAccountBatchV21(String source,
                                                                               String appId,
                                                                               int status,
                                                                               List<String> fsAccountList,
                                                                               String outEa);

    Result<Void> deleteOrResumeEmployee(String source, String ea, String appId, List<String> outAccounts, boolean isDelete);

    /**企业的外部账号转内部账号
     * 俊文侧会使用，保留
     * @param outEa :企业在外部平台上的账号
     * @param source: "qywx"是企业微信
     * @return : 企业在纷享的账号
     * */
    Result<String> outEaToFsEa(String source, String outEa);

    /**企业的外部账号转内部账号
     * @param outEa :企业在外部平台上的账号
     * @param source: "qywx"是企业微信
     * @return : 企业在纷享的账号
     * */
    Result<String> outEaToFsEa(String source, String outEa, String depId);

//    Result<QyweixinAccountEnterpriseMapping> isvOutEaToFsEa(String source, String outEa);

    /**企业的外部账号转内部账号
     * @param fsEa :企业在纷享上的账号
     * @param source: "qywx"是企业微信
     * @return : 企业在外部平台上的账号
     * */
    Result<String> fsEaToOutEa(String source, String fsEa);

    Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult(String source, String fsEa);

    Result<List<QyweixinAccountEnterpriseMapping>> fsEaToOutEaResultList(String source, String fsEa);

    Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult2(String source, String fsEa, String outEa);

    Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaAllResult2(String source, String fsEa, String outEa);

    /**通过纷享部门id查 企部门映射完整信息。
     * @param fsEa :企业在纷享上的账号
     * @param source: "qywx"是企业微信
     *@param fsDepartmentIdList : 纷享部门id 列表
     * @return : 部门映射信息列表。
     * */
    @Deprecated
    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByFsDepartment(String source, String fsEa, List<Integer> fsDepartmentIdList);

    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByFsDepartment(String source,
                                                                                     String fsEa,
                                                                                     String appId,
                                                                                     List<Integer> fsDepartmentIdList);

    Result<Void> deleteOrResumeDepartment(String source, String ea, String appId, List<String> outDepartmentIds, boolean isDelete);
    /**通过 在外部平台上的部门id查  部门映射完整信息。默认CRM appId查询
     * @param outEa :企业在外部平台上的账号
     * @param source: "qywx"是企业微信
     * @param outDepartmentIdList : 外部部门id 列表，统一转为字符串处理。
     * @return : 部门映射信息列表。
     * */
    @Deprecated
    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source, String outEa, List<String> outDepartmentIdList);

    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source,
                                                                                      String outEa,
                                                                                      String appId,
                                                                                      List<String> outDepartmentIdList);

    /**
     * status为-1查询所有状态下的部门
     * outDepartmentIdList空List查询全部
     */
    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source,
                                                                                      String outEa,
                                                                                      String appId,
                                                                                      int status,
                                                                                      List<String> outDepartmentIdList);
    /**
     * 分页获取已绑定微信的纷享账号（过滤掉解绑的企业微信）
     * @param pageNum   页码从1开始
     * @param pageSize  每页数量，默认50条
     * @return
     */
    Result<List<String>> queryFsEaBindBatch(int pageNum, int pageSize);

    /**
     * 企业微信用户修改UserId后根据旧的UserId查询企业微信的绑定关系
     * 默认CRM appId查询
     *
     * @param
     * @return
     **/
    @Deprecated
    Result<List<QyweixinAccountEmployeeMapping>> queryFsAccountBindByOldOutAccount(String source,List outAccountList, String outEa);

    Result<List<QyweixinAccountEmployeeMapping>> queryFsAccountBindByOldOutAccount(String source,
                                                                                   List outAccountList,
                                                                                   String appId,
                                                                                   String outEa);
    /**
     * 更新企业微信的userId绑定关系。默认CRM appId
     *
     * @param
     * @return
     **/
    @Deprecated
    int updateByNewOutAccount(String newAccount,String oldAccount, String outEa);

    int updateByNewOutAccount(String newAccount, String oldAccount, String appId, String outEa);

    int updateAccountByIsv(String newAccount, String oldAccount, String appId, String outEa);

    /**
     * 恢复被停用的员工
     * @param source
     * @param fsEa
     * @return
     */
    void resumeStoppedEmployees(String source,String fsEa,String appId);

    /**
     * 恢复被停用的部门
     * @param source
     * @param fsEa
     * @return
     */
    void resumeStoppedDepartments(String source,String fsEa,String appId);

    /**
     * 更新代开发
     */
    Result<Integer> bindOutAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping arg);
    Result<List<QyweixinAccountEmployeeMapping>> selectAll(String corpId);
    Result<Integer> batchUpdateEmployeeMapping(List<QyweixinAccountEmployeeMapping> employeeMappingList);

    /**
     * 获取企业的绑定信息
     * @param source
     * @param outEa
     * @return
     */
    Result<List<QyweixinAccountEnterpriseMapping>> selectEnterpriseBind(String source, String outEa);

    /**
     * 获取企业的全部绑定信息
     * @param source
     * @param outEa
     * @return
     */
    Result<List<QyweixinAccountEnterpriseMapping>> selectAllEnterpriseBind(String source, String outEa);

    Result<Integer> updateQyweixinAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping enterpriseMapping);

    Result<Integer> batchUpdateDepartmentBind(String outEa, String openOutEa);

    Result<Boolean> isManualBinding(String fsEa, String eid);

    /**
     * 查询指定绑定类型的已绑定企业信息
     * @param bindType
     * @return
     */
    Result<List<QyweixinAccountEnterpriseMapping>> queryEnterpriseMappingByBindType(Integer bindType);

    /**
     * 获取crm下所有的员工绑定关系
     * @param fsEa
     * @param appId
     * @return
     */
    @Deprecated
    Result<List<QyweixinAccountEmployeeMapping>> batchGetEmployeeMapping(String fsEa, String appId);
    Result<List<QyweixinAccountEmployeeMapping>> batchGetEmployeeMapping2(String fsEa, String appId, String outEa);

    /**
     * 更新企业绑定状态
     * @param fsEa
     * @param status
     * @return
     */
    Result<Integer> updateEnterpriseBindStatus(String fsEa,int status);

    /**
     * 更新部门绑定状态
     * @param fsEa
     * @param fsDepIdList
     * @param status
     * @return
     */
    @Deprecated
    Result<Integer> batchUpdateFsDepBindStatus(String fsEa,List<String> fsDepIdList,int status,String appId);
    Result<Integer> batchUpdateFsDepBindStatus2(String fsEa,List<String> fsDepIdList,int status,String appId, String outEa);

    /**
     * 更新部门绑定状态
     * @param fsEa
     * @param outDepIdList
     * @param status
     * @return
     */
    Result<Integer> batchUpdateOutDepBindStatus(String fsEa,List<String> outDepIdList,int status,String appId);

    /**
     * 更新员工绑定状态
     * @param fsAccount 格式： E.fs.1000
     * @param status
     * @return
     */
    Result<Integer> updateEmployeeBindStatus(String fsAccount,int status);

    /**
     * 批量更新员工绑定状态
     * @param fsAccountList 格式： E.fs.1000
     * @param status
     * @return
     */
    @Deprecated
    Result<Integer> batchUpdateFsEmpBindStatus(List<String> fsAccountList,
                                               int status,
                                               String appId);

    Result<Integer> batchUpdateFsEmpBindStatus2(List<String> fsAccountList,
                                                int status,
                                                String appId,
                                                String outEa);

    /**
     * 批量更新员工绑定状态
     * @param fsEa
     * @param outAccountList
     * @param status
     * @return
     */
    Result<Integer> batchUpdateOutEmpBindStatus(String fsEa,
                                                List<String> outAccountList,
                                                int status,
                                                String appId);

    Result<List<QyweixinAccountEmployeeMapping>> findEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings);

    Result<Integer> saveEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings);

    Result<Integer> updateEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings);

    Result<List<QyweixinAccountDepartmentMapping>> findDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings);

    Result<Integer> saveDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings);

    Result<Integer> updateDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings);

    Result<List<QyweixinEmployeeAccountModel>> queryRepeatEmployees();

    Result<BatchEmpBindResult> batchGetEmpBind(BatchGetEmpBindArg arg);
}
