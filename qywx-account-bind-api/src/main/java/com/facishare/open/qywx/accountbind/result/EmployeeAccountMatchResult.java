package com.facishare.open.qywx.accountbind.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeAccountMatchResult implements Serializable {
    private String fsAccount; //员工的纷享账号 如E.56305.1000
    private String outAccount;//员工的企业微信账号, qywx是userId.
    private String outEa;//企业微信企业ID
    private String appId;

    public static String getOutAccount(String fsAccount, List<EmployeeAccountMatchResult> list) {
        for (EmployeeAccountMatchResult result : list) {
            if(StringUtils.equalsIgnoreCase(fsAccount, result.getFsAccount())) {
                return result.getOutAccount();
            }
        }
        return null;
    }
}
