<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare.open</groupId>
    <artifactId>fs-open-qywx-gateway</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>i18n-client</artifactId>
                <version>4.3.5-SNAPSHOT</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.facishare.open</groupId>-->
<!--                <artifactId>retry-helper</artifactId>-->
<!--                <version>1.0-SNAPSHOT</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.fxiaoke</groupId>-->
<!--                <artifactId>fs-rocketmq-support</artifactId>-->
<!--                <version>2.0.0-SNAPSHOT</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.apache.rocketmq</groupId>-->
<!--                <artifactId>rocketmq-client</artifactId>-->
<!--                <version>4.5.2</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>5.8.5</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>qywx-i18n</module>
        <module>qywx-account-sync-api</module>
        <module>qywx-account-bind-api</module>
        <module>qywx-message-send-api</module>
        <module>qywx-account-inner-api</module>
        <module>qywx-account-sync-provider</module>
        <module>qywx-account-bind-provider</module>
        <module>qywx-message-send-provider</module>
        <module>qywx-event-handler-web</module>
        <module>qywx-message-save-provider</module>
        <module>qywx-message-save-api</module>
    </modules>




</project>
