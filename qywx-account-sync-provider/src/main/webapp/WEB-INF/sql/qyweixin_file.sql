create table if not exists qyweixin_file(
id int auto_increment primary key comment '主键ID',
fs_ea varchar(24) not null comment '纷享EA',
npath varchar(256) unique not null comment '纷享文件npath',
file_size int comment '文件大小，单位字节',
type varchar(24) not null comment '文件类型',
media_id varchar(128) comment '媒体文件ID',
create_at timestamp comment 'media_id生成时间',
err_msg varchar(256) comment '失败原因',
create_time timestamp default current_timestamp comment '创建时间',
update_time timestamp default current_timestamp on update current_timestamp comment '更新时间'
);