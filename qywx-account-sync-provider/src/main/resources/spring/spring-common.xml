<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <!-- yunzhijia数据源 -->
    <bean id="fsOpenQywxDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="fs-open-qywx-db"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="fsOpenQywxDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.qywx.accountsync.model.qyweixin.bo"/>
        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>
    </bean>
    <!-- scan for mapper and let them be autowired -->
    <bean id="dbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.qywx.accountsync.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <!-- 日志上报数据源 -->
    <bean id="fsClickHouseDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="eye-clickhouse-db"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="clickHouseSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="fsClickHouseDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.qywx.accountsync.model.qyweixin.ckBo"/>
        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>
    </bean>
    <!-- scan for mapper and let them be autowired -->
    <bean id="clickHouseDbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.qywx.accountsync.ckDao"/>
        <property name="sqlSessionFactoryBeanName" value="clickHouseSqlSessionFactory"/>
    </bean>

<!--    <import resource="classpath:spring/fs-webhook-common.xml"/>-->
    <import resource="classpath:fs-paas-dao-support.xml"/>
    <import resource="classpath:META-INF/fs-spring-rest-plugin.xml"/>
    <import resource="classpath:spring/ei-ea-converter.xml"/>
<!--    <import resource="classpath:appcenterrest/appcenterrest.xml"/>-->
    <import resource="classpath:/spring/spring-job.xml"/>
    <import resource="classpath:spring/cus-crmrest.xml"/>
    <import resource="classpath:otherrest/otherrest.xml"/>
    <import resource="classpath*:mongo/mongo-store.xml"/>

    <!--fs-orgainzation-adapter-api -->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>

<!--    <import resource="classpath:fs-organization-cache-no-dubbo.xml"/>-->
    <!--fs-orgainzation-api -->
    <import resource="classpath:spring/fs-organization-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-api-dubbo-rest-client-host-config.xml"/>

<!--    <bean id="commonCrmObjectService" class="com.facishare.webhook.common.service.CrmObjectService"/>-->

    <import resource="classpath:spring/license-client.xml"/>

    <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置 -->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>

    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiServiceProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>
    </bean>
    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>
    </bean>

    <!-- 消费方应用名，用于计算依赖关系，不是匹配条件，不要与提供方一样 -->
    <dubbo:application id="fsOpenQywxAccountSync" name="fs-open-qywx-accountsync" />
    <!-- 使用multicast广播注册中心暴露发现服务地址 -->
    <dubbo:registry id="fsOpenQywxAccountSyncRegistry" address="${dubbo.registry.address}" file="${dubbo.registry.file}" />
    <dubbo:protocol id="dubbo"
                    name="dubbo"
                    port="${duboo.port}"
                    threadpool="cached"
    />
    <dubbo:provider id="fsOpenQywxAccountSyncProvider" application="fsOpenQywxAccountSync" protocol="dubbo"  registry="fsOpenQywxAccountSyncRegistry"  filter="tracerpc" />
    <dubbo:consumer id="fsOpenQywxAccountSyncConsumer"   registry="fsOpenQywxAccountSyncRegistry"
                    init="false"    check="false" timeout="300000"
                    retries="2"   filter="tracerpc" />

    <!--<dubbo:registry id="local" address="zookeeper://localhost:2181"/>-->

    <bean id="qyweixinAccountSyncService" class="com.facishare.open.qywx.accountsync.service.impl.QyweixinAccountSyncServiceImpl"/>
    <dubbo:service interface="com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService"
                   ref="qyweixinAccountSyncServiceImpl"
                   protocol="dubbo"
                   timeout="300000"
                   version="2.0"
                   />
    <dubbo:service interface="com.facishare.open.qywx.accountsync.service.SuperAdminService"
                   ref="superAdminServiceImpl"
                   protocol="dubbo"
                   timeout="300000"
                   version="2.0"
                   />
    <bean id="qyweixinGatewayInnerService" class="com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl"/>
    <dubbo:service  interface="com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService"
                    ref="qyweixinGatewayInnerService"
                    protocol="dubbo"
                    timeout="300000"
                    version="1.0"
                    retries="0"
                    />

    <bean id="contactBindInnerService" class="com.facishare.open.qywx.accountsync.service.impl.ContactBindInnerServiceImpl"/>
    <dubbo:service   interface="com.facishare.open.qywx.accountinner.service.ContactBindInnerService"
                     ref="contactBindInnerService"
                     protocol="dubbo"
                     timeout="300000"
                     version="1.0"
                     retries="0"
                     />

    <dubbo:service interface="com.facishare.open.qywx.accountinner.service.ToolsService"
                   ref="toolsServiceImpl"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   />

    <dubbo:service interface="com.facishare.open.qywx.accountsync.service.FileUploadService"
                   ref="fileUploadService"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   />

    <dubbo:service interface="com.facishare.open.qywx.accountinner.service.ExternalContactsService"
                   ref="externalContactsService"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   />

    <dubbo:service interface="com.facishare.open.qywx.accountinner.service.NotificationService"
                   ref="notificationService"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   />

    <dubbo:reference  id="qyweixinAccountBindService"
                      interface="com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService"
                      protocol="dubbo"
                      timeout="300000"
                      version="1.0"
                      check="false"
                      retries="0"
                      />
    <dubbo:reference  id="qyweixinAccountBindInnerService"
                      interface="com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService"
                      protocol="dubbo"
                      timeout="300000"
                      version="1.0"
                      check="false"
                      retries="0"
                      />
    <dubbo:reference id="messageGeneratingService"
                     interface="com.facishare.open.qywx.save.service.MessageGeneratingService"
                     protocol="dubbo"
                     version="1.0"
                     check="false"
                     />

    <dubbo:reference id="autoPullMessageService"
                     interface="com.facishare.open.qywx.save.service.AutoPullMessageService"
                     protocol="dubbo"
                     version="1.0"
                     check="false"
                     group="${dubboConfigGroup}"/>

    <dubbo:reference id="authService" interface="com.facishare.open.oauth.service.AuthService" protocol="dubbo" timeout="20000" version="1.1"/>

<!--    <dubbo:reference id="appEaVisibleService"  interface="com.facishare.open.app.center.api.service.AppEaVisibleService" timeout="20000" version="1.3" check="false"/>-->

    <dubbo:reference id="enterpriseEditionService" interface="com.facishare.uc.api.service.EnterpriseEditionService" protocol="dubbo" retries="0"/>
<!--    <dubbo:reference id="accountBindService" interface="com.facishare.open.webhook.accountbind.service.AccountBindService" version="1.0"/>-->
<!--    <dubbo:reference id="accountAsyncService" interface="com.facishare.open.webhook.accountsync.service.AccountSyncService" version="1.0"/>-->
<!--    <dubbo:reference id="smsSender" interface="com.facishare.sms.api.provider.SMSSender" protocol="dubbo" retries="0"/>-->
<!--    <dubbo:reference id="departmentService" interface="com.facishare.organization.adapter.api.service.DepartmentService"/>-->
<!--    <dubbo:reference id="employeeService" interface="com.facishare.organization.adapter.api.service.EmployeeService"/>-->
    <dubbo:reference id="permissionService" interface="com.facishare.organization.adapter.api.permission.service.PermissionService" version="5.7"/>
<!--    <dubbo:reference id="employeeProviderService" interface="com.facishare.organization.api.service.EmployeeProviderService" version="5.7"/>-->
<!--    <dubbo:reference id="departmentProviderService" version="5.7" interface="com.facishare.organization.api.service.DepartmentProviderService"/>-->
<!--    <dubbo:reference id="coreRegisterService" interface="com.facishare.register.api.service.CoreRegisterService" timeout="120000"/>-->
<!--    <dubbo:reference id="enterpriseConfigService" interface="com.facishare.organization.adapter.api.config.service.EnterpriseConfigService"/>-->
<!--    <dubbo:reference id="enterpriseService" interface="com.facishare.organization.adapter.api.service.EnterpriseService"/>-->
<!--    <dubbo:reference id="employeeEditionService" interface="com.facishare.uc.api.service.EmployeeEditionService"/>-->
<!--    <dubbo:reference id="versionRegisterService" interface="com.facishare.webhook.api.service.VersionRegisterService"/>-->

    <dubbo:reference id="fsOrderServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>
    <dubbo:reference id="fsContactsServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>
    <dubbo:reference id="fsEmployeeServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>
    <dubbo:reference id="fsDepartmentServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>
    <dubbo:reference id="fsObjServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>

    <dubbo:reference id="oaQyweixinAccountSyncService"
                     interface="com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService"
                     protocol="dubbo"
                     version="2.0"
                     timeout="300000"
                     check="false"
                     group="oaTemp"/>

    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>
    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fs-open-qywx-redis-config"/>

    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!-- 启用@AspectJ注解 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="fsOpenQywxDB" />
    </bean>

    <tx:annotation-driven transaction-manager="transactionManager" />

<!--    <bean id="qywxEventNotifyMQSender" class="com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-mq-cms"/>-->
<!--    </bean>-->

    <bean id="qywxEventNotifyMQSender" class="com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="fs-open-qywx-provider-section"/>
    </bean>

    <bean id="outEventDataChangeMQSender" class="com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="out-event-data-change-section"/>
    </bean>

    <!-- 可见范围人员变更事件 -->
<!--    <bean id="userChangeEventSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg index="1" value="USER_CHANGE_NAMESERVER"/>-->
<!--        <constructor-arg index="2" value="USER_CHANGE_GROUP_PROVIDER"/>-->
<!--        <constructor-arg index="3" value="USER_CHANGE_TOPIC"/>-->
<!--    </bean>-->

    <bean id="userChangeEventSender" class="com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="qywx-cooked-event-provider-section"/>
    </bean>

<!--    <bean id="enterpriseWechatEventMQProcessor" class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg index="1" value="enterprise.wechat.event.name.server"/>-->
<!--        <constructor-arg index="2" value="enterprise.wechat.event.consumer.group"/>-->
<!--        <constructor-arg index="3" value="enterprise.wechat.event.consume.topic"/>-->
<!--        <constructor-arg index="4" ref="enterpriseWeChatMQListener"/>-->
<!--    </bean>-->

<!--    企微事件生产者-->
    <bean id="enterpriseWechatEventMQSender" class="com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="fs-enterprise-wechat-web-provider-section"/>
    </bean>
<!--企微事件消费者-->
<!--    <bean id="enterpriseWeChatMQConsumer" class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"-->
<!--          init-method="start" destroy-method="shutdown">-->
<!--        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg name="sectionNames" value="fs-enterprise-wechat-consumer-section"/>-->
<!--        <constructor-arg name="messageListener" ref="enterpriseWeChatMQListener"/>-->
<!--    </bean>-->

<!--    <bean id="enterpriseEventConsumer" class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"-->
<!--          init-method="start" destroy-method="shutdown">-->
<!--        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg name="sectionNames" value="QYWX_ENTERPRISE_REGISTER_SECTION"/>-->
<!--        <constructor-arg name="messageListener" ref="enterpriseEventListener"/>-->
<!--    </bean>-->

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="out-event-data-change-consume-section"/>
        <constructor-arg name="messageListener" ref="outEventDataChangeListener"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="oaconnector-event-data-change-consume-section"/>
        <constructor-arg name="messageListener" ref="oaconnectorEventDataChangeListener"/>
    </bean>

<!--    <bean id="retryAspect" class="com.facishare.open.retry.RetryAspect" />-->
<!--    <aop:config>-->
<!--        <aop:aspect id="retryMonitor" ref="retryAspect">-->
<!--            <aop:pointcut id="monitor"-->
<!--                          expression="(execution(* com.facishare.open.qywx.accountsync.manager.*.*(..)))"/>-->
<!--            <aop:around pointcut-ref="monitor" method="around"/>-->
<!--        </aop:aspect>-->
<!--    </aop:config>-->

    <!-- rest接口 -->
    <bean id="restServiceProxy" class="com.facishare.rest.proxy.RestServiceProxyFactory" init-method="init"
          p:configName="fs-online-consult-core-rest"/>
<!--    <bean class="com.facishare.rest.proxy.RestServiceProxyFactoryBean" p:type="com.facishare.open.qywx.accountsync.manager.CrmRestApi">-->
<!--        <property name="factory" ref="restServiceProxy"/>-->
<!--    </bean>-->
<!--    <bean class="com.facishare.rest.proxy.RestServiceProxyFactoryBean" p:type="com.facishare.open.qywx.accountsync.manager.SendFsEaApi">-->
<!--        <property name="factory" ref="restServiceProxy"/>-->
<!--    </bean>-->
    <import resource="classpath:spring/qywx-i18n.xml"/>
    <import resource="classpath:META-INF/fs-spring-dubbo-plugin.xml"/>

    <!--redisson-->
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-open-qywx-redis-config"/>
    </bean>

    <bean id="fileManager" class="com.facishare.open.qywx.accountsync.excel.FileManager"/>

    <context:component-scan base-package="com.facishare.open.order.contacts.proxy,com.facishare.open.qywx.accountsync,com.facishare.open.qywx.i18n"/>
</beans>