create table if not exists wechat_interface_license (
	id	varchar(64) not null,
	tenant_id	varchar(16) not null,
	name	varchar(128),
	account_status	varchar(64),
	bind_wechat_userid	varchar(64),
	order_id	varchar(64),
	to_active_code	varchar(64),
	from_active_code	varchar(64),
	account_type	varchar(null),
	account_duration	varchar(14),
	active_time	varchar(null),
	pay_time	varchar(null),
	expire_time	varchar(null),
	active_expire_time	varchar(null),
	remaining_time	varchar(null),
	wechat_employee_id	varchar(null),
	display_name	varchar(128),
	owner	varchar(32),
	lock_status	varchar(16),
	life_status	varchar(16),
	record_type	varchar(200),
	created_by	varchar(32),
	create_time	bigint,
	last_modified_by	varchar(32),
	last_modified_time	bigint,
	extend_obj_data_id	varchar(64),
	package	varchar(50),
	object_describe_id	varchar(50),
	object_describe_api_name	varchar(100),
	version	integer,
	lock_user	varchar(32),
	lock_rule	varchar(32),
	life_status_before_invalid	varchar(16),
	is_deleted	int2,
	out_tenant_id	varchar(50),
	out_owner	varchar(50),
	data_own_department	varchar(32),
	data_own_organization	varchar(32),
	data_auth_code	varchar(64),
	change_type	int4,
	out_data_auth_code	varchar(64),
	order_by	int4,
	data_auth_id	int4,
	out_data_auth_id	int4,
	dimension_d1	varchar[64],
	dimension_d2	varchar[64],
	dimension_d3	varchar[64],
	mc_currency	varchar(128),
	mc_exchange_rate	numeric,
	mc_functional_currency	varchar(128),
	mc_exchange_rate_version	varchar(64),
	origin_source	varchar(32),
	out_data_own_department	varchar(64),
	out_data_own_organization	varchar(64),
	sys_modified_time	bigint,
	primary key (id, tenant_id));
CREATE INDEX IF NOT EXISTS wechat_interface_license_tenant_id_name_idx on wechat_interface_license using btree (tenant_id, name, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS wechat_interface_license_tenant_dname_idx on wechat_interface_license using btree (tenant_id, display_name, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS wechat_interface_license_tenant_extend_obj_data_id_idx on wechat_interface_license using btree (extend_obj_data_id, tenant_id);
CREATE INDEX IF NOT EXISTS wechat_interface_license_last_modified_time_tenant_id_api_name_idx on wechat_interface_license using btree (last_modified_time desc, tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS wechat_interface_license_create_time_tenant_idx on wechat_interface_license using btree (create_time desc, tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS wechat_interface_license_tenant_id_owner_idx on wechat_interface_license using btree (tenant_id, owner, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS wechat_interface_license_tenant_id_data_own_department_idx on wechat_interface_license using btree (tenant_id, data_own_department, is_deleted, object_describe_api_name);
CREATE INDEX IF NOT EXISTS wechat_interface_license_data_auth_code_idx on wechat_interface_license using btree (data_auth_code);
CREATE INDEX IF NOT EXISTS wechat_interface_license_out_data_auth_code_idx on wechat_interface_license using btree (out_data_auth_code);
CREATE INDEX IF NOT EXISTS wechat_interface_license_data_auth_id_idx on wechat_interface_license using btree (data_auth_id);
CREATE INDEX IF NOT EXISTS wechat_interface_license_out_data_auth_id_idx on wechat_interface_license using btree (out_data_auth_id);
CREATE INDEX IF NOT EXISTS wechat_interface_license_last_modified_time_out_tenant_id_idx on wechat_interface_license using btree (last_modified_time desc, out_tenant_id, is_deleted, tenant_id);
CREATE INDEX IF NOT EXISTS wechat_interface_license_sys_modified_time_tenant_id_idx on wechat_interface_license using btree (sys_modified_time desc, tenant_id, is_deleted, object_describe_api_name);
DROP TRIGGER IF EXISTS x_audit_changes ON wechat_interface_license;
CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON wechat_interface_license FOR EACH ROW EXECUTE PROCEDURE f_change_detail('id','tenant_id','object_describe_api_name');
DROP TRIGGER IF EXISTS x_system_changes ON wechat_interface_license;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON wechat_interface_license FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();
