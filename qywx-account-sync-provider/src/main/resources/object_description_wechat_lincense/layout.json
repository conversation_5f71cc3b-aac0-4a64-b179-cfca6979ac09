{"api_name": "layout_WechatInterfaceLicenseObj_default", "display_name": "默认布局", "is_default": true, "is_deleted": false, "components": [{"field_section": [{"form_fields": [{"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "account_status"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "bind_wechat_userid"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "account_type"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "account_duration"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "wechat_employee_id"}, {"is_readonly": false, "is_required": true, "render_type": "date_time", "field_name": "active_time"}, {"is_readonly": false, "is_required": true, "render_type": "date_time", "field_name": "active_expire_time"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "pay_time"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "expire_time"}, {"is_readonly": false, "is_required": false, "render_type": "formula", "field_name": "remaining_time"}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_required": false, "field_name": "lock_status", "render_type": "select_one"}, {"is_required": false, "field_name": "out_owner", "render_type": "employee"}, {"field_name": "relevant_team", "is_readonly": true, "is_required": false, "render_type": "relevant_team"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_f4v1D__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "unDeletable": true, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "_id": "form_component", "order": 5}], "layout_type": "detail", "layout_description": "默认布局", "ref_object_api_name": "WechatInterfaceLicenseObj", "buttons": [{"action_type": "default", "api_name": "Add_button_default", "action": "Add", "label": "新建"}, {"action_type": "default", "api_name": "Edit_button_default", "action": "Edit", "label": "编辑"}, {"action_type": "default", "api_name": "Import_button_default", "action": "Import", "label": "导入"}, {"action_type": "default", "api_name": "Export_button_default", "action": "Export", "label": "导出"}, {"action_type": "default", "api_name": "Abolish_button_default", "action": "Abolish", "label": "作废"}, {"action_type": "default", "api_name": "Lock_button_default", "action": "Lock", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "action": "Unlock", "label": "解锁"}], "events": [], "default_component": "form_component", "package": "CRM", "api_version": 1.0}