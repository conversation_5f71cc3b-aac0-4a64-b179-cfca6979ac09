package com.facishare.open.qywx.accountsync.utils;

import cn.hutool.core.util.IdUtil;
import com.facishare.open.qywx.accountinner.annotation.LogLevel;
import com.facishare.open.qywx.accountinner.enums.LogLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
@Slf4j
@Service
public class IdGenerator {

    /**
     * id生成器，
     * @deprecated 推荐直接使用 {@link com.fxiaoke.api.IdGenerator#get()}
     * @return
     */
    @LogLevel(value = LogLevelEnum.TRACE)
    @Deprecated
    public String get() {
        String idStr;
        try {
            idStr = com.fxiaoke.api.IdGenerator.get();
        } catch (Exception e) {
            idStr = IdUtil.nanoId();
        }
        return idStr;
    }
}
