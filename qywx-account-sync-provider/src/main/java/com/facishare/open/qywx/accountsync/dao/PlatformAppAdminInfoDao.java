package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.PlatformAppAdminInfoBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.springframework.stereotype.Repository;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/11/19
 */
@Repository
public interface PlatformAppAdminInfoDao extends ICrudMapper<PlatformAppAdminInfoBo> {

    @Insert("<script>" +"INSERT INTO platform_app_admin_info (source, app_id, out_ea, mobile, name)" +
            " VALUES(#{source}, #{appId}, #{outEa}, #{mobile}, #{name}) ON DUPLICATE KEY UPDATE " +
            " source=#{source}, app_id=#{appId}, out_ea=#{outEa}, mobile=#{mobile}, name=#{name};"+ "</script>")
    int savePlatformAdminInfo(PlatformAppAdminInfoBo platformAdminInfoBo);

}
