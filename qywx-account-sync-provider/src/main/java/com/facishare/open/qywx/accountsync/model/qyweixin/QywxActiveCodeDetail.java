package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/10/25 19:11
 * 企业微信许可接口详情
 * @desc
 */
@Data
public class QywxActiveCodeDetail extends QywxBaseResult implements Serializable {


    private List<ActiveCodeInfo> active_info_list;
    private Integer active_status;//get_active_info_by_user 在请求这个接口的时候，才返回数据
    @Data
    public class ActiveCodeInfo implements Serializable {

        //账号码，订单类型为购买账号时，返回该字段
        private String active_code;

        //1:基础账号，2:互通账号
        private Integer type;

        private Integer status;
        //账号绑定激活的企业成员userid 未激活则不返回该字段。返回加密的userid
        private String userid;

        private Long create_time;

        private Long active_time;

        private Long expire_time;

        private MergeInfo merge_info;

        private ShareInfo share_info;

    }

    @Data
    public static class MergeInfo implements Serializable {

        //该激活码合并到的新激活码信息
        private String to_active_code;

        //激活码激活userid时，若userid原来已经绑定了一个激活码，则会返回该字段
        private String from_active_code;

    }

    @Data
    public static class ShareInfo implements Serializable {

        //分配信息 下游企业corpid。当激活码通过上游分配给下游时，获取上游企业该激活码详情时返回该字段，表示被分配给了哪个下游企业
        private String to_corpid;

        //上游企业corpid。当激活码通过上游分配给下游时，获取下游企业该激活码详情时返回该字段，表示从哪个上游企业分配过来
        private String from_corpid;

    }
}
