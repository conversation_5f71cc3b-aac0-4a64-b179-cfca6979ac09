package com.facishare.open.qywx.accountsync.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * erp企业连接信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErpConnectInfoEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 渠道
     */
    @Column(name = "channel")
    private String channel;

    /**
     * 数据中心名称
     */
    @Column(name = "data_center_name")
    private String dataCenterName;


    /**
     * 企业名称
     */
    @Column(name = "enterprise_name")
    private String enterpriseName;

    /**
     * 连接参数
     */
    @Column(name = "connect_params")
    @ToString.Exclude
    private String connectParams;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
    /**
     * 编号，渠道内序号，百位为渠道id，个位十位为渠道内序号,从0开始,如K3第一个渠道，为0.
     *
     */
    @Column(name = "number")
    private Integer number;
}