package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QyweixinIdToOpenidDao extends ICrudMapper<QyweixinIdToOpenidBo> {
    @Insert("<script>" + "insert ignore into qyweixin_id_to_openid(corp_id,plaintext_id,openid,type, " +
            "update_time, create_time) " + "values " +
            "<foreach collection='qyweixinIdToOpenidBos' item='mapping' separator=','>" +
            "(#{mapping.corpId}, #{mapping.plaintextId}, #{mapping.openid}, #{mapping.type}, now(), now())" + "</foreach>" + "</script>")
    int batchSaveQyweixinIdToOpenid(@Param("qyweixinIdToOpenidBos") List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos);

    @Delete("<script>" + "delete from qyweixin_id_to_openid where openid is null"
            + "</script>"
    )
    int deleteSwitchFailed();

    @Select("<script>" + "select * from qyweixin_id_to_openid where corp_id=#{corpId} and plaintext_id in " +
            "<foreach collection='plaintextIds' item='item' open='(' close=')'  separator=','>" +
            "#{item} </foreach>"
            + "</script>"
    )
    List<QyweixinIdToOpenidBo> getByPlaintextIds(@Param("corpId") String corpId, @Param("plaintextIds") List<String> plaintextIds);

    @Select("<script>" + "select * from qyweixin_id_to_openid where corp_id=#{corpId} and openid in " +
            "<foreach collection='openids' item='item' open='(' close=')'  separator=','> " +
            "#{item} </foreach> " +
            "</script>"
    )
    List<QyweixinIdToOpenidBo> getByOpenids(@Param("corpId") String corpId, @Param("openids") List<String> openids);

    @Select("<script>" + "select id from qyweixin_id_to_openid where corp_id=#{corpId} and openid=#{openid} and plaintext_id=#{plaintextId} " +
            "</script>"
    )
    List<String> getIdsByOpenidAndPlaintextId(@Param("corpId") String corpId, @Param("openid") String openid, @Param("plaintextId") String plaintextId);


//    @Delete("<script>" + "delete from qyweixin_id_to_openid where id=#{id} "
//            + "</script>"
//    )
//    int deleteById(@Param("id") String id);
}
