package com.facishare.open.qywx.accountsync.service.impl;

import com.facishare.open.qywx.accountsync.dao.SuperAdminDao;
import com.facishare.open.qywx.accountsync.manager.QywxPermissionManager;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinUserDetailInfoRsp;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.SuperAdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 管理工具类实现类
 * <AUTHOR>
 * @date 2021/11/01
 */
@Slf4j
@Service("superAdminServiceImpl")
public class SuperAdminServiceImpl implements SuperAdminService {
    @Autowired
    private SuperAdminDao superAdminDao;
    @Autowired
    private QywxPermissionManager qywxPermissionManager;

    @Override
    public Result<List<Map<String, Object>>> superQuerySql(String sqlStr) {
        return new Result(superAdminDao.superQuerySql(sqlStr));
    }

    @Override
    public Result<Integer> superInsertSql(String sqlStr) {
        try {
            int result = superAdminDao.superInsertSql(sqlStr);
            return new Result(result);
        } catch (Exception e) {
            return new Result("-1",e.getMessage(),null);
        }
    }

    @Override
    public Result<Integer> superUpdateSql(String sqlStr) {
        try {
            int result = superAdminDao.superUpdateSql(sqlStr);
            return new Result(result);
        } catch (Exception e) {
            return new Result("-1",e.getMessage(),null);
        }
    }

    @Override
    public Result<Integer> superDeleteSql(String sqlStr) {
        try {
            int result = superAdminDao.superDeleteSql(sqlStr);
            return new Result(result);
        } catch (Exception e) {
            return new Result("-1",e.getMessage(),null);
        }
    }

    @Override
    public Result<Integer> completeOrder(String orderId) {
        qywxPermissionManager.handlerOrderPaySuccess(orderId);
        return new Result<>();
    }

    public static void main(String[] args) {
        List<QyweixinUserDetailInfoRsp> userlist = new ArrayList<>();
        QyweixinUserDetailInfoRsp rsp = QyweixinUserDetailInfoRsp.builder()
                .userid("kgd1")
                .build();
        QyweixinUserDetailInfoRsp rsp2 = QyweixinUserDetailInfoRsp.builder()
                .userid("kgd1")
                .build();
        userlist.add(rsp);
        userlist.add(rsp2);

        Map<String, QyweixinUserDetailInfoRsp> qyweixinEmployeeMap = userlist.stream()
                .collect(Collectors.toMap(v -> v.getUserid(), Function.identity(),(v1,v2) -> v1));
        System.out.println(qyweixinEmployeeMap);
    }
}
