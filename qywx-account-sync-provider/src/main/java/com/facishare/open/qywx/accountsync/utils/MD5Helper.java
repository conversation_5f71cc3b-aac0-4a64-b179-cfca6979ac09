package com.facishare.open.qywx.accountsync.utils;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-06-21 16:06
 * @<NAME_EMAIL>
 */
public class MD5Helper {

    private final static char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

    /**
     * 获取MD5值
     * @param string
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String getStringMD5(String string) throws NoSuchAlgorithmException {
        byte[] byteString=string.getBytes(Charset.forName("utf-8"));
        MessageDigest md=MessageDigest.getInstance("MD5");
        byte[] array=md.digest(byteString);
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < array.length; ++i) {
            sb.append(Integer.toHexString((array[i] & 0xFF) | 0x100).substring(1,3));
        }
        return sb.toString();
    }

    /**
     * 获取Md5值
     * @param str
     * @return
     */
    public static String getMD5HexString(String str) {
        try {
            MessageDigest md5=MessageDigest.getInstance("MD5");
            byte[] newstr=md5.digest(str.getBytes("utf-8"));
            return byteArrayToHex(newstr);
        } catch (Exception e) {
            return null;
        }
    }

    private static String byteArrayToHex(byte[] byteArray) {
        char[] resultCharArray = new char[byteArray.length * 2];
        int index = 0;
        for (byte b : byteArray) {
            resultCharArray[index++] = hexDigits[b >>> 4 & 0xf];
            resultCharArray[index++] = hexDigits[b & 0xf];
        }
        return new String(resultCharArray);
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        String md5 = getStringMD5("testmeimei1234");
        System.out.println(md5);
    }

}
