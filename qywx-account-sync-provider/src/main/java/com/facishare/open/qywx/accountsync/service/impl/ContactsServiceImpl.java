package com.facishare.open.qywx.accountsync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutDepModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutUpdateTagModel;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.manager.CorpManager;
import com.facishare.open.qywx.accountsync.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.model.ContactScopeModel;
import com.facishare.open.qywx.accountsync.model.TagDetailModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.accountsync.utils.ContactsUtils;
import com.facishare.open.qywx.accountsync.utils.FSAccountUtil;
import com.facishare.open.qywx.accountsync.utils.RedisLockUtils;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountsync.utils.xml.ContactsXml;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service("contactsService")
public class ContactsServiceImpl implements ContactsService {
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CorpManager corpManager;
    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Resource
    private RedissonClient redissonClient;

    @ReloadableProperty("repAppId")
    private String repAppId;

    @Override
    public Result<Void> initContactsAsync(String appId,String outEa,String fsEa) {
        String traceId = TraceUtil.get();
        Executors.newScheduledThreadPool(1).schedule(()->{
            TraceUtil.initTrace(traceId);
            initContacts(appId, outEa, fsEa);
        },3 * 1000L, TimeUnit.MILLISECONDS);
        return new Result<>();
    }

    private Result<Void> initContacts(String appId,String outEa,String fsEa) {
        log.info("ContactsServiceImpl.initContactsAsync,appId={},outEa={},fsEa={}",appId,outEa,fsEa);
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        //1.获取应用信息，包括可见范围信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(outEa, appId);
        log.info("ContactsServiceImpl.initContactsAsync,appInfo={}",appInfoResult);
        if(!appInfoResult.isSuccess() || appInfoResult.getData()==null) return new Result<>(appInfoResult.getCode(),appInfoResult.getMsg(),null);

        //2.获取应用可见范围内的所有部门，递归获取
        com.facishare.open.qywx.accountinner.result.Result<List<OutDepModel>> outDepModelListResult = qyWeixinManager.getDepInfoList(appId, outEa, "");
        log.info("ContactsServiceImpl.initContactsAsync,outDepModelList={}", outDepModelListResult);

        //3.初始化纷享部门并更新部门映射表
        if(outDepModelListResult.isSuccess() && CollectionUtils.isNotEmpty(outDepModelListResult.getData())) {
            initDepAndMapping(ei,fsEa,outEa,appId,outDepModelListResult.getData());
        }

        //4.获取应用可见范围内的不在部门内的员工列表，包括单独添加在可见范围内的员工和标签下不在任何部门内的员工
        List<OutEmpModel> empList = getEmpListInAppVisibleRange(appInfoResult.getData(),outEa,appId);

        //5.初始化纷享员工并更新员工映射表
        initEmpAndMapping(ei,fsEa,outEa,appId,outDepModelListResult.getData(),empList);

        return new Result<>();
    }

    @Override
    public Result<Void> onAvailableRangeChanged(String appId, String outEa) {
        log.info("ContactsServiceImpl.onAvailableRangeChanged,appId={},outEa={}",appId,outEa);
        //分布式锁
        boolean gotFirstLock = Boolean.FALSE;
        boolean gotSecondLock = Boolean.FALSE;
        RLock rLock = redissonClient.getLock(String.format("qywx_change_contacts_%s", outEa));
        try {
            if (!rLock.isLocked()) {
                gotFirstLock = rLock.tryLock(1, 60 * 5, TimeUnit.SECONDS);
                log.info("ContactsServiceImpl.onAvailableRangeChanged,first gotFirstLock={}",gotFirstLock);
                if(gotFirstLock) {
                    try {
                        onAvailableRangeChangedLimit(appId, outEa);
                    } finally {
                        rLock.unlock();
                        gotFirstLock = Boolean.FALSE;
                    }
                } else {
                    log.info("ContactsServiceImpl.onAvailableRangeChanged,first lock acquisition failed,outEa={}",outEa);
                }
            } else {
                gotSecondLock = RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_repeat_change_contacts_%s", outEa), outEa, 60 * 2);
                log.info("ContactsServiceImpl.onAvailableRangeChanged,first gotSecondLock={}",gotSecondLock);
                if(gotSecondLock) {
                    long startTime = System.currentTimeMillis();
                    gotFirstLock = rLock.tryLock(60 * 5, 60 * 5, TimeUnit.SECONDS);
                    RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_repeat_change_contacts_%s", outEa), outEa);
                    gotSecondLock = Boolean.FALSE;
                    log.info("ContactsServiceImpl.onAvailableRangeChanged,second gotFirstLock={},cost={}ms",gotFirstLock, System.currentTimeMillis() - startTime);
                    if(gotFirstLock) {
                        try {
                            onAvailableRangeChangedLimit(appId, outEa);
                        } finally {
                            rLock.unlock();
                            gotFirstLock = Boolean.FALSE;
                        }
                    } else {
                        log.info("ContactsServiceImpl.onAvailableRangeChanged,second lock acquisition failed,outEa={}",outEa);
                    }
                } else {
                    log.info("ContactsServiceImpl.onAvailableRangeChanged,The visible range of operations is too frequent, discard this event,outEa={}",outEa);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            log.info("ContactsServiceImpl.onAvailableRangeChanged,gotFirstLock={},gotSecondLock={}",gotFirstLock, gotSecondLock);
            if(gotFirstLock) {
                rLock.unlock();
            }
            if(gotSecondLock) {
                RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_repeat_change_contacts_%s", outEa), outEa);
            }
        }
        return new Result<>();
    }

    public Result<Void> onAvailableRangeChangedLimit(String appId, String outEa) {
        String fsEa = getFsEa(outEa);
        log.info("ContactsServiceImpl.onAvailableRangeChanged,fsEa={}",fsEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        if(qyweixinAccountBindService.isManualBinding(fsEa,outEa).getData()) {
            log.info("ContactsServiceImpl.onAvailableRangeChanged,not support available range change event");
            return new Result<>();
        }

        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        //1.获取应用信息，包括可见范围信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(outEa, appId);
        log.info("ContactsServiceImpl.onAvailableRangeChanged,appInfo={}",appInfoResult);
        if(!appInfoResult.isSuccess() || appInfoResult.getData()==null) return new Result<>(appInfoResult.getCode(),appInfoResult.getMsg(),null);

        //2.获取应用可见范围内的所有部门，包括标签下的部门，递归获取
        com.facishare.open.qywx.accountinner.result.Result<List<OutDepModel>> visibleOutDepModelListResult = qyWeixinManager.getDepInfoList(appId, outEa, "");
        log.info("ContactsServiceImpl.onAvailableRangeChanged,visibleOutDepModelList={}", visibleOutDepModelListResult);

        //3.查询DB里状态正常的企微部门ID列表
        List<String> dbActiveOutDepIdList = getDepMappingList(outEa,null,0,appId).stream()
                .map(QyweixinAccountDepartmentMapping::getOutDepartmentId)
                .collect(Collectors.toList());
        log.info("ContactsServiceImpl.onAvailableRangeChanged,dbActiveOutDepIdList={}", dbActiveOutDepIdList);

        //4.计算不在可见范围内的企微部门ID列表
        List<String> notInAvailableRangeOutDepIdList = null;

        if(visibleOutDepModelListResult.isSuccess() && CollectionUtils.isNotEmpty(visibleOutDepModelListResult.getData())) {
            //可见范围内的企微部门ID列表
            List<String> visibleOutDepIdList = visibleOutDepModelListResult.getData().stream()
                    .map(OutDepModel::getId)
                    .collect(Collectors.toList());
            log.info("ContactsServiceImpl.onAvailableRangeChanged,visibleOutDepIdList={}", visibleOutDepIdList);

            //不在可见范围内的企微部门ID列表
            notInAvailableRangeOutDepIdList = ListUtils.removeAll(dbActiveOutDepIdList, visibleOutDepIdList);

            log.info("ContactsServiceImpl.onAvailableRangeChanged,notInAvailableRangeOutDepIdList={}", notInAvailableRangeOutDepIdList);
        } else {
            //可见范围内只有员工，没有部门
            notInAvailableRangeOutDepIdList = dbActiveOutDepIdList;
        }

        //5.获取应用可见范围内的不在部门内的员工列表
        List<OutEmpModel> empList = getEmpListInAppVisibleRange(appInfoResult.getData(),outEa,appId);
        log.info("ContactsServiceImpl.onAvailableRangeChanged,empList={}", empList);

        Map<String,List<String>> excludeDepEmpMap = new HashMap<>();
        excludeDepEmpMap.put(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"",new ArrayList<>());
        for(OutEmpModel outEmpModel : empList) {
            QyweixinAccountEmployeeMapping employeeMapping = getEmpMappingByOutUserId(outEa, outEmpModel.getId(), appId);
            if(employeeMapping!=null) {
                excludeDepEmpMap.get(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"").add(FSAccountUtil.getEmpIdFromFSAccount(employeeMapping.getFsAccount())+"");
            }
        }

        //6.停用部门下的员工并停用部门
        stopDepListInner(appId,outEa,notInAvailableRangeOutDepIdList,excludeDepEmpMap);

        //7.新增或更新部门，同时新增或更新部门员工
        addDepListInner(appId,outEa,visibleOutDepModelListResult.getData());

        //去掉不需要更新的人员
        List<OutEmpModel> updateEmpList = removeEmployeeList(empList, appInfoResult.getData(), appId, outEa);
        log.info("ContactsServiceImpl.onAvailableRangeChanged,updateEmpList={}", updateEmpList);

        //8.创建和更新部门员工，并更新员工绑定表
        createAndUpdateDepEmpList(ei,
                fsEa,
                GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"",
                outEa,
                appId,
                updateEmpList);
        return new Result<>();
    }

    /**
     * 企微标签变更事件
     * 对应企微的 update_tag 事件
     *
     * @param appId
     * @param outEa
     * @return
     */
    private Result<Void> onUpdateTag(String appId, String outEa, OutUpdateTagModel outUpdateTagModel) {
        log.info("ContactsServiceImpl.onUpdateTag,outUpdateTagModel={}",outUpdateTagModel);
        if(!(ConfigCenter.TAG_SYNC_CONTACT_OUT_EA.contains(outEa) || ConfigCenter.TAG_SYNC_CONTACT_OUT_EA.contains(Boolean.TRUE.toString()))) {
            onAvailableRangeChanged(appId, outEa);
            return new Result<>();
        } else {
            //异步处理，防止堵塞mq
            Thread thread = new Thread(() -> updateTagEvent(appId, outEa, outUpdateTagModel));
            thread.start();
        }
        return new Result<>();
    }

    private void updateTagEvent(String appId, String outEa, OutUpdateTagModel outUpdateTagModel) {
        //分布式锁
        if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa, 60)) {
            try {
                log.info("ContactsServiceImpl.updateTagEvent,outUpdateTagModel={}",outUpdateTagModel);
                //企微推送事件过来时，为避免企微可见范围没有处理完成，先sleep5s
                Thread.sleep(5000);
                //更新标签的时候，区分事件维度，单独处理
                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getAddEmpList())) {
                    addUserList(appId,outEa,outUpdateTagModel.getAddEmpList());
                }

                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getRemoveEmpList())) {
                    //停用员工操作，先查询员工详情，如果能查询到，证明其部门还在可见范围，忽略
                    List<String> removeEmpList = new LinkedList<>();
                    for(String empId : outUpdateTagModel.getRemoveEmpList()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> empInfoResult = qyWeixinManager.getUserInfo(appId, outEa, empId);
                        if(empInfoResult.isSuccess() && ObjectUtils.isNotEmpty(empInfoResult)) {
                            continue;
                        }
                        removeEmpList.add(empId);
                    }
                    if(CollectionUtils.isNotEmpty(removeEmpList)) {
                        stopUserList(appId,outEa,removeEmpList);
                    }
                }

                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getAddDepList())) {
                    //新增部门的时候，要考虑其子部门
                    addTagDepList(appId,outEa,outUpdateTagModel.getAddDepList());
                }

                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getRemoveDepList())) {
                    //标签移除部门时，有几种情况
                    //该部门、子部门和部门下全部人员都被移除了
                    //该部门被移除，子部门保留了全部或者一部分，人员移除了全部或者一部分
                    //该部门被移除，子部门也全部移除，但是保留了部分人员
                    //该部门被移除，但是父部门仍保留
                    String fsEa = getFsEa(outEa);
                    if (StringUtils.isNotEmpty(fsEa)) {
                        int ei = eieaConverter.enterpriseAccountToId(fsEa);
                        for (String depId : outUpdateTagModel.getRemoveDepList()) {
                            stopTagDep(appId, outEa, depId, ei, fsEa);
                        }
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } finally {
                RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa);
            }
        } else {
            log.info("ContactsServiceImpl.onUpdateTag,The visible range of operations is too frequent, discard this event,outEa={}",outEa);
        }
    }

    private void addTagDepList(String appId, String outEa, List<String> outDepIdList) {
        Set<String> outAllDepIdSet = new HashSet<>();
        for(String depId : outDepIdList) {
            log.info("ContactsServiceImpl.addTagDepList,depId={},outAllDepIdSet={}",depId, outAllDepIdSet);
            //去重
            if(outAllDepIdSet.contains(depId)) {
                continue;
            }
            //查询子部门详情
            com.facishare.open.qywx.accountinner.result.Result<List<OutDepModel>> outDepModelListResult = qyWeixinManager.getDepInfoList(appId, outEa, depId);
            if(outDepModelListResult.isSuccess() && CollectionUtils.isNotEmpty(outDepModelListResult.getData())) {
                Set<String> outDepIdSet = outDepModelListResult.getData().stream()
                        .map(OutDepModel::getId)
                        .collect(Collectors.toSet());
                outAllDepIdSet.addAll(outDepIdSet);
                addDepListInner(appId, outEa, outDepModelListResult.getData());
            }
        }
    }

    private Boolean stopTagDep(String appId, String outEa, String depId, Integer ei, String fsEa) {
        //是否可以查询到企微部门详情
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(appId, outEa, depId);
        if(ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
            return Boolean.FALSE;
        }
        //是否具体绑定关系
        QyweixinAccountDepartmentMapping depMapping = getDepMappingByOutDepId(outEa, depId, -1, appId);
        log.info("ContactsServiceImpl.stopTagDep,depMapping={}", depMapping);
        if(ObjectUtils.isEmpty(depMapping)) {
            return Boolean.FALSE;
        }
        Integer fsDepId = depMapping.getFsDepartmentId();
        //人员是否全部停用
        Boolean isStopDepByEmp = stopDepEmployees(appId, outEa, ei, fsEa, fsDepId);
        log.info("ContactsServiceImpl.stopTagDep,isStopDepByEmp={}", isStopDepByEmp);
        //子部门是否可以全部停用
        com.facishare.open.order.contacts.proxy.api.result.Result<List<DepartmentDto>> childrenDepartmentResult =
                fsDepartmentServiceProxy.getChildrenDepartment(ei, fsDepId);
        List<DepartmentDto> departmentDtos = childrenDepartmentResult.getData();
        //是否停用部门
        boolean flag = isStopDepByEmp;

        if(CollectionUtils.isNotEmpty(departmentDtos)) {
            //子部门是否停用
            List<Integer> childrenDepIds = departmentDtos.stream()
                    .map(DepartmentDto::getDepartmentId)
                    .collect(Collectors.toList());

            Result<List<QyweixinAccountDepartmentMapping>> childrenDepMappingsResult = qyweixinAccountBindInnerService.queryDepartmentBindV21(SourceTypeEnum.QYWX.getSourceType(),
                    fsEa,
                    appId,
                    -1,
                    childrenDepIds,
                    outEa);
            if(childrenDepMappingsResult.getData().size() < childrenDepIds.size()) {
                //查到的绑定关系少于子部门数量，不停用部门
                flag = Boolean.FALSE;
            }
            List<QyweixinAccountDepartmentMapping> childrenDepMappings = childrenDepMappingsResult.getData();
            if(CollectionUtils.isNotEmpty(childrenDepMappings)) {
                for(QyweixinAccountDepartmentMapping mapping : childrenDepMappings) {
                    Boolean isStopChildrenDep = stopTagDep(appId, outEa, mapping.getOutDepartmentId(), ei, fsEa);
                    log.info("ContactsServiceImpl.stopTagDep,isStopChildrenDep={}", isStopChildrenDep);
                    if(flag && !isStopChildrenDep) {
                        flag = Boolean.FALSE;
                    }
                }
            }
        }

        if(flag) {
            //停用部门
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result
                    = fsDepartmentServiceProxy.toggle(ei + "", String.valueOf(fsDepId), Boolean.FALSE);
            if(result.isSuccess()) {
                //更新数据库状态
                batchUpdateFsDepBindStatus(fsEa,
                        Lists.newArrayList(String.valueOf(fsDepId)),
                        1,
                        appId,
                        outEa);
            }
        }

        return flag;
    }


    private Boolean stopDepEmployees(String appId, String outEa, Integer ei, String fsEa, Integer departmentId) {
        boolean flag = Boolean.TRUE;
        //查询部门下的人员
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> empListResult = fsEmployeeServiceProxy.listByDepId(ei, String.valueOf(departmentId));
        if(!empListResult.isSuccess()) {
            //查询失败就不停用人员，返回不停用部门的信息
            return Boolean.FALSE;
        }
        if(CollectionUtils.isNotEmpty(empListResult.getData())) {
            //查询人员的绑定关系
            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult =
                    qyweixinAccountBindService.fsAccountToOutAccountBatchV21(SourceTypeEnum.QYWX.getSourceType(),
                            appId,
                            -1,
                            empListResult.getData().stream()
                            .map(v -> String.format("E.%S.%S", fsEa, v.getId())).collect(Collectors.toList()),outEa);
            if(accountResult.getData().size() < empListResult.getData().size()) {
                //有的人员没有绑定关系，不停用部门
                flag = Boolean.FALSE;
            }
            if(CollectionUtils.isNotEmpty(accountResult.getData())) {
                for(QyweixinAccountEmployeeMapping mapping : accountResult.getData()) {
                    //查询人员详情
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> empInfoResult = qyWeixinManager.getUserInfo(appId, outEa, mapping.getOutAccount());
                    if(ObjectUtils.isNotEmpty(empInfoResult.getData())) {
                        if(flag) {
                            //查询到企微人员信息，不停用部门
                            flag = Boolean.FALSE;
                        }
                        continue;
                    }
                    //停用人员
                    List<String> accountList = Splitter.on(".").splitToList(mapping.getFsAccount());
                    int fsUserId = Integer.parseInt(accountList.get(2));
                    if(fsUserId==1000) {
                        if(flag) {
                            //管理员（1000）不能停用，不停用部门
                            flag = Boolean.FALSE;
                        }
                        continue;
                    }
                    com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
                            ei + "",
                            fsUserId + "",
                            false,
                            null,
                            null);
                    log.info("ContactsServiceImpl.stopDepEmployees,stopResult={}", stopResult);
                    batchUpdateFsEmpBindStatus(Lists.newArrayList(mapping.getFsAccount()),1,appId, mapping.getOutEa());
                }
            }
        }
        return flag;
    }

    @Override
    public Result<Void> onChangeContacts(ContactsXml contactsXml) {
        log.info("ContactsServiceImpl.onChangeContacts,contactsXml={}", contactsXml);
        String appId=contactsXml.getSuiteId();
        String outEa=contactsXml.getAuthCorpId();
        String changeType = contactsXml.getChangeType();
        log.info("ContactsServiceImpl.onChangeContacts,changeType={}", changeType);

        if(qyweixinAccountBindService.isManualBinding(null,outEa).getData()) {
            log.info("ContactsServiceImpl.onChangeContacts,not support available range change event");
            return new Result<>();
        }

        if ("create_user".contains(changeType)) {
            com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> outEmpModelResult = qyWeixinManager.getUserModel(appId, outEa, contactsXml.getUserID());
            if(!outEmpModelResult.isSuccess() || ObjectUtils.isEmpty(outEmpModelResult.getData())) {
                log.info("ContactsServiceImpl.onChangeContacts,outEmpModelResult={}", outEmpModelResult);
                return new Result<>();
            }
            addUser(outEa, outEmpModelResult.getData(), appId);
        } else if ("update_user".equals(changeType)) {  //修改了userid的把fsAccount传过去就不用再调用sync-provider的方法了
            String outUserId = StringUtils.isEmpty(contactsXml.getNewUserID()) ? contactsXml.getUserID() : contactsXml.getNewUserID();
            com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> outEmpModelResult = qyWeixinManager.getUserModel(appId, outEa, outUserId);
            if(!outEmpModelResult.isSuccess() || ObjectUtils.isEmpty(outEmpModelResult.getData())) {
                log.info("ContactsServiceImpl.onChangeContacts,outEmpModelResult1={}", outEmpModelResult);
                return new Result<>();
            }
            outEmpModelResult.getData().setNewId(contactsXml.getNewUserID());
            updateUser(outEa, outEmpModelResult.getData(), appId);
        } else if ("delete_user".equals(changeType)) {
            stopUser(outEa, contactsXml.getUserID(), appId);
        } else if ("create_party".contains(changeType)) {
            addDepList(appId, outEa, Lists.newArrayList(contactsXml.getId()));
        } else if ("update_party".contains(changeType)) {
            updateDepList(appId, outEa, Lists.newArrayList(contactsXml.getId()));
        } else if ("delete_party".equals(changeType)) {
            stopDepList(appId, outEa, Lists.newArrayList(contactsXml.getId()));
        } else if ("update_tag".equals(changeType)) {
            onUpdateTag(appId, outEa, ContactsUtils.getOutUpdateTagModel(contactsXml));
        }
        return new Result<>();
    }

    /**
     * 获取应用可见范围内所有不在部门内的员工列表，包括标签内不在部门内的员工列表
     *
     * @param appInfo
     * @param outEa
     * @param appId
     * @return
     */
    private List<OutEmpModel> getEmpListInAppVisibleRange(AppInfo appInfo,String outEa,String appId) {
        List<OutEmpModel> userList = new ArrayList<>();
        if(appInfo.getAllow_userinfos()!=null) {
            //获取应用可见范围内的员工信息
            if(CollectionUtils.isNotEmpty(appInfo.getAllow_userinfos().getUser())) {
                List<String> userIdList = appInfo.getAllow_userinfos().getUser().stream()
                        .map(AppInfo.AllowUserInfos.User::getUserid).collect(Collectors.toList());
                for(String userId : userIdList) {
                    com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> outEmpModelResult = qyWeixinManager.getUserModel(appId, outEa, userId);
                    log.info("ContactsServiceImpl.getUserListInAppVisibleRange,getUserModel,outEmpModelResult={}", outEmpModelResult);
                    if(!outEmpModelResult.isSuccess() || ObjectUtils.isEmpty(outEmpModelResult.getData())) continue;
                    userList.add(outEmpModelResult.getData());
                }
            }
        }

        if(appInfo.getAllow_tags()!=null) {
            //获取应用可见范围内的标签内的员工信息
            if(CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
                for(String tagId : appInfo.getAllow_tags().getTagid()) {
                    com.facishare.open.qywx.accountinner.result.Result<TagDetailModel> tagDetailModelResult = qyWeixinManager.getTagDetail(appId, outEa, tagId);
                    log.info("ContactsServiceImpl.getUserListInAppVisibleRange,getTagDetail,tagDetailModel={}", tagDetailModelResult);
                    if(!tagDetailModelResult.isSuccess() || tagDetailModelResult.getData()==null || CollectionUtils.isEmpty(tagDetailModelResult.getData().getEmpList())) continue;

                    userList.addAll(tagDetailModelResult.getData().getEmpList());
                }
            }
        }

        log.info("ContactsServiceImpl.getUserListInAppVisibleRange,userList={}", JSONObject.toJSONString(userList));
        return userList;
    }

    private void initDepAndMapping(int ei,String fsEa,String outEa,String appId,List<OutDepModel> outDepModelList) {
        for(OutDepModel outDepModel : outDepModelList) {

            //如果父部门ID为0，说明是企微的根部门
            if(StringUtils.equalsIgnoreCase(outDepModel.getParentId(),qywxRootDepId)) {
                updateRootDepAndMapping(ei,fsEa,outEa,appId,outDepModel.getId(),outDepModel.getName());
                continue;
            }

            //创建子部门并入库
            createAndSaveDepMapping(ei,
                    fsEa,
                    outEa,
                    outDepModel.getName(),
                    outDepModel.getId(),
                    outDepModel.getParentId(),
                    appId);
        }
    }

    private void initEmpAndMapping(int ei,String fsEa,String outEa,String appId,
                                   List<OutDepModel> outDepModelList,
                                   List<OutEmpModel> empList) {
        log.info("ContactsServiceImpl.initEmpAndMapping,empList={}", JSONObject.toJSONString(empList));

        List<OutEmpModel> totalEmpList = new ArrayList<>();
        totalEmpList.addAll(empList);
        //获取可见部门内的所有员工
        for(OutDepModel outDepModel : outDepModelList) {
            com.facishare.open.qywx.accountinner.result.Result<List<OutEmpModel>> depEmpListResult = qyWeixinManager.getDepEmpList(appId, outEa, outDepModel.getId());
            log.info("ContactsServiceImpl.initEmpAndMapping,depEmpList={}", depEmpListResult);
            if(!depEmpListResult.isSuccess() || CollectionUtils.isEmpty(depEmpListResult.getData())) continue;
            totalEmpList.addAll(depEmpListResult.getData());
        }
        log.info("ContactsServiceImpl.initEmpAndMapping,totalEmpList={}", JSONObject.toJSONString(totalEmpList));
        //去重
        totalEmpList = totalEmpList.stream().distinct().collect(Collectors.toList());

        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEmployeeMapping> employeeMapping
                = qyweixinAccountBindService.getQywxEmployeeMapping2(fsEa,"1000",outEa);
        if(employeeMapping!=null) {
            //去掉可见范围内的管理员，避免管理员重复创建
            totalEmpList = totalEmpList.stream()
                    .filter((outEmpModel)->!StringUtils.equalsIgnoreCase(outEmpModel.getId(),employeeMapping.getData().getOutAccount()))
                    .collect(Collectors.toList());
            log.info("ContactsServiceImpl.initEmpAndMapping,remove admin user,totalEmpList={}", JSONObject.toJSONString(totalEmpList));
        }

        for(OutEmpModel outEmpModel : totalEmpList) {
            String fsMainDepId = getFsMainDepId(outEa,outEmpModel.getDepIdList(),appId);
            createAndSaveEmpMapping(ei,fsEa,outEa,outEmpModel.getName(),outEmpModel.getId(),fsMainDepId,appId);
        }
    }

    private void updateRootDepAndMapping(int ei,String fsEa,String outEa,String appId,String outDepId,String outDepName) {
        //把纷享的全公司和企微的根部门映射，更新纷享的全公司名称为企微的根部门名称
        updateDep(ei,GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"",outDepId,outDepName, "0", null);

        QyweixinAccountDepartmentMapping rootDepMapping = getDepMappingByOutDepId(outEa,outDepId,-1,appId);
        log.info("ContactsServiceImpl.updateRootDepAndMapping,rootDepMapping={}", rootDepMapping);
        if(rootDepMapping==null) {
            saveDepMapping(fsEa,GlobalValue.ALL_COMPANY_DEPARTMENT_ID,outEa,outDepId,appId,0);
            rootDepMapping = getDepMappingByOutDepId(outEa,outDepId,-1,appId);
        }
        log.info("ContactsServiceImpl.updateRootDepAndMapping,rootDepMapping2={}", rootDepMapping);
        com.facishare.open.qywx.accountinner.result.Result<List<OutEmpModel>> rootDepEmpListResult = qyWeixinManager.getDepEmpList(appId, outEa, outDepId);
        log.info("ContactsServiceImpl.updateRootDepAndMapping,rootDepEmpList={}", rootDepEmpListResult);
        if(rootDepEmpListResult.isSuccess() && CollectionUtils.isNotEmpty(rootDepEmpListResult.getData()) && rootDepMapping!=null) {
            //如果部门绑定关系正常，创建和更新部门员工，并更新员工绑定表
            createAndUpdateDepEmpList(ei,
                    fsEa,
                    rootDepMapping.getFsDepartmentId()+"",
                    outEa,
                    appId,
                    rootDepEmpListResult.getData());
        }
    }

    private void saveDepMapping(String fsEa,Integer fsDepId,String outEa,String outDepId,String appId,int status) {
        appId = convertAppId(appId);

        QyweixinAccountDepartmentMapping mapping = new QyweixinAccountDepartmentMapping();
        mapping.setSource(SourceTypeEnum.QYWX.getSourceType());
        mapping.setFsEa(fsEa);
        mapping.setFsDepartmentId(fsDepId);
        mapping.setOutEa(outEa);
        mapping.setOutDepartmentId(outDepId);
        mapping.setAppId(appId);
        mapping.setStatus(status);

        log.info("ContactsServiceImpl.saveDepMapping,mapping={}", JSONObject.toJSONString(mapping));
        com.facishare.open.qywx.accountbind.result.Result<Boolean> result = qyweixinAccountBindService.bindAccountDepartmentMapping(Lists.newArrayList(mapping));
        log.info("ContactsServiceImpl.saveDepMapping,result={}", result);
    }

    private void saveEmpMapping(String fsEa,Integer fsUserId,String outEa,String outUserId,String appId,int status) {
        appId = convertAppId(appId);

        QyweixinAccountEmployeeMapping mapping = new QyweixinAccountEmployeeMapping();
        mapping.setSource(SourceTypeEnum.QYWX.getSourceType());
        mapping.setFsAccount("E."+fsEa+"."+fsUserId);
        mapping.setOutAccount(outUserId);
        mapping.setIsvAccount(outUserId);
        //mapping.setAppId(appId);
        mapping.setOutEa(outEa);
        mapping.setStatus(status);

        log.info("ContactsServiceImpl.saveEmpMapping,mapping={}", JSONObject.toJSONString(mapping));
        com.facishare.open.qywx.accountbind.result.Result<Boolean> result = qyweixinAccountBindService.bindAccountEmployeeMapping(Lists.newArrayList(mapping));
        log.info("ContactsServiceImpl.saveEmpMapping,result={}", result);
    }

    private Integer createAndSaveDepMapping(Integer ei,String fsEa,String outEa,String outDepName,String outDepId,
                                            String outParentDepId,String appId) {
        //查询父部门是否已绑定
        QyweixinAccountDepartmentMapping parentDepMapping = getDepMappingByOutDepId(outEa,outParentDepId,-1,appId);
        log.info("ContactsServiceImpl.createAndSaveDepMapping,outParentDepId={},parentDepMapping={}",outParentDepId, parentDepMapping);

        String fsParentDepId = null;
        if(parentDepMapping==null) {
            fsParentDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        } else {
            fsParentDepId = parentDepMapping.getFsDepartmentId()+"";
        }

        FsDeptArg arg = FsDeptArg.builder()
                .ei(ei + "")
                .name(outDepName)
                .code(outDepId)
                .status("0")
                .parentId(Lists.newArrayList(fsParentDepId))
                .build();
        log.info("ContactsServiceImpl.createAndSaveDepMapping,arg={}", JSONObject.toJSONString(arg));
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsDepartmentServiceProxy.create(arg);
        log.info("ContactsServiceImpl.createAndSaveDepMapping,result={}", result);
        if(result.isSuccess()) {
            Integer fsDepId = Integer.valueOf(result.getData().getId());
            saveDepMapping(fsEa,fsDepId,outEa,outDepId,appId,0);
            return fsDepId;
        } else {
            log.info("ContactsServiceImpl.createAndSaveDepMapping,create fs dep failed,result={}", result);
        }
        return null;
    }

    private String getFsMainDepId(String outEa,
                                  List<String> outDepIdList,
                                  String appId) {
        appId = convertAppId(appId);

        String outMainDepId = null;
        if(CollectionUtils.isNotEmpty(outDepIdList)) {
            outMainDepId = outDepIdList.get(0);
        }

        String fsMainDepId = null;
        if(StringUtils.isEmpty(outMainDepId)) {
            fsMainDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        } else {
            //查询主部门是否已绑定
            QyweixinAccountDepartmentMapping mainDepMapping = getDepMappingByOutDepId(outEa,outMainDepId,0,appId);
            log.info("ContactsServiceImpl.getFsMainDepId,mainDepMapping={}", mainDepMapping);
            if(mainDepMapping!=null) {
                fsMainDepId = mainDepMapping.getFsDepartmentId()+"";
            } else {
                //首次初始化通讯录，先创建部门，后创建员工，应该不会出现员工主部门映射查询不到的场景，为了保险起见，还是做一下兼容处理
                fsMainDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
            }
        }
        return fsMainDepId;
    }

    /**
     * 创建纷享员工并保存员工绑定关系
     * @param ei
     * @param fsEa
     * @param outEa
     * @param outUserName
     * @param outUserId
     * @param fsMainDepId
     * @param appId
     * @return
     */
    private Integer createAndSaveEmpMapping(Integer ei,
                                            String fsEa,
                                            String outEa,
                                            String outUserName,
                                            String outUserId,
                                            String fsMainDepId,
                                            String appId) {

        FsEmpArg arg = FsEmpArg.builder()
                .ei(ei + "")
                .name(outUserName)
                .fullName(outUserName)
                .mainDepartment(Lists.newArrayList(fsMainDepId))
                .status("0")
                .isActive(true)
                .build();
        log.info("ContactsServiceImpl.createAndSaveEmpMapping,arg={}", arg);
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.SALES.getRoleCode());
        log.info("ContactsServiceImpl.createAndSaveEmpMapping,result={}", result);
        if(result.isSuccess()) {
            Integer fsUserId = Integer.valueOf(result.getData().getId());
            saveEmpMapping(fsEa,fsUserId,outEa,outUserId,appId,0);
            return fsUserId;
        }
        return null;
    }

    @Override
    public Result<ContactScopeModel> getContactScopeData(String appId, String outEa) {

        return null;
    }

    @Override
    public Result<Void> addUserList(String appId, String outEa, List<String> outUserIdList) {
        log.info("ContactsServiceImpl.addUserList,appId={},outEa={},outUserIdList={}", appId,outEa,outUserIdList);
        String fsEa = getFsEa(outEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(String outUserId : outUserIdList) {
            com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> outEmpModelResult = qyWeixinManager.getUserModel(appId,outEa,outUserId);
            log.info("ContactsServiceImpl.addUserList,outEmpoutEmpModelResultModel={}", outEmpModelResult);
            if(!outEmpModelResult.isSuccess() || ObjectUtils.isEmpty(outEmpModelResult.getData())) continue;
            addOrUpdateUser(ei,fsEa,outEa,outEmpModelResult.getData(),appId);
        }
        return new Result<>();
    }

    /**
     * 添加或更新用户以及用户映射表
     * @param ei
     * @param fsEa
     * @param outEa
     * @param outEmpModel
     * @param appId
     */
    private void addOrUpdateUser(int ei,String fsEa,String outEa,OutEmpModel outEmpModel,String appId) {
        String fsMainDepId = getFsMainDepId(outEa,outEmpModel.getDepIdList(),appId);
        QyweixinAccountEmployeeMapping employeeMapping = getEmpMappingByOutUserId(outEa, outEmpModel.getId(), appId);
        log.info("ContactsServiceImpl.addOrUpdateUser,query user mapping,employeeMapping={}", employeeMapping);
        //如果员工绑定关系不存在，创建员工并更新员工映射表
        if(employeeMapping==null) {
            Integer fsUserId = createAndSaveEmpMapping(ei,fsEa,outEa,outEmpModel.getName(),outEmpModel.getId(),fsMainDepId,appId);
            log.info("ContactsServiceImpl.addOrUpdateUser,fsUserId={}", fsUserId);
        } else {
            updateEmpAndMapping(ei,employeeMapping.getFsAccount(),outEmpModel.getName(),fsMainDepId);
        }
    }

    @Override
    public Result<Void> stopUserList(String appId, String outEa, List<String> outUserIdList) {
        log.info("ContactsServiceImpl.stopUserList,appId={},outEa={},outUserIdList={}", appId,outEa,outUserIdList);
        for(String outUserId : outUserIdList) {
            stopUser(outEa,outUserId,appId);
        }
        return new Result<>();
    }

    @Override
    public Result<Void> addDepList(String appId, String outEa, List<String> outDepIdList) {
        List<OutDepModel> outDepModelList = getOutDepModelList(appId, outEa, outDepIdList);
        return addDepListInner(appId,outEa,outDepModelList);
    }

    private List<OutDepModel> getOutDepModelList(String appId, String outEa, List<String> outDepIdList) {
        List<OutDepModel> outDepModelList = new ArrayList<>();
        for(String outDepId : outDepIdList) {
            com.facishare.open.qywx.accountinner.result.Result<OutDepModel> outDepModelResult = qyWeixinManager.getDepInfo(appId,outEa,outDepId);
            log.info("ContactsServiceImpl.getOutDepModelList,outDepModel={}", outDepModelResult);
            if(!outDepModelResult.isSuccess() || ObjectUtils.isEmpty(outDepModelResult.getData())) continue;
            outDepModelList.add(outDepModelResult.getData());
        }
        return outDepModelList;
    }

    private Result<Void> addDepListInner(String appId, String outEa, List<OutDepModel> outDepModelList) {
        String fsEa = getFsEa(outEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        for(OutDepModel outDepModel : outDepModelList) {
            log.info("ContactsServiceImpl.addDepListInner,outDepModel={}", outDepModel);

            //如果父部门ID为0，说明是企微的根部门
            if(StringUtils.equalsIgnoreCase(outDepModel.getParentId(),qywxRootDepId)) {
                updateRootDepAndMapping(ei,fsEa,outEa,appId,outDepModel.getId(),outDepModel.getName());
                continue;
            }

            //获取当前部门下的员工，不包括子部门下的员工
            com.facishare.open.qywx.accountinner.result.Result<List<OutEmpModel>> depEmpListResult = qyWeixinManager.getDepEmpList(appId, outEa, outDepModel.getId());
            log.info("ContactsServiceImpl.addDepListInner,appId={},outEa={},outDepId={},depEmpList={}", appId,outEa,outDepModel.getId(),depEmpListResult);

            if(!depEmpListResult.isSuccess()) continue;
            List<OutEmpModel> depEmpList = depEmpListResult.getData();
            //查询外部部门是否绑定
            QyweixinAccountDepartmentMapping depMapping = getDepMappingByOutDepId(outEa,outDepModel.getId(),-1,appId);
            log.info("ContactsServiceImpl.addDepListInner,depMapping={}", depMapping);

            //如果部门绑定关系不存在，新增部门和员工
            if(depMapping==null) {
                //新增部门并保存部门绑定关系
                createAndSaveDepMapping(ei,fsEa,outEa,outDepModel.getName(),outDepModel.getId(),outDepModel.getParentId(),appId);
                String fsMainDepId = getFsMainDepId(outEa,Lists.newArrayList(outDepModel.getId()),appId);
                if(CollectionUtils.isNotEmpty(depEmpList)) {
                    //新增员工并保存绑定关系
                    createAndUpdateDepEmpList(ei,
                            fsEa,
                            fsMainDepId,
                            outEa,
                            appId,
                            depEmpList);
                }
            } else {
                if(depMapping.getStatus()==0) {
                    //更新部门名称
                    updateDepByOutParentDepId(ei, depMapping.getFsDepartmentId()+"", outDepModel.getId(), outDepModel.getName(), "0", outDepModel.getParentId(), outEa, appId);
                    if(CollectionUtils.isNotEmpty(depEmpList)) {
                        //如果部门绑定关系正常，创建和更新部门员工，并更新员工绑定表
                        createAndUpdateDepEmpList(ei,
                                fsEa,
                                depMapping.getFsDepartmentId()+"",
                                outEa,
                                appId,
                                depEmpList);
                    }
                } else {
                    //如果部门绑定关系被停用，启用纷享部门，并更新部门绑定状态
                    Result<Void> result = updateDepByOutParentDepId(ei, depMapping.getFsDepartmentId() + "", outDepModel.getId(), outDepModel.getName(), "0", outDepModel.getParentId(), outEa, appId);
                    log.info("ContactsServiceImpl.addDepListInner,enable dep,fsDepId={},result={}", depMapping.getFsDepartmentId(),result);
                    if(result.isSuccess()) {
                        //更新部门绑定状态
                        batchUpdateFsDepBindStatus(fsEa,
                                Lists.newArrayList(depMapping.getFsDepartmentId()+""),
                                0,
                                appId,
                                outEa);
                        if(CollectionUtils.isNotEmpty(depEmpList)) {
                            createAndUpdateDepEmpList(ei,
                                    fsEa,
                                    depMapping.getFsDepartmentId()+"",
                                    outEa,
                                    appId,
                                    depEmpList);
                        }
                    }
                }
            }
        }
        return new Result<>();
    }

    /**
     * 创建和更新部门员工，并更新员工绑定表
     */
    private void createAndUpdateDepEmpList(int ei,String fsEa,String fsMainDepId,String outEa,String appId,List<OutEmpModel> depEmpList) {
        for(OutEmpModel outEmpModel : depEmpList) {
            log.info("ContactsServiceImpl.createAndUpdateDepEmpList,outEmpModel={}", outEmpModel);
            QyweixinAccountEmployeeMapping employeeMapping = getEmpMappingByOutUserId(outEa, outEmpModel.getId(), appId);
            log.info("ContactsServiceImpl.createAndUpdateDepEmpList,employeeMapping={}", employeeMapping);

            if(employeeMapping==null) {
                //如果员工绑定关系不存在，创建纷享员工，并更新员工绑定表
                createAndSaveEmpMapping(ei,
                        fsEa,
                        outEa,
                        outEmpModel.getName(),
                        outEmpModel.getId(),
                        fsMainDepId,
                        appId);
            } else {
                updateEmpAndMapping(ei,employeeMapping.getFsAccount(),outEmpModel.getName(),fsMainDepId);
            }
        }
    }

    /**
     * 更新纷享员工信息，并更新映射表
     * 1、更新员工的时候，如果部门被停用了或者没有的话，就不更新主属部门
     * 2、一律不更新角色权限
     * 3、如果是密文的姓名，不用更新
     * @param ei
     * @param fsAccount
     */
    private void updateEmpAndMapping(int ei, String fsAccount, String outUserName,String fsMainDepId) {
        String fsEmpId = FSAccountUtil.getEmpIdFromFSAccount(fsAccount)+"";

        FsEmpArg arg = FsEmpArg.builder()
                .ei(ei + "")
                .id(fsEmpId)
//                .name(outUserName)
//                .fullName(outUserName)
                .status("0")
                //.mainDepartment(Lists.newArrayList(fsMainDepId))
                .build();
        if(StringUtils.isNotEmpty(fsMainDepId)
                && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.ALL_COMPANY_DEPARTMENT_ID.toString())
                && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.UNALLOCATED_DEPARTMENT_ID.toString())) {
            arg.setMainDepartment(Lists.newArrayList(fsMainDepId));
        }

        if(StringUtils.isNotEmpty(outUserName) && !outUserName.contains(ContactsUtils.emp_prefix)) {
            arg.setName(outUserName);
            arg.setFullName(outUserName);
        }

        LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,update,arg={}", arg);
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.update(arg,
                null,
                null);
        LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,update,result={}", result);

        //code=*********代表CRM返回的数据已作废或已删除
        if(!result.isSuccess() && result.getCode()==*********) {
            try {
                //兼容历史客户fsEmpId为字符串的场景，需要把数字ID转换成真实的基于对象的_id字段
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> detail = fsEmployeeServiceProxy.detail(ei + "",
                        fsEmpId);
                LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,detail,result={}", detail);
                if(detail.isSuccess()) {
                    String newFsEmpId = detail.getData().getId();
                    LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,detail,fsEmpId={},newFsEmpId={}", fsEmpId,newFsEmpId);
                    arg.setId(newFsEmpId);

                    result = fsEmployeeServiceProxy.update(arg,
                            null,
                            null);
                    LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,update,result2={}", result);
                }
            } catch (Exception e) {

            }
        }

        if(result.isSuccess()) {
            corpManager.updateEmployeeBindStatus(fsAccount,0);
        }
    }

    @Override
    public Result<Void> updateDepList(String appId, String outEa, List<String> outDepIdList) {
        List<OutDepModel> outDepModelList = getOutDepModelList(appId, outEa, outDepIdList);
        return updateDepListInner(appId,outEa,outDepModelList);
    }

    private Result<Void> updateDepListInner(String appId, String outEa, List<OutDepModel> outDepModelList) {
        String fsEa = getFsEa(outEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(OutDepModel outDepModel : outDepModelList) {
            log.info("ContactsServiceImpl.updateDepList,outDepModel={}",outDepModel);
            //查询部门绑定关系，不区分绑定状态
            QyweixinAccountDepartmentMapping depMapping = getDepMappingByOutDepId(outEa,outDepModel.getId(),-1,appId);
            log.info("ContactsServiceImpl.updateDepList,depMapping={}", depMapping);
            if(depMapping==null) {
                log.info("ContactsServiceImpl.updateDepList,dep not in mapping,appId={},outEa={},outDepId={},",appId,outEa,outDepModel.getId());
                continue;
            }

//            if(StringUtils.equalsIgnoreCase(outDepModel.getId(),outDepModel.getName())) {
//                //如果部门名称是密文，则停止更新部门名称
//                log.info("ContactsServiceImpl.updateDepList,depName={},是密文，停止更新部门名称",outDepModel.getName());
//                continue;
//            }
//            updateDep(ei,depMapping.getFsDepartmentId()+"",outDepModel.getId(),outDepModel.getName(), "0", null);
            updateDepByOutParentDepId(ei,depMapping.getFsDepartmentId()+"",outDepModel.getId(),outDepModel.getName(), "0",outDepModel.getParentId(),outEa,appId);
        }
        return new Result<>();
    }

    private Result<Void> updateDepByOutParentDepId(int ei,String fsDepId,String outDepId,String outDepName,String status,String outParentDepId,String outEa,String appId) {
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        //查询父部门是否已绑定或者是否已停用，父部门被停用，需要把父部门启用
        QyweixinAccountDepartmentMapping parentDepMapping = getDepMappingByOutDepId(outEa,outParentDepId,-1,appId);
        log.info("ContactsServiceImpl.updateDepByOutParentDepId,outParentDepId={},parentDepMapping={}",outParentDepId, parentDepMapping);

        String fsParentDepId = null;
        if(parentDepMapping==null) {
            fsParentDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        } else {
            if(parentDepMapping.getStatus() == 1) {
                String departmentId = parentDepMapping.getFsDepartmentId()+"";
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> updateDepResult = fsDepartmentServiceProxy.detail(ei, departmentId);
                if(updateDepResult.isSuccess() && ObjectUtils.isNotEmpty(updateDepResult.getData()) && ObjectUtils.isNotEmpty(updateDepResult.getData().get("dept_parent_path"))) {
                    List<String> departmentIdList = Splitter.on(".").splitToList(updateDepResult.getData().get("dept_parent_path").toString());
                    //过滤掉根部门和待分配部门
                    List<String> updateDepartmentIdList = departmentIdList.stream()
                            .filter(v -> (!v.equals(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"") && !v.equals(GlobalValue.UNALLOCATED_DEPARTMENT_ID+"")))
                            .collect(Collectors.toList());
                    updateDepartmentIdList.add(departmentId);
                    //避免前面将部门乱序，多次循环调用
                    //无论是企微还是纷享，子部门id不一定比父部门id大
                    List<String> updateFsDepIdList;
                    do {
                        updateFsDepIdList = new LinkedList<>(updateDepartmentIdList);
                        for(String depId : updateFsDepIdList) {
                            //查询数据库，看是否已停用
                            if(!outParentDepId.equals(depId)) {
                                QyweixinAccountDepartmentMapping departmentMapping = getDepMappingByOutDepId(outEa,depId,-1,appId);
                                if(ObjectUtils.isNotEmpty(departmentMapping) && departmentMapping.getStatus() == 0) {
                                    updateDepartmentIdList.remove(depId);
                                    continue;
                                }
                            }
                            //启用部门
                            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result
                                    = fsDepartmentServiceProxy.toggle(ei + "", depId, true);
                            if(result.isSuccess()) {
                                //更新数据库状态
                                batchUpdateFsDepBindStatus(ea,
                                        Lists.newArrayList(depId),
                                        0,
                                        appId,
                                        outEa);
                                updateDepartmentIdList.remove(depId);
                            }
                        }
                        log.info("ContactsServiceImpl.updateDepByOutParentDepId,fsDepIdList.size={},updateFsDepIdList.size={}", updateDepartmentIdList.size(), updateFsDepIdList.size());
                    } while(CollectionUtils.isNotEmpty(updateDepartmentIdList) && updateFsDepIdList.size() != updateDepartmentIdList.size());
                }
            }
            fsParentDepId = parentDepMapping.getFsDepartmentId()+"";
        }
        return this.updateDep(ei, fsDepId, outDepId, outDepName, status, fsParentDepId);
    }

    private Result<Void> updateDep(int ei,String fsDepId,String outDepId,String outDepName,String status,String fsParentDepId) {
        FsDeptArg fsDeptArg = FsDeptArg.builder()
                .ei(ei+"")
                .id(fsDepId)
                //.name(outDepId.equals(outDepName) ? null : outDepName)
                .code(outDepId)
                .status(status)
                .parentId(StringUtils.isNotEmpty(fsParentDepId) ? Lists.newArrayList(fsParentDepId) : null)
                .build();
        if(StringUtils.isNotEmpty(outDepName) && !outDepName.contains(ContactsUtils.dep_prefix)) {
            fsDeptArg.setName(outDepName);
        }
        log.info("ContactsServiceImpl.updateDep,batch update dep,fsDeptArg={}", fsDeptArg);
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateDepResult = fsDepartmentServiceProxy.update(fsDeptArg);
        log.info("ContactsServiceImpl.updateDep,batch update dep,updateDepResult={}", updateDepResult);
        if(updateDepResult.isSuccess()) {
            return new Result<>();
        } else {
            return new Result<Void>().addError(ErrorRefer.START_DEPARTMENT_ERROR.getCode(), updateDepResult.getMsg(),null);
        }
    }

    @Override
    public Result<Void> stopDepList(String appId, String outEa, List<String> outDepIdList) {
        return stopDepListInner(appId,outEa,outDepIdList,new HashMap<>());
    }

    /**
     * 批量停用部门内部方法
     * @param appId
     * @param outEa
     * @param outDepIdList
     * @param excludeFsDepEmpMap 停用部门时，不需要被停用的纷享员工map
     * @return
     */
    public Result<Void> stopDepListInner(String appId, String outEa, List<String> outDepIdList, Map<String,List<String>> excludeFsDepEmpMap) {
        log.info("ContactsServiceImpl.stopDepList,outDepIdList={},excludeFsDepEmpMap={}",outDepIdList,excludeFsDepEmpMap);
        if(CollectionUtils.isEmpty(outDepIdList)) {
            log.info("ContactsServiceImpl.stopDepList,outDepIdList is empty,return");
            return new Result<>();
        }
        String fsEa = getFsEa(outEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        List<QyweixinAccountDepartmentMapping> depMappingList = getDepMappingList(outEa,outDepIdList,0,appId);
        log.info("ContactsServiceImpl.stopDepList,depMappingList={}", depMappingList);
        List<String> fsDepIdList = depMappingList.stream()
                .map((depBind)->depBind.getFsDepartmentId()+"")
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
        log.info("ContactsServiceImpl.stopDepList,fsDepIdList={}", fsDepIdList);
        if(CollectionUtils.isEmpty(fsDepIdList)) {
            return new Result<>();
        }
        //无论是企微还是纷享，子部门id不一定比父部门id大
        List<String> updateFsDepIdList;
        do {
            updateFsDepIdList = new LinkedList<>(fsDepIdList);
            for(String fsDepId : updateFsDepIdList) {
                //1.先停用当前部门包括子部门下的所有员工，并更新员工映射表
                batchStopFsEmp(ei,fsEa,outEa,fsDepId,appId,excludeFsDepEmpMap.get(fsDepId));
                //2.再停用当前部门包括子部门，并更新部门映射表
                Result<Void> batchStopFsDepResult = batchStopFsDep(ei, fsEa, fsDepId, appId, outEa);
                if(batchStopFsDepResult.isSuccess()) {
                    fsDepIdList.remove(fsDepId);
                }
            }
            log.info("ContactsServiceImpl.stopDepList,fsDepIdList.size={},updateFsDepIdList.size={}", fsDepIdList.size(), updateFsDepIdList.size());
        } while(CollectionUtils.isNotEmpty(fsDepIdList) && updateFsDepIdList.size() != fsDepIdList.size());

        return new Result<>();
    }

    @Override
    public Result<Void> addUser(String outEa, OutEmpModel outEmpModel, String appId) {
        String fsEa = getFsEa(outEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        String fsMainDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        //查询用户的部门的绑定状态
        List<QyweixinAccountDepartmentMapping> depMappingList = getDepMappingList(outEa,outEmpModel.getDepIdList(),0,appId);
        log.info("ContactsServiceImpl.addUser,depMappingList={}", depMappingList);

        if(CollectionUtils.isNotEmpty(depMappingList)) {
            fsMainDepId = depMappingList.get(0).getFsDepartmentId()+"";
        }
        Integer fsUserId = createAndSaveEmpMapping(ei, fsEa, outEa, outEmpModel.getName(), outEmpModel.getId(), fsMainDepId, appId);
        log.info("ContactsServiceImpl.addUser,create fs emp,fsUserId={}", fsUserId);
        if(fsUserId==null) {
            return Result.newInstance(ErrorRefer.FS_EMP_CREATE_FAILED);
        }
        return new Result<>();
    }

    //一对多场景，默认获取第一个绑定的纷享企业，如果客户不想
    private String getFsEa(String outEa) {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseBind = qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), outEa);
        log.info("ContactsServiceImpl.getFsEa,query enterprise mapping,enterpriseBind={}", enterpriseBind);
        if(CollectionUtils.isNotEmpty(enterpriseBind.getData())) {
            if(enterpriseBind.getData().size()==1) {
                return enterpriseBind.getData().get(0).getFsEa();
            }
            if(enterpriseBind.getData().size()>1) {
                log.info("ContactsServiceImpl.getFsEa,企微一对多场景，可能有问题");
                return enterpriseBind.getData().get(0).getFsEa();
            }
        }
        return null;
    }

    @Override
    public Result<Void> updateUser(String outEa, OutEmpModel outEmpModel, String appId) {
        log.info("ContactsServiceImpl.updateUserInfo,appId={},outEa={},outEmpModel={}",appId,outEa,outEmpModel);
        QyweixinAccountEmployeeMapping employeeMapping = getEmpMappingByOutUserId(outEa, outEmpModel.getId(), appId);
        log.info("ContactsServiceImpl.updateUserInfo,employeeMapping={}",employeeMapping);
        if(employeeMapping==null) {
            //重新尝试绑定
            return addUser(outEa, outEmpModel, appId);
        }

        String fsEa = FSAccountUtil.getFsEaFromFSAccount(employeeMapping.getFsAccount());
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        Integer fsUserId = FSAccountUtil.getEmpIdFromFSAccount(employeeMapping.getFsAccount());

        //如果客户修改了用户ID，更新用户ID
        if(StringUtils.isNotEmpty(outEmpModel.getNewId()) && StringUtils.equalsIgnoreCase(outEmpModel.getId(),outEmpModel.getNewId())==false) {
            qyweixinAccountBindService.updateByNewOutAccount(outEmpModel.getNewId(),
                    outEmpModel.getId(), appId, outEa);
        }

        if(StringUtils.equalsIgnoreCase(outEmpModel.getId(),outEmpModel.getName())==false) {
            String fsMainDepId = null;
            if(CollectionUtils.isNotEmpty(outEmpModel.getDepIdList())) {
                List<QyweixinAccountDepartmentMapping> depMappingList = getDepMappingList(outEa,outEmpModel.getDepIdList(),0,appId);
                log.info("ContactsServiceImpl.updateUserInfo,depMappingList={}", depMappingList);
                if(CollectionUtils.isNotEmpty(depMappingList)) {
                    fsMainDepId = depMappingList.get(0).getFsDepartmentId()+"";
                }
            }

            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei + "")
                    .id(fsUserId+"")
                    .status("0")
//                    .name(outEmpModel.getName())
//                    .fullName(outEmpModel.getName())
                    .build();
            if(StringUtils.isNotEmpty(fsMainDepId)
                    && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.ALL_COMPANY_DEPARTMENT_ID.toString())
                    && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.UNALLOCATED_DEPARTMENT_ID.toString())) {
                arg.setMainDepartment(Lists.newArrayList(fsMainDepId));
            }

            if(StringUtils.isNotEmpty(outEmpModel.getName()) && !outEmpModel.getName().contains(ContactsUtils.emp_prefix)) {
                arg.setName(outEmpModel.getName());
                arg.setFullName(outEmpModel.getName());
            }

            log.info("ContactsServiceImpl.updateUserInfo,update emp, arg={}", arg);
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateEmpResult = fsEmployeeServiceProxy.update(arg,
                    null,
                    null);
            log.info("ContactsServiceImpl.updateUserInfo,update emp, result={}", updateEmpResult);
        }

        return new Result<>();
    }

    @Override
    public Result<Void> stopUser(String outEa, String outUserId,String appId) {
        log.info("ContactsServiceImpl.stopUser,outEa={},outUserId={},appId={}", outEa, outUserId, appId);
        QyweixinAccountEmployeeMapping employeeMapping = getEmpMappingByOutUserId(outEa, outUserId, appId);
        if(employeeMapping==null) {
            return Result.newInstance(ErrorRefer.QYWX_EMP_MAPPING_NOT_EXIST);
        }
        String fsEa = FSAccountUtil.getFsEaFromFSAccount(employeeMapping.getFsAccount());

        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        Integer fsUserId = FSAccountUtil.getEmpIdFromFSAccount(employeeMapping.getFsAccount());
        if(fsUserId==1000) {
            log.info("ContactsServiceImpl.stopUser,fsUserId={},纷享企业管理员不能停用", fsUserId);
            return Result.newInstance(ErrorRefer.FS_ADMIN_CANNOT_STOP);
        }
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
                ei + "",
                fsUserId + "",
                false,
                null,
                null);
        log.info("ContactsServiceImpl.stopUser,stopResult={}", stopResult);
        batchUpdateFsEmpBindStatus(Lists.newArrayList(employeeMapping.getFsAccount()),1,appId,outEa);

        return new Result<>();
    }

    private QyweixinAccountEmployeeMapping getEmpMappingByOutUserId(String outEa, String outUserId,String appId) {
        appId = convertAppId(appId);

        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result
                = qyweixinAccountBindService.employeeToOutAccount(SourceTypeEnum.QYWX.getSourceType(),
                outEa,
                appId,
                Lists.newArrayList(outUserId));
        log.info("ContactsServiceImpl.stopUser,query emp mapping,result={}", result);
        if(CollectionUtils.isEmpty(result.getData()))
            return null;

        QyweixinAccountEmployeeMapping employeeMapping = result.getData().get(0);
        return employeeMapping;
    }

    private QyweixinAccountDepartmentMapping getDepMappingByOutDepId(String outEa,String outDepId,int status,String appId) {
        appId = convertAppId(appId);

        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountDepartmentMapping>> departmentBind = null;
        //查询企微的根部门是否已经和纷享的根部门绑定
        departmentBind = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(),
                outEa,
                appId,
                status,
                Lists.newArrayList(outDepId));
        if(CollectionUtils.isNotEmpty(departmentBind.getData())) {
            return departmentBind.getData().get(0);
        }
        return null;
    }

    private List<QyweixinAccountDepartmentMapping> getDepMappingList(String outEa,List<String> outDepIdList,int status,String appId) {
        appId = convertAppId(appId);

        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountDepartmentMapping>> departmentBind = null;
        //查询企微的根部门是否已经和纷享的根部门绑定
        departmentBind = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(),
                outEa,
                appId,
                status,
                outDepIdList);
        if(CollectionUtils.isNotEmpty(departmentBind.getData())) return departmentBind.getData();
        return new ArrayList<>();
    }

    private void batchStopFsEmp(int ei, String fsEa, String outEa, String fsDepId, String appId,List<String> excludeFsUserIdList) {
        log.info("ContactsServiceImpl.batchStopFsEmp,fsEa={},fsDepId={},excludeFsUserIdList={}", fsEa,fsDepId,excludeFsUserIdList);
        com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> result
                = fsContactsServiceProxy.batchStopFsEmp(ei, fsEa, fsDepId,excludeFsUserIdList);
        log.info("ContactsServiceImpl.batchStopFsEmp,fsEa={},fsDepId={},result={}", fsEa,fsDepId,result);
        //批量更新员工绑定状态为停用
        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            List<String> fsAccountList = result.getData().stream()
                    .map((fsUserId)->"E."+fsEa+"."+fsUserId).collect(Collectors.toList());
            log.info("ContactsServiceImpl.batchStopFsEmp,fsAccountList={}", fsAccountList);
            batchUpdateFsEmpBindStatus(fsAccountList,1,appId,outEa);
        }
    }

    private Result<Void> batchStopFsDep(int ei, String fsEa, String fsDepId, String appId, String outEa) {
        if(StringUtils.equalsIgnoreCase(fsDepId,GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"")
                || StringUtils.equalsIgnoreCase(fsDepId,GlobalValue.UNALLOCATED_DEPARTMENT_ID+"")) {
            //只需要停用中间表的绑定关系
            log.info("ContactsServiceImpl.batchStopFsDep,stop dept bind,ei={},fsDepId={}", ei, fsDepId);
            batchUpdateFsDepBindStatus(fsEa, Lists.newArrayList(fsDepId), 1, appId, outEa);
            return new Result<>();
        }

        com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> result
                = fsContactsServiceProxy.batchStopFsDep(ei, fsEa, fsDepId);
        log.info("ContactsServiceImpl.batchStopFsDep,result={}", result);
        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            batchUpdateFsDepBindStatus(fsEa,result.getData(),1,appId,outEa);
        } else {
            return Result.newInstance(ErrorRefer.STOP_DEPARTMENT_ERROR);
        }
        return new Result<>();
    }

    private void batchUpdateFsEmpBindStatus(List<String> fsAccountList,
                                            int status,
                                            String appId,
                                            String outEa) {
        appId = convertAppId(appId);

        qyweixinAccountBindService.batchUpdateFsEmpBindStatus2(fsAccountList, status, appId, outEa);
    }

    private void batchUpdateFsDepBindStatus(String fsEa,List<String> fsDepIdList,int status,String appId, String outEa) {
        appId = convertAppId(appId);

        qyweixinAccountBindService.batchUpdateFsDepBindStatus2(fsEa,fsDepIdList,status,appId,outEa);
    }

    /**
     * 把代开发应用的ID转换成非代开发应用的ID
     * @param appId
     * @return
     */
    private String convertAppId(String appId) {
        appId = StringUtils.equalsIgnoreCase(appId,repAppId) ? ConfigCenter.crmAppId:appId;
        return appId;
    }

    /**
     * 选择的人员和人员所属的部门处于可见范围时，应去掉这个人员
     */
    private List<OutEmpModel> removeEmployeeList(List<OutEmpModel> empList, AppInfo appInfo, String appId, String outEa) {
        Set<String> employeeInfoSet = new HashSet<>();
        //获取标签下单独选择的人员列表，是没有部门返回的
        if(ObjectUtils.isNotEmpty(appInfo.getAllow_tags()) && CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
            //调用部门下的人员列表，是因为人员详情接口，即使部门在标签下，仍然获取不到该人员的所属部门
            Set<String> departmentIdList = new HashSet<>(appInfo.getAllow_partys().getPartyid());
            for(String tagId : appInfo.getAllow_tags().getTagid()) {
                com.facishare.open.qywx.accountinner.result.Result<TagDetailModel> tagDetailModelResult = qyWeixinManager.getTagDetail(appId, outEa, tagId);
                log.info("ContactsServiceImpl.removeEmployeeList,getTagDetail,tagDetailModel={}", tagDetailModelResult);
                if(!tagDetailModelResult.isSuccess() || tagDetailModelResult.getData()==null || CollectionUtils.isEmpty(tagDetailModelResult.getData().getEmpList())) continue;
                departmentIdList.addAll(tagDetailModelResult.getData().getDepList());
            }
            log.info("ContactsServiceImpl.removeEmployeeList,departmentIdList={}", departmentIdList);
            for(String departmentId : departmentIdList) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeList = qyWeixinManager.getDepartmentEmployeeList(appId, outEa, departmentId);
                if(!departmentEmployeeList.isSuccess() || ObjectUtils.isEmpty(departmentEmployeeList.getData()) || CollectionUtils.isEmpty(departmentEmployeeList.getData().getUserlist())) {
                    continue;
                }
                Set<String> departmentEmployeeSet = departmentEmployeeList.getData().getUserlist().stream()
                        .map(QyweixinUserDetailInfoRsp :: getUserid)
                        .collect(Collectors.toSet());
                if(CollectionUtils.isNotEmpty(departmentEmployeeSet)) {
                    employeeInfoSet.addAll(departmentEmployeeSet);
                }
            }
        }
        log.info("ContactsServiceImpl.removeEmployeeList,employeeInfoSet={}", employeeInfoSet);
        //如果人员有所属部门或者在部门下，证明已处理过,去掉
        List<OutEmpModel> updateEmpList = empList.stream()
                .filter(v -> CollectionUtils.isEmpty(v.getDepIdList()))
                .filter(v -> !employeeInfoSet.contains(v.getId()))
                .collect(Collectors.toList());
        return updateEmpList;
    }

    @Override
    public Result<Void> addUser(String appId, String outEa, String outUserId) {
        log.info("ContactsServiceImpl.addUser,appId={},outEa={},outUserId={}", appId,outEa,outUserId);
        String fsEa = getFsEa(outEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> outEmpModelResult = qyWeixinManager.getUserModel(appId,outEa,outUserId);
        log.info("ContactsServiceImpl.addUser,outEmpModelResultModel={}", outEmpModelResult);
        if(!outEmpModelResult.isSuccess()) {
            if(outEmpModelResult.getCode().equalsIgnoreCase("60011")) {
                return Result.newInstance(ErrorRefer.QYWX_NOT_PRIVILEGE_ERROR);
            }
            return new Result<>(outEmpModelResult.getCode(), outEmpModelResult.getMsg(),null);
        }

        QyweixinAccountEmployeeMapping employeeMapping = getEmpMappingByOutUserId(outEa, outEmpModelResult.getData().getId(), appId);
        log.info("ContactsServiceImpl.addUser,query user mapping,employeeMapping={}", employeeMapping);
        //如果员工绑定关系不存在，创建员工并更新员工映射表
        if(employeeMapping==null) {
            String fsMainDepId = getFsMainDepId(outEa,outEmpModelResult.getData().getDepIdList(),appId);
            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei + "")
                    .name(outEmpModelResult.getData().getName())
                    .fullName(outEmpModelResult.getData().getName())
                    .mainDepartment(Lists.newArrayList(fsMainDepId))
                    .status("0")
                    .isActive(true)
                    .build();
            log.info("ContactsServiceImpl.addUser,arg={}", arg);
            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
            log.info("ContactsServiceImpl.addUser,result={}", result);
            if(result.isSuccess()) {
                Integer fsUserId = Integer.valueOf(result.getData().getId());
                saveEmpMapping(fsEa,fsUserId,outEa,outUserId,appId,0);
                return new Result<>();
            }
            if(result.getCode() == 44) {
                return Result.newInstance(ErrorRefer.CRM_USER_UPPER_LIMIT_INITED);
            }
            return new Result<>(ErrorRefer.FS_EMP_CREATE_FAILED.getCode(), result.getMsg(),null);
        }
        return new Result<>();

    }
}
