package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取单个部门详情接口返回值
 * <AUTHOR>
 * @date ********
 */
@Data
public class QyweixinDepartmentInfoRsp implements Serializable {
    private Integer errcode;
    private String errmsg;
    QyweixinDepartmentRsp department;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
