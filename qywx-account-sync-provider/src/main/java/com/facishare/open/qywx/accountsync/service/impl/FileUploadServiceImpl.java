package com.facishare.open.qywx.accountsync.service.impl;

import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.a.AGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.arg.FileUploadArg;
import com.facishare.open.qywx.accountsync.dao.QyweixinFileDao;
import com.facishare.open.qywx.accountsync.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.model.FileUploadModel;
import com.facishare.open.qywx.accountsync.model.GetFileSourceModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinFileBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.result.UploadResult;
import com.facishare.open.qywx.accountsync.service.FileUploadService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 文件上传服务类
 *
 * 上传的媒体文件限制
 * 所有文件size必须大于5个字节
 *
 * 图片（image）：10MB，支持JPG,PNG格式
 * 语音（voice） ：2MB，播放长度不超过60s，仅支持AMR格式
 * 视频（video） ：10MB，支持MP4格式
 * 普通文件（file）：20MB
 *
 * <AUTHOR>
 * @date ********
 */

@Service("fileUploadService")
@Slf4j
public class FileUploadServiceImpl implements FileUploadService {
    @Resource
    private NFileStorageService nFileStorageService;
    @Resource
    private QYWeixinManager qyWeixinManager;
    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Resource
    private QyweixinFileDao qyweixinFileDao;
    @Resource
    private AFileStorageService aFileStorageService;

    private static final Long max_file_size = 20 * 1024 * 1024L;
    private static final Long max_video_file_size = 10 * 1024 * 1024L;
    private static final Long max_image_file_size = 5 * 1024 * 1024L;
    private static final Long max_voice_file_size = 2 * 1024 * 1024L;

    private static final Integer SYS_USER = -10000;


    private ConcurrentLinkedQueue<UploadTaskModel> uploadQueue = new ConcurrentLinkedQueue<>();
    private ExecutorService executorService = Executors.newSingleThreadExecutor();
    private Runnable task;

    private void uploadTask() {
        while (uploadQueue.peek()!=null) {
            UploadTaskModel model = uploadQueue.poll();
            try {
                Result<Void> result = uploadNow(model);
                log.info("FileUploadServiceImpl.uploadTask,result={}",result);
                if(!result.isSuccess()) {
                    updateUploadTask(model.getFsEa(),model.getNpath(),model.getUrl(),null,null,null,result.getErrorMsg());
                }
            } catch (Exception e) {
                log.info("FileUploadServiceImpl.uploadTask,exception={}",e.getMessage());
                updateUploadTask(model.getFsEa(),model.getNpath(),model.getUrl(),null,null,null,e.getMessage());
            }
        }
        task=null;
        log.info("FileUploadServiceImpl.uploadTask,all upload task finished");
    }

    @Override
    public Result<Void> upload(FileUploadArg arg) {
        log.info("FileUploadServiceImpl.upload,arg={}",arg);
        Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindInnerService.getEnterpriseMapping(arg.getFsEa());
        log.info("FileUploadServiceImpl.upload,enterpriseMappingResult={}",enterpriseMappingResult);
        if(enterpriseMappingResult.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        for(FileUploadArg.FileItem fileItem : arg.getFileList()) {
            if(StringUtils.isEmpty(fileItem.getNpath()) && StringUtils.isEmpty(fileItem.getUrl())) continue;
            QyweixinFileBo entity = new QyweixinFileBo();
            entity.setFsEa(arg.getFsEa());
            //npath和url都传过来的时候，只选择npath查询
            if(StringUtils.isNotEmpty(fileItem.getNpath())) {
                entity.setNpath(fileItem.getNpath());
            } else {
                entity.setUrl(fileItem.getUrl());
            }
            List<QyweixinFileBo> entityList = qyweixinFileDao.findByEntity(entity);
            //1.检查是否存在npath记录，如果存在并且超过3天，刚删除npath记录
            if(CollectionUtils.isNotEmpty(entityList)) {
                QyweixinFileBo qyweixinFileBo = entityList.get(0);

                DateTime dateTime = DateTime.now().minusDays(3);
                if(qyweixinFileBo.getCreateAt()==null || (dateTime.toDate().getTime()-qyweixinFileBo.getCreateAt().getTime()>0)) {
                    int delete = qyweixinFileDao.deleteById(qyweixinFileBo.getId());
                    log.info("FileUploadServiceImpl.upload,delete={}",delete);
                } else {
                    continue;
                }
            }

            //2.添加上传任务
            uploadQueue.add(new UploadTaskModel(arg.getFsEa(),fileItem.getNpath(),fileItem.getUrl(),fileItem.getFileName(),fileItem.getEmployId(),fileItem.getBusiness(),fileItem.getFileSecurityGroup(),arg.getFileType()));
            insertUploadTask(arg.getFsEa(),fileItem.getNpath(),fileItem.getUrl(),arg.getFileType(),null,null,null,null);
        }

        //3.启用上传任务
        if(task==null && CollectionUtils.isNotEmpty(uploadQueue)) {
            task = ()->uploadTask();
            executorService.submit(task);
        }
        return new Result<>();
    }

    private Result<Void> uploadNow(UploadTaskModel model) {
        Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindInnerService.getEnterpriseMapping(model.getFsEa());
        log.info("FileUploadServiceImpl.uploadNow,enterpriseMappingResult={}",enterpriseMappingResult);
        if(enterpriseMappingResult.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingResult.getData();
        Result<GetFileSourceModel> getFileSourceModelResult;
        if(StringUtils.isNotEmpty(model.getNpath())) {
            if(model.getNpath().startsWith("A_") || model.getNpath().startsWith("TA_")) {
                getFileSourceModelResult = getApathSource(model, enterpriseMapping);
            } else {
                getFileSourceModelResult = getNpathSource(model, enterpriseMapping);
            }
        } else {
            getFileSourceModelResult = getUrlSource(model, enterpriseMapping);
        }

        if(!getFileSourceModelResult.isSuccess()) {
            return new Result<>(getFileSourceModelResult.getErrorCode(), getFileSourceModelResult.getErrorMsg(),null);
        }
        GetFileSourceModel getFileSourceModel = getFileSourceModelResult.getData();

        com.facishare.open.qywx.accountinner.result.Result<UploadResult> uploadResult = qyWeixinManager.uploadFile(getFileSourceModel.getOutEa(),
                getFileSourceModel.getFileName(),
                model.getFileType(),
                getFileSourceModel.getData());
        log.info("FileUploadServiceImpl.upload,uploadResult={}",uploadResult);
        if(!uploadResult.isSuccess() || ObjectUtils.isEmpty(uploadResult.getData())) {
            return new Result<>(ErrorRefer.FILE_UPLOAD_FAILED.getCode(),uploadResult.getMsg(),null);
        }

        updateUploadTask(model.getFsEa(),model.getNpath(),model.getUrl(),getFileSourceModel.getFileSize(),uploadResult.getData().getMediaId(),uploadResult.getData().getCreatedAt(),null);
        return new Result<>();
    }

    private void insertUploadTask(String fsEa, String npath, String url, FileUploadArg.FileTypeEnum fileType, Long fileSize, String mediaId, Long createAt, String errMsg) {
        QyweixinFileBo qyweixinFileBo = new QyweixinFileBo();
        qyweixinFileBo.setFsEa(fsEa);
        //npath和url都传过来的时候，只选择npath查询
        if(StringUtils.isNotEmpty(npath)) {
            qyweixinFileBo.setNpath(npath);
        } else {
            qyweixinFileBo.setUrl(url);
        }
        qyweixinFileBo.setFileSize(fileSize);
        qyweixinFileBo.setType(fileType.name());
        qyweixinFileBo.setMediaId(mediaId);
        if(createAt!=null) {
            qyweixinFileBo.setCreateAt(new Timestamp(createAt * 1000));
        }
        qyweixinFileBo.setErrMsg(errMsg);
        int count = qyweixinFileDao.insert(qyweixinFileBo);
        log.info("FileUploadServiceImpl.upload,insert,count={}",count);
    }

    private void updateUploadTask(String fsEa,String npath,String url,Long fileSize,String mediaId,Long createAt,String errMsg) {
        QyweixinFileBo qyweixinFileBo = new QyweixinFileBo();
        qyweixinFileBo.setFsEa(fsEa);
        //npath和url都传过来的时候，只选择npath查询
        if(StringUtils.isNotEmpty(npath)) {
            qyweixinFileBo.setNpath(npath);
        } else {
            qyweixinFileBo.setUrl(url);
        }
        List<QyweixinFileBo> entityList = qyweixinFileDao.findByEntity(qyweixinFileBo);
        log.info("FileUploadServiceImpl.upload,entityList={}",entityList);
        if(CollectionUtils.isNotEmpty(entityList)) {
            QyweixinFileBo entity = entityList.get(0);
            entity.setFileSize(fileSize);
            entity.setMediaId(mediaId);
            if(createAt != null) {
                entity.setCreateAt(new Timestamp(createAt * 1000));
            }
            entity.setErrMsg(errMsg);
            int count = qyweixinFileDao.update(entity);
            log.info("FileUploadServiceImpl.upload,update,count={}",count);
        }
    }

    private Result<GetFileSourceModel> getNpathSource(UploadTaskModel model, QyweixinAccountEnterpriseMapping enterpriseMapping) {
        //npath上传路径
        GetFileSourceModel getFileSourceModel = new GetFileSourceModel();
        getFileSourceModel.setOutEa(enterpriseMapping.getOutEa());
        getFileSourceModel.setFileType(model.getFileType());
        NGetFileMetaData.Result fileMetaData = getFileMetaData(model.getFsEa(), model.getNpath());
        log.info("FileUploadServiceImpl.getNpathSource,fileMetaData={}",fileMetaData);
        if(fileMetaData==null) {
            return Result.newInstance(ErrorRefer.GET_FILE_META_DATA_FAILED);
        }
        if(StringUtils.isNotEmpty(model.getNPathFileName())) {
            fileMetaData.setOriginName(model.getNPathFileName());
            fileMetaData.setFileName(model.getNPathFileName());
        }

        if(model.getFileType() == FileUploadArg.FileTypeEnum.file && fileMetaData.getSize() > max_file_size) {
            return Result.newInstance(ErrorRefer.FILE_OVERSIZE);
        }
        if(model.getFileType() == FileUploadArg.FileTypeEnum.video && fileMetaData.getSize() > max_video_file_size) {
            return Result.newInstance(ErrorRefer.VIDEO_OVERSIZE);
        }
        if(model.getFileType() == FileUploadArg.FileTypeEnum.image && fileMetaData.getSize() > max_image_file_size) {
            return Result.newInstance(ErrorRefer.IMAGE_OVERSIZE);
        }
        if(model.getFileType() == FileUploadArg.FileTypeEnum.voice && fileMetaData.getSize() > max_voice_file_size) {
            return Result.newInstance(ErrorRefer.VOICE_OVERSIZE);
        }
        getFileSourceModel.setFileSize(fileMetaData.getSize());

        byte[] data = downloadFile(model.getFsEa(), model.getNpath(),fileMetaData.getSecurityGroup());
        if(data==null) {
            return Result.newInstance(ErrorRefer.FILE_DOWNLOAD_FAILED);
        }
        getFileSourceModel.setData(data);
        String fileName = StringUtils.isNotEmpty(fileMetaData.getOriginName()) ? fileMetaData.getOriginName() : fileMetaData.getFileName() + "." + fileMetaData.getExtensionName().replace(".","");
        log.info("FileUploadServiceImpl.getNpathSource,fileName={}",fileName);
        getFileSourceModel.setFileName(fileName);
        return new Result<>(getFileSourceModel);
    }

    private Result<GetFileSourceModel> getApathSource(UploadTaskModel model, QyweixinAccountEnterpriseMapping enterpriseMapping) {
        if(model.getEmployId() == null) {
            model.setEmployId(SYS_USER);
        }
        //apath上传路径
        GetFileSourceModel getFileSourceModel = new GetFileSourceModel();
        getFileSourceModel.setOutEa(enterpriseMapping.getOutEa());
        getFileSourceModel.setFileType(model.getFileType());
        AGetFileMetaData.Result fileMetaData = getFileMetaDataByApath(model.getFsEa(), model.getEmployId(), model.getNpath(), model.getBusiness(), model.getFileSecurityGroup());
        log.info("FileUploadServiceImpl.getApathSource,fileMetaData={}",fileMetaData);
        if(fileMetaData==null) {
            return Result.newInstance(ErrorRefer.GET_FILE_META_DATA_FAILED);
        }

        if(model.getFileType() == FileUploadArg.FileTypeEnum.file && fileMetaData.getSize() > max_file_size) {
            return Result.newInstance(ErrorRefer.FILE_OVERSIZE);
        }
        if(model.getFileType() == FileUploadArg.FileTypeEnum.video && fileMetaData.getSize() > max_video_file_size) {
            return Result.newInstance(ErrorRefer.VIDEO_OVERSIZE);
        }
        if(model.getFileType() == FileUploadArg.FileTypeEnum.image && fileMetaData.getSize() > max_image_file_size) {
            return Result.newInstance(ErrorRefer.IMAGE_OVERSIZE);
        }
        if(model.getFileType() == FileUploadArg.FileTypeEnum.voice && fileMetaData.getSize() > max_voice_file_size) {
            return Result.newInstance(ErrorRefer.VOICE_OVERSIZE);
        }
        getFileSourceModel.setFileSize(fileMetaData.getSize());

        byte[] data = downloadFileByApath(model.getFsEa(), model.getEmployId(), model.getNpath(), model.getBusiness(), model.getFileSecurityGroup());
        if(data==null) {
            return Result.newInstance(ErrorRefer.FILE_DOWNLOAD_FAILED);
        }
        getFileSourceModel.setData(data);
        String fileName = StringUtils.isNotEmpty(model.getNPathFileName()) ? model.getNPathFileName() : String.valueOf(System.currentTimeMillis());
        log.info("FileUploadServiceImpl.getApathSource,fileName={}",fileName);
        getFileSourceModel.setFileName(fileName);
        return new Result<>(getFileSourceModel);
    }

    private Result<GetFileSourceModel> getUrlSource(UploadTaskModel model, QyweixinAccountEnterpriseMapping enterpriseMapping) {
        try {
            GetFileSourceModel getFileSourceModel = new GetFileSourceModel();
            getFileSourceModel.setOutEa(enterpriseMapping.getOutEa());
            getFileSourceModel.setFileType(model.getFileType());
            URL url = new URL(model.getUrl());
            InputStream inputStream = url.openStream();
            URLConnection connection = url.openConnection();
            connection.connect();
            String contentLength = connection.getHeaderField("Content-Length");
            long fileSize = Long.parseLong(contentLength);
            if(model.getFileType() == FileUploadArg.FileTypeEnum.file && fileSize > max_file_size) {
                return Result.newInstance(ErrorRefer.FILE_OVERSIZE);
            }
            if(model.getFileType() == FileUploadArg.FileTypeEnum.video && fileSize > max_video_file_size) {
                return Result.newInstance(ErrorRefer.VIDEO_OVERSIZE);
            }
            if(model.getFileType() == FileUploadArg.FileTypeEnum.image && fileSize > max_image_file_size) {
                return Result.newInstance(ErrorRefer.IMAGE_OVERSIZE);
            }
            if(model.getFileType() == FileUploadArg.FileTypeEnum.voice && fileSize > max_voice_file_size) {
                return Result.newInstance(ErrorRefer.VOICE_OVERSIZE);
            }
            getFileSourceModel.setFileSize(fileSize);
            // 创建一个 byte 数组输出流
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
            // 读取数据到缓冲区，然后写入到 byte 数组输出流中
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }

            // 将 byte 数组输出流中的数据写入到 byte 数组中
            byte[] data = byteArrayOutputStream.toByteArray();

            // 关闭输入流和 byte 数组输出流
            inputStream.close();
            byteArrayOutputStream.close();
            bufferedInputStream.close();
            if(ObjectUtils.isEmpty(data)) {
                return Result.newInstance(ErrorRefer.FILE_DOWNLOAD_FAILED);
            }
            getFileSourceModel.setData(data);
            String fileName = StringUtils.isNotEmpty(model.getNPathFileName()) ? model.getNPathFileName() : String.valueOf(System.currentTimeMillis());
            log.info("FileUploadServiceImpl.getUrlSource,fileName={}",fileName);
            getFileSourceModel.setFileName(fileName);
            return new Result<>(getFileSourceModel);
        } catch (IOException e) {
            log.info("FileUploadServiceImpl.getUrlSource,exception={}", e.getMessage());
            return new Result<>(ErrorRefer.FILE_UPLOAD_FAILED.getCode(), e.getMessage(),null);
        }
    }

    @Override
    public Result<FileUploadModel> query(String fsEa, String npath, String url) {
        QyweixinFileBo entity = new QyweixinFileBo();
        entity.setFsEa(fsEa);
        //npath和url都传过来的时候，只选择npath查询
        if(StringUtils.isNotEmpty(npath)) {
            entity.setNpath(npath);
        } else {
            entity.setUrl(url);
        }
        List<QyweixinFileBo> entityList = qyweixinFileDao.findByEntity(entity);

        FileUploadModel model = new FileUploadModel();
        model.setFsEa(fsEa);
        if(StringUtils.isNotEmpty(npath)) {
            model.setNpath(npath);
        } else {
            model.setUrl(url);
        }
        if(CollectionUtils.isNotEmpty(entityList)) {
            model.setMediaId(entityList.get(0).getMediaId());
            model.setErrMsg(entityList.get(0).getErrMsg());
        }
        return new Result<>(model);
    }

    private NGetFileMetaData.Result getFileMetaData(String fsEa,String npath) {
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
        arg.setEa(fsEa);
        arg.setFileName(npath);
        NGetFileMetaData.Result result = nFileStorageService.nGetFileMetaData(arg, fsEa);
        return result;
    }

    private AGetFileMetaData.Result getFileMetaDataByApath(String fsEa, Integer employId, String apath, String business, String fileSecurityGroup) {
        AGetFileMetaData.Arg arg = new AGetFileMetaData.Arg();
        arg.setBusiness(business);
        arg.setFileName(apath);
        arg.setFileSecurityGroup(fileSecurityGroup);
        User user = new User();
        user.setEnterpriseAccount(fsEa);
        user.setEmployId(employId);
        arg.setUser(user);
        AGetFileMetaData.Result result = aFileStorageService.getFileMetaData(arg);
        return result;
    }

    private byte[] downloadFile(String fsEa, String npath,String securityGroup) {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setEa(fsEa);
        arg.setnPath(npath);
        arg.setDownloadSecurityGroup(securityGroup);
        NDownloadFile.Result result = nFileStorageService.nDownloadFile(arg, fsEa);
        if(result==null) return null;
        return result.getData();
    }

    private byte[] downloadFileByApath(String fsEa, Integer employId, String apath, String business, String fileSecurityGroup) {
        ADownloadFile.Arg arg = new ADownloadFile.Arg();
        arg.setaPath(apath);
        arg.setFileSecurityGroup(fileSecurityGroup);
        arg.setBusiness(business);
        User user = new User();
        user.setEmployId(employId);
        user.setEnterpriseAccount(fsEa);
        arg.setUser(user);
        ADownloadFile.Result result = aFileStorageService.downloadFile(arg);
        if(result==null) return null;
        return result.getData();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UploadTaskModel implements Serializable {
        private String fsEa;
        private String npath;
        private String url;
        private String nPathFileName;
        private Integer employId;
        private String business;
        private String fileSecurityGroup;
        private FileUploadArg.FileTypeEnum fileType;
    }
}
