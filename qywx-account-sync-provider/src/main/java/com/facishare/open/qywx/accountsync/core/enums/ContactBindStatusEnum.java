package com.facishare.open.qywx.accountsync.core.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/09/26
 */
public enum ContactBindStatusEnum {

    INIT_STATUS(0, "不存在绑定关系（引导安装通讯录应用）"),
    HAVE_BINDING_FS_EA(1,"该企业微信账号已绑定其它纷享账号 请联系纷享客服"),
    FIRST_BIND(2,"首次进入绑定"),
    NOT_FIRST_BIND(3,"已绑定"),
    HAVE_BINDING_QYWX_EA(4,"该纷享账号已绑定其它企业微信账号 请联系纷享客服")
    ;
    //0：不存在绑定关系（引导安装通讯录应用）1：该企业微信账号已绑定其它纷享账号 请纷享客服 2：首次进入绑定 3：已绑定

    private Integer code;
    private String name;

    ContactBindStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
