package com.facishare.open.qywx.accountsync.upload;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.content.InputStreamBody;
import org.apache.http.util.Args;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 文件上传服务，充分考虑带宽消耗，支持按企业配置带宽限额，默认上传速率为1MB每秒
 * <AUTHOR>
 * @date ********
 */
@Slf4j
public class FsInputStreamBody extends InputStreamBody {
    private Long oneMB = 1 * 1024 * 1024L;
    private Long maxUploadBytesPerSecond = oneMB;

    public FsInputStreamBody(InputStream in, String mimeType, String filename,Long maxUploadBytesPerSecond) {
        super(in, mimeType, filename);
        this.maxUploadBytesPerSecond = maxUploadBytesPerSecond;
    }

    public FsInputStreamBody(InputStream in, String filename,Long maxUploadBytesPerSecond) {
        super(in, filename);
        this.maxUploadBytesPerSecond = maxUploadBytesPerSecond;
    }

    public FsInputStreamBody(InputStream in, ContentType contentType, String filename,Long maxUploadBytesPerSecond) {
        super(in, contentType, filename);
        this.maxUploadBytesPerSecond = maxUploadBytesPerSecond;
    }

    public FsInputStreamBody(InputStream in, ContentType contentType,Long maxUploadBytesPerSecond) {
        super(in, contentType);
        this.maxUploadBytesPerSecond = maxUploadBytesPerSecond;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        Args.notNull(out, "Output stream");
        InputStream in =getInputStream();

        try {
            byte[] tmp = new byte[10 * 1024];

            int read;
            int totalBytesPerSecond = 0;
            Long startTime = System.currentTimeMillis();
            Long beginTime = startTime;
            while((read = in.read(tmp)) != -1) {
                out.write(tmp, 0, read);
                totalBytesPerSecond += read;
                Long offset = System.currentTimeMillis() - startTime;
                if(offset < 1000 && totalBytesPerSecond >=oneMB) {
                    out.flush();
                    try {
                        Thread.sleep(offset);
                    } catch (Exception e) {

                    }
                    log.info("FsInputStreamBody.writeTo, offset={},totalBytesPerSecond={}",offset,totalBytesPerSecond);
                    totalBytesPerSecond = 0;
                    startTime = System.currentTimeMillis();
                }
                if(offset >= 1000) {
                    out.flush();
                    totalBytesPerSecond = 0;
                    startTime = System.currentTimeMillis();
                    log.info("FsInputStreamBody.writeTo, offset > 1000");
                }
            }
            out.flush();
            log.info("FsInputStreamBody.writeTo, total time={}",System.currentTimeMillis()-beginTime);
        } finally {
            in.close();
        }
    }
}