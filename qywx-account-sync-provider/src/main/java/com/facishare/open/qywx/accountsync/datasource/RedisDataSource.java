package com.facishare.open.qywx.accountsync.datasource;

import com.github.jedis.support.MergeJedisCmd;
import org.slf4j.LoggerFactory;

import java.util.logging.Logger;

/**
 * Created by fengyh on 2018/3/1.
 */
//public class RedisDataSource  {
//
//  //  public static final Logger logger = LoggerFactory.getLogger(RedisDataSource.class);
//
//    private MergeJedisCmd jedisCmd;
//
//    public MergeJedisCmd getRedisClient() {
//        return jedisCmd;
//    }
//
//    public void setJedisCmd(MergeJedisCmd jedisCmd) {
//        this.jedisCmd = jedisCmd;
//    }
//}

