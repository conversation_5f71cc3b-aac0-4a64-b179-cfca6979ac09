package com.facishare.open.qywx.accountsync.mq.listener;

import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.model.OaconnectorEventDateChangeProto;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("oaconnectorEventDataChangeListener")
@Slf4j
public class OaconnectorEventDataChangeListener implements MessageListenerConcurrently {
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : list) {
            TraceUtil.initTrace(msg.getMsgId());
            try {
                String receiveTags = msg.getTags();
                if(!receiveTags.equals(ChannelEnum.qywx.name())) {
                    continue;
                }

                //测试环境没有专属云
                String env = System.getProperty("process.profile");
                if(!ConfigCenter.IS_TEST) {
                    if(env.equals("fstest") || env.equals("fstest-gray")) {
                        continue;
                    }
                }

                if(!ConfigCenter.MAIN_ENV) {
                    continue;
                }

                OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
                proto.fromProto(msg.getBody());
                if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("oaconnector_enterprise_bind")) {
                    log.info("OaconnectorEventDataChangeListener.consumeMessage,proto={}", proto);
                    if(proto.getType().equals("insert") || proto.getType().equals("update")) {
                        QyweixinAccountEnterpriseMapping enterpriseMapping = new Gson().fromJson(proto.getContent(), QyweixinAccountEnterpriseMapping.class);
                        if(proto.getType().equals("insert")) {
                            //通过outEa找到自动开通的企业
                            Result<List<QyweixinAccountEnterpriseMapping>> selectAllEnterpriseBindResult = qyweixinAccountBindService.selectAllEnterpriseBind("qywx", enterpriseMapping.getOutEa());
                            if(selectAllEnterpriseBindResult.isSuccess() && CollectionUtils.isNotEmpty(selectAllEnterpriseBindResult.getData())) {
                                for(QyweixinAccountEnterpriseMapping mapping : selectAllEnterpriseBindResult.getData()) {
                                    if(mapping.getBindType() == 0) {
                                        String mainAppId = qyweixinGatewayInnerService.getMainAppId(mapping.getOutEa()).getData();
                                        com.facishare.open.qywx.accountsync.result.Result<Void> deleted = qyweixinAccountBindInnerService.deleteQYWXAccountBind(mapping.getFsEa(),
                                                mainAppId,
                                                mapping.getOutEa());
                                        log.info("OaconnectorEventDataChangeListener,mapping={},result={}", mapping, deleted);
                                        break;
                                    }
                                }
                            }
                        }

                        qyweixinAccountBindService.bindAccountEnterpriseMapping(enterpriseMapping);
                        //跨云接收到的新增事件，到纷享云的，把自动开通的企业删除掉
                    } else if(proto.getType().equals("deleteQYWXBindByOutEa")) {
                        qyweixinAccountBindInnerService.deleteQYWXBindByOutEa(Lists.newArrayList(proto.getOutEa()), proto.getAppId());
                    } else if(proto.getType().equals("deleteQYWXAccountBind")) {
                        qyweixinAccountBindInnerService.deleteQYWXAccountBind(proto.getFsEa(), proto.getAppId(), proto.getOutEa());
                    }
                }
            } catch (Exception e) {
                log.info("OaconnectorEventDataChangeListener.consumeMessage,exception={}",e.getMessage(),e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
