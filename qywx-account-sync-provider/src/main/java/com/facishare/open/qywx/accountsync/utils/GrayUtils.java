package com.facishare.open.qywx.accountsync.utils;

import lombok.extern.slf4j.Slf4j;

import com.google.common.base.Splitter;
import com.google.common.base.Strings;

import java.util.List;

/**
 * <AUTHOR>
 * Created on 2019/1/18
 */
@Slf4j
public class GrayUtils {
    public static boolean checkInGrayByEa(String grayEa, String enterpriseAccount) {

        boolean grayRet = false;

        log.info("trace cms grayEa={}", grayEa);

        //包含"all"的则视为全网开放
        if ("all".equalsIgnoreCase(grayEa)) {
            return true;
        }

        if (!Strings.isNullOrEmpty(grayEa)) {
            List<String> grayEaList = Splitter.on(",").splitToList(grayEa);

            if (grayEaList != null && grayEaList.size() > 0) {
                grayRet = grayEaList.contains(enterpriseAccount);
            }
        }

        return grayRet;
    }
}
