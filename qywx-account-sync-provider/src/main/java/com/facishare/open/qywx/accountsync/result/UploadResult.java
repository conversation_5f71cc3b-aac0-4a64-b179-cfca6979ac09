package com.facishare.open.qywx.accountsync.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class UploadResult implements Serializable {

    private static final long serialVersionUID = 7874934532916571497L;

    private Integer errcode;
    private String errmsg;

    private String type;

    @SerializedName("media_id")
    @JSONField(name = "media_id")
    private String mediaId;

    /**
     * 媒体文件创建时间，单位精确到秒
     */
    @SerializedName("created_at")
    @JSONField(name = "created_at")
    private Long createdAt;
}