package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinUserBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface QyweixinUserDao extends ICrudMapper<QyweixinUserBo> {
    @Update("<script>" + "update qyweixin_user set mobile = #{mobile} where out_ea=#{outEa} and user_id=#{userId}" +"</script>")
    int updateMobile(@Param("outEa") String outEa,
                                @Param("userId") String userId,
                                @Param("mobile") String mobile);

    @Delete("<script>" + "delete from qyweixin_user where out_ea=#{outEa} or out_ea=#{openOutEa}" + "</script>")
    int deleteAllUserIds(@Param("outEa") String outEa,
                                @Param("openOutEa") String openOutEa);
}
