package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/10/25 19:11
 * 服务商查询自己某段时间内的平台能力服务订单列表
 * @desc
 */
@Data
public class QywxPermissionOrderList extends QywxBaseResult implements Serializable {

    private List<OrderList> order_list;

    //分页游标，再下次请求时填写以获取之后分页的记录
    private String next_cursor;

    //是否有更多。 0: 没有， 1: 有
    private Boolean has_more;

    @Data
    public  static  class OrderList implements Serializable {
        private String order_id;
        private String order_type;

    }
}
