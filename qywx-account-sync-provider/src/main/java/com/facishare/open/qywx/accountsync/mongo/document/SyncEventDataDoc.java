package com.facishare.open.qywx.accountsync.mongo.document;

import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.List;

/**
 * 企微事件推送文档模板
 * <AUTHOR>
 * @date 2023/8/23
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class SyncEventDataDoc implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    private String outEa;
    private String appId;
    private String eventType;
    private String event;
    private Integer status;
    /**
     * 数据创建时间
     */
    private Long createTime;
    /**
     * 数据更新时间
     */
    private Long updateTime;
}
