package com.facishare.open.qywx.accountsync.manager;

import com.facishare.open.qywx.accountsync.mongo.dao.OutUserInfoMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Data
@Component
public class OutUserInfoManger {
    @Autowired
    private OutUserInfoMongoDao outUserInfoMongoDao;

    public BulkWriteResult batchReplace(List<OutUserInfoDoc> docList) {
        return outUserInfoMongoDao.batchReplace(docList);
    }

    public List<OutUserInfoDoc> queryUserInfos(String outEa) {
        return outUserInfoMongoDao.queryUserInfos(outEa);
    }

    public DeleteResult deleteUserInfo(OutUserInfoDoc doc) {
        return outUserInfoMongoDao.deleteUserInfo(doc);
    }

    public DeleteResult deleteUserInfoByUserId(String outEa, String outUserId) {
        return outUserInfoMongoDao.deleteUserInfoByUserId(outEa, outUserId);
    }

    public DeleteResult deleteUserInfoByOutEa(String outEa) {
        return outUserInfoMongoDao.deleteUserInfoByOutEa(outEa);
    }

    public DeleteResult deleteNotInCollectionDocs(String outEa, List<OutUserInfoDoc> docList) {
        return outUserInfoMongoDao.deleteNotInCollectionDocs(outEa, docList);
    }
}
