package com.facishare.open.qywx.accountsync.utils;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/27
 */
public class CompareListUtil {

    /**
     * 优化的比较方法 获取aList相对于bList的增加的情况 以及减少的数据
     * 参考文章：https://blog.csdn.net/zhangchen124/article/details/********
     *          https://blog.csdn.net/u014021893/article/details/********
     * @param newList        新数据
     * @param cacheList        比对数据
     * @param addList      返回的增加数据
     * @param cancleList   返回的减少数据
     * @param <E>
     * @return
     */
    public static <E> void compareAddAndReduceList(List<E> newList, List<E> cacheList, List<E> addList, List<E> cancleList) {
        List<String> diff1 = new ArrayList<String>();
        List<String> maxList = (List<String>) newList;
        List<String> minList = (List<String>) cacheList;
        boolean exchange = false;
        if(cacheList.size()> newList.size())
        {
            exchange = true;
            maxList = (List<String>) cacheList;
            minList = (List<String>) newList;
        }
        Map<String,Integer> map = new HashMap<String,Integer>(maxList.size());
        for (String string : maxList) {
            map.put(string, 1);
        }
        for (String string : minList) {
            if(map.get(string)!=null)
            {
                map.put(string, 2);
                continue;
            }
            diff1.add(string);
        }

        List<String> diff2 = new ArrayList<String>();
        for(Map.Entry<String, Integer> entry:map.entrySet())
        {
            if(entry.getValue()==1)
            {
                diff2.add(entry.getKey());
            }
        }

        if(exchange){
            addList.addAll((List<E>) diff1);
            cancleList.addAll((List<E>) diff2);
        } else {
            addList.addAll((List<E>) diff2);
            cancleList.addAll((List<E>) diff1);
        }
    }

    public static void main(String[] args) {
        List<String> aList = Arrays.asList("".split(","));
        List<String> bList = Lists.newArrayList("13","4","5","6","12","43","45");

        List<String > addList = Lists.newArrayList();
        List<String> cancleList = Lists.newArrayList();
        compareAddAndReduceList(aList, bList, addList ,cancleList);

        System.out.println(" addList:" + addList.stream().collect(Collectors.joining(",")));
        System.out.println(" cancleList:" + cancleList.stream().collect(Collectors.joining(",")));
    }
}
