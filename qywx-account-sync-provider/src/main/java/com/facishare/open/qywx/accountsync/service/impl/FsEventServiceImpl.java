package com.facishare.open.qywx.accountsync.service.impl;

import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEventService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.manager.CorpManager;
import com.facishare.open.qywx.accountsync.manager.WechatMessageService;
import com.facishare.open.qywx.accountsync.model.CreateCrmEnterpriseEventProto;
import com.facishare.open.qywx.accountsync.mq.MQSender;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.accountsync.utils.WechatRegisterConfigHelper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 *  纷享事件服务，目前主要处理纷享企业开通事件
 * <AUTHOR>
 * @date 2023.02.06
 */
@Service("fsEventService")
public class FsEventServiceImpl implements FsEventService {
    @Autowired
    private CorpManager corpManager;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private WechatMessageService wechatMessageService;
    @Autowired
    private ContactsService contactsService;
    @Resource
    private MQSender mqSender;

    @Override
    public Result<Boolean> isEnterpriseBind(String ea) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult = qyweixinAccountBindService.fsEaToOutEaResult(SourceTypeEnum.QYWX.getSourceType(), ea);
        LogUtils.info("FsEventServiceImpl.isEnterpriseBind,fsEaToOutEaResult,fsEaToOutEaResult={}", fsEaToOutEaResult);
        if(fsEaToOutEaResult.getData()!=null) {
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Void> onEnterpriseOpened(Integer ei, String ea, String enterpriseName) {
        LogUtils.info("FsEventServiceImpl.onEnterpriseOpened,ei={},ea={},enterpriseName={}", ei,ea,enterpriseName);

        //更新企业绑定状态为正常
        corpManager.updateEnterpriseBindStatus(ea,0);
        //更新管理员绑定状态为正常
        corpManager.updateEmployeeBindStatus("E." + ea + ".1000", 0);

        //给管理员发送欢迎消息
        sendWelcomeMessage(ea, Lists.newArrayList("1000"));

        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEmployeeMapping> employeeMapping
                = qyweixinAccountBindService.getQywxEmployeeMapping2(ea, "1000",null);
        LogUtils.info("FsEventServiceImpl.onEnterpriseOpened,employeeMapping={}", employeeMapping);

        //发送安装成功的mq
        CreateCrmEnterpriseEventProto eventProto = new CreateCrmEnterpriseEventProto();
        eventProto.setAppId(employeeMapping.getData().getAppId());
        eventProto.setCorpId(employeeMapping.getData().getOutEa());
        eventProto.setFsEa(ea);
        eventProto.setFsUserId("1000");
        eventProto.setUserId(employeeMapping.getData().getOutAccount());
        mqSender.sendEnterpriseCreateMQ("tag_create_crm_enterprise", eventProto);

        contactsService.initContactsAsync(employeeMapping.getData().getAppId(),employeeMapping.getData().getOutEa(),ea);
        return Result.newSuccess();
    }

    public void sendWelcomeMessage(String fsEA, List<String> userIds) {
        WechatRegisterConfigHelper.getWechatMessageValue().forEach((key, value) -> {
            wechatMessageService.sendMessage(
                    fsEA,
                    value.getTitle(),
                    value.getUrl(),
                    value.getPicUrl(),
                    userIds
            );
        });
    }
}
