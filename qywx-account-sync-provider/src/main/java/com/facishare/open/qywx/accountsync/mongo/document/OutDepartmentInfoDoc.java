package com.facishare.open.qywx.accountsync.mongo.document;

import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;

/**
 * 企微部门详情文档模板
 * <AUTHOR>
 * @date 2023/8/23
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class OutDepartmentInfoDoc implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    private String outEa;
    private String outDepartmentId;
    private String outDepartmentInfo;
    /**
     * 数据创建时间
     */
    private Long createTime;
    /**
     * 数据更新时间
     */
    private Long updateTime;
}
