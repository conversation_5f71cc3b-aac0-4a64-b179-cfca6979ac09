package com.facishare.open.qywx.accountsync.handler;

import com.facishare.open.qywx.accountsync.manager.OANewBaseManager;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业微信第三方应用事件处理器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class ThirdCmdEventHandler extends EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;
    @Resource
    private OANewBaseManager oANewBaseManager;

    @Override
    public void handle(String plainMsg,String appId) {
        super.handle(plainMsg,appId);
        //log.info("ThirdCmdEventHandler.handle,eventProto={}", eventProto);

        //String plainMsg = null;
        try {
            //plainMsg = decryptMsg(eventProto.getSignature(), eventProto.getTimestamp(), eventProto.getNonce(), eventProto.getData(),null);
            log.info("ThirdCmdEventHandler.handle,plainMsg={}", plainMsg);

            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
            log.info("ThirdCmdEventHandler.handle,baseMsgXml={}",baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("ThirdCmdEventHandler.handle,infoType={}",infoType);

            if(!supportedCmdEvent.contains(infoType)) {
                log.info("ThirdCmdEventHandler.handle,not support event,event={}", infoType);
                return;
            }
            //去掉不支持的外部联系人事件
            List<String> externalContactChangeTypeList = Lists.newArrayList("add_external_contact","edit_external_contact","del_external_contact","del_follow_user");
            if(StringUtils.equalsIgnoreCase("change_external_contact",infoType)
                    && !externalContactChangeTypeList.contains(baseMsgXml.getChangeType())) {
                return;
            }

//            if(runInCurrentEnv(baseMsgXml.getAuthCorpId(),plainMsg)==false) return;

            //是否执行在新基座
            if(oANewBaseManager.canRunInNewBase(baseMsgXml.getAuthCorpId(), plainMsg)) {
                log.info("ThirdCmdEventHandler.handle,runInNewBase,baseMsgXml.getToUserName()={},plainMsg={}", baseMsgXml.getToUserName(), plainMsg);
                return;
            }

        } catch (Exception e) {
            log.error("ThirdCmdEventHandler.handle,exception={}", e.getMessage(),e);
            return;
        }

        log.info("ThirdCmdEventHandler.handle,plainMsg2={}", plainMsg);
        String result = qyweixinGatewayInnerService.recvMsgEvent2(plainMsg);

        log.info("ThirdCmdEventHandler.handle,result={}", result);
    }
}
