package com.facishare.open.qywx.accountsync.mongo.dao;

import com.facishare.open.qywx.accountsync.mongo.document.OutDepartmentInfoDoc;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.facishare.open.qywx.accountsync.mongo.store.OutUserInfoMongoStore;
import com.google.common.collect.Lists;
import com.mongodb.MongoException;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.currentDate;

/**
 * 企微人员详情操作mongo的dao类封装
 * <AUTHOR>
 * @date 2023/8/23
 */
@Slf4j
@Repository
public class OutUserInfoMongoDao {
    public static final String f_id = "_id";
    public static final String f_outEa = "outEa";
    public static final String f_outUserId = "outUserId";
    public static final String f_outUserInfo = "outUserInfo";
    public static final String f_createTime = "createTime";
    public static final String f_updateTime = "updateTime";

    private final OutUserInfoMongoStore store;

    public OutUserInfoMongoDao(OutUserInfoMongoStore store) {
        this.store = store;
    }

    private List<ObjectId> convertObjectIds(Collection<String> ids) {
        List<ObjectId> objIds = ids.stream()
                .filter(ObjectId::isValid)
                .map(v -> new ObjectId(v)).collect(Collectors.toList());
        return objIds;
    }

    private static ObjectId getObjId(String id) {
        return new ObjectId(id);
    }

    private int update(List<Bson> filters, List<Bson> updates) {
        updates.add(currentDate(f_updateTime));
        Bson filter = and(filters);
        Bson update = combine(updates);
        UpdateResult updateResult = store.getOrCreateCollection().updateOne(filter, update);
        return (int) updateResult.getModifiedCount();
    }


    public List<OutUserInfoDoc> listByTenantId(Integer offset, Integer limit) {
        List<OutUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find().limit(limit).skip(offset).into(res);
        return res;
    }

    /**
     * 忽略结果
     *
     * @param outUserInfoDoc
     * @return 1 成功 0 失败
     */

    public int insertIgnore(OutUserInfoDoc outUserInfoDoc) {
        MongoCollection<OutUserInfoDoc> collection = store.getOrCreateCollection();
        try {
            collection.insertOne(outUserInfoDoc);
        } catch (MongoException mongoException) {
            log.error("sync data insert exception", mongoException);
            return 0;
        }
        return 1;
    }


    public OutUserInfoDoc getById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        OutUserInfoDoc outUserInfoDoc = store.getOrCreateCollection().find(eq(new ObjectId(id))).limit(1).first();
        return outUserInfoDoc;
    }


    public OutUserInfoDoc getSimpleById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        OutUserInfoDoc outUserInfoDoc = store.getOrCreateCollection()
                .find(eq(new ObjectId(id)))
                .limit(1).first();
        return outUserInfoDoc;
    }


    public List<OutUserInfoDoc> listByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<OutUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }


    public List<OutUserInfoDoc> listSimpleByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<OutUserInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public void save(OutUserInfoDoc outUserInfoDoc) {
        log.info("OutUserInfoMongoDao.save,outUserInfoDoc={}", outUserInfoDoc);
        Bson filter = and(eq(f_id, outUserInfoDoc.getId()));
        store.getOrCreateCollection().replaceOne(filter, outUserInfoDoc, new ReplaceOptions().upsert(true));
    }

    /**
     * 批量插入或替换记录
     *
     * @param docList
     * @return
     */
    public BulkWriteResult batchReplace(Collection<OutUserInfoDoc> docList) {
        log.info("OutUserInfoMongoDao.batchReplace,docList={}", docList);
        List<WriteModel<OutUserInfoDoc>> request = new ArrayList<>();
        for (OutUserInfoDoc doc : docList) {
            Bson filter = and(eq(f_outEa, doc.getOutEa()), eq(f_outUserId, doc.getOutUserId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("OutUserInfoMongoDao.batchReplace,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }


    /**
     * 批量更新，无则不插入。
     *
     * @param docList
     * @return insert count
     */
    public BulkWriteResult batchUpdate(Collection<OutUserInfoDoc> docList) {
        log.info("OutUserInfoMongoDao.batchUpdate,docList={}", docList);
        List<WriteModel<OutUserInfoDoc>> request = new ArrayList<>();
        for (OutUserInfoDoc doc : docList) {
            Bson filter = and(eq(f_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("OutUserInfoMongoDao.batchUpdate,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }

    public List<OutUserInfoDoc> queryUserInfos(String outEa) {
        List<OutUserInfoDoc> res = new ArrayList<>();
        Bson filter = and(eq(f_outEa, outEa));
        store.getOrCreateCollection().find(filter).into(res);
        Map<String,OutUserInfoDoc> map = new HashMap<>();
        for(OutUserInfoDoc doc : res) {
            if(!map.containsKey(doc.getOutUserId())) {
                map.put(doc.getOutUserId(),doc);
            }
        }
        return map.values().stream().collect(Collectors.toList());
    }

    public DeleteResult deleteUserInfo(OutUserInfoDoc doc) {
        Bson filter = and(eq(f_id, doc.getId()));
        return store.getOrCreateCollection().deleteMany(filter);
    }

    public DeleteResult deleteUserInfoByUserId(String outEa, String outUserId) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_outEa, outEa), Filters.eq(f_outUserId, outUserId));
        Bson filter = Filters.and(filters);
        return store.getOrCreateCollection().deleteOne(filter);
    }

    public DeleteResult deleteUserInfoByOutEa(String outEa) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_outEa, outEa));
        Bson filter = Filters.and(filters);
        return store.getOrCreateCollection().deleteMany(filter);
    }

    public DeleteResult deleteNotInCollectionDocs(String outEa, Collection<OutUserInfoDoc> docList) {
        Bson filter = deleteNotInCollectionDocsBson(outEa, docList);
        return store.getOrCreateCollection().deleteMany(filter);
    }

    private Bson deleteNotInCollectionDocsBson(String outEa, Collection<OutUserInfoDoc> docList) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_outEa, outEa));
        List<Bson> neOutUserFilters = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(docList)) {
            for(OutUserInfoDoc doc : docList) {
                List<Bson> eventTypeFilters = Lists.newArrayList(Filters.eq(f_outUserId, doc.getOutUserId()));
                Bson filterBson = Filters.and(eventTypeFilters);
                neOutUserFilters.add(filterBson);
            }
            filters.add(Filters.nor(neOutUserFilters));
        }
        return Filters.and(filters);
    }

    public void deleteIndex(String index) {
        store.getOrCreateCollection().dropIndex(index);
    }

    public void addIndex() {
        List<IndexModel> indexList = Lists.newArrayList();
        Bson bson = Indexes.compoundIndex(
                Indexes.ascending(OutUserInfoMongoDao.f_outEa),
                Indexes.ascending(OutUserInfoMongoDao.f_outUserId));
        indexList.add(new IndexModel(bson, new IndexOptions()
                .name("index_outEa_outUserId")
                .background(true)));
        List<String> indexes = store.getOrCreateCollection().createIndexes(indexList);
        System.out.println(indexes);
    }
}
