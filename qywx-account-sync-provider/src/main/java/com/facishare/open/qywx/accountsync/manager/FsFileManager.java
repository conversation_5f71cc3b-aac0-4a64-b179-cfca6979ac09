package com.facishare.open.qywx.accountsync.manager;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.open.qywx.accountsync.arg.UploadFileArg;
import com.facishare.open.qywx.accountsync.constant.Constant;
import com.facishare.open.qywx.accountsync.core.enums.FileEnum;
import com.facishare.open.qywx.accountsync.result.UploadFileResult;
import com.facishare.qixin.api.exeception.IllegalImageException;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileImageProcessRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileImageProcessResponse;
import com.facishare.stone.sdk.response.StoneFileImageThumbnailResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.facishare.qixin.api.model.message.content.*;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/19 16:22 上传到纷享文件服务器manager
 * @Version 1.0
 */
@Service
@Slf4j
public class FsFileManager {

    @Autowired
    private StoneProxyApi stoneProxyApi;
    @Autowired
    private NFileStorageService nFileStorageService;
    private static final List<String> HD_THUMBNAILS = Lists.newArrayList("150, 150, 84, 84", "270, 270, 84, 84", "1280, 1280, 0, 0", "75, 75, 42, 42");

    public byte[] downloadNFile(String fsEa, String path) {
        try {
            if (path == null) {
                return null;
            }
            NDownloadFile.Arg arg = new NDownloadFile.Arg();
            arg.setEa(fsEa);
            arg.setnPath(path);
            arg.setDownloadUser("E.1000");

            NDownloadFile.Result result = nFileStorageService.nDownloadFile(arg, fsEa);

            return result.getData();
        } catch (Exception e) {
            log.error("Error: downloadAFile! path:{}", path, e);
            return null;
        }
    }

    public UploadFileResult uploadFile(InputStream inputStream, UploadFileArg uploadFileArg) {

        StoneFileUploadRequest request = new StoneFileUploadRequest();
        if (FileEnum.IMAGE_TYPE.getMessageType().equals(uploadFileArg.getMessageType())) {
            //构建图片请求
            StoneFileImageProcessRequest imageProcessRequest = new StoneFileImageProcessRequest();
            imageProcessRequest.setMakeThumbnailList(HD_THUMBNAILS);
            request.setImageProcessRequest(imageProcessRequest);
            request.setNeedThumbnail(true);
        }
        request.setEa(uploadFileArg.getEa());
        request.setEmployeeId(-10000);
        request.setBusiness(Constant.FS_CONSULT_BUSINESS);
        request.setFileSize(uploadFileArg.getFileSize().intValue());
        request.setExtensionName(uploadFileArg.getFileExt());
        StoneFileUploadResponse uploadResponse;
        try {
            uploadResponse = stoneProxyApi.uploadByStream("n", request, inputStream);
            log.info("upload file by stream,request:{},response:{}", request, uploadResponse);
        } catch (FRestClientException e) {
            log.error("upload file failed", e);
            return null;
        }
        String content = buildContent(uploadResponse, uploadFileArg.getFileName(), uploadFileArg.getMessageType());
        log.info("upload file to fs success,ea:{},messageType:{},result content:{}", uploadFileArg.getEa(), uploadFileArg.getMessageType(), content);
        UploadFileResult result = new UploadFileResult();
        result.setContent(content);
        result.setFileSize(uploadResponse.getSize());
        result.setNpath(uploadResponse.getPath());
        return result;

    }


    private String buildContent(StoneFileUploadResponse result, String fileName, String messageType) {
        log.info("build content result:{},fileName:{},messageType:{}",result,fileName,messageType);
        switch (messageType) {
            case Constant.IMAGE_TYPE:
                return createImage(result, fileName).toJson();
            case Constant.DOC_TYPE:
                return createDocument(result, fileName).toJson();
            default:
                return Constant.UNSUPPORTED_MSG_TYPE;
        }
    }

    private Image createImage(StoneFileUploadResponse response, String fileName) {
        Image image = new Image();
        image.setOriginalName(fileName);
        image.setFileSize(response.getSize());

        StoneFileImageProcessResponse imageProcessResponse = response.getImageProcessResponse();
        log.info("image process response:{}",imageProcessResponse);
        if (imageProcessResponse == null) {
            //获取图片列表异常
            log.warn("image response error,response:{}", response);
//            ConsultCommunicationErrorCode.UPLOAD_IMAGE_FAILED.assertFail();
            return image;
        }
        List<StoneFileImageThumbnailResponse> thumbnailList = imageProcessResponse.getThumbnailList();

        //正常图片
        StoneFileImageThumbnailResponse normalImage = thumbnailList.get(2);
        image.setImage(buildNpath(normalImage.getPath(), normalImage.getExtensionName()));
        image.setImageW(normalImage.getWidth());
        image.setImageH(normalImage.getHeight());
        image.setImageSize(normalImage.getSize());
        //高清原图
        image.setHdImage(buildNpath(response.getPath(), response.getExtensionName()));
        image.setHdSize(response.getSize());
        //缩略图
        StoneFileImageThumbnailResponse smallThumbnail = thumbnailList.get(0);
        image.setThumbnail(buildNpath(smallThumbnail.getPath(), smallThumbnail.getExtensionName()));
        image.setThumbH(smallThumbnail.getHeight());
        image.setThumbW(smallThumbnail.getWidth());
        //中缩略图
        StoneFileImageThumbnailResponse middleThumbnail = thumbnailList.get(1);
        image.setHdThumbnail(buildNpath(middleThumbnail.getPath(), middleThumbnail.getExtensionName()));
        //小缩略图
        StoneFileImageThumbnailResponse tinyThumbnail = thumbnailList.get(3);
        image.setSmallThumbnail(buildNpath(tinyThumbnail.getPath(), tinyThumbnail.getExtensionName()));
        imageCheck(image);
        return image;
    }

    private static void imageCheck(Image image) {
        if (image.getThumbW() <= 0 || image.getThumbH() <= 0) {
            throw new IllegalImageException("illegal image:" + image);
        }
    }

    private Document createDocument(StoneFileUploadResponse result, String fileName) {
        Document document = new Document();
        document.setSize(result.getSize().intValue());
        document.setName(fileName);
        document.setFile(buildNpath(result.getPath(), result.getExtensionName()));
        return document;
    }

    private String buildNpath(String npath, String ext) {
        if (StringUtils.isNotBlank(ext)) {
            return npath + "." + ext;
        } else {
            return npath;
        }
    }


}
