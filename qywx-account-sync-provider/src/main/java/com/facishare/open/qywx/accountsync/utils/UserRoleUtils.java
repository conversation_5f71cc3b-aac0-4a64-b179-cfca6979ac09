//package com.facishare.open.qywx.accountsync.utils;
//
//import com.alibaba.rocketmq.common.message.Message;
//import com.facishare.common.rocketmq.AutoConfRocketMQSender;
//import com.facishare.converter.EIEAConverter;
//import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
//import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
//import com.facishare.open.qywx.accountsync.model.CookedEventChange;
//import com.facishare.open.qywx.accountsync.model.RoleData;
//import com.github.autoconf.spring.reloadable.ReloadableProperty;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Sets;
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.ListUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * Create by max on 2020/03/11
// **/
//@Slf4j
//@Service
//public class UserRoleUtils {
//
//    @Resource(name = "userChangeEventSender")
//    private AutoConfRocketMQSender userChangeEventSender;
//    @Autowired
//    private EIEAConverter eieaConverter;
//    @Autowired
//    private QyweixinAccountBindService qyweixinAccountBindService;
//
//    @ReloadableProperty("crmAppId")
//    private String crmAppId;
//
//    @ReloadableProperty("roleData")
//    private String dynamicRoleData;
//
////    /**
////     * 设置人员角色
////     *
////     * @param roleCode 销售角色(00000000000000000000000000000015)
////     */
////    private CRMUserRoleResult setUserRole(Integer enterpriseId, String enterpriseAccount, List<String> userIds, String roleCode) {
////        CRMUserRoleResult crmUserRoleResult = new CRMUserRoleResult();
////        CRMUserRole crmUserRole = new CRMUserRole();
////        crmUserRole.setUserIds(userIds);
////        crmUserRole.setRoleCode(roleCode);
////        Map<String, String> headers = Maps.newHashMap();
////        headers.put("content-type", "application/json");
////        headers.put("x-app-id", "CRM");
////        headers.put("x-fs-ea", enterpriseAccount);
////        headers.put("x-fs-ei", String.valueOf(enterpriseId));
////        headers.put("x-fs-userinfo", String.valueOf(Constant.OPERATOR.getOperatorId()));
////
////        try {
////            String setRoleUrl = WechatRegisterConfigHelper.getUserRoleUrl() + "user_role";
////            byte[] postResult = HttpUtils.webPost(new URL(setRoleUrl), JSON.toJSONString(crmUserRole).getBytes(), headers, 5000, 5000);
////            crmUserRoleResult = JSON.parseObject(postResult, CRMUserRoleResult.class, new Feature[0]);
////        } catch (Exception var11) {
////            log.error("set role exception.", var11);
////        }
////        return crmUserRoleResult;
////    }
////
////    public CRMUserRoleResult setUserRole(String ea, String appId, List<String> userIds) {
////        CRMUserRoleResult result = new CRMUserRoleResult();
////        List<String> roleCodes = this.getRoleCodesByAppId(appId);
////        int ei = eieaConverter.enterpriseAccountToId(ea);
////        log.debug("setUserRole start. ea:{}, appId:{}, userIds:{}", ea, appId, userIds);
////        for (String roleCode : roleCodes) {
////            result = this.setUserRole(ei, ea, userIds, roleCode);
////            if (result.getCode() != 0) {
////                log.warn("setUserRole failed. ea:{}, appId:{}, userIds:{}, result:{}", ea, appId, userIds, result);
////                return result;
////            }
////        }
////        // 发送人员变更消息
////        sendChangeEvent(ea, appId, userIds, CookedEventChange.ADD_FLAG);
////        return result;
////    }
//
//    /**
//     * 发送人员变更MQ
//     */
//    private void sendChangeEvent(String ea, String appId, List<String> userIds, int flag) {
//        if (userIds.isEmpty()) return;
//        List<Integer> userList = userIds.stream().map(Integer::valueOf).collect(Collectors.toList());
//        CookedEventChange body = new CookedEventChange(ea, appId, userList);
//        String key = String.valueOf(body.hashCode());
//
//        Message message = new Message();
//        message.setBody(body.toProto());
//        message.setFlag(flag);
//        message.setKeys(key);
//        String msgId = userChangeEventSender.send(message);
//        log.info("SendChangeEvent success. flag:{}, body:{}, key:{}, msgId:{}", flag, body, key, msgId);
//    }
//
////    /**
////     * 移出角色
////     */
////    private CRMUserRoleResult removeUserRole(Integer enterpriseId, String enterpriseAccount, List<String> userIds, String roleCode) {
////        CRMUserRoleResult crmUserRoleResult = new CRMUserRoleResult();
////        CRMUserRole crmUserRole = new CRMUserRole();
////        crmUserRole.setUserIds(userIds);
////        crmUserRole.setRoleCode(roleCode);
////        Map<String, String> headers = Maps.newHashMap();
////        headers.put("content-type", "application/json");
////        headers.put("x-app-id", "CRM");
////        headers.put("x-fs-ea", enterpriseAccount);
////        headers.put("x-fs-ei", String.valueOf(enterpriseId));
////        headers.put("x-fs-userinfo", String.valueOf(Constant.OPERATOR.getOperatorId()));
////
////        try {
////            String setRoleUrl = WechatRegisterConfigHelper.getUserRoleUrl() + "user_role/delete_users";
////            byte[] postResult = HttpUtils.webPost(new URL(setRoleUrl), JSON.toJSONString(crmUserRole).getBytes(), headers, 5000, 5000);
////            crmUserRoleResult = JSON.parseObject(postResult, CRMUserRoleResult.class, new Feature[0]);
////        } catch (Exception var11) {
////            log.error("set role exception.", var11);
////        }
////        return crmUserRoleResult;
////    }
//
////    public void removeUserRole(String ea, String appId, List<String> userIds) {
////        if (CollectionUtils.isEmpty(userIds)) return;
////        List<String> roleCodes = this.getRoleCodesByAppId(appId);
////        int ei = eieaConverter.enterpriseAccountToId(ea);
////        log.debug("removeUserRole start. ea:{}, appId:{}, userIds:{}", ea, appId, userIds);
////        userIds.forEach(userId -> getUserRemoveRoleCodes(ea, Integer.parseInt(userId), appId, roleCodes)
////                .forEach(roleCode -> removeUserRole(ei, ea, Lists.newArrayList(userId), roleCode)));
////        // 发送人员变更消息
////        sendChangeEvent(ea, appId, userIds, CookedEventChange.REMOVE_FLAG);
////    }
//
//    /**
//     * 获取需要移除的角色code集合
//     */
//    @SuppressWarnings("unchecked")
//    private List<String> getUserRemoveRoleCodes(String ea, int userId, String currentAppId, List<String> removeRoleCodes) {
//        Set<String> roleCodes = Sets.newHashSet();
//        List<String> fsAccounts = Lists.newArrayList(FSAccountUtil.makeUserId(ea, userId));
//        qyweixinAccountBindService.fsAccountToOutAccountBatchV2(SourceTypeEnum.QYWX.getSourceType(), null, 0, fsAccounts)
//                .getData().stream()
//                .filter(mapping -> !Objects.equals(currentAppId, mapping.getAppId()))
//                .forEach(mapping -> roleCodes.addAll(getRoleCodesByAppId(mapping.getAppId())));
//        log.info("getUserRemoveRoleCodes success. ea:{}, userId:{}, currentAppId:{}, removeRoleCodes:{}, roleCodes:{}",
//                ea, userId, currentAppId, removeRoleCodes, roleCodes);
//        return ListUtils.removeAll(removeRoleCodes, roleCodes);
//    }
//
//    public List<String> getRoleCodesByAppId(String appId) {
//        List<RoleData> roleData = getRoleDataMap().get(appId);
//        if (CollectionUtils.isEmpty(roleData)) return Collections.emptyList();
//        return roleData.stream().map(RoleData::getRoleCode).collect(Collectors.toList());
//    }
//
//    public Map<String, List<RoleData>> getRoleDataMap() {
//        return new Gson().fromJson(dynamicRoleData, new TypeToken<Map<String, List<RoleData>>>() {
//        }.getType());
//    }
//
//    /**
//     * 创建配置中心所有角色
//     */
////    public void createRole(String ea) {
////        getRoleDataMap().keySet().forEach(appId -> this.createRole(ea, appId));
////        log.info("create all role success. ea:{}", ea);
////    }
//
//    /**
//     * 创建指定角色并赋予角色相关权限。根据appId从配置中心取。
//     */
////    public void createRole(String ea, String appId) {
////        List<RoleData> roleData = getRoleDataMap().get(appId);
////        log.info("create role start. ea:{}, appId:{}, roleData:{}", ea, appId, roleData);
////        if (CollectionUtils.isEmpty(roleData)) return;
////        roleData.stream().filter(RoleData::isCreate).forEach(data -> createRoleAndFunction(ea, data));
////    }
//
////    private void createRoleAndFunction(String ea, RoleData roleData) {
////        String roleCode = roleData.getRoleCode();
////
////        CrmRoleType.AddRole argument = new CrmRoleType.AddRole();
////        argument.setRoleCode(roleCode);
////        argument.setRoleName(roleData.getRoleName());
////        argument.setDescription(roleData.getDescription());
////        argument.setTenantId(eieaConverter.enterpriseAccountToId(ea));
////        CrmRoleType.AddRoleResult addRoleResult = crmRestApi.addCrmRole(argument);
////
////        if (!addRoleResult.getSuccess()) {
////            log.error("createRole failed. ea:{}, entity:{}, result:{}", ea, argument, addRoleResult);
////        }
////        log.info("create role success. update role function start. argument:{}, result:{}", argument, addRoleResult);
////        // 角色权限
////        updateRoleFunctions(ea, roleCode, roleData.getFunctions());
////    }
//
////    private void updateRoleFunctions(String ea, String roleCode, String functions) {
////        if (StringUtils.isBlank(functions)) return;
////        // 权限列表
////        Splitter.on(";").trimResults().omitEmptyStrings().splitToList(functions).forEach(o -> {
////            List<String> f = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(o);
////            this.updateSingleObjFunctions(ea, roleCode, f);
////        });
////    }
////
////    private void updateSingleObjFunctions(String ea, String roleCode, List<String> functions) {
////        UpdateRoleFuncCodeArg updateRoleFuncCodeArg = new UpdateRoleFuncCodeArg();
////        updateRoleFuncCodeArg.setFsEa(ea);
////        updateRoleFuncCodeArg.setRoleId(roleCode);
////        updateRoleFuncCodeArg.setFuncCodes(functions);
////        SupportModelResult<Boolean> result = crmRoleService.addRoleFuncCode(updateRoleFuncCodeArg);
////        if (!result.isSuccess()) {
////            log.error("updateSingleObjFunctions failed. entity:{}, result:{}", updateRoleFuncCodeArg, result);
////        }
////        log.info("updateSingleObjFunctions success. entity:{}, result:{}", updateRoleFuncCodeArg, result);
////    }
//}