//package com.facishare.open.qywx.accountsync.mq;
//
//import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
//import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
//import com.alibaba.rocketmq.client.exception.MQClientException;
//import com.alibaba.rocketmq.common.consumer.ConsumeFromWhere;
//import com.alibaba.rocketmq.common.protocol.heartbeat.MessageModel;
//import com.github.autoconf.ConfigFactory;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.DisposableBean;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Value;
//
//import javax.annotation.PreDestroy;
//import java.util.Objects;
//import java.util.UUID;
//
//@Slf4j
//public abstract class RocketMQueueConsumer implements MessageListenerConcurrently, InitializingBean {
//
//    private final String topic;
//
//    private final String mqConfig;
//
//    public RocketMQueueConsumer(String topic, String mqConfig) {
//        this.topic = topic;
//        this.mqConfig = mqConfig;
//    }
//
//    public static final String KEY_NAME_SERVER = "NAMESERVER";
//    public static final String KEY_GROUP_CONSUMER = "GROUP_CONSUMER";
//    private final static String KEY_DEFAULT_FETCH_SIZE = "DEFAULT_FETCH_SIZE";
//    private final static String KEY_CONSUME_THREAD_MIN="CONSUME_THREAD_MIN";
//    private final static String KEY_CONSUME_THREAD_MAX="CONSUME_THREAD_MAX";
//
//    private volatile DefaultMQPushConsumer consumer;
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        ConfigFactory.getConfig(mqConfig, config -> {
//
//            DefaultMQPushConsumer innerConsumer = new DefaultMQPushConsumer(config.get(KEY_GROUP_CONSUMER));
//            innerConsumer.setNamesrvAddr(config.get(KEY_NAME_SERVER));
//            innerConsumer.setMessageModel(MessageModel.CLUSTERING);
//
//            try {
//                innerConsumer.subscribe(topic, "*");
//            } catch (MQClientException e) {
//                throw new RuntimeException(e);
//            }
//
//            innerConsumer.setInstanceName(UUID.randomUUID().toString().replaceAll("-", ""));
//            innerConsumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
//
//            innerConsumer.setPullBatchSize(config.getInt(KEY_DEFAULT_FETCH_SIZE,100));
//            innerConsumer.registerMessageListener(this);
//            innerConsumer.setConsumeThreadMax(config.getInt(KEY_CONSUME_THREAD_MAX,20));
//            innerConsumer.setConsumeThreadMin(config.getInt(KEY_CONSUME_THREAD_MIN,10));
//            try {
//                innerConsumer.start();
//            } catch (MQClientException e) {
//                log.error(e.getMessage(), e);
//                throw new RuntimeException(e);
//            }
//
//
//            if (Objects.nonNull(consumer)) {
//                consumer.shutdown();
//            }
//            consumer = innerConsumer;
//        });
//    }
//}
