package com.facishare.open.qywx.accountsync.crm;


import com.facishare.open.qywx.accountsync.config.ConfigCenter;

/**
 * <AUTHOR> url的组装
 * @Date 2021/2/23 19:46
 * @Version 1.0
 */
public class CrmUrlUtils {

    //crm rest接口前缀
    public static String preUrl= ConfigCenter.CRM_REST_OBJ_URL;

    public static String activeRecordUrl= ConfigCenter.ACTIVERECORDOBJ_URL;
    /**
     *  字段查询对象
     */
    public static String queryList(String apiName){
        return preUrl.concat(apiName).concat("/controller/List");
    }
}

