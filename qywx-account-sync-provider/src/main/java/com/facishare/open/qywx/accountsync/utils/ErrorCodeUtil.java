//package com.facishare.open.qywx.accountsync.utils;
//
//import org.springframework.context.support.AbstractMessageSource;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.Locale;
//import java.util.Map;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * 获取错误码
// * Created by liu<PERSON> on 2018/08/02
// */
//@Component
//public class ErrorCodeUtil {
//
//    @Resource
//    AbstractMessageSource messageSource;
//
//    public String getErrorString(String code, String locale) {
//        return messageSource.getMessage(code, null, Lang.of(locale).getLocale());
//    }
//
//    public String getErrorStringWithArgs(String code, Object[] args, Locale locale) {
//        return messageSource.getMessage(code, args, locale);
//    }
//
//    enum Lang {
//        zh_CN("zh-CN", Locale.SIMPLIFIED_CHINESE),
//        zh_TW("zh-TW", Locale.TRADITIONAL_CHINESE),
//        en("en", Locale.ENGLISH);
//
//        private static final Map<String, Lang> all;
//
//        static {
//            all = Arrays.stream(Lang.values()).collect(Collectors.toMap(Lang::getValue, a -> a));
//        }
//
//        private String value;
//        private Locale locale;
//
//
//        Lang(String value, Locale locale) {
//            this.value = value;
//            this.locale = locale;
//        }
//
//        public static Lang of(String value) {
//            //兼容fcp传值
//            if (Objects.nonNull(value)) {
//                value  = value.replaceAll("_","-");
//            }
//            return all.getOrDefault(value, Lang.zh_CN);
//        }
//
//        public String getValue() {
//            return value;
//        }
//
//        public Locale getLocale() {
//            return locale;
//        }
//    }
//}
