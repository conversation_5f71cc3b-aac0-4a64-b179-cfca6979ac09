package com.facishare.open.qywx.accountsync.manager;

import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.model.OaConnectorDataModel;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountsync.utils.XorUtils;
import com.fxiaoke.common.IpUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.OAConnectorDataLog;
import com.fxiaoke.log.dto.OAConnectorDataLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OAConnectorDataManager {
    public void send(String tenantId, Long createTime,
                     String chanel, String dataType, String outEa, String outUserId, String suiteTicket,
                     String suiteAccessToken, String errorCode, String errorMsg) {
        if(createTime == null) {
            createTime = System.currentTimeMillis();
        }

        if(StringUtils.isEmpty(tenantId)) {
            tenantId = "0";
        }

        //这里统一做加密
        String suiteTicketByXor = null;
        String suiteAccessTokenByXor = null;
        if(StringUtils.isNotEmpty(suiteTicket)) {
            suiteTicketByXor = XorUtils.EncodeByXor(suiteTicket, ConfigCenter.XOR_SECRET_KEY);
        }
        if(StringUtils.isNotEmpty(suiteAccessToken)) {
            suiteAccessTokenByXor = XorUtils.EncodeByXor(suiteTicket, ConfigCenter.XOR_SECRET_KEY);
        }

        OAConnectorDataLogDTO logDTO = OAConnectorDataLogDTO.builder()
                .appName(ConfigHelper.getProcessInfo().getName())
                .traceId(TraceUtil.get())
                .tenantId(tenantId)
                .createTime(createTime)
                .serverIp(IpUtil.getSiteLocalIp())
                .chanel(chanel)
                .dataType(dataType)
                .outEa(outEa)
                .outUserId(outUserId)
                .suiteTicket4Encryption(suiteTicketByXor)
                .suiteAccessToken4Encryption(suiteAccessTokenByXor)
                .errorCode(errorCode)
                .errorMsg(errorMsg)
                .build();
        log.info("OAConnectorDataManager.send,logDTO={}", logDTO);
        try {
            BizLogClient.send("biz-log-oaconnectordata", Pojo2Protobuf.toMessage(logDTO, OAConnectorDataLog.class).toByteArray());
        } catch (Exception e) {
            log.info("OAConnectorDataManager.send,exception={}",e.getMessage());
        }
    }

    public void send(OaConnectorDataModel oaConnectorDataModel) {
        this.send(oaConnectorDataModel.getTenantId(),
                oaConnectorDataModel.getCreateTime(),
                oaConnectorDataModel.getChanel(),
                oaConnectorDataModel.getDataType(),
                oaConnectorDataModel.getOutEa(),
                oaConnectorDataModel.getOutUserId(),
                oaConnectorDataModel.getSuiteTicket(),
                oaConnectorDataModel.getSuiteAccessToken(),
                oaConnectorDataModel.getErrorCode(),
                oaConnectorDataModel.getErrorMsg());
    }
}
