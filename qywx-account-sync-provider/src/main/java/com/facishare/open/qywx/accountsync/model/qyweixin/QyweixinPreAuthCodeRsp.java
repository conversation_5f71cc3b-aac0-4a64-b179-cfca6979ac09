package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by fengyh on 2018/6/15.
 */
@Data
public class QyweixinPreAuthCodeRsp implements Serializable {
    private int errcode;
    private String errmsg;
    private String pre_auth_code;
    private int expires_in;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
