package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinOrderInfoBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 * Created on 2019/2/18
 */
@Repository
public interface QyweixinOrderInfoDao  extends ICrudMapper<QyweixinOrderInfoBo> {

    @Insert("<script>" +"INSERT INTO `qyweixin_order_info` (order_id, app_id, order_status, order_type, paid_corpid, " +
            "suite_id, edition_id, edition_name, price, user_count, order_period, order_time, paid_time, begin_time, end_time, processing_status, operator_id)" +
            " VALUES(#{orderId}, #{appId}, #{orderStatus}, #{orderType}, #{paidCorpid}, #{suiteId}, #{editionId}, " +
            "#{editionName}, #{price}, #{userCount}, #{orderPeriod}, #{orderTime}, #{paidTime}, #{beginTime}, #{endTime}, #{processingStatus}, #{operatorId})" +
            " ON DUPLICATE KEY UPDATE " +
            " paid_time=#{paidTime}, begin_time=#{beginTime}, end_time=#{endTime}, order_status=#{orderStatus}, " +
            "order_from=#{orderFrom}, operator_corpid=#{operatorCorpid}, processing_status=#{processingStatus}, " +
            "operator_id=#{operatorId};</script>")
    int saveOrUpdateOrder(QyweixinOrderInfoBo qyweixinOrderInfoBo);

    /**
     * 通过paidCorpId查询最新的已支付订单
     *
     * @param paidCorpId 支付订单的企业账号
     * @param orderId    需要排除的订单ID，可为空
     * @return 订单信息
     */
    @Select("<script>" + "SELECT * FROM qyweixin_order_info WHERE paid_corpid=#{paidCorpId} AND order_status=1 " +
            "<if test='orderId != null and orderId != \"\"'> AND order_id!=#{orderId} </if> and app_id=#{appId}" +
            " ORDER BY paid_time DESC LIMIT 1;" +
            "</script>")
    QyweixinOrderInfoBo getLatestPaidOrder(@Param("paidCorpId") String paidCorpId,
                                           @Param("appId") String appId,
                                           @Param("orderId") String orderId);

    @Update("<script>" + "update qyweixin_order_info set paid_corpid=#{openOutEa} where paid_corpid=#{outEa}</script>")
    int BatchUpdateDepartmentBind(@Param("outEa") String outEa, @Param("openOutEa") String openOutEa);
}
