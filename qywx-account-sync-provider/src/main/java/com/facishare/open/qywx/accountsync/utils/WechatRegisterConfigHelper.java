package com.facishare.open.qywx.accountsync.utils;

import com.facishare.open.qywx.accountsync.model.MessageModel;
import com.github.autoconf.ConfigFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.yaml.snakeyaml.Yaml;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Created by mawr on 2016/12/12.
 */
@Component
public class WechatRegisterConfigHelper implements InitializingBean {

    private static volatile Map<String, MessageModel> wechatMessageValue = new HashMap<>();

    private static final String DEFAULT_DEPARTMENT_NAME_INVALID = "[^-()（）_a-zA-Z0-9\u4E00-\u9fAF\u3400-\u4dBF\u3300-\u33FF\uF900-\uFAFF]";
    private static Pattern departmentNameInvalidCharacterRegex;
    private static final String DEFAULT_EMPLOYEE_NAME_INVALID = "[^-·_a-zA-Z0-9\u4E00-\u9fAF\u3400-\u4dBF\u3300-\u33FF\uF900-\uFAFF]";
    private static Pattern employeeNameInvalidCharacterRegex;

    private static String crmCode;

    private static String userRoleUrl;

    private static String tryEdition;

    private static String baseEdition;

    private static String proEdition;

    private static String wechatCustomerOwner;

    private static String validateCodeValue;

    @Override
    public void afterPropertiesSet() throws Exception {

        ConfigFactory.getConfig("fs-webhook-provider", config -> {
            Map<String, String> ossValue = new HashMap<>(config.getAll());
            crmCode = ossValue.get("crmCode");
            userRoleUrl = ossValue.get("userRoleUrl");
            tryEdition = ossValue.get("tryEdition");
            baseEdition = ossValue.get("baseEdition");
            proEdition = ossValue.get("proEdition");
            wechatCustomerOwner = ossValue.get("wechatCustomerOwner");
        });

        ConfigFactory.getConfig("fs-register-provider-vcode", config -> {
            Map<String, String> vcodeValue = new HashMap<>(config.getAll());
            validateCodeValue = vcodeValue.get("SendAgentRegisterMessageText");
        });

        ConfigFactory.getInstance().getConfig("fs-webhook-wechat-message", (iConfig -> {

            Map<String, Map<String, String>> configure = (Map<String, Map<String, String>>) new Yaml().load(iConfig.getString());
            if (CollectionUtils.isEmpty(configure)) {
                return;
            }

            configure.entrySet().stream().forEach(item -> wechatMessageValue.put(item.getKey(), new MessageModel(
                            item.getValue().get("title"),
                            item.getValue().get("url"),
                            item.getValue().get("picUrl")
                    ))
            );
        }));

        ConfigFactory.getConfig("fs-organization-adapter-properties", config -> {
            Map<String, String> all = config.getAll();

            String departmentNameInvalid = config.get("departmentNameInvalid", DEFAULT_DEPARTMENT_NAME_INVALID);
            departmentNameInvalidCharacterRegex = Pattern.compile(departmentNameInvalid);

            String employeeNameInvalid = config.get("employeeNameInvalid", DEFAULT_EMPLOYEE_NAME_INVALID);
            employeeNameInvalidCharacterRegex = Pattern.compile(employeeNameInvalid);
        });
    }

    public static Pattern getEmployeeNameRegex() {
        return employeeNameInvalidCharacterRegex;
    }

    public static Pattern getDepartmentNameRegex() {
        return departmentNameInvalidCharacterRegex;
    }

    public static String getCrmCode() {
        return crmCode;
    }

    public static String getUserRoleUrl() {
        return userRoleUrl;
    }

    public static String getTryEdition() {
        return tryEdition;
    }

    public static String getBaseEdition() {
        return baseEdition;
    }

    public static String getProEdition() {
        return proEdition;
    }

    public static String getWechatCustomerOwner() {
        return wechatCustomerOwner;
    }

    public static String getValidateCodeValue() {
        return validateCodeValue;
    }

    public static Map<String, MessageModel> getWechatMessageValue() {
        return wechatMessageValue;
    }
}