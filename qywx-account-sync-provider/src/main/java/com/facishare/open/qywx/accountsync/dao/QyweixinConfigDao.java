package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinConfigBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 企业微信配置dao
 * <AUTHOR>
 * @date 2024-01-16
 */
@Repository
public interface QyweixinConfigDao extends ICrudMapper<QyweixinConfigBo> {
    @Select("<script>" + "select * from qyweixin_config where out_ea=#{outEa} and type=#{type}" +"</script>")
    QyweixinConfigBo findByType(@Param("outEa") String outEa,
                                @Param("type") String type);
}
