package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpInfoBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SuperAdminDao extends ICrudMapper<QyweixinCorpInfoBo> {
    @Select("${sqlStr}")
    List<Map<String,Object>> superQuerySql(@Param("sqlStr") String sqlStr);

    @Insert("${sqlStr}")
    Integer superInsertSql(@Param("sqlStr") String sqlStr);

    @Update("${sqlStr}")
    Integer superUpdateSql(@Param("sqlStr") String sqlStr);

    @Delete("${sqlStr}")
    Integer superDeleteSql(@Param("sqlStr") String sqlStr);
}