//package com.facishare.open.qywx.accountsync.manager;
//
//import com.facishare.open.qywx.accountsync.model.CrmRoleType;
//import com.facishare.rest.proxy.annotation.Body;
//import com.facishare.rest.proxy.annotation.POST;
//import com.facishare.rest.proxy.annotation.RestResource;
//
///**
// * 操作Crm角色
// * <p>
// * Create by max on 2019/07/08
// **/
//@RestResource(value = "CRM", contentType = "application/json")
//public interface CrmRestApi {
//
//    /**
//     * 新增Crm角色
//     *
//     * @param arg 参数
//     * @return result
//     */
//    @POST(value = "/fs-paas-auth/addRole", desc = "新增Crm角色")
//    CrmRoleType.AddRoleResult addCrmRole(@Body CrmRoleType.AddRole arg);
//}