package com.facishare.open.qywx.accountsync.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.model.EmployeeBindModel;
import com.facishare.open.feishu.syncapi.model.PageModel;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.utils.PageUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.QueryBindArg;
import com.facishare.open.qywx.accountinner.arg.QueryOutUnbindArg;
import com.facishare.open.qywx.accountinner.arg.QueryUnBindArg;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.enums.SearchEmployeeInfoEnum;
import com.facishare.open.qywx.accountinner.model.*;
import com.facishare.open.qywx.accountinner.model.qywx.EmployeeBindFile;
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinDepartmentBind;
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinEmployeeBind;
import com.facishare.open.qywx.accountinner.result.ExportDataResult;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.ContactBindStatusEnum;
import com.facishare.open.qywx.accountsync.core.enums.QyweixinBindStatusEnum;
import com.facishare.open.qywx.accountsync.core.enums.QyweixinBindTypeEnum;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.dao.QyweixinContactBindDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpInfoDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinUserDao;
import com.facishare.open.qywx.accountsync.excel.FileManager;
import com.facishare.open.qywx.accountsync.manager.*;
import com.facishare.open.qywx.accountsync.model.EaMappingModel;
import com.facishare.open.qywx.accountsync.model.GetFsUserIdsByRestResult;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinContactBindBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinUserBo;
import com.facishare.open.qywx.accountsync.mongo.document.OutDepartmentInfoDoc;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.accountsync.utils.FSAccountUtil;
import com.facishare.open.qywx.accountsync.utils.GrayUtils;
import com.facishare.open.qywx.accountsync.utils.PinYinUtil;
import com.facishare.open.qywx.i18n.CustomCellWriteHandler;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.facishare.open.qywx.i18n.I18NStringManager;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeesDtoArg;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult;
import com.facishare.organization.api.model.employee.result.GetAllEmployeesDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.common.Pair;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/09/26
 */
@Slf4j
public class ContactBindInnerServiceImpl implements ContactBindInnerService {

    @Autowired
    private QyweixinContactBindDao qyweixinContactBindDao;

    @Autowired
    private QyweixinUserDao qyweixinUserDao;

    @Autowired
    private QyweixinCorpBindDao qyweixinCorpBindDao;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @Autowired
    private QYWeixinManager qyWeixinManager;

    @Autowired
    private FsManager fsManager;

    @Autowired
    private CorpManager corpManager;

    @Autowired
    private QyweixinCorpInfoDao qyweixinCorpInfoDao;

    @Autowired
    EmployeeProviderService employeeProviderService;

    @Autowired
    private FileManager fileManager;

    @Autowired
    private OutDepartmentInfoManger outDepartmentInfoManger;

    @Autowired
    private OutUserInfoManger outUserInfoManger;

    @Autowired
    private ContactsService contactsService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private I18NStringManager i18NStringManager;

    @ReloadableProperty("contactAppId")
    private String contactAppId;

    @ReloadableProperty("gray_ea")
    private String grayEa;

    @ReloadableProperty("repAppId")
    private String repAppId;

    /**
     * 获取企业微信代开发应用可见范围内的所有员工，包括部门和标签下面的员工
     * @param fsEa
     * @param type 0 : 排除已绑定用户 1：全量-包含已绑定用户
     * @return
     */
//    @Override
//    public Result<List<SimpleContactBindInfo>> getWXAccounts(String fsEa, String type, String outEa) {
//        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(),
//                fsEa,
//                outEa,
//                ConfigCenter.crm_domain);
//        if (ObjectUtils.isEmpty(mapping)) {
//            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(),"没有找到和当前CRM企业绑定的企业微信企业", I18NStringEnum.s97.getI18nKey());
//        }
//        QyweixinDepartmentEmployeeListRsp departmentEmployeeListResult = new QyweixinDepartmentEmployeeListRsp();
//        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(mapping.getOutEa(), repAppId);
//        if(corpBindBo==null) {
//            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(),"请安装代开发应用", I18NStringEnum.s98.getI18nKey());
//        }
//
//        List<QyweixinUserDetailInfoRsp> userList = getAllEmployeeListVisible2App(mapping.getOutEa(),mapping.getDepId());
//        departmentEmployeeListResult.setUserlist(userList);
//        log.info("ContactBindInnerServiceImpl.getWXAccounts,departmentEmployeeListResult={}", departmentEmployeeListResult);
//
//        Map<String, QyweixinDepartmentRsp> departmentInfoMap = getQyweixinDepartmentInfoMap(mapping.getOutEa());
//        log.info("ContactBindInnerServiceImpl.getWXAccounts,departmentInfoMap={}", departmentInfoMap);
//
//        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
//
//        List<String> bindOutEas=qyweixinAccountBindInnerService.queryIsvAccountBindByFsEaOnlyOutAccount2(fsEa, mainAppId, mapping.getOutEa());
//        Map<String, QyweixinDepartmentRsp> finalDepartmentInfoMap = departmentInfoMap;
//        List<SimpleContactBindInfo> excludeBindInfo = departmentEmployeeListResult.getUserlist().stream()
//                .map(v -> convertContactBindInfo(v, finalDepartmentInfoMap)).collect(Collectors.toList()).stream()
//                .filter(v -> !bindOutEas.contains(v.getQywxEmployeeAccount()))
//                .collect(Collectors.toList());
//
//        return new Result<>(excludeBindInfo);
//    }

    //检测通讯录是否取消授权   已取消：true  未取消：false
//    @Override
//    public Boolean checkContactAuth(String contactAppId, String corpId) {
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetCorptokenRsp> corpAccessTokenRspResult = qyWeixinManager.getCorpAccessTokenForCheck(contactAppId, corpId);
//        if (!corpAccessTokenRspResult.isSuccess() && corpAccessTokenRspResult.getCode().equals("40084")) {
//            return true;
//        } else {
//            return false;
//        }
//    }

//    @Override
//    public Result<Object> getContactAppBind(String fsEa, String outEa) {
//        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
//        //判断绑定类型，若为新建渠道0 则提示无需重复绑定；若为绑定渠道1 则显示绑定页面
//        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(),
//                fsEa,
//                outEa,
//                ConfigCenter.crm_domain);
//        log.info("getContactAppBind, mapping={},connectParam={}",mapping,outEa);
//        if (null != mapping) {
//            if (mapping.getBindType() == 0) {
//                log.info("trace queryMappingFromOutFsEa getBindType{} 0:新建企业，1:原有企业", mapping.getBindType());
//                //String errorData = errorCodeUtil.getErrorString(ErrorRefer.CONTACT_NOT_ALLOW.getCode(), Locale.SIMPLIFIED_CHINESE.toLanguageTag());
//                return Result.newInstance(ErrorRefer.CONTACT_NOT_ALLOW);
//            }
//        }
//
//        //判断企业是否在白名单内，不在白名单内则屏蔽绑定接口
//        if (!GrayUtils.checkInGrayByEa(grayEa, fsEa)) {
////            String errorData = errorCodeUtil.getErrorString(ErrorRefer.CONTACT_NOT_AUTHORIZED.getCode(), Locale.SIMPLIFIED_CHINESE.toLanguageTag());
////            log.info("trace getContactAppBind getErrorString data:{}", errorData);
//            return Result.newInstance(ErrorRefer.CONTACT_NOT_AUTHORIZED);
//        }
//
//        QyweixinContactBindBo qyweixinContactBindBo = new QyweixinContactBindBo();
//        qyweixinContactBindBo.setFsEa(fsEa);
//        List<QyweixinContactBindBo> contactBindEntity = qyweixinContactBindDao.findByEntity(qyweixinContactBindBo);
//        if (contactBindEntity.isEmpty()) {
//            return Result.newInstance(ErrorRefer.CONTACT_NOT_BIND);
//        }
//
//        if (ContactBindStatusEnum.HAVE_BINDING_FS_EA.getCode().equals(contactBindEntity.get(0).getStatus())) {
//            return Result.newInstance(ErrorRefer.CONTACT_QYWX_BIND_EXIT);
//        }
//
//        if (ContactBindStatusEnum.HAVE_BINDING_QYWX_EA.getCode().equals(contactBindEntity.get(0).getStatus())) {
//            return Result.newInstance(ErrorRefer.CONTACT_FSEA_BIND_EXIT);
//        }
//
//        if (ContactBindStatusEnum.FIRST_BIND.getCode().equals(contactBindEntity.get(0).getStatus())) {
//            Boolean contactAuth = checkContactAuth(getAppId(contactBindEntity.get(0).getCorpId()), contactBindEntity.get(0).getCorpId());
//            if (contactAuth == true) {
//                return Result.newInstance(ErrorRefer.CONTACT_CANCEL_BIND);
//            }
//            JSONObject qywxInfo = new JSONObject();
//            qywxInfo.put("qywxCorpId", contactBindEntity.get(0).getCorpId());
//            qywxInfo.put("qywxCorpName", contactBindEntity.get(0).getCorpName());
//
//            //检测有绑定数据 》 更新数据，返回 NOT_FIRST_BIND(3,"已绑定")
//            int countAccount = qyweixinAccountBindInnerService.countAccountBind(fsEa, mainAppId);
//            if (countAccount > 0) {
//                contactBindEntity.get(0).setStatus(ContactBindStatusEnum.NOT_FIRST_BIND.getCode());
//                qyweixinContactBindDao.save(contactBindEntity.get(0));
//                return new Result<>(qywxInfo);
//            }
//
//            return Result.newInstance2(ErrorRefer.CONTACT_FIRST_BIND,qywxInfo);
//
//        }
//
//        if (ContactBindStatusEnum.NOT_FIRST_BIND.getCode().equals(contactBindEntity.get(0).getStatus())) {
//            Boolean contactAuth = checkContactAuth(getAppId(contactBindEntity.get(0).getCorpId()), contactBindEntity.get(0).getCorpId());
//            if (contactAuth == true) {
//                return Result.newInstance(ErrorRefer.CONTACT_CANCEL_BIND);
//            }
//            JSONObject qywxInfo = new JSONObject();
//            qywxInfo.put("qywxCorpId", contactBindEntity.get(0).getCorpId());
//            qywxInfo.put("qywxCorpName", contactBindEntity.get(0).getCorpName());
//            return new Result<>(qywxInfo);
//        }
//
//        if (ContactBindStatusEnum.INIT_STATUS.getCode().equals(contactBindEntity.get(0).getStatus())) {
//            return Result.newInstance(ErrorRefer.CONTACT_NOT_BIND);
//        }
//        return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
//
//    }

    private void insertOrUpdateUser(String outEa,String userId,String name) {
        QyweixinUserBo userBo = new QyweixinUserBo();
        userBo.setOutEa(outEa);
        userBo.setUserId(userId);
        List<QyweixinUserBo> userBoList = qyweixinUserDao.findByEntity(userBo);
        if (CollectionUtils.isEmpty(userBoList)) {
            userBo.setUserName(name);
            int count = qyweixinUserDao.insert(userBo);
            if(count!=1) {
                log.info("ContactBindInnerServiceImpl.insertOrUpdateUser,insert failed,userBo={}",userBo);
            }
        } else {
            QyweixinUserBo qyweixinUserBo = userBoList.get(0);
            qyweixinUserBo.setUserName(name);
            int count = qyweixinUserDao.update(qyweixinUserBo);
            if(count!=1) {
                log.info("ContactBindInnerServiceImpl.insertOrUpdateUser,update failed,qyweixinUserBo={}",qyweixinUserBo);
            }
        }
    }

    /**
     * 缓存代开发应用标签下的员工
     * @param outEa
     */
    @Override
    public void getTagEmployeeListAndSave(String outEa) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinTagListRsp> tagListRspResult = qyWeixinManager.getTagInfoList(repAppId, outEa);
        log.info("ContactBindInnerServiceImpl.getTagEmployeeListAndSave,tagListRsp={}",tagListRspResult);

        if(tagListRspResult.isSuccess() && tagListRspResult.getData()!=null && CollectionUtils.isNotEmpty(tagListRspResult.getData().getTaglist())) {
            for(QyweixinTagListRsp.QyweixinTagRsp tagRsp : tagListRspResult.getData().getTaglist()) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = qyWeixinManager.getTagEmployeeList(repAppId, outEa, tagRsp.getTagid());
                if(!tagEmployeeListRspResult.isSuccess() || ObjectUtils.isEmpty(tagEmployeeListRspResult.getData())) {
                    continue;
                }
                QyweixinTagEmployeeListRsp tagEmployeeListRsp = tagEmployeeListRspResult.getData();
                if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getUserlist())) {
                    for(QyweixinUserDetailInfoRsp userDetailInfoRsp : tagEmployeeListRsp.getUserlist()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> rspResult = qyWeixinManager.getUserInfo(repAppId, outEa, userDetailInfoRsp.getUserid());
                        if(!rspResult.isSuccess() || ObjectUtils.isEmpty(rspResult.getData())) continue;
                        QyweixinUserDetailInfoRsp rsp = rspResult.getData();
                        insertOrUpdateUser(outEa,rsp.getUserid(),rsp.getMobile());
                    }
                }
                //获取TAG下的部门下的员工
                if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getPartylist())) {
                    for(String depId : tagEmployeeListRsp.getPartylist()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(repAppId,
                                outEa, depId);
                        if(!departmentEmployeeListResult.isSuccess() || departmentEmployeeListResult.getData()==null || CollectionUtils.isEmpty(departmentEmployeeListResult.getData().getUserlist())) continue;

                        for(QyweixinUserDetailInfoRsp userDetailInfoRsp : departmentEmployeeListResult.getData().getUserlist()) {
                            insertOrUpdateUser(outEa,userDetailInfoRsp.getUserid(),userDetailInfoRsp.getMobile());
                        }
                    }
                }
            }
        }
        log.info("ContactBindInnerServiceImpl.getTagEmployeeListAndSave,success");
    }

    @Override
    public Result<List<QyweixinUserDetailInfoRsp>> getAllEmployeeList(String outEa, String rootDepId) {
        return new Result<>(getAllEmployeeListVisible2App(outEa, rootDepId));
    }

    /**
     * 获取企业微信代开发应用可见范围内的所有员工，部门，标签以及部门和标签下面的所有员工
     * 必须使用代开发应用，不然拿到的用户信息可能都是密文，包括用户名称
     * @param outEa
     * @return
     */
    public List<QyweixinUserDetailInfoRsp> getAllEmployeeListVisible2App(String outEa,String rootDepId) {
        rootDepId = StringUtils.isEmpty(rootDepId) ? "1" : rootDepId;
        String validRepAppId = qyweixinGatewayInnerService.getValidRepAppId(outEa).getData();
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(outEa,validRepAppId);
        List<QyweixinUserDetailInfoRsp> userList = new ArrayList<>();

        if(appInfoResult.isSuccess() && ObjectUtils.isNotEmpty(appInfoResult.getData())) {
            AppInfo appInfo = appInfoResult.getData();
            //非一对多绑定场景
            if(StringUtils.equalsIgnoreCase(rootDepId,"1")) {
                //CRM应用可见范围内的人员,客户单独添加在可见范围内的人员
                if(appInfo.getAllow_userinfos()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_userinfos().getUser())) {
                    for(AppInfo.AllowUserInfos.User user : appInfo.getAllow_userinfos().getUser()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = qyWeixinManager.getUserInfo(validRepAppId, outEa, user.getUserid());
                        if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                            userList.add(userDetailInfoRspResult.getData());
                        }
                    }
                }

                //CRM应用可见范围内的部门
                if(appInfo.getAllow_partys()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_partys().getPartyid())) {
                    for(String depId : appInfo.getAllow_partys().getPartyid()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListRspResult = qyWeixinManager.getDepartmentEmployeeList(validRepAppId, outEa, depId);
                        if(departmentEmployeeListRspResult.isSuccess() && departmentEmployeeListRspResult.getData()!=null) {
                            userList.addAll(departmentEmployeeListRspResult.getData().getUserlist());
                        }
                    }
                }

                //CRM可见范围内的标签
                if(appInfo.getAllow_tags()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
                    for(String tagId : appInfo.getAllow_tags().getTagid()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = qyWeixinManager.getTagEmployeeList(validRepAppId, outEa, tagId);
                        if(tagEmployeeListRspResult.isSuccess() && tagEmployeeListRspResult.getData()!=null) {
                            QyweixinTagEmployeeListRsp tagEmployeeListRsp = tagEmployeeListRspResult.getData();
                            if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getUserlist())) {
                                userList.addAll(tagEmployeeListRsp.getUserlist());
                            }
                            if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getPartylist())) {
                                userList.addAll(getDepartmentUserList(tagEmployeeListRsp.getPartylist(),outEa));
                            }
                        }
                    }
                }
            } else {
                //一个企业微信对多个CRM场景，可见范围内只能放根部门或者根部门下面的一级部门
                //CRM应用可见范围内的部门
                if(appInfo.getAllow_partys()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_partys().getPartyid())) {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListRspResult = qyWeixinManager.getDepartmentEmployeeList(validRepAppId, outEa, rootDepId);
                    if(departmentEmployeeListRspResult.isSuccess() && departmentEmployeeListRspResult.getData()!=null) {
                        userList.addAll(departmentEmployeeListRspResult.getData().getUserlist());
                    }
                }
            }
        }

        //根据userId来去重
        List<QyweixinUserDetailInfoRsp> distinctUserList = new ArrayList<>();
        for (QyweixinUserDetailInfoRsp userDetailInfoRsp : userList) {
            List<QyweixinUserDetailInfoRsp> list = distinctUserList.stream()
                    .filter(rsp -> StringUtils.equalsIgnoreCase(userDetailInfoRsp.getUserid(), rsp.getUserid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                distinctUserList.add(userDetailInfoRsp);
            }
        }
        log.info("ContactBindInnerServiceImpl.getAllEmployeeListVisible2App,distinctUserList={},time={}",
                distinctUserList);
        return distinctUserList;
    }

    private List<QyweixinUserDetailInfoRsp> getDepartmentUserList(List<String> depList,String corpId) {
        List<QyweixinUserDetailInfoRsp> userList = new ArrayList<>();
        for(String depId : depList) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(repAppId,
                    corpId, depId);
            if(departmentEmployeeListResult.isSuccess() && departmentEmployeeListResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListResult.getData().getUserlist())) {
                userList.addAll(departmentEmployeeListResult.getData().getUserlist());
            }
        }
        return userList;
    }

    /**
     * 获取企业微信代开发应用可见范围内的所有员工，部门，标签以及部门和标签下面的所有员工
     * 必须使用代开发应用，不然拿到的用户信息可能都是密文，包括用户名称
     * 这个专门给自动账号绑定用的，实时获取人员详情，标签人员详情
     * @param outEa
     * @return
     */
    public List<QyweixinUserDetailInfoRsp> getAllEmployeeListVisible2App2(String outEa,String rootDepId) {
        rootDepId = StringUtils.isEmpty(rootDepId) ? "1" : rootDepId;
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(outEa,repAppId);
        List<QyweixinUserDetailInfoRsp> userList = new ArrayList<>();

        if(appInfoResult.isSuccess() && ObjectUtils.isNotEmpty(appInfoResult.getData())) {
            AppInfo appInfo = appInfoResult.getData();
            //非一对多绑定场景
            if(StringUtils.equalsIgnoreCase(rootDepId,"1")) {
                //CRM应用可见范围内的人员,客户单独添加在可见范围内的人员
                if(appInfo.getAllow_userinfos()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_userinfos().getUser())) {
                    for(AppInfo.AllowUserInfos.User user : appInfo.getAllow_userinfos().getUser()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = qyWeixinManager.getUserInfo(repAppId, outEa, user.getUserid());
                        if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                            QyweixinUserDetailInfoRsp userDetailInfoRsp = userDetailInfoRspResult.getData();
                            userList.add(userDetailInfoRsp);
                        }
                    }
                }

                //CRM应用可见范围内的部门
                if(appInfo.getAllow_partys()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_partys().getPartyid())) {
                    for(String depId : appInfo.getAllow_partys().getPartyid()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListRspResult = qyWeixinManager.getDepartmentEmployeeList(repAppId, outEa, depId);
                        if(departmentEmployeeListRspResult.isSuccess() && departmentEmployeeListRspResult.getData()!=null) {
                            userList.addAll(departmentEmployeeListRspResult.getData().getUserlist());
                        }
                    }
                }

                //CRM可见范围内的标签
                if(appInfo.getAllow_tags()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
                    for(String tagId : appInfo.getAllow_tags().getTagid()) {
                        com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = qyWeixinManager.getTagEmployeeList(repAppId, outEa, tagId);
                        if(tagEmployeeListRspResult.isSuccess() && tagEmployeeListRspResult.getData()!=null) {
                            QyweixinTagEmployeeListRsp tagEmployeeListRsp = tagEmployeeListRspResult.getData();
                            if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getUserlist())) {
                                //还需要调用人员详情接口
                                for(QyweixinUserDetailInfoRsp userDetailInfoRsp : tagEmployeeListRsp.getUserlist()) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> updateUserDetailInfoRspResult = qyWeixinManager.getUserInfo(repAppId, outEa, userDetailInfoRsp.getUserid());
                                    if(updateUserDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(updateUserDetailInfoRspResult.getData())) {
                                        QyweixinUserDetailInfoRsp updateUserDetailInfoRsp = updateUserDetailInfoRspResult.getData();
                                        userList.add(updateUserDetailInfoRsp);
                                    }
                                }
                                userList.addAll(tagEmployeeListRsp.getUserlist());
                            }
                            if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getPartylist())) {
                                userList.addAll(getDepartmentUserList(tagEmployeeListRsp.getPartylist(),outEa));
                            }
                        }
                    }
                }
            } else {
                //一个企业微信对多个CRM场景，可见范围内只能放根部门或者根部门下面的一级部门
                //CRM应用可见范围内的部门
                if(appInfo.getAllow_partys()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_partys().getPartyid())) {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListRspResult = qyWeixinManager.getDepartmentEmployeeList(repAppId, outEa, rootDepId);
                    if(departmentEmployeeListRspResult.isSuccess() && departmentEmployeeListRspResult.getData()!=null) {
                        userList.addAll(departmentEmployeeListRspResult.getData().getUserlist());
                    }
                }
            }
        }

        //根据userId来去重
        List<QyweixinUserDetailInfoRsp> distinctUserList = new ArrayList<>();
        for (QyweixinUserDetailInfoRsp userDetailInfoRsp : userList) {
            List<QyweixinUserDetailInfoRsp> list = distinctUserList.stream()
                    .filter(rsp -> StringUtils.equalsIgnoreCase(userDetailInfoRsp.getUserid(), rsp.getUserid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                distinctUserList.add(userDetailInfoRsp);
            }
        }
        log.info("ContactBindInnerServiceImpl.getAllEmployeeListVisible2App,distinctUserList={},time={}",
                distinctUserList);
        return distinctUserList;
    }

    @Override
    public Result<List<ContactBindInfo>> automaticAccountMactch(String fsEa, Integer employeeId, String outEa) {
        log.info("ContactBindInnerServiceImpl.automaticAccountMactch,fsEa={},employeeId={}",fsEa,employeeId);
        GetFsUserIdsByRestResult fsUserIds = fsManager.getFsUserIdsByRestService(fsEa, employeeId);
        if(fsUserIds.getErrCode()!=0) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(),fsUserIds.getErrMsg(),null);
        }

        List<Integer> userIdList = fsUserIds.getData().stream()
                .map(v -> Integer.valueOf(v.substring(v.lastIndexOf(".") + 1)))
                .collect(Collectors.toList());

        List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(fsEa, employeeId,userIdList);
        Map<Integer, String> allCirclesMap = fsManager.getAllCircles(fsEa, employeeId);

        //使用代开发进行获取
        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(),
                fsEa,
                outEa,
                ConfigCenter.crm_domain);
        if (ObjectUtils.isEmpty(mapping) || ObjectUtils.isEmpty(mapping.getIsvOutEa())) {
            return new Result<>();
        }

        //优先选择代开发授权
        if (!repAppId.equals(getAppIdByCorpId(mapping.getIsvOutEa()))) {
            return new Result<>();
        }

        List<QyweixinUserDetailInfoRsp> userlist = getAllEmployeeListVisible2App(mapping.getIsvOutEa(),mapping.getDepId());

        QyweixinDepartmentEmployeeListRsp departmentEmployeeListResult = new QyweixinDepartmentEmployeeListRsp();
        if (!ObjectUtils.isEmpty(userlist)) {
            List<QyweixinUserDetailInfoRsp> list = new ArrayList<> (new LinkedHashSet<>(userlist));
            departmentEmployeeListResult.setUserlist(list);
        }
        log.info("ContactBindInnerServiceImpl.automaticAccountMactch,departmentEmployeeListResult={}",departmentEmployeeListResult);
        if (ObjectUtils.isEmpty(departmentEmployeeListResult.getUserlist())) {
            return new Result<>();
        }

        //手机号出现重复的，默认使用其中一个
        Map<String, QyweixinUserDetailInfoRsp>  qyweixinEmployeeMap = departmentEmployeeListResult.getUserlist().stream().filter(v -> !StringUtils.isBlank(v.getMobile()))
                .collect(Collectors.toMap(QyweixinUserDetailInfoRsp::getMobile, Function.identity(),
                        (value1, value2) -> "1".equals(String.valueOf(value1.getStatus())) ? value1 : value2));

        log.info("ContactBindInnerServiceImpl.automaticAccountMactch,qyweixinEmployeeMap={}",qyweixinEmployeeMap);

        List<ContactBindInfo> contactBindCollect = employeeInfos.stream().map(employeeInfo -> {
            ContactBindInfo contactBindInfo = new ContactBindInfo();
            contactBindInfo.setFsDepartmentName(allCirclesMap.get(getMainDepId(employeeInfo)));
            contactBindInfo.setFsEmployeeAccount(employeeInfo.getEmployeeId() + "");
            contactBindInfo.setFsEmployeeMobile(employeeInfo.getMobile());
            contactBindInfo.setFsEmployeeName(employeeInfo.getName());
            contactBindInfo.setFsEmployeeNamePinYin(PinYinUtil.getFirstSpell(employeeInfo.getName()).toLowerCase());

            String fsEmployeeMobile = employeeInfo.getMobile().contains("-") ? employeeInfo.getMobile().replaceAll("-", "") : employeeInfo.getMobile();
            if (!StringUtils.isBlank(employeeInfo.getMobile()) && null != qyweixinEmployeeMap.get(fsEmployeeMobile)) {
                //对国际号码支持  纷享格式 +123-************   企业微信格式 +123************
                contactBindInfo.setQywxEmployeeAccount(qyweixinEmployeeMap.get(fsEmployeeMobile).getUserid());
                contactBindInfo.setQywxEmployeeMobile(qyweixinEmployeeMap.get(fsEmployeeMobile).getMobile());
                contactBindInfo.setQywxEmployeeName(qyweixinEmployeeMap.get(fsEmployeeMobile).getName());
                contactBindInfo.setQywxEmployeeEmail(qyweixinEmployeeMap.get(fsEmployeeMobile).getEmail());
            }
            return contactBindInfo;
        }).sorted(Comparator.comparing(ContactBindInfo::getFsEmployeeNamePinYin)).collect(Collectors.toList());

        //匹配了手机号的放到前面
        contactBindCollect.sort((a, b) -> {
            String comA = StringUtils.isBlank(a.getQywxEmployeeMobile()) ? "null" : "mactch_ok";
            String comB = StringUtils.isBlank(b.getQywxEmployeeMobile()) ? "null" : "mactch_ok";
            return comA.compareTo(comB);
        });
        log.info("ContactBindInnerServiceImpl.automaticAccountMactch,contactBindCollect={}",contactBindCollect);
        return new Result<>(contactBindCollect);
    }

    public int getMainDepId(EmployeeDto employeeDto) {
        List<Integer> mainDepartmentIds = employeeDto.getMainDepartmentIds();
        return org.apache.commons.collections4.CollectionUtils.isEmpty(mainDepartmentIds) ?
                GlobalValue.ALL_COMPANY_DEPARTMENT_ID : mainDepartmentIds.get(0);
    }

    @Override
    public Result<String> saveAccountBind(String fsEa, List<String> bindList, String outEa) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
                qyweixinAccountBindService.fsEaToOutEaResult2("qywx", fsEa, outEa);
        if (!qyweixinCorpIDResult.isSuccess() || ObjectUtils.isEmpty(qyweixinCorpIDResult.getData()) || StringUtils.isEmpty(qyweixinCorpIDResult.getData().getIsvOutEa())) {
            return new Result<String>().addError(qyweixinCorpIDResult.getErrorCode(),null,null);
        }
        String finalOutEa = qyweixinCorpIDResult.getData().getOutEa();
        qyweixinAccountBindService.bindAccountEmployeeMapping(bindList.stream().map(v -> {
            QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping = new QyweixinAccountEmployeeMapping();
            String[] bindInfoArray = v.split("&");
            if (!StringUtils.isBlank(bindInfoArray[0]) && !StringUtils.isBlank(bindInfoArray[1])) {
                qyweixinAccountEmployeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
                qyweixinAccountEmployeeMapping.setOutEa(finalOutEa);
                qyweixinAccountEmployeeMapping.setFsAccount("E." + fsEa + "." + bindInfoArray[0]);

                String isvAccount = bindInfoArray[1];
                String outAccount = isvAccount;

                if(!ConfigCenter.SERVICE_PROVIDER.contains(fsEa)) {
                    com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> openUserIdInfoListResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(isvAccount),
                            finalOutEa);
                    log.info("ContactBindInnerServiceImpl.saveAccountBind,openUserIdInfoList={}",openUserIdInfoListResult);
                    if(openUserIdInfoListResult.isSuccess() && CollectionUtils.isNotEmpty(openUserIdInfoListResult.getData())) {
                        outAccount = openUserIdInfoListResult.getData().get(0).getOpen_userid();
                    }
                }

                qyweixinAccountEmployeeMapping.setIsvAccount(isvAccount);
                qyweixinAccountEmployeeMapping.setOutAccount(outAccount);

                log.info("ContactBindInnerServiceImpl.saveAccountBind,qyweixinAccountEmployeeMapping={}",qyweixinAccountEmployeeMapping);
            }
            return qyweixinAccountEmployeeMapping;
        }).collect(Collectors.toList()));

        //触发发送欢迎消息
        corpManager.sendMsgAfterAccountBind(bindList, finalOutEa, fsEa);

        return new Result<>();
    }

    @Override
    public Result<String> deleteAccountBind(String fsEa, List<String> fsEmployeeAccountList, String outEa) {
        //获取开通企业的管理员
        //需要使用到第三方服务商的corpid
        String mainAppid = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
        if(StringUtils.isEmpty(outEa)) {
            outEa = qyweixinAccountBindService.fsEaToOutEa("qywx", fsEa).getData();
        }
        Result<QyweixinAccountEnterpriseMapping> enterpriseMapping2 = qyweixinAccountBindInnerService.getEnterpriseMapping2(fsEa, outEa);
        Integer bindType = 0;
        if(enterpriseMapping2.getData()!=null) {
            bindType = enterpriseMapping2.getData().getBindType();
        }
        String fsAdminAccount = "";
        if(bindType==0) {
            String AdminUserId = corpManager.getAdminUserId(outEa);
            fsAdminAccount = qyweixinAccountBindService.outAccountToFsAccountBatch("qywx", fsEa, Lists.newArrayList(AdminUserId)).getData().get(AdminUserId);
            log.info("trace deleteAccountBind getFsAdminAccount result:{}", fsAdminAccount);
        }
        //获取员工fs_account并删除,但不允许删除企业开通者
        int result = qyweixinAccountBindInnerService.deleteAccountBind(fsAdminAccount,
                fsEmployeeAccountList.stream().map(v -> "E." + fsEa + "." + v).collect(Collectors.toList()), mainAppid, outEa);
        //含有企业开通者账号则提示解绑失败
        if (result == 0) {
            return Result.newInstance(ErrorRefer.CONTACT_DELETE_ADMIN);
        } else {
            return new Result<>();
        }
    }

//    @Override
//    public Result<String> updateAccountBind(String fsEa, List<String> bindList, String outEa) {
//        //QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa);
//        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
//        Result<List<QyweixinAccountEmployeeMapping>> qyweixinAccountEmployeeMappingResult =
//                qyweixinAccountBindInnerService.queryAccountBind2(bindList.stream().map(v -> "E." + fsEa + "." + v.split("&")[0]).collect(Collectors.toList()),
//                        mainAppId, outEa);
//        Map<String, String> bindMap = bindList.stream().collect(Collectors.toMap(v -> v.split("&")[0], v -> v.split("&")[1]));
//        for (QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping : qyweixinAccountEmployeeMappingResult.getData()) {
//            String qwOldUserId = qyweixinAccountEmployeeMapping.getOutAccount();
//            String isvAccount = bindMap.get(qyweixinAccountEmployeeMapping.getFsAccount().substring(qyweixinAccountEmployeeMapping.getFsAccount().lastIndexOf(".") + 1));
//            String outAccount = isvAccount;
//
//            //这里不需要进行转换明文密文了
////            if(!ConfigCenter.SERVICE_PROVIDER.contains(fsEa)) {
////                com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> openUserIdInfoListResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(isvAccount),mapping.getOutEa());
////                log.info("ContactBindInnerServiceImpl.updateAccountBind,openUserIdInfoList={}",openUserIdInfoListResult);
////                if(openUserIdInfoListResult.isSuccess() && CollectionUtils.isNotEmpty(openUserIdInfoListResult.getData())) {
////                    outAccount = openUserIdInfoListResult.getData().get(0).getOpen_userid();
////                }
////            }
//
//            qyweixinAccountEmployeeMapping.setIsvAccount(isvAccount);
//            qyweixinAccountEmployeeMapping.setOutAccount(outAccount);
//
//            log.info("ContactBindInnerServiceImpl.updateAccountBind,qyweixinAccountEmployeeMapping={}",qyweixinAccountEmployeeMapping);
//            qyweixinAccountBindInnerService.updateQyweixinAccountEmployee(qyweixinAccountEmployeeMapping, qwOldUserId);
//        }
//
//        return new Result<>();
//    }

//    @Override
//    public Result<List<ContactBindInfo>> getAccountBind(String fsEa, int bindType, Integer employeeId, String outEa, String outDepId) {
//        //绑定类型：0 :未绑定 1：
//        GetAllEmployeeIdsResult fsUserIds = fsManager.getAllEmployeeIds(fsEa);
//        List<Integer> userIdList = fsUserIds.getEmployeeIds();
//        log.info("ContactBindInnerServiceImpl.getAccountBind,userIdList={}",userIdList);
//
//        List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(fsEa, employeeId,userIdList);
//        log.info("ContactBindInnerServiceImpl.getAccountBind,employeeInfos={}",employeeInfos);
//        Map<Integer, String> allCirclesMap = fsManager.getAllCircles(fsEa, employeeId);
//        log.info("ContactBindInnerServiceImpl.getAccountBind,allCirclesMap={}",allCirclesMap);
//        Map<Integer, EmployeeDto> employeeInfoMaps = employeeInfos.stream().collect(Collectors.toMap(v -> v.getEmployeeId(),
//                Function.identity(),(v1,v2) -> v1));
//
//        log.info("ContactBindInnerServiceImpl.getAccountBind,employeeInfoMaps={}",employeeInfoMaps);
//        if(StringUtils.isEmpty(outEa)) {
//            com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
//                    qyweixinAccountBindService.fsEaToOutEaResult("qywx", fsEa);
//            if (!qyweixinCorpIDResult.isSuccess() || ObjectUtils.isEmpty(qyweixinCorpIDResult.getData())) {
//                return new Result<List<ContactBindInfo>>().addError(qyweixinCorpIDResult.getErrorCode(),null,null);
//            }
//            outEa = qyweixinCorpIDResult.getData().getIsvOutEa();
//            outDepId = qyweixinCorpIDResult.getData().getDepId();
//        }
//        outDepId = StringUtils.isEmpty(outDepId) ? "1" : outDepId;
//
//        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
//        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
//                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, mainAppId, outEa);
//        if (0 == bindType) {
//            // 已经绑定的企业微信账号Set集合
//            Set<String> outAccountsWithBind = Sets.newHashSetWithExpectedSize(64);
//            List<Integer> fsEaBindList = Lists.newArrayList();
//            fsEaBindList.addAll(userIdList);
//            //过滤已绑定的纷享用户
//            if (!qyweixinAccountEmployeeMappings.isEmpty()) {
//                Set<String> fsAccountSet1 = qyweixinAccountEmployeeMappings.stream()
//                        .map(v -> v.getFsAccount())
//                        .collect(Collectors.toSet());
////                List<Integer> fsAccountSet = fsAccountSet1.stream()
////                        .map(v -> Integer.valueOf(v.substring(v.lastIndexOf(".") + 1)))
////                        .collect(Collectors.toList());
//                List<Integer> fsAccountSet = new ArrayList<>();
//                for(String fsAccount : fsAccountSet1) {
//                    try {
//                        Integer empId = Integer.valueOf(fsAccount.substring(fsAccount.lastIndexOf(".") + 1));
//                        fsAccountSet.add(empId);
//                    } catch (Exception e) {
//                        log.info("ContactBindInnerServiceImpl.getAccountBind,emp mapping invalid,fsAccount={}",fsAccount);
//                    }
//                }
//                outAccountsWithBind = qyweixinAccountEmployeeMappings.stream()
//                        .map(QyweixinAccountEmployeeMapping::getIsvAccount)
//                        .collect(Collectors.toSet());
//                fsEaBindList = fsEaBindList.stream()
//                        .filter(v -> !fsAccountSet.contains(v))
//                        .collect(Collectors.toList());
//            }
//
//            QyweixinDepartmentEmployeeListRsp departmentEmployeeListResult = new QyweixinDepartmentEmployeeListRsp();
//            //优先选择代开发授权
//            if(StringUtils.isNotEmpty(outEa)) {
//                List<QyweixinUserDetailInfoRsp> userList = getAllEmployeeListVisible2App(outEa,
//                        outDepId);
//                departmentEmployeeListResult.setUserlist(userList);
//            }
//            log.info("ContactBindInnerServiceImpl.getAccountBind,departmentEmployeeListResult={}",departmentEmployeeListResult);
//            Map<String, QyweixinUserDetailInfoRsp> qyweixinEmployeeMap = departmentEmployeeListResult.getUserlist().stream()
//                    .filter(v -> !StringUtils.isBlank(v.getMobile()))
//                    .collect(Collectors.toMap(QyweixinUserDetailInfoRsp::getMobile, Function.identity(),
//                            (value1, value2) -> "1".equals(String.valueOf(value1.getStatus())) ? value1 : value2));
//            if (ObjectUtils.isEmpty(departmentEmployeeListResult.getUserlist()) || ObjectUtils.isEmpty(qyweixinEmployeeMap)) {
//                List<ContactBindInfo> contactBindCollect = fsEaBindList.stream()
//                        .map(fsUserId -> {
//                            EmployeeDto employeeInfo = employeeInfoMaps.get(fsUserId);
//                            if (employeeInfo == null) return null;
//                            ContactBindInfo contactBindInfo = new ContactBindInfo();
//                            Integer fsDepartmentId = getMainDepId(employeeInfo);
//                            contactBindInfo.setFsDepartmentName(allCirclesMap.get(fsDepartmentId));
//                            contactBindInfo.setFsDepartmentId(fsDepartmentId);
//                            contactBindInfo.setFsEmployeeAccount(employeeInfo.getEmployeeId() + "");
//                            contactBindInfo.setFsEmployeeMobile(employeeInfo.getMobile());
//                            contactBindInfo.setFsEmployeeName(employeeInfo.getName());
//                            contactBindInfo.setFsEmployeeNamePinYin(PinYinUtil.getFirstSpell(employeeInfo.getName()).toLowerCase());
//                            contactBindInfo.setFsEmployeeStatus(employeeInfo.getStatus().getValue());
//                            return contactBindInfo;
//                        })
//                        .filter(Objects::nonNull)
//                        .filter(v -> v.getFsEmployeeStatus() == 1)
//                        .sorted(Comparator.comparing(ContactBindInfo::getFsEmployeeNamePinYin))
//                        .collect(Collectors.toList());
//                return new Result<>(contactBindCollect);
//            }
//
//            log.info("ContactBindInnerServiceImpl.getAccountBind,bindType=0,qyweixinEmployeeMap={}",qyweixinEmployeeMap);
//            Set<String> finalOutAccountsWithBind = outAccountsWithBind;
//            List<ContactBindInfo> contactBindCollect = fsEaBindList.stream().map(fsUserId -> {
//                EmployeeDto employeeInfo = employeeInfoMaps.get(fsUserId);
//                if (employeeInfo == null) return null;
//                ContactBindInfo contactBindInfo = new ContactBindInfo();
//                Integer fsDepartmentId = getMainDepId(employeeInfo);
//                contactBindInfo.setFsDepartmentName(allCirclesMap.get(fsDepartmentId));
//                contactBindInfo.setFsDepartmentId(fsDepartmentId);
//                contactBindInfo.setFsEmployeeAccount(employeeInfo.getEmployeeId() + "");
//                contactBindInfo.setFsEmployeeMobile(employeeInfo.getMobile());
//                contactBindInfo.setFsEmployeeName(employeeInfo.getName());
//                contactBindInfo.setFsEmployeeNamePinYin(PinYinUtil.getFirstSpell(employeeInfo.getName()).toLowerCase());
//                contactBindInfo.setFsEmployeeStatus(employeeInfo.getStatus().getValue());
//
//                String fsEmployeeMobile = employeeInfo.getMobile().contains("-") ? employeeInfo.getMobile().replaceAll("-", "") : employeeInfo.getMobile();
//                QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = qyweixinEmployeeMap.get(fsEmployeeMobile);
//
//                if (!StringUtils.isBlank(employeeInfo.getMobile()) && qyweixinUserDetailInfoRsp!=null) {
//                    // 已经绑定的不包含当前的才会匹配
//                    if (!finalOutAccountsWithBind.contains(qyweixinUserDetailInfoRsp.getUserid())) {
//                        //对国际号码支持  纷享格式 +123-************   企业微信格式 +123************
//                        contactBindInfo.setQywxEmployeeAccount(qyweixinUserDetailInfoRsp.getUserid());
//                        contactBindInfo.setQywxEmployeeMobile(qyweixinUserDetailInfoRsp.getMobile());
//                        contactBindInfo.setQywxEmployeeName(qyweixinUserDetailInfoRsp.getName());
//                        contactBindInfo.setQywxEmployeeEmail(qyweixinUserDetailInfoRsp.getEmail());
//                    }
//                }
//                log.info("ContactBindInnerServiceImpl.getAccountBind,bindType=0,contactBindInfo={},qyweixinUserDetailInfoRsp={}",
//                        contactBindInfo,qyweixinUserDetailInfoRsp);
//                return contactBindInfo;
//            }).filter(Objects::nonNull)
//                    .filter(v -> v.getFsEmployeeStatus() == 1)
//                    .sorted(Comparator.comparing(ContactBindInfo::getFsEmployeeNamePinYin)).collect(Collectors.toList());
//
//            //匹配了手机号的放到前面
//            contactBindCollect.sort((a, b) -> {
//                String comA = StringUtils.isBlank(a.getQywxEmployeeMobile()) ? "null" : "mactch_ok";
//                String comB = StringUtils.isBlank(b.getQywxEmployeeMobile()) ? "null" : "mactch_ok";
//                return comA.compareTo(comB);
//            });
//
//            log.info("ContactBindInnerServiceImpl.getAccountBind,bindType=0,contactBindCollect={}",
//                    contactBindCollect);
//            return new Result<>(contactBindCollect);
//        }
//
//        if (1 == bindType) {
//            QyweixinDepartmentEmployeeListRsp departmentEmployeeListResult = new QyweixinDepartmentEmployeeListRsp();
//            //优先选择代开发授权
//            if(StringUtils.isNotEmpty(outEa)) {
//                List<QyweixinUserDetailInfoRsp> userList = getAllEmployeeListVisible2App(outEa,
//                        outDepId);
//                departmentEmployeeListResult.setUserlist(userList);
//            }
//            log.info("ContactBindInnerServiceImpl.getAccountBind,bindType=1,departmentEmployeeListResult={}",departmentEmployeeListResult);
//            if (ObjectUtils.isEmpty(departmentEmployeeListResult.getUserlist())) {
//                List<ContactBindInfo> contactBindCollect = qyweixinAccountEmployeeMappings.stream().map(employeeBindInfo -> {
//                    Integer empId = null;
//                    try {
//                        empId=Integer.parseInt(employeeBindInfo.getFsAccount().substring(employeeBindInfo.getFsAccount().lastIndexOf(".") + 1));
//                    } catch (Exception e) {
//
//                    }
//                    if(empId==null) {
//                        return null;
//                    }
//                    EmployeeDto employeeInfo = employeeInfoMaps.get(empId);
//
//                    if (null == employeeInfo) {
//                        List<EmployeeDto> result = fsManager.getEmployeeInfos(fsEa, employeeId, Lists.newArrayList(empId));
//                        if (result.isEmpty()) {
//                            return null;
//                        }
//                        employeeInfo = result.get(0);
//                    }
//                    ContactBindInfo contactBindInfo = new ContactBindInfo();
//                    Integer fsDepartmentId = getMainDepId(employeeInfo);
//                    contactBindInfo.setFsDepartmentName(allCirclesMap.get(fsDepartmentId));
//                    contactBindInfo.setFsDepartmentId(fsDepartmentId);
//                    contactBindInfo.setFsEmployeeAccount(employeeInfo.getEmployeeId() + "");
//                    contactBindInfo.setFsEmployeeMobile(employeeInfo.getMobile());
//                    contactBindInfo.setFsEmployeeName(employeeInfo.getName());
//                    contactBindInfo.setFsEmployeeNamePinYin(PinYinUtil.getFirstSpell(employeeInfo.getName()).toLowerCase());
//                    contactBindInfo.setFsEmployeeStatus(employeeInfo.getStatus().getValue());
//                    //统一为账号不存在
//                    contactBindInfo.setQywxEmployeeName("企业微信账号不存在！");
//                    return contactBindInfo;
//                }).sorted(Comparator.comparing(ContactBindInfo::getFsEmployeeNamePinYin)).collect(Collectors.toList());
//                return new Result<>(contactBindCollect);
//            }
//            Map<String, QyweixinUserDetailInfoRsp> qyweixinEmployeeMap = departmentEmployeeListResult.getUserlist().stream()
//                    .collect(Collectors.toMap(v -> v.getUserid(), Function.identity(), (v1,v2) -> v1));
//
//            Map<String, QyweixinDepartmentRsp> departMentInfoMap = getQyweixinDepartmentInfoMap(outEa);
//
//            List<ContactBindInfo> contactBindCollect = qyweixinAccountEmployeeMappings.stream().map(employeeBindInfo -> {
//                EmployeeDto employeeInfo = employeeInfoMaps.get(Integer.parseInt(employeeBindInfo.getFsAccount().substring(employeeBindInfo.getFsAccount().lastIndexOf(".") + 1)));
//
//                if (null == employeeInfo) {
//                    Integer empId = null;
//                    try {
//                        empId=Integer.parseInt(employeeBindInfo.getFsAccount().substring(employeeBindInfo.getFsAccount().lastIndexOf(".") + 1));
//                    } catch (Exception e) {
//
//                    }
//                    if(empId==null) {
//                        return null;
//                    }
//                    List<EmployeeDto> result = fsManager.getEmployeeInfos(fsEa, employeeId, Lists.newArrayList(empId));
//                    if (result.isEmpty()) {
//                        return null;
//                    }
//                    employeeInfo = result.get(0);
//                }
//                ContactBindInfo contactBindInfo = new ContactBindInfo();
//                Integer fsDepartmentId = getMainDepId(employeeInfo);
//                contactBindInfo.setFsDepartmentName(allCirclesMap.get(fsDepartmentId));
//                contactBindInfo.setFsDepartmentId(fsDepartmentId);
//                contactBindInfo.setFsEmployeeAccount(employeeInfo.getEmployeeId() + "");
//                contactBindInfo.setFsEmployeeMobile(employeeInfo.getMobile());
//                contactBindInfo.setFsEmployeeName(employeeInfo.getName());
//                contactBindInfo.setFsEmployeeNamePinYin(PinYinUtil.getFirstSpell(employeeInfo.getName()).toLowerCase());
//                contactBindInfo.setFsEmployeeStatus(employeeInfo.getStatus().getValue());
//
//                QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = qyweixinEmployeeMap.get(employeeBindInfo.getIsvAccount());
//                if(ObjectUtils.isEmpty(qyweixinUserDetailInfoRsp)) {
//                    qyweixinUserDetailInfoRsp = qyweixinEmployeeMap.get(employeeBindInfo.getOutAccount());
//                }
//                if (qyweixinUserDetailInfoRsp!=null) {
//                    contactBindInfo.setQywxEmployeeAccount(qyweixinUserDetailInfoRsp.getUserid());
//                    contactBindInfo.setQywxEmployeeMobile(qyweixinUserDetailInfoRsp.getMobile());
//                    String employeeName = this.getEmployeeName(qyweixinUserDetailInfoRsp, departMentInfoMap);
//                    contactBindInfo.setQywxEmployeeName(employeeName);
//                    contactBindInfo.setQywxEmployeeEmail(qyweixinUserDetailInfoRsp.getEmail());
//                    if (StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getMain_department()) && ObjectUtils.isNotEmpty(departMentInfoMap)) {
//                        contactBindInfo.setQywxDepartmentName(departMentInfoMap.get(qyweixinUserDetailInfoRsp.getMain_department()).getName());
//                    }
//                } else {
//                    contactBindInfo.setQywxEmployeeName("企业微信账号不存在！");
//                }
//                log.info("ContactBindInnerServiceImpl.getAccountBind,bindType=1,contactBindInfo={},qyweixinUserDetailInfoRsp={}",
//                        contactBindInfo,qyweixinUserDetailInfoRsp);
//                return contactBindInfo;
//            }).sorted(Comparator.comparing(ContactBindInfo::getFsEmployeeNamePinYin)).collect(Collectors.toList());
//            return new Result<>(contactBindCollect);
//        }
//        return new Result<>();
//    }

    private Result<String> getFsEaToOutEa(String fsEa) {
        com.facishare.open.qywx.accountbind.result.Result<String> fsEaToOutEaResult = qyweixinAccountBindService.fsEaToOutEa(SourceTypeEnum.QYWX.getSourceType(), fsEa);

        if (StringUtils.isBlank(fsEaToOutEaResult.getData())) {
            return Result.newInstance(ErrorRefer.CONTACT_NOT_BIND);
        }
        return new Result<>(fsEaToOutEaResult.getData());
    }


//    private SimpleContactBindInfo convertContactBindInfo(QyweixinUserDetailInfoRsp qyweixinUserDetailInfo, Map<String, QyweixinDepartmentRsp> departmentInfoMap) {
//        SimpleContactBindInfo contactBindInfo = new SimpleContactBindInfo();
//        contactBindInfo.setQywxEmployeeAccount(qyweixinUserDetailInfo.getUserid());
//        String employeeName = this.getEmployeeName(qyweixinUserDetailInfo, departmentInfoMap);
//        contactBindInfo.setQywxEmployeeName(employeeName);
//        contactBindInfo.setQywxEmployeeMobile(qyweixinUserDetailInfo.getMobile());
////        if (CollectionUtils.isNotEmpty(qyweixinUserDetailInfo.getDepartment())) {
////            contactBindInfo.setQywxDepartmentName(departMentInfoMap.get(qyweixinUserDetailInfo.getDepartment().stream().findFirst().get()).getName());
////        }
//        return contactBindInfo;
//    }
//
//
//    private Map<String, QyweixinDepartmentRsp> getQyweixinDepartmentInfoMap(String corpId) {
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentListRsp> departmentInfoListResult = qyWeixinManager.getDepartmentInfoList(getAppIdByCorpId(corpId), corpId, null);
//        if(!departmentInfoListResult.isSuccess() || ObjectUtils.isEmpty(departmentInfoListResult.getData())) {
//            return new HashMap<>();
//        }
//        return departmentInfoListResult.getData().getDepartment().stream().collect(Collectors.toMap(QyweixinDepartmentRsp::getId, Function.identity()));
//    }

    /**
     * 解绑操作接口，分下面四种处理情况
     * 0-普通删除绑定关系（删除部门，员工，企业，通讯录绑定）
     * 1-该企业微信账号已绑定其它纷享账号
     * 2-该纷享账号已绑定其它企业微信账号
     * 3-企业微信绑定了其他纷享，企业微信通讯录又绑定其他应用，解绑旧纷享，绑定新纷享企业
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Result<Object> deleteQYWXBind(Map<String, String> request) {

        // 0-普通删除绑定关系  1-该企业微信账号已绑定其它纷享账号 请纷享客服解绑   2-该纷享账号已绑定其它企业微信账号 请纷享客服解绑
        String deleteBindType = request.get("deleteBindType");
        String corpIds = request.get("corpIds");
        String appId = request.get("appId");
        if (StringUtils.isBlank(deleteBindType)) {
            return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "缺少参数deleteBindType",I18NStringEnum.s99.getI18nKey());
        }
        if (StringUtils.isBlank(corpIds)) {
            return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "缺少参数corpIds",I18NStringEnum.s100.getI18nKey());
        }
        if (StringUtils.isBlank(appId)) {
            return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "缺少参数appId",I18NStringEnum.s101.getI18nKey());
        }

        if ("0".equals(deleteBindType)) {
            List<String> corpIdList = Arrays.asList(corpIds.split(","));
            String corpId = corpIdList.get(0);
            //针对普通企业进行解绑处理
            qyweixinAccountBindInnerService.deleteQYWXBindByOutEa(Arrays.asList(corpId), appId);
            qyweixinContactBindDao.deleteByOutEa(Arrays.asList(corpId));
            return new Result<>();
        }

        if ("1".equals(deleteBindType)) {
            String fsEa = request.get("fsEa");
            String corpId = corpIds.split(",")[0];
            if (StringUtils.isBlank(fsEa)) {
                return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "缺少参数fsEa",I18NStringEnum.s102.getI18nKey());
            }

            //检测该微信企业是否需要修改
            QyweixinContactBindBo qyweixinContactBindBo = new QyweixinContactBindBo();
            qyweixinContactBindBo.setCorpId(corpId);
            List<QyweixinContactBindBo> findContactBind = qyweixinContactBindDao.findByEntity(qyweixinContactBindBo);
            if (findContactBind.isEmpty()) {
                return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "该微信企业没有安装通讯录！ corpId:" + corpId,null);
            } else {
                if (!ContactBindStatusEnum.HAVE_BINDING_FS_EA.getCode().equals(findContactBind.get(0).getStatus())) {
                    return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(),
                            "该微信企业通讯录绑定状态不能修改(状态为1的才能修改)！ corpId:" + corpId + " status:" + findContactBind.get(0).getStatus(),null);
                }
            }

            qyweixinAccountBindInnerService.deleteQYWXBindByOutEa(Lists.newArrayList(corpId), appId);
            return retryBindQYWX(corpId, fsEa);
        }

        if ("2".equals(deleteBindType)) {
            String fsEa = request.get("fsEa");
            if (StringUtils.isBlank(fsEa)) {
                return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "缺少参数fsEa",I18NStringEnum.s102.getI18nKey());
            }

            //检测该纷享账号是否需要修改
            QyweixinContactBindBo qyweixinContactBindBo = new QyweixinContactBindBo();
            qyweixinContactBindBo.setFsEa(fsEa);
            List<QyweixinContactBindBo> findContactBind = qyweixinContactBindDao.findByEntity(qyweixinContactBindBo);
            if (findContactBind.isEmpty()) {
                return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "该纷享企业没有安装通讯录！ fsEa:" + fsEa,null);
            } else {
                if (!ContactBindStatusEnum.HAVE_BINDING_QYWX_EA.getCode().equals(findContactBind.get(0).getStatus())) {
                    return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(),
                            "该纷享账号通讯录绑定状态不能修改(状态为4的才能修改)！ fsEa:" + fsEa + " status:" + findContactBind.get(0).getStatus(),null);
                }
            }

            String corpId = corpIds.split(",")[0];
            qyweixinAccountBindInnerService.deleteQYWXAccountBind(fsEa, appId, corpId);

            return retryBindFsEa(corpId, fsEa);
        }
        if ("3".equals(deleteBindType)) {
            String fsEa = request.get("fsEa");
            String corpId = corpIds.split(",")[0];
            if (StringUtils.isBlank(fsEa)) {
                return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "缺少参数fsEa",I18NStringEnum.s102.getI18nKey());
            }

            //检测该微信企业是否需要修改
            QyweixinContactBindBo qyweixinContactBindBo = new QyweixinContactBindBo();
            qyweixinContactBindBo.setCorpId(corpId);
            List<QyweixinContactBindBo> findContactBind = qyweixinContactBindDao.findByEntity(qyweixinContactBindBo);
            if (!findContactBind.isEmpty()) {
                return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "该微信企业已经安装通讯录无需解绑！ corpId:" + corpId,null);
            }
            // 建立绑定关系
            return retryBindQYWXToBindNewFa(corpId, fsEa);
        }

        return new Result<>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "未知的删除类型！",null);
    }

    /**
     * 3-企业微信绑定了其他纷享，企业微信通讯录又绑定其他应用，解绑旧纷享，绑定新纷享企业
     *
     * @param corpId
     * @param fsEa
     * @return
     */
    private Result<Object> retryBindQYWXToBindNewFa(String corpId, String fsEa) {
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(corpId).getData();
        //检测微信企业信息是否存在
        QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
        qyweixinCorpBindBo.setCorpId(corpId);
        qyweixinCorpBindBo.setAppId(mainAppId);
        qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
        List<QyweixinCorpBindBo> byEntity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);

        if (byEntity.isEmpty()) {
            throw new RuntimeException("该微信企业没有安装通讯录应用！请先扫码安装通讯录应用！ ");
        }


        QyweixinContactBindBo qyweixinContactBindBo = new QyweixinContactBindBo();
        qyweixinContactBindBo.setCorpId(corpId);
        qyweixinContactBindBo.setFsEa(fsEa);
        qyweixinContactBindBo.setStatus(ContactBindStatusEnum.NOT_FIRST_BIND.getCode());
        qyweixinContactBindBo.setCorpName(byEntity.get(0).getCorpName());
        int saveCount = qyweixinContactBindDao.saveOrUpdateEntity(qyweixinContactBindBo);
        if (saveCount >= 1) {
            //绑定新纷享企业
            QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
            qyweixinAccountEnterpriseMapping.setSource("qywx");
            qyweixinAccountEnterpriseMapping.setOutEa(corpId);
            qyweixinAccountEnterpriseMapping.setFsEa(fsEa);
            qyweixinAccountEnterpriseMapping.setBindType(QyweixinBindTypeEnum.NEW_CORP_BIND.getCode()); //1 新建企业并绑定
            qyweixinAccountEnterpriseMapping.setDomain(ConfigCenter.crm_domain);
            com.facishare.open.qywx.accountbind.result.Result<Boolean> bindAccountEnterpriseMappingResult =
                    qyweixinAccountBindService.bindAccountEnterpriseMapping(qyweixinAccountEnterpriseMapping);
            if (bindAccountEnterpriseMappingResult.isSuccess()) {
                // 新绑定的纷享企业需要管理员，默认给企业微信管理为纷享企业管理员
                Map<String, String> userIdMap = qyweixinContactBindDao.queryUserIdByCorpId(corpId);
                if (!userIdMap.isEmpty() && !userIdMap.get("userId").isEmpty()) {
                    String userId = userIdMap.get("userId");
                    String fsAccount = "E." + fsEa + ".1000";    // 1000 为管理员
                    QyweixinAccountEmployeeMapping arg = new QyweixinAccountEmployeeMapping();
                    arg.setSource("qywx");
                    arg.setFsAccount(fsAccount);
                    arg.setOutAccount(userId);
                    arg.setOutEa(corpId);
                    //arg.setAppId(crmAppId);
                    ArrayList<QyweixinAccountEmployeeMapping> employeeLists = Lists.newArrayList(arg);
                    com.facishare.open.qywx.accountbind.result.Result<Boolean> booleanbindAccountEmployeeResult =
                            qyweixinAccountBindService.bindAccountEmployeeMapping(employeeLists);
                    if (booleanbindAccountEmployeeResult.isSuccess()) {
                        return new Result<>();
                    }
                    throw new RuntimeException("绑定新纷享企业管理员失败！请核对corpId fsEa");
                }
                throw new RuntimeException("获取企业信息管理员信息失败！请核对corpId");
            } else {
                throw new RuntimeException("绑定新纷享企业失败！请核对corpId fsEa");
            }
        } else {
            throw new RuntimeException("通讯录绑定更新失败！");
        }
    }


    /**
     * 2-该纷享账号已绑定其它企业微信账号。 解绑原微信企业绑定，绑定到新的企业微信账号
     *
     * @param corpId
     * @param fsEa
     * @return
     */
    private Result<Object> retryBindFsEa(String corpId, String fsEa) {
        //检测微信企业信息是否存在
        QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
        qyweixinCorpBindBo.setCorpId(corpId);
        qyweixinCorpBindBo.setAppId(contactAppId);
        qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
        List<QyweixinCorpBindBo> byEntity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);

        if (byEntity.isEmpty()) {
            throw new RuntimeException("该微信企业没有安装通讯录应用！请先扫码安装通讯录应用！");
        }

        QyweixinContactBindBo qyweixinContactBindBo = new QyweixinContactBindBo();
        qyweixinContactBindBo.setCorpId(corpId);
        qyweixinContactBindBo.setFsEa(fsEa);
        qyweixinContactBindBo.setStatus(ContactBindStatusEnum.FIRST_BIND.getCode());
        qyweixinContactBindBo.setCorpName(byEntity.get(0).getCorpName());
        int updateCount = qyweixinContactBindDao.updateCorpInfoByFsEa(qyweixinContactBindBo);
        if (updateCount >= 1) {
            QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
            qyweixinAccountEnterpriseMapping.setSource("qywx");
            qyweixinAccountEnterpriseMapping.setOutEa(corpId);
            qyweixinAccountEnterpriseMapping.setFsEa(fsEa);
            qyweixinAccountEnterpriseMapping.setBindType(QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
            qyweixinAccountEnterpriseMapping.setDomain(ConfigCenter.crm_domain);
            com.facishare.open.qywx.accountbind.result.Result<Boolean> bindAccountEnterpriseMappingResult =
                    qyweixinAccountBindService.bindAccountEnterpriseMapping(qyweixinAccountEnterpriseMapping);
            if (bindAccountEnterpriseMappingResult.isSuccess()) {
                return new Result<>();
            } else {
                throw new RuntimeException("新建绑定关系不成功！请核对corpId fsEa");
            }
        } else {
            throw new RuntimeException("通讯录绑定更新失败！");
        }
    }

    /**
     * 1-该企业微信账号已绑定其它纷享账号。 解除原纷享企业绑定，绑定到新的纷享企业
     *
     * @param corpId
     * @param fsEa
     * @return
     */
    private Result<Object> retryBindQYWX(String corpId, String fsEa) {
        //检测微信企业信息是否存在
        QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
        qyweixinCorpBindBo.setCorpId(corpId);
        qyweixinCorpBindBo.setAppId(contactAppId);
        qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
        List<QyweixinCorpBindBo> byEntity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);

        if (byEntity.isEmpty()) {
            throw new RuntimeException("该微信企业没有安装通讯录应用！请先扫码安装通讯录应用！ ");
        }

        QyweixinContactBindBo qyweixinContactBindBo = new QyweixinContactBindBo();
        qyweixinContactBindBo.setCorpId(corpId);
        qyweixinContactBindBo.setFsEa(fsEa);
        qyweixinContactBindBo.setStatus(ContactBindStatusEnum.FIRST_BIND.getCode());
        qyweixinContactBindBo.setCorpName(byEntity.get(0).getCorpName());
        int updateCount = qyweixinContactBindDao.updateFsEaByCorpId(qyweixinContactBindBo);//2
        if (updateCount >= 1) {
            QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
            qyweixinAccountEnterpriseMapping.setSource("qywx");
            qyweixinAccountEnterpriseMapping.setOutEa(corpId);
            qyweixinAccountEnterpriseMapping.setFsEa(fsEa);
            qyweixinAccountEnterpriseMapping.setBindType(QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());//1
            qyweixinAccountEnterpriseMapping.setDomain(ConfigCenter.crm_domain);
            com.facishare.open.qywx.accountbind.result.Result<Boolean> bindAccountEnterpriseMappingResult =
                    qyweixinAccountBindService.bindAccountEnterpriseMapping(qyweixinAccountEnterpriseMapping);
            if (bindAccountEnterpriseMappingResult.isSuccess()) {
                return new Result<>();
            } else {
                throw new RuntimeException("新建绑定关系不成功！请核对corpId fsEa");
            }
        } else {
            throw new RuntimeException("通讯录绑定更新失败！");
        }
    }

//    /**
//     * 获取应用ID。通讯录应用优先
//     *
//     * @param corpId 企业微信企业ID
//     * @return
//     */
//    private String getAppId(String corpId) {
//        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, contactAppId);
//        if (Objects.nonNull(corpBindBo) && corpBindBo.getStatus() == 0) {
//            return contactAppId;
//        }
//        return ConfigCenter.crmAppId;
//    }

    /**
     * 获取应用ID。代开发应用优先
     *
     * @param corpId 企业微信企业ID
     * @return
     */
    private String getAppIdByCorpId(String corpId) {
        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, repAppId);
        if (Objects.nonNull(corpBindBo) && corpBindBo.getStatus() == 0) {
            return repAppId;
        }
        return ConfigCenter.crmAppId;
    }

//    /**
//     * 从Crm应用获取用户信息
//     * 这个接口只能用于 手工账号绑定页面。 在手工账号绑定页面下，name是企业微信上的员工账号。
//     * 在其它的地方，name字段需要加个前缀，方便业务方识别这个name字段存放的实际是企业微信放回的userid.
//     */
//    private QyweixinDepartmentEmployeeListRsp getCrmAppUserInfos(String corpId) {
//        QyweixinDepartmentEmployeeListRsp result = new QyweixinDepartmentEmployeeListRsp();
//        result.setErrcode(QyweixinErrorCodeEnum.SUCCESS.getErrCode());
//        List<QyweixinUserDetailInfoRsp> userInfos = Lists.newArrayListWithCapacity(128);
//
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> corpInfoResult = qyWeixinManager.getCorpInfo(corpId, crmAppId);
//        if(!corpInfoResult.isSuccess() || ObjectUtils.isEmpty(corpInfoResult.getData())) {
//            return result;
//        }
//        QyweixinGetAuthInfoRsp corpInfo = corpInfoResult.getData();
//        // 部门
//        List<String> allowParty = corpInfo.getAuth_info().getAgent().get(0).getPrivilege().getAllow_party();
//        if (CollectionUtils.isNotEmpty(allowParty)) {
//            allowParty.forEach(v -> {
//                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(crmAppId, corpId, v);
//                userInfos.addAll(departmentEmployeeListResult.getData().getUserlist());
//            });
//        }
//
//        // 人员
//        /**
//         * CRM应用作为普通应用，已经获取不到名字和手机号码了。只能获取到一个员工账号
//         * 所以不需要再访问企业微信接口获取详细信息。*/
//        List<String> allowUser = corpInfo.getAuth_info().getAgent().get(0).getPrivilege().getAllow_user();
//        if (CollectionUtils.isNotEmpty(allowUser)) {
//            allowUser.forEach(userId -> userInfos.add(QyweixinUserDetailInfoRsp.builder().userid(userId).name(userId).mobile("").build()));
//        }
//
//        result.setUserlist(userInfos.stream().filter(distinctByKey(QyweixinUserDetailInfoRsp::getUserid)).collect(Collectors.toList()));
//        return result;
//    }

    /**
     * 去重
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    @Override
    public Result<String> getJobId(String ea) {
        String corpId = getCorpIdByFsEa(ea);
        if (StringUtils.isBlank(corpId))
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), "企业未绑定企业微信",null);
        try {
            File file = getFile(ea, corpId);
            // 如果文件是空的， 返回错误码!
            if (file == null) {
                return new Result<String>().addError(ErrorRefer.CONTACT_NOT_TRANSLATION.getCode(), "没有需要转译的内容!",null);
            }
            com.facishare.open.qywx.accountinner.result.Result<String> jobIdResult = qyWeixinManager.uploadTranslateFile(corpId, file);
            log.info("getJobId success. ea:{}, corpId:{}, jobId:{}", ea, corpId, jobIdResult);
            if(!jobIdResult.isSuccess() || StringUtils.isEmpty(jobIdResult.getData())) {
                return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), jobIdResult.getMsg(),null);
            }
            return new Result<>(jobIdResult.getData());
        } catch (Exception e) {
            log.error("getJobId error. ea:{}, corpId:{}", ea, corpId);
            return new Result<>(null, ErrorRefer.INTERNAL_ERROR.getCode(), e.getMessage());
        }
    }

    public File getFile(String ea, String corpId) throws RemoteException {
        StringBuilder content = new StringBuilder();
        content.append(getDeptLine(ea, corpId));
        content.append(getEmployeeLine(ea, corpId));

        if (StringUtils.isBlank(content.toString())) {
            log.warn("file content is blank. ea:{}, content:{}", ea, content);
            return null;
        }

        String path = this.getClass().getResource("/").getPath() + ea + "-" + System.currentTimeMillis() + ".txt";
        File file = new File(path);
        try {
            FileUtils.writeStringToFile(file, content.toString(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("write content to file failed. ea:{}, corpId:{}, content:{}", ea, corpId, content.toString(), e);
            e.printStackTrace();
        }
        return file;
    }

    public String getDeptLine(String ea, String corpId) {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountDepartmentMapping>> result = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(), corpId,
                null, -1, null);
        Map<Integer, String> deptMap =
                result.getData().stream()
                        .filter(distinctByKey(QyweixinAccountDepartmentMapping::getFsDepartmentId))
                        .collect(Collectors.toMap(QyweixinAccountDepartmentMapping::getFsDepartmentId, QyweixinAccountDepartmentMapping::getOutDepartmentId));

        log.info("queryDepartmentBindByOutDepartment success. ea:{}, result:{}, deptMap:{}", ea, result, deptMap);
        String line = "%s\t%s\t$departmentName=%s$\n";
        StringBuilder sb = new StringBuilder();
        deptMap.forEach((fsDeptId, outDeptId) -> sb.append(String.format(line, 1, fsDeptId, outDeptId)));
        log.info("ContactBindInnerServiceImpl.getDeptLine,模板转译ID之后的值是： {}", sb.toString());
        return sb.toString();
    }

    public String getEmployeeLine(String ea, String corpId) throws RemoteException {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.outAccountToFsAccountBatch(SourceTypeEnum.QYWX.getSourceType(), ea, null,
                Lists.newArrayList(), -1);
        //enterpriseId  查找出企业下所有的员工信息需要的ID
        int enterpriseId = eieaConverter.enterpriseAccountToId(ea);
        //保留FsAccount前缀 如E.81002.
        String deleOfKey = "E." + ea + ".";

        GetAllEmployeesDtoArg arg = new GetAllEmployeesDtoArg();

        arg.setEnterpriseId(enterpriseId);
        //将上面得到的enterpriseId去查询一个企业下的所有的员工，并且转为list
        GetAllEmployeesDtoResult getEmps = employeeProviderService.getAllEmployees(arg);
        log.info("Emps 数据： {}", getEmps);
        List<EmployeeDto> empsList = getEmps.getEmployeeDtoList();
        //过滤查重并且将fsCount，outAccount进行保存
        Map<String, String> employeeMap = result.getData().stream()
                .filter(distinctByKey(QyweixinAccountEmployeeMapping::getFsAccount))
                .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount, QyweixinAccountEmployeeMapping::getOutAccount));
        //判断查询出来的员工中的name是否符合条件，不符合条件就将其删除，不进行转译
        for (EmployeeDto emp : empsList) {
            log.info("name的值是： {}", emp.getName());
            if (emp.getName() == null || !emp.getName().contains("FSQYWX")) {
                //拼接要删除的key
                String toDel = null;
                toDel = deleOfKey.concat(String.valueOf(emp.getEmployeeId()));
                employeeMap.remove(toDel);
            }
        }
        log.info("outAccountToFsAccountBatch success. ea:{}, result:{}, employeeMap:{}", ea, result, employeeMap);
        //设置模板
        String line = "%s\t%s\t$userName=%s$\n";
        StringBuilder sb = new StringBuilder();
        employeeMap.forEach((fsUserId, outUserId) -> sb.append(String.format(line, 2, FSAccountUtil.getEmpIdFromFSAccount(fsUserId), outUserId)));
        log.info("模板转译ID之后的值是： {}", sb.toString());
        return sb.toString();
    }

    private String getCorpIdByFsEa(String fsEnterpriseAccount) {
        String corpId = qyweixinAccountBindService.fsEaToOutEa(SourceTypeEnum.QYWX.getSourceType(), fsEnterpriseAccount).getData();
        if (org.apache.commons.lang3.StringUtils.isBlank(corpId)) {
            log.error("trace getCorpIdByFsEa not find corpId fsEa:{}", fsEnterpriseAccount);
            return null;
        }
        return corpId;
    }

    @Override
    public Result<Pair<Integer, String>> getTranslateUrl(String ea, String jobId) {
        com.facishare.open.qywx.accountinner.result.Result<Pair<Integer, String>> pairResult = qyWeixinManager.getTranslateUrl(ea, jobId);
        log.info("getTranslateUrl success. ea:{}, jobId:{} pair:{}", ea, jobId, pairResult);
        if(pairResult.isSuccess()) {
            return new Result<>(pairResult.getData());
        }
        return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), pairResult.getMsg(),null);
    }

    @Override
    public Result<Integer> getAutoBind(String fsEa, String outEa) {
        int aut = qyweixinCorpBindDao.getAutoBind(fsEa,outEa, ConfigCenter.crm_domain);
        log.info("ContactBindInnerServiceImpl.getAutoBind aut={}.", aut);
        return new Result<>(aut);
    }

    @Override
    public Result<Integer> saveAutoBind(String fs_ea, String outEa, int flag) {
        int aut = qyweixinCorpBindDao.saveAutoBind(fs_ea, outEa, flag);
        return new Result<>(aut);
    }

    @Override
    public Result<Integer> getAutoContactBind() {
        //查询开启了自动绑定的企业
        List<EaMappingModel> eaList = qyweixinCorpBindDao.autoAccountBind(repAppId, ConfigCenter.crm_domain);
        if(CollectionUtils.isEmpty(eaList)) {
            log.info("ContactBindInnerServiceImpl.getAutoContactBind:  eaList is not autoContactBind");
            //一个企业都没有，直接退出
            return new Result<>();
        }
        log.info("ContactBindInnerServiceImpl.getAutoContactBind:  eaList={}", eaList);
        for (int i = 0; i < eaList.size(); i++) {
            try {
                synchronized (ContactBindInnerServiceImpl.class) {
                    EaMappingModel eaMappingModel = eaList.get(i);
                    saveAutoContactBind(eaMappingModel.getFsEa(), 1000,eaMappingModel.getOutEa());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public synchronized Result<Integer> saveAutoContactBind(String fs_ea, Integer employeeId, String outEa) {
        Result<List<ContactBindInfo>> automaticAccountMactchResult = automaticAccountMactch(fs_ea, employeeId, outEa);
        log.info("ContactBindInnerServiceImpl.saveAutoContactBind,fs_ea={},automaticAccountMactchResult={}",
                fs_ea, automaticAccountMactchResult);
        if(!automaticAccountMactchResult.isSuccess()) {
            return Result.newInstance(ErrorRefer.CONTACT_AUTO_ATTACHMENTS);
        }
        if(automaticAccountMactchResult.getData() == null || automaticAccountMactchResult.getData().size() == 0) {
            return new Result<>();
        }
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();

        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fs_ea, mainAppId, outEa);
        log.info("ContactBindInnerServiceImpl.saveAutoContactBind,qyweixinAccountEmployeeMappings={}",
                qyweixinAccountEmployeeMappings);
        if(qyweixinAccountEmployeeMappings != null && qyweixinAccountEmployeeMappings.size() != 0) {
            List<ContactBindInfo> automaticAccounts = new LinkedList<>();
            for (int i = 0; i < automaticAccountMactchResult.getData().size(); i++) {
                for (int j = 0; j < qyweixinAccountEmployeeMappings.size(); j++) {
                    String fsAccount = qyweixinAccountEmployeeMappings.get(j).getFsAccount().substring(qyweixinAccountEmployeeMappings.get(j).getFsAccount().length()-4);
                    if(fsAccount.equals(automaticAccountMactchResult.getData().get(i).getFsEmployeeAccount())) {
                        automaticAccounts.add(automaticAccountMactchResult.getData().get(i));
                    }
                }
            }
            automaticAccountMactchResult.getData().removeAll(automaticAccounts);
        }
        List<String> bindList = new LinkedList<>();
        automaticAccountMactchResult.getData().stream().forEach(item -> {
            if(!StringUtils.isEmpty(item.getQywxEmployeeAccount())) {
                bindList.add(item.getFsEmployeeAccount() + "&" + item.getQywxEmployeeAccount());
            }
        });
        log.info("ContactBindInnerServiceImpl.saveAutoContactBind,bindList={}", bindList);
        if(bindList == null || bindList.size() == 0) {
            return new Result<>();
        }

        Result<String> result = saveAccountBind(fs_ea, bindList, outEa);
        log.info("ContactBindInnerServiceImpl.saveAutoContactBind,result={}", result);
        if(!result.isSuccess()) {
            return Result.newInstance(ErrorRefer.CONTACT_AUTO_ATTACHMENTS);
        }
        log.info("ContactBindInnerServiceImpl.saveAutoContactBind autoBind is success.");
        return new Result<>();
    }

    private Result<String> queryCorpNameInner(String fsEa, String outEa, String outDepId) {
        Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindInnerService.getEnterpriseMapping2(fsEa, outEa);
        if(enterpriseMappingResult.getData()==null) return new Result<>();
        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingResult.getData();
        outEa = enterpriseMapping.getOutEa();
        outDepId = enterpriseMapping.getDepId();

        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> corpInfoResult = qyWeixinManager.getCorpInfo(outEa, mainAppId);

        String corpName = null;
        if(corpInfoResult.isSuccess() && corpInfoResult.getData()!=null) {
            corpName = corpInfoResult.getData().getAuth_corp_info().getCorp_name();
            if(!StringUtils.equalsIgnoreCase(outDepId,"1")) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(mainAppId,
                        outEa,
                        outDepId);
                if(departmentInfoResult.isSuccess() && departmentInfoResult.getData()!=null) {
                    corpName+="-"+departmentInfoResult.getData().getDepartment().getName();
                }
            }
        }
        return new Result<>(corpName);
    }

    @Override
    public Result<String> queryCorpName2(String fsEa, String outEa, String outDepId) {
        return queryCorpNameInner(fsEa, outEa, outDepId);
    }

    @Override
    public Result<QyweixinCorpIdInfo> queryOutEaByFsEa(String fsEa) {
        QyweixinCorpIdInfo qyweixinCorpIdInfo = new QyweixinCorpIdInfo();
        //先找到enterprise表看看有没有企业绑定
        Result<String> fsEaToOutEaResult = getFsEaToOutEa(fsEa);
        if(!fsEaToOutEaResult.isSuccess() || StringUtils.isEmpty(fsEaToOutEaResult.getData())) {
            return new Result<>(qyweixinCorpIdInfo);
        }
        //再去找contact表
        QyweixinContactBindBo enterpriseMapping = qyweixinContactBindDao.getContactBind(fsEa);
        if(ObjectUtils.isEmpty(enterpriseMapping)) {
            //兼容直接进去创建的企业
            qyweixinCorpIdInfo.setCorpId(fsEaToOutEaResult.getData());
            qyweixinCorpIdInfo.setIsvCorpId(fsEaToOutEaResult.getData());
            qyweixinCorpIdInfo.setStatus(3);
            return new Result<>(qyweixinCorpIdInfo);
        }
        if(StringUtils.isNotEmpty(enterpriseMapping.getIsvCorpId())) {
            //这是新绑定的企业
            qyweixinCorpIdInfo.setCorpId(fsEaToOutEaResult.getData());
            qyweixinCorpIdInfo.setIsvCorpId(enterpriseMapping.getIsvCorpId());
            qyweixinCorpIdInfo.setStatus(2);
            return new Result<>(qyweixinCorpIdInfo);
        }
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(fsEaToOutEaResult.getData()).getData();
        //这时候看看使用的明文还是密文，通过corpId和appId进行判断
        QyweixinCorpBindBo crmCorpBind = qyweixinCorpBindDao.queryQyweixinCorpBind(fsEaToOutEaResult.getData(), mainAppId);
        QyweixinCorpBindBo repCorpBind = qyweixinCorpBindDao.queryQyweixinCorpBind(fsEaToOutEaResult.getData(), repAppId);

        if(ObjectUtils.isNotEmpty(crmCorpBind) && ObjectUtils.isNotEmpty(repCorpBind)) {
            //证明是旧的企业并且是旧的授权
            qyweixinCorpIdInfo.setCorpId(fsEaToOutEaResult.getData());
            qyweixinCorpIdInfo.setIsvCorpId(fsEaToOutEaResult.getData());
            qyweixinCorpIdInfo.setStatus(0);
            return new Result<>(qyweixinCorpIdInfo);
        } else if(ObjectUtils.isNotEmpty(crmCorpBind) && ObjectUtils.isEmpty(repCorpBind)) {
            //旧的企业新的授权
            QyweixinCorpBindBo repCorpBindByIsv = qyweixinCorpBindDao.queryQyweixinCorpBindByIsv(fsEaToOutEaResult.getData(), repAppId);
            if(ObjectUtils.isNotEmpty(repCorpBindByIsv)) {
                qyweixinCorpIdInfo.setCorpId(repCorpBindByIsv.getCorpId());
                qyweixinCorpIdInfo.setIsvCorpId(fsEaToOutEaResult.getData());
                qyweixinCorpIdInfo.setStatus(1);
                return new Result<>(qyweixinCorpIdInfo);
            }
        }
        //对于以上数据都没有的话，证明还是没有进行代开发的企业，不能影响其逻辑，直接返回现有的corpid
        qyweixinCorpIdInfo.setCorpId(fsEaToOutEaResult.getData());
        qyweixinCorpIdInfo.setIsvCorpId(fsEaToOutEaResult.getData());
        qyweixinCorpIdInfo.setStatus(4);
        return new Result<>(qyweixinCorpIdInfo);
    }

    @Override
    public Result<Void> uploadDepartmentsAndEmployeesFile(List<QyweixinDeptAndEmpSyncInfo> deptAndEmpSyncInfos, Integer i) {
        //刷库完成，库里都是密文的corpId
        String openCorpId = qyWeixinManager.corpId2OpenCorpId(deptAndEmpSyncInfos.get(0).getCorpId()).getData();
        if(StringUtils.isEmpty(openCorpId)) {
            return new Result<>();
        }
        //验证excel表ea和corpId是否正确
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> EnterpriseResult = qyweixinAccountBindService.fsEaToOutEaResult(SourceTypeEnum.QYWX.getSourceType(), deptAndEmpSyncInfos.get(0).getEa());
        if(ObjectUtils.isEmpty(EnterpriseResult.getData()) || !StringUtils.equalsIgnoreCase(openCorpId, EnterpriseResult.getData().getOutEa())) {
            return new Result<>();
        }
        log.info("ControllerQYWeixinContactBind.uploadDepartmentsAndEmployeesFile,deptAndEmpSyncInfos={}, i={}.", deptAndEmpSyncInfos, i);
        switch (i) {
            case 0 :
                //部门绑定
                insertDepartmentBindByExcel(deptAndEmpSyncInfos, openCorpId);
                break;
            case 1 :
                insertEmployeeBindByExcel(deptAndEmpSyncInfos, openCorpId);
                break;
            default :
                log.info("ControllerQYWeixinContactBind.uploadDepartmentsAndEmployeesFile.default,deptAndEmpSyncInfos={}, i={}.", deptAndEmpSyncInfos, i);
        }
        return new Result<>();
    }

    public void insertDepartmentBindByExcel(List<QyweixinDeptAndEmpSyncInfo> deptAndEmpSyncInfos, String openCorpId) {
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(deptAndEmpSyncInfos.get(0).getCorpId()).getData();
        List<QyweixinAccountDepartmentMapping> accountDepartmentMappings = deptAndEmpSyncInfos.stream().map(v -> {
            QyweixinAccountDepartmentMapping qyweixinAccountDepartmentMapping = new QyweixinAccountDepartmentMapping();
            qyweixinAccountDepartmentMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
            qyweixinAccountDepartmentMapping.setOutEa(openCorpId);
            qyweixinAccountDepartmentMapping.setFsEa(v.getEa());
            qyweixinAccountDepartmentMapping.setAppId(mainAppId);
            qyweixinAccountDepartmentMapping.setFsDepartmentId(v.getFsId());
            qyweixinAccountDepartmentMapping.setOutDepartmentId(v.getOutId());
            return qyweixinAccountDepartmentMapping;
        }).collect(Collectors.toList());
        log.info("ContactBindInnerServiceImpl.insertDepartmentBindByExcel,accountDepartmentMappings={}.", accountDepartmentMappings);
        qyweixinAccountBindService.bindAccountDepartmentMapping(accountDepartmentMappings);
    }

    public void insertEmployeeBindByExcel(List<QyweixinDeptAndEmpSyncInfo> deptAndEmpSyncInfos, String openCorpId) {
        //封装用户的明文账号
        List<String> accountLists = deptAndEmpSyncInfos.stream().map(QyweixinDeptAndEmpSyncInfo::getOutId).collect(Collectors.toList());
        //批量转换用户密文账号
        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> openUserIdInfosResult = qyWeixinManager.userId2OpenUserId(accountLists, openCorpId);
        if(!openUserIdInfosResult.isSuccess() || CollectionUtils.isEmpty(openUserIdInfosResult.getData())) {
            return;
        }
        log.info("ContactBindInnerServiceImpl.insertEmployeeBindByExcel,openUserIdInfos={}.", openUserIdInfosResult);
        //转换为map集合
        Map<String, QyweixinOpenUserIdInfo> openUserIdsMap = openUserIdInfosResult.getData().stream().collect(Collectors.toMap(QyweixinOpenUserIdInfo::getUserid,
                Function.identity(),(v1,v2) -> v1));
        //过滤一下无效的/不在可见范围的用户的账号,再去匹配
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappings = deptAndEmpSyncInfos.stream().filter(
                v -> openUserIdsMap.containsKey(v.getOutId())).map(v -> {
            QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping = new QyweixinAccountEmployeeMapping();
            qyweixinAccountEmployeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
            qyweixinAccountEmployeeMapping.setOutEa(openCorpId);
            //qyweixinAccountEmployeeMapping.setAppId(crmAppId);
            qyweixinAccountEmployeeMapping.setFsAccount("E." + v.getEa() + "." + v.getFsId());
            qyweixinAccountEmployeeMapping.setOutAccount(openUserIdsMap.get(v.getOutId()).getOpen_userid());
            qyweixinAccountEmployeeMapping.setIsvAccount(openUserIdsMap.get(v.getOutId()).getOpen_userid());
            return qyweixinAccountEmployeeMapping;
        }).collect(Collectors.toList());
        log.info("ContactBindInnerServiceImpl.insertEmployeeBindByExcel,accountEmployeeMappings={}.", accountEmployeeMappings);
        qyweixinAccountBindService.bindAccountEmployeeMapping(accountEmployeeMappings);
    }

    @Override
    public Result<Void> changAuthEventToBindAccount(String fsEa, String corpId) {
        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa, corpId, ConfigCenter.crm_domain);
        log.info("ContactBindInnerServiceImpl.changAuthEventToBindAccount,mapping={}.", mapping);
        if (ObjectUtils.isEmpty(mapping)) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(),"没有找到和当前CRM企业绑定的企业微信企业",I18NStringEnum.s97.getI18nKey());
        }
        //拉取可见范围的人
        List<QyweixinUserDetailInfoRsp> allEmployeeListVisible = this.getAllEmployeeListVisible2App(mapping.getIsvOutEa(), mapping.getDepId());
        log.info("ContactBindInnerServiceImpl.changAuthEventToBindAccount,allEmployeeListVisible={}.", allEmployeeListVisible);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(allEmployeeListVisible)) {
            return new Result<>();
        }
        //绑定
        corpManager.autoBindAccountEnterprise2(fsEa, corpId, allEmployeeListVisible);
        return new Result<>();
    }

    @Override
    public Result<Void>  listenMqEventToBindAccount(String fsEa, String corpId, EmployeeDto employeeDto) {
        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa, corpId, ConfigCenter.crm_domain);
        log.info("ContactBindInnerServiceImpl.listenMqEventToBindAccount,mapping={}.", mapping);
        if (ObjectUtils.isEmpty(mapping)) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(),"没有找到和当前CRM企业绑定的企业微信企业",I18NStringEnum.s97.getI18nKey());
        }
        //拉取可见范围的人
        List<QyweixinUserDetailInfoRsp> allEmployeeListVisible = this.getAllEmployeeListVisible2App(mapping.getIsvOutEa(), mapping.getDepId());
        log.info("ContactBindInnerServiceImpl.listenMqEventToBindAccount,allEmployeeListVisible={}.", allEmployeeListVisible);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(allEmployeeListVisible)) {
            return new Result<>();
        }

        //绑定
        corpManager.autoBindAccountEnterprise3(fsEa, corpId, allEmployeeListVisible, employeeDto);
        return new Result<>();
    }

//    private String getEmployeeName(QyweixinUserDetailInfoRsp qyweixinUserDetailInfo, Map<String, QyweixinDepartmentRsp> departmentInfoMap) {
//        String employeeName = qyweixinUserDetailInfo.getName();
//        if(StringUtils.isNotEmpty(qyweixinUserDetailInfo.getAlias())) {
//            employeeName = employeeName + "(" + qyweixinUserDetailInfo.getAlias() + ")";
//        }
//        if(ObjectUtils.isNotEmpty(departmentInfoMap) && StringUtils.isNotEmpty(qyweixinUserDetailInfo.getMain_department())) {
//            if(departmentInfoMap.containsKey(qyweixinUserDetailInfo.getMain_department())) {
//                employeeName = employeeName + "-" + departmentInfoMap.get(qyweixinUserDetailInfo.getMain_department()).getName();
//            }
//        }
//        return employeeName;
//    }

    @Override
    public Result<Void> autoBindEmpAccount(String fsEa, String corpId) {
        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa, corpId, ConfigCenter.crm_domain);
        log.info("ContactBindInnerServiceImpl.autoBindEmpAccount,mapping={}.", mapping);
        if (ObjectUtils.isEmpty(mapping)) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(),"没有找到和当前CRM企业绑定的企业微信企业",I18NStringEnum.s97.getI18nKey());
        }
        //拉取可见范围的人
        List<QyweixinUserDetailInfoRsp> allEmployeeListVisible = this.getAllEmployeeListVisible2App2(mapping.getIsvOutEa(), mapping.getDepId());
        log.info("ContactBindInnerServiceImpl.autoBindEmpAccount,allEmployeeListVisible={}.", allEmployeeListVisible);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(allEmployeeListVisible)) {
            return new Result<>();
        }
        if(StringUtils.isEmpty(corpId)) {
            corpId = mapping.getOutEa();
        }
        //绑定
        corpManager.autoBindEmpAccount(fsEa, corpId, allEmployeeListVisible);
        return new Result<>();
    }

    @Override
    public Result<List<QyweixinEmployeeBindModel>> queryEmployeeBind(QyweixinEmployeeBind employeeBind) {
        //查询企业绑定关系
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2(SourceTypeEnum.QYWX.getSourceType(),
                employeeBind.getFsEa(),
                employeeBind.getOutEa());
        if(!enterpriseMappingResult.isSuccess() || ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        //组装数据
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappings = swapEmployeeBindModel(employeeBind.getEmployeeBinds());
        log.info("ContactBindInnerServiceImpl.queryEmployeeBind,accountEmployeeMappings={}.", accountEmployeeMappings);
        if(CollectionUtils.isEmpty(accountEmployeeMappings)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.findEmployeeBinds(accountEmployeeMappings);
        log.info("ContactBindInnerServiceImpl.queryEmployeeBind,result={}.", result);
        if(!result.isSuccess()) {
            return new Result<>(result.getErrorCode(), result.getErrorMsg(),null);
        }
        List<QyweixinEmployeeBindModel> employeeBindModels = swapEmployeeBindMapping(result.getData());
        log.info("ContactBindInnerServiceImpl.queryEmployeeBind,employeeBindModels={}.", employeeBindModels);
        return new Result<>(employeeBindModels);
    }

    @Override
    public Result<Integer> saveEmployeeBind(QyweixinEmployeeBind employeeBind) {
        //查询企业绑定关系
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2(SourceTypeEnum.QYWX.getSourceType(),
                employeeBind.getFsEa(),
                employeeBind.getOutEa());
        if(!enterpriseMappingResult.isSuccess() || ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        //组装数据
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappings = swapEmployeeBindModel(employeeBind.getEmployeeBinds());
        log.info("ContactBindInnerServiceImpl.saveEmployeeBind,accountEmployeeMappings={}.", accountEmployeeMappings);
        if(CollectionUtils.isEmpty(accountEmployeeMappings)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.saveEmployeeBinds(accountEmployeeMappings);
        return new Result<>(result.getData());
    }

    @Override
    public Result<Integer> updateEmployeeBind(QyweixinEmployeeBind employeeBind) {
        //查询企业绑定关系
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2(SourceTypeEnum.QYWX.getSourceType(),
                employeeBind.getFsEa(),
                employeeBind.getOutEa());
        if(!enterpriseMappingResult.isSuccess() || ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        //组装数据
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappings = swapEmployeeBindModel(employeeBind.getEmployeeBinds());
        log.info("ContactBindInnerServiceImpl.updateEmployeeBind,accountEmployeeMappings={}.", accountEmployeeMappings);
        if(CollectionUtils.isEmpty(accountEmployeeMappings)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.updateEmployeeBinds(accountEmployeeMappings);
        return new Result<>(result.getData());
    }

    @Override
    public Result<List<QyweixinDepartmentBindModel>> queryDepartmentBind(QyweixinDepartmentBind departmentBind) {
        //查询企业绑定关系
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2(SourceTypeEnum.QYWX.getSourceType(),
                departmentBind.getFsEa(),
                departmentBind.getOutEa());
        if(!enterpriseMappingResult.isSuccess() || ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        //组装数据
        List<QyweixinAccountDepartmentMapping> accountDepartmentMappings = swapDepartmentBindModel(departmentBind.getDepartmentBinds());
        log.info("ContactBindInnerServiceImpl.queryDepartmentBind,accountDepartmentMappings={}.", accountDepartmentMappings);
        if(CollectionUtils.isEmpty(accountDepartmentMappings)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountDepartmentMapping>> result = qyweixinAccountBindService.findDepartmentBinds(accountDepartmentMappings);
        log.info("ContactBindInnerServiceImpl.queryDepartmentBind,result={}.", result);
        if(!result.isSuccess()) {
            return new Result<>(result.getErrorCode(), result.getErrorMsg(),null);
        }
        List<QyweixinDepartmentBindModel> departmentBindModels = swapDepartmentBindMapping(result.getData());
        log.info("ContactBindInnerServiceImpl.queryDepartmentBind,departmentBindModels={}.", departmentBindModels);
        return new Result<>(departmentBindModels);
    }

    @Override
    public Result<Integer> saveDepartmentBind(QyweixinDepartmentBind departmentBind) {
        //查询企业绑定关系
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2(SourceTypeEnum.QYWX.getSourceType(),
                departmentBind.getFsEa(),
                departmentBind.getOutEa());
        if(!enterpriseMappingResult.isSuccess() || ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        //组装数据
        List<QyweixinAccountDepartmentMapping> accountDepartmentMappings = swapDepartmentBindModel(departmentBind.getDepartmentBinds());
        log.info("ContactBindInnerServiceImpl.saveDepartmentBind,accountDepartmentMappings={}.", accountDepartmentMappings);
        if(CollectionUtils.isEmpty(accountDepartmentMappings)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.saveDepartmentBinds(accountDepartmentMappings);
        return new Result<>(result.getData());
    }

    @Override
    public Result<Integer> updateDepartmentBind(QyweixinDepartmentBind departmentBind) {
        //查询企业绑定关系
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2(SourceTypeEnum.QYWX.getSourceType(),
                departmentBind.getFsEa(),
                departmentBind.getOutEa());
        if(!enterpriseMappingResult.isSuccess() || ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        //组装数据
        List<QyweixinAccountDepartmentMapping> accountDepartmentMappings = swapDepartmentBindModel(departmentBind.getDepartmentBinds());
        log.info("ContactBindInnerServiceImpl.updateDepartmentBind,accountDepartmentMappings={}.", accountDepartmentMappings);
        if(CollectionUtils.isEmpty(accountDepartmentMappings)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.updateDepartmentBinds(accountDepartmentMappings);
        return new Result<>(result.getData());
    }

    private List<QyweixinAccountEmployeeMapping> swapEmployeeBindModel(List<QyweixinEmployeeBindModel> employeeBinds) {
        List<QyweixinAccountEmployeeMapping> employeeMappings = new LinkedList<>();
        for(QyweixinEmployeeBindModel model : employeeBinds) {
            QyweixinAccountEmployeeMapping mapping = new QyweixinAccountEmployeeMapping();
            mapping.setAppId(model.getAppId());
            mapping.setOutEa(model.getOutEa());
            mapping.setOutAccount(model.getOutAccount());
            mapping.setSource(model.getSource());
            mapping.setFsAccount(model.getFsAccount());
            mapping.setStatus(model.getStatus());
            employeeMappings.add(mapping);
        }
        return employeeMappings;
    }

    private List<QyweixinEmployeeBindModel> swapEmployeeBindMapping(List<QyweixinAccountEmployeeMapping> employeeMappings) {
        List<QyweixinEmployeeBindModel> employeeBindModels = new LinkedList<>();
        if(CollectionUtils.isEmpty(employeeMappings)) {
            return employeeBindModels;
        }
        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
            QyweixinEmployeeBindModel model = new QyweixinEmployeeBindModel();
            model.setAppId(mapping.getAppId());
            model.setFsAccount(mapping.getFsAccount());
            model.setOutAccount(mapping.getOutAccount());
            model.setOutEa(mapping.getOutEa());
            model.setSource(mapping.getSource());
            model.setStatus(mapping.getStatus());
            employeeBindModels.add(model);
        }
        return employeeBindModels;
    }

    private List<QyweixinAccountDepartmentMapping> swapDepartmentBindModel(List<QyweixinDepartmentBindModel> departmentBinds) {
        List<QyweixinAccountDepartmentMapping> departmentMappings = new LinkedList<>();
        for(QyweixinDepartmentBindModel model : departmentBinds) {
            QyweixinAccountDepartmentMapping mapping = new QyweixinAccountDepartmentMapping();
            mapping.setAppId(model.getAppId());
            mapping.setOutEa(model.getOutEa());
            mapping.setOutDepartmentId(model.getOutDepartmentId());
            mapping.setSource(model.getSource());
            mapping.setFsDepartmentId(model.getFsDepartmentId());
            mapping.setStatus(model.getStatus());
            mapping.setFsEa(model.getFsEa());
            departmentMappings.add(mapping);
        }
        return departmentMappings;
    }

    private List<QyweixinDepartmentBindModel> swapDepartmentBindMapping(List<QyweixinAccountDepartmentMapping> departmentMappings) {
        List<QyweixinDepartmentBindModel> departmentBindModels = new LinkedList<>();
        if(CollectionUtils.isEmpty(departmentMappings)) {
            return departmentBindModels;
        }
        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
            QyweixinDepartmentBindModel model = new QyweixinDepartmentBindModel();
            model.setAppId(mapping.getAppId());
            model.setFsDepartmentId(mapping.getFsDepartmentId());
            model.setOutDepartmentId(mapping.getOutDepartmentId());
            model.setOutEa(mapping.getOutEa());
            model.setSource(mapping.getSource());
            model.setStatus(mapping.getStatus());
            model.setFsEa(mapping.getFsEa());
            departmentBindModels.add(model);
        }
        return departmentBindModels;
    }

    @Override
    public Result<PageModel<List<EmployeeBindModel.FsEmployee>>> queryFsUnbind(QueryUnBindArg arg) {
        if(ObjectUtils.isEmpty(arg.getPageSize()) || arg.getPageSize() == 0) {
            //不传默认为100
            arg.setPageSize(100);
        }
        String outEa = QYWXConnectParam.isInvalid(arg.getConnectParam()) ? null : arg.getConnectParam().getOutEa();
        //查询未绑定员工信息
        Result<List<EmployeeBindModel.FsEmployee>> fsEmployeeListResult = getFsUnbind(arg.getEa(), outEa);
        log.info("ContactBindInnerServiceImpl.queryFsUnbind,fsEmployeeListResult={}",fsEmployeeListResult);
        if(!fsEmployeeListResult.isSuccess()) {
            return new Result<>(fsEmployeeListResult.getErrorCode(), fsEmployeeListResult.getErrorMsg(),null);
        }
        //搜索条件
        List<EmployeeBindModel.FsEmployee> fsEmployeeList = fsEmployeeListResult.getData();
        if(ObjectUtils.isNotEmpty(arg.getSearchContentMap())) {
            fsEmployeeList = fsEmployeeList.stream()
                    .filter(v -> (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName()))
                            || StringUtils.containsIgnoreCase(v.getUserName(), arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName()).toString()))
                            && (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName()))
                            || StringUtils.containsIgnoreCase(v.getMobile(), arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName()).toString()))
                            && (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName()))
                            || v.getStatus().equals(Integer.valueOf(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName()).toString())))
                            && (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName()))
                            || v.getDepartmentId().equals(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName()).toString())))
                    .collect(Collectors.toList());
        }
        log.info("ContactBindInnerServiceImpl.queryFsUnbind,fsEmployeeList={}",fsEmployeeList);
        return getPagedFsUnbind(fsEmployeeList, arg.getPageSize());
    }

    private Result<List<EmployeeBindModel.FsEmployee>> getFsUnbind(String fsEa, String outEa) {
        if(StringUtils.isEmpty(outEa)) {
            //查询企业是否有绑定关系
            QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa, outEa, ConfigCenter.crm_domain);
            log.info("ContactBindInnerServiceImpl.getFsUnbind,mapping={}.", mapping);
            if (ObjectUtils.isEmpty(mapping)) {
                return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
            }
        }
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
        //查询企业已帮定的员工数据
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, mainAppId, outEa);
//        List<Integer> accountBindEmployeeIds = qyweixinAccountEmployeeMappings.stream()
//                .map(v -> Integer.valueOf(v.getFsAccount().substring(v.getFsAccount().lastIndexOf(".") + 1)))
//                .collect(Collectors.toList());
        List<Integer> accountBindEmployeeIds = new ArrayList<>();
        for(QyweixinAccountEmployeeMapping employeeMapping : qyweixinAccountEmployeeMappings) {
            try {
                Integer empId = Integer.valueOf(employeeMapping.getFsAccount().substring(employeeMapping.getFsAccount().lastIndexOf(".") + 1));
                accountBindEmployeeIds.add(empId);
            } catch (Exception e) {
                log.info("ContactBindInnerServiceImpl.getFsUnbind,emp mapping invalid,employeeMapping={}", employeeMapping);
            }
        }
        log.info("ContactBindInnerServiceImpl.getFsUnbind,accountBindEmployeeIds={}.", accountBindEmployeeIds);
        //查询所有员工id
        GetAllEmployeeIdsResult fsUserIds = fsManager.getAllEmployeeIds(fsEa);
        List<Integer> userIdList = fsUserIds.getEmployeeIds();
        //过滤掉已绑定的员工
        List<Integer> unbindUserIdList = userIdList.stream()
                .filter(v -> !accountBindEmployeeIds.contains(v))
                .collect(Collectors.toList());
        log.info("ContactBindInnerServiceImpl.getFsUnbind,unbindUserIdList={}",unbindUserIdList);
        if(CollectionUtils.isEmpty(unbindUserIdList)) {
            //全部已绑定
            //正常无绑定账号，无须返回错误
            return new Result<>(new LinkedList<>());
        }
        //查询员工详情
        List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(fsEa, CrmConstants.SYSTEM_USER, unbindUserIdList);
        log.info("ContactBindInnerServiceImpl.getFsUnbind,employeeInfos={}",employeeInfos);
        if(CollectionUtils.isEmpty(employeeInfos)) {
            return Result.newInstance(ErrorRefer.QUERY_CRM_USER_INFO_ERROR);
        }
        //查询所有部门
        Map<Integer, String> allCirclesMap = fsManager.getAllCircles(fsEa, CrmConstants.SYSTEM_USER);
        log.info("ContactBindInnerServiceImpl.getFsUnbind,allCirclesMap={}",allCirclesMap);
        List<EmployeeBindModel.FsEmployee> fsEmployeeList = new ArrayList<>();
        for(EmployeeDto employeeDto : employeeInfos) {
            EmployeeBindModel.FsEmployee fsEmployee = getFsEmployee(employeeDto, allCirclesMap);
            fsEmployeeList.add(fsEmployee);
        }
        return new Result<>(fsEmployeeList);
    }

    private Result<PageModel<List<EmployeeBindModel.FsEmployee>>> getPagedFsUnbind(List<EmployeeBindModel.FsEmployee> fsEmployeeList,
                                                                                                                            Integer pageSize) {
        //排序：按id从大到小
        List<EmployeeBindModel.FsEmployee> orderFsEmployeeList = fsEmployeeList.stream()
                .sorted(Comparator.comparing(EmployeeBindModel.FsEmployee::getUserId).reversed())
                .collect(Collectors.toList());

        PageModel<List<EmployeeBindModel.FsEmployee>> pageModel = new PageModel<>();
        List<List<EmployeeBindModel.FsEmployee>> pageList = PageUtils.getPageList(orderFsEmployeeList, pageSize);
        pageModel.setPageList(pageList);
        pageModel.setPageSize(pageSize);
        pageModel.setTotalPage(pageList.size());
        pageModel.setTotal(orderFsEmployeeList.size());
        return new Result<>(pageModel);
    }

    @Override
    public Result<PageModel<List<EmployeeBindModel>>> queryBind(QueryBindArg arg) {
        log.info("ContactBindInnerServiceImpl.queryBind,arg={}", JSONObject.toJSONString(arg));
        if(ObjectUtils.isEmpty(arg.getPageSize()) || arg.getPageSize() == 0) {
            //不传默认为100
            arg.setPageSize(100);
        }
        String outEa = null;
        String outDepId = null;
        if(!QYWXConnectParam.isInvalid(arg.getConnectParam())) {
            outEa = arg.getConnectParam().getOutEa();
            outDepId = arg.getConnectParam().getOutDepId();
        }
        Result<List<EmployeeBindModel>> getBindResult = getBind(arg.getEa(), outEa, outDepId);
        log.info("ContactBindInnerServiceImpl.queryBind,getBindResult={}", getBindResult);
        if(!getBindResult.isSuccess()) {
            return new Result<>(getBindResult.getErrorCode(), getBindResult.getErrorMsg(),null);
        }
        List<EmployeeBindModel> employeeBindModels = getBindResult.getData();
        //搜索条件
        if(ObjectUtils.isNotEmpty(arg.getSearchContentMap())) {
            employeeBindModels = employeeBindModels.stream()
                    .filter(v -> (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName()))
                            || StringUtils.containsIgnoreCase(v.getFsEmployee().getUserName(), arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName()).toString()))
                            && (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName()))
                            || StringUtils.containsIgnoreCase(v.getFsEmployee().getMobile(), arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName()).toString()))
                            && (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName()))
                            || v.getFsEmployee().getStatus().equals(Integer.valueOf(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName()).toString())))
                            && (!arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName()))
                            || v.getFsEmployee().getDepartmentId().equals(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName()).toString())))
                    .collect(Collectors.toList());
        }
        log.info("ContactBindInnerServiceImpl.queryBind,employeeBindModels={}", employeeBindModels);
        return getPagedBindData(employeeBindModels, arg.getPageSize());
    }

    @Override
    public Result<List<EmployeeBindModel.OutEmployee>> queryOutUnbind(QueryOutUnbindArg arg) {
        if(ObjectUtils.isEmpty(arg.getPageSize()) || arg.getPageSize() == 0) {
            //不传默认为100
            arg.setPageSize(100);
        }
        String outEa = null;
        String outDepId = null;
        if(!QYWXConnectParam.isInvalid(arg.getConnectParam())) {
            outEa = arg.getConnectParam().getOutEa();
            outDepId = arg.getConnectParam().getOutDepId();
        }
        Result<List<EmployeeBindModel.OutEmployee>> outUnbindResult = getOutUnbind(arg.getEa(), outEa, outDepId);
        if(!outUnbindResult.isSuccess()) {
            return new Result<>(outUnbindResult.getErrorCode(), outUnbindResult.getErrorMsg(),null);
        }
        List<EmployeeBindModel.OutEmployee> outEmployees = outUnbindResult.getData();
        //搜索条件
        if(ObjectUtils.isNotEmpty(arg.getSearchContentMap())) {
            outEmployees = outEmployees.stream()
                    .filter(v -> !arg.getSearchContentMap().containsKey(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName())
                            || ObjectUtils.isEmpty(arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName()))
                            || StringUtils.containsIgnoreCase(v.getUserName(), arg.getSearchContentMap().get(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName()).toString()))
                    .collect(Collectors.toList());
        }
        //return getPagedOutUnbind(outEmployees, arg.getPageSize());
        return new Result<>(outEmployees);
    }

    public Result<List<EmployeeBindModel.OutEmployee>> getOutUnbind(String fsEa, String outEa, String outDepId) {
        if(StringUtils.isEmpty(outEa)) {
            //查询企业是否有绑定关系
            QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa, outEa, ConfigCenter.crm_domain);
            log.info("ContactBindInnerServiceImpl.getOutUnbind,mapping={}.", mapping);
            if (ObjectUtils.isEmpty(mapping)) {
                return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
            }
            outEa = mapping.getOutEa();
            outDepId = mapping.getDepId();
        }

        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();

        //查询企业已帮定的员工数据
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, mainAppId, outEa);
        List<String> accountBindEmployeeIds = qyweixinAccountEmployeeMappings.stream()
                .map(QyweixinAccountEmployeeMapping::getOutAccount)
                .collect(Collectors.toList());
        log.info("ContactBindInnerServiceImpl.getOutUnbind,accountBindEmployeeIds={}", accountBindEmployeeIds);
        //查询企微部门信息
        List<OutDepartmentInfoDoc> departmentInfoDocs = getOuDepartmentInfoDocs(outEa, outDepId);
        log.info("ContactBindInnerServiceImpl.getOutUnbind,departmentInfoDocs={}", departmentInfoDocs);
        Map<String, OutDepartmentInfoDoc> outDepartmentInfoDocMap = new HashMap<>();
        for(OutDepartmentInfoDoc departmentInfoDoc : departmentInfoDocs) {
            if(!outDepartmentInfoDocMap.containsKey(departmentInfoDoc.getOutDepartmentId())) {
                outDepartmentInfoDocMap.put(departmentInfoDoc.getOutDepartmentId(),departmentInfoDoc);
            }
        }
        log.info("ContactBindInnerServiceImpl.getOutUnbind,outDepartmentInfoDocMap={}", outDepartmentInfoDocMap);
        //查询企微人员信息
        List<OutUserInfoDoc> outUserInfoDocs = getOutUserInfoDocs(outEa, outDepId, departmentInfoDocs);
        log.info("ContactBindInnerServiceImpl.getOutUnbind,outUserInfoDocs={}", outUserInfoDocs);
        //去重
        outUserInfoDocs = outUserInfoDocs.stream().filter(v -> !accountBindEmployeeIds.contains(v.getOutUserId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(outUserInfoDocs)) {
            //正常返回
            return new Result<>(new LinkedList<>());
        }
        Map<String, OutUserInfoDoc> outUserInfoDocMap = new HashMap<>();
        for(OutUserInfoDoc userInfoDoc : outUserInfoDocs) {
            if(!outUserInfoDocMap.containsKey(userInfoDoc.getOutUserId())) {
                outUserInfoDocMap.put(userInfoDoc.getOutUserId(),userInfoDoc);
            }
        }
        log.info("ContactBindInnerServiceImpl.getOutUnbind,outUserInfoDocMap={}", outUserInfoDocMap);
        List<EmployeeBindModel.OutEmployee> outEmployees = new LinkedList<>();
        for(OutUserInfoDoc doc : outUserInfoDocs) {
            EmployeeBindModel.OutEmployee outEmployee = getOutEmployee(doc.getOutUserId(), outUserInfoDocMap, outDepartmentInfoDocMap, outDepId, departmentInfoDocs);
            outEmployees.add(outEmployee);
        }
        return new Result<>(outEmployees);
    }

    public Result<List<EmployeeBindModel>> getBind(String fsEa, String outEa, String outDepId) {
        if(StringUtils.isEmpty(outEa)) {
            //查询企业是否有绑定关系
            QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa, outEa, ConfigCenter.crm_domain);
            log.info("ContactBindInnerServiceImpl.getBind,mapping={}.", mapping);
            if (ObjectUtils.isEmpty(mapping)) {
                return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
            }
            outEa = mapping.getOutEa();
            outDepId = mapping.getDepId();
        }
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
        //查询企业已帮定的员工数据
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, mainAppId, outEa);
        if(CollectionUtils.isEmpty(qyweixinAccountEmployeeMappings)) {
            //正常返回
            return new Result<>(new LinkedList<>());
        }
//        List<Integer> accountBindEmployeeIds = qyweixinAccountEmployeeMappings.stream()
//                .map(v -> Integer.valueOf(v.getFsAccount().substring(v.getFsAccount().lastIndexOf(".") + 1)))
//                .collect(Collectors.toList());
        List<Integer> accountBindEmployeeIds = new ArrayList<>();
        for(QyweixinAccountEmployeeMapping employeeMapping : qyweixinAccountEmployeeMappings) {
            try {
                Integer empId = Integer.valueOf(employeeMapping.getFsAccount().substring(employeeMapping.getFsAccount().lastIndexOf(".") + 1));
                accountBindEmployeeIds.add(empId);
            } catch (Exception e) {
                log.info("ContactBindInnerServiceImpl.getBind,emp mapping invalid,employeeMapping={}", employeeMapping);
            }
        }
        log.info("ContactBindInnerServiceImpl.getBind,accountBindEmployeeIds={}.", accountBindEmployeeIds);
        //查询纷享人员信息
        List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(fsEa, CrmConstants.SYSTEM_USER, accountBindEmployeeIds);
        log.info("ContactBindInnerServiceImpl.getBind,employeeInfos={}", employeeInfos);
        if(CollectionUtils.isEmpty(employeeInfos)) {
            return Result.newInstance(ErrorRefer.QUERY_CRM_USER_INFO_ERROR);
        }
        Map<Integer, EmployeeDto> employeeDtoMap = employeeInfos.stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, Function.identity()));
        //查询纷享部门信息
        Map<Integer, String> allCirclesMap = fsManager.getAllCircles(fsEa, CrmConstants.SYSTEM_USER);
        log.info("ContactBindInnerServiceImpl.getBind,allCirclesMap={}", allCirclesMap);
        //查询企微部门信息
        List<OutDepartmentInfoDoc> departmentInfoDocs = getOuDepartmentInfoDocs(outEa, outDepId);
        log.info("ContactBindInnerServiceImpl.getBind,departmentInfoDocs={}", departmentInfoDocs);

        Map<String, OutDepartmentInfoDoc> outDepartmentInfoDocMap = new HashMap<>();
        for(OutDepartmentInfoDoc outDepartmentInfoDoc : departmentInfoDocs) {
            if(!outDepartmentInfoDocMap.containsKey(outDepartmentInfoDoc.getOutDepartmentId())) {
                outDepartmentInfoDocMap.put(outDepartmentInfoDoc.getOutDepartmentId(),outDepartmentInfoDoc);
            }
        }
        log.info("ContactBindInnerServiceImpl.getBind,outDepartmentInfoDocMap={}",outDepartmentInfoDocMap);
        //查询企微人员信息
        List<OutUserInfoDoc> outUserInfoDocs = getOutUserInfoDocs(outEa, outDepId, departmentInfoDocs);
        log.info("ContactBindInnerServiceImpl.getBind,outUserInfoDocs={}", outUserInfoDocs);
        Map<String, OutUserInfoDoc> outUserInfoDocMap = new HashMap<>();
        for(OutUserInfoDoc userInfoDoc : outUserInfoDocs) {
            if(!outUserInfoDocMap.containsKey(userInfoDoc.getOutUserId())) {
                outUserInfoDocMap.put(userInfoDoc.getOutUserId(),userInfoDoc);
            }
        }
        log.info("ContactBindInnerServiceImpl.getBind,outUserInfoDocMap={}", outUserInfoDocMap);
        List<EmployeeBindModel> employeeBindModels = new LinkedList<>();
        for(QyweixinAccountEmployeeMapping employeeMapping : qyweixinAccountEmployeeMappings) {
            EmployeeBindModel model = new EmployeeBindModel();
            Integer empId = null;
            try {
                empId = Integer.valueOf(employeeMapping.getFsAccount().substring(employeeMapping.getFsAccount().lastIndexOf(".") + 1));
            } catch (Exception e) {
                continue;
            }
            EmployeeDto employeeDto = employeeDtoMap.get(empId);
            if(employeeDto==null) continue;
            EmployeeBindModel.FsEmployee fsEmployee = getFsEmployee(employeeDto, allCirclesMap);
            EmployeeBindModel.OutEmployee outEmployee = getOutEmployee(employeeMapping.getOutAccount(), outUserInfoDocMap, outDepartmentInfoDocMap, outDepId, departmentInfoDocs);
            model.setFsEmployee(fsEmployee);
            model.setOutEmployee(outEmployee);
            employeeBindModels.add(model);
        }
        return new Result<>(employeeBindModels);
    }

    private List<OutDepartmentInfoDoc> getOuDepartmentInfoDocs(String outEa, String depId) {
        List<OutDepartmentInfoDoc> departmentInfoDocs = outDepartmentInfoManger.queryDepartmentInfos(outEa);
        String validRepAppId = qyweixinGatewayInnerService.getValidRepAppId(outEa).getData();
        log.info("getOuDepartmentInfoDocs,ConfigCenter.notUseCacheOutEAList={}",ConfigCenter.notUseCacheOutEAList);
        if(CollectionUtils.isEmpty(departmentInfoDocs) || StringUtils.containsIgnoreCase(ConfigCenter.notUseCacheOutEAList,outEa)) {
            //从企微获取部门信息
            com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentListRsp> departmentInfoListResult = qyWeixinManager.getDepartmentInfoList(validRepAppId, outEa, null);
            if(departmentInfoListResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoListResult.getData())) {
                departmentInfoDocs.clear();
                //保存入库
                for(QyweixinDepartmentRsp rsp : departmentInfoListResult.getData().getDepartment()) {
                    OutDepartmentInfoDoc departmentInfoDoc = new OutDepartmentInfoDoc();
                    //departmentInfoDoc.setId(ObjectId.get());
                    departmentInfoDoc.setOutEa(outEa);
                    departmentInfoDoc.setOutDepartmentId(rsp.getId());
                    departmentInfoDoc.setOutDepartmentInfo(new Gson().toJson(rsp));
                    departmentInfoDoc.setCreateTime(System.currentTimeMillis());
                    departmentInfoDoc.setUpdateTime(System.currentTimeMillis());
                    departmentInfoDocs.add(departmentInfoDoc);
                }
                //为防止插入耗时，异步操作
                Thread thread = new Thread(() -> outDepartmentInfoManger.batchReplace(departmentInfoDocs));
                thread.start();
            }
        }
        log.info("ContactBindInnerServiceImpl.getOuDepartmentInfoDocs,departmentInfoDocs={}",departmentInfoDocs);
        if(CollectionUtils.isEmpty(departmentInfoDocs)) {
            return departmentInfoDocs;
        }
        //一对多
        if(!depId.equals("1")) {
            List<OutDepartmentInfoDoc> outDepartmentInfoDocs = new LinkedList<>();
            Map<String, OutDepartmentInfoDoc> outDepartmentInfoDocMap = departmentInfoDocs.stream().collect(Collectors.toMap(OutDepartmentInfoDoc::getOutDepartmentId, Function.identity()));
            if(!outDepartmentInfoDocMap.containsKey(depId)) {
                //一对多逻辑，子部门一定得在代开发可见范围
                return new LinkedList<>();
            }
            outDepartmentInfoDocs.add(outDepartmentInfoDocMap.get(depId));
            List<String> outDepartmentIds = new LinkedList<>();
            outDepartmentIds.add(depId);
            do {
                if(CollectionUtils.isNotEmpty(outDepartmentIds)) {
                    List<String> temOutDepartmentIds = new LinkedList<>(outDepartmentIds);
                    outDepartmentIds.clear();
                    departmentInfoDocs.forEach(v -> {
                        QyweixinDepartmentRsp qyweixinDepartmentRsp = new Gson().fromJson(v.getOutDepartmentInfo(), QyweixinDepartmentRsp.class);
                        log.info("ContactBindInnerServiceImpl.getOuDepartmentInfoDocs,qyweixinDepartmentRsp={},temOutDepartmentIds={}",qyweixinDepartmentRsp, temOutDepartmentIds);
                        if(temOutDepartmentIds.contains(qyweixinDepartmentRsp.getParentid())) {
                            outDepartmentIds.add(qyweixinDepartmentRsp.getId());
                            outDepartmentInfoDocs.add(outDepartmentInfoDocMap.get(qyweixinDepartmentRsp.getId()));
                        }
                    });
                }
            } while(CollectionUtils.isNotEmpty(outDepartmentIds));
            return outDepartmentInfoDocs;
        } else {
            return departmentInfoDocs;
        }
    }

    private List<OutUserInfoDoc> getOutUserInfoDocs(String outEa, String depId, List<OutDepartmentInfoDoc> departmentInfoDocs) {
        List<OutUserInfoDoc> outUserInfoDocs = outUserInfoManger.queryUserInfos(outEa);
        log.info("getOutUserInfoDocs,ConfigCenter.notUseCacheOutEAList={}",ConfigCenter.notUseCacheOutEAList);
        if(CollectionUtils.isEmpty(outUserInfoDocs) || StringUtils.containsIgnoreCase(ConfigCenter.notUseCacheOutEAList,outEa)) {
            //从企微获取人员信息
            //获取人员详情，查询范围默认为1
            Result<List<QyweixinUserDetailInfoRsp>> allEmployeeLisResult = getAllEmployeeList(outEa, "1");
            if (allEmployeeLisResult.isSuccess() && CollectionUtils.isNotEmpty(allEmployeeLisResult.getData())) {
                outUserInfoDocs.clear();
                //保存入库
                for (QyweixinUserDetailInfoRsp rsp : allEmployeeLisResult.getData()) {
                    OutUserInfoDoc outUserInfoDoc = new OutUserInfoDoc();
                    //outUserInfoDoc.setId(ObjectId.get());
                    outUserInfoDoc.setOutEa(outEa);
                    outUserInfoDoc.setOutUserId(rsp.getUserid());
                    outUserInfoDoc.setOutUserInfo(new Gson().toJson(rsp));
                    outUserInfoDoc.setCreateTime(System.currentTimeMillis());
                    outUserInfoDoc.setUpdateTime(System.currentTimeMillis());
                    outUserInfoDocs.add(outUserInfoDoc);
                }
                //为防止插入耗时，异步操作
                Thread thread = new Thread(() -> outUserInfoManger.batchReplace(outUserInfoDocs));
                thread.start();
            }
        }
        log.info("ContactBindInnerServiceImpl.getOutUserInfoDocs,outUserInfoDocs={},departmentInfoDocs={}", outUserInfoDocs, departmentInfoDocs);
        if(CollectionUtils.isEmpty(outUserInfoDocs)) {
            return outUserInfoDocs;
        }
        //一对多
        if(!depId.equals("1")) {
            if(CollectionUtils.isEmpty(departmentInfoDocs)) {
                return new LinkedList<>();
            }
            List<String> outDepartmentIds = departmentInfoDocs.stream()
                    .map(OutDepartmentInfoDoc::getOutDepartmentId)
                    .collect(Collectors.toList());
            List<OutUserInfoDoc> outUserInfoDocList = new LinkedList<>();
            for(OutUserInfoDoc doc : outUserInfoDocs) {
                QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = new Gson().fromJson(doc.getOutUserInfo(), QyweixinUserDetailInfoRsp.class);
                log.info("ContactBindInnerServiceImpl.getOutUserInfoDocs,qyweixinUserDetailInfoRsp={}", qyweixinUserDetailInfoRsp);
                //这种方法在元素数量相对较少的情况下表现得很好。如果你有两个包含大量元素的List，那么可能需要考虑性能优化，例如使用HashSet代替List，因为contains方法在HashSet中的平均时间复杂度是O(1)，而在ArrayList中的时间复杂度是O(n)
                if(CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment()) && qyweixinUserDetailInfoRsp.getDepartment().stream().anyMatch(outDepartmentIds::contains)) {
                    outUserInfoDocList.add(doc);
                }
            }
            return outUserInfoDocList;
        } else {
            return outUserInfoDocs;
        }
    }

    private EmployeeBindModel.FsEmployee getFsEmployee(EmployeeDto employeeDto, Map<Integer, String> allCirclesMap) {
        EmployeeBindModel.FsEmployee fsEmployee = new EmployeeBindModel.FsEmployee();
        fsEmployee.setUserId(employeeDto.getEmployeeId() + "");
        fsEmployee.setUserName(employeeDto.getName());
        fsEmployee.setMobile(employeeDto.getMobile());
        fsEmployee.setStatus(employeeDto.getStatus().getValue());

        Integer mainDepId = null;
        List<Integer> depIdList = employeeDto.getMainDepartmentIds();
        if(CollectionUtils.isEmpty(depIdList)) {
            depIdList= Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID);
        }

        mainDepId = depIdList.get(0);
        fsEmployee.setDepartmentId(mainDepId + "");
        fsEmployee.setDepartmentName(allCirclesMap.get(mainDepId));
        return fsEmployee;
    }

    private EmployeeBindModel.OutEmployee getOutEmployee(String outUserId, Map<String, OutUserInfoDoc> outUserInfoDocMap, Map<String, OutDepartmentInfoDoc> outDepartmentInfoDocMap, String depId, List<OutDepartmentInfoDoc> departmentInfoDocs) {
        String lang = TraceUtil.getLocale();
        log.info("getOutEmployee,lang={}",lang);
        EmployeeBindModel.OutEmployee outEmployee = new EmployeeBindModel.OutEmployee();
        outEmployee.setUserId(outUserId);
        if(!outUserInfoDocMap.containsKey(outUserId)) {
            outEmployee.setUserName(i18NStringManager.get(I18NStringEnum.s192,lang,null));
            outEmployee.setDepartmentId("0");
            outEmployee.setDepartmentName(i18NStringManager.get(I18NStringEnum.s197,lang,null));
            return outEmployee;
        }
        OutUserInfoDoc userInfoDoc = outUserInfoDocMap.get(outUserId);
        QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = new Gson().fromJson(userInfoDoc.getOutUserInfo(), QyweixinUserDetailInfoRsp.class);
        log.info("ContactBindInnerServiceImpl.getOutEmployee,qyweixinUserDetailInfoRsp={}", qyweixinUserDetailInfoRsp);
        if(ObjectUtils.isEmpty(qyweixinUserDetailInfoRsp)) {
            outEmployee.setUserName(i18NStringManager.get(I18NStringEnum.s192,lang,null));
            outEmployee.setDepartmentId("0");
            outEmployee.setDepartmentName(i18NStringManager.get(I18NStringEnum.s197,lang,null));
            return outEmployee;
        }
        if(StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getName())) {
            outEmployee.setUserName(qyweixinUserDetailInfoRsp.getName());
        } else {
            outEmployee.setUserName("U-FSQYWX-" + outUserId);
        }
        //获取用户部门id，先找主属部门，没有的话，再从部门列表去找
        //一对多时不能直接找第一个
        String departmentId = "0";
        if(!depId.equals("1")) {
            if(CollectionUtils.isEmpty(departmentInfoDocs)) {
                outEmployee.setUserName(i18NStringManager.get(I18NStringEnum.s192,lang,null));
                outEmployee.setDepartmentId("0");
                outEmployee.setDepartmentName(i18NStringManager.get(I18NStringEnum.s197,lang,null));
                return outEmployee;
            }
            List<String> outDepartmentIds = departmentInfoDocs.stream()
                    .map(OutDepartmentInfoDoc::getOutDepartmentId)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment())) {
                for(String id : qyweixinUserDetailInfoRsp.getDepartment()) {
                    if(outDepartmentIds.contains(id)) {
                        //找到一个就行
                        departmentId = id;
                        break;
                    }
                }
            }
        } else {
            departmentId = StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getMain_department()) ? qyweixinUserDetailInfoRsp.getMain_department() : (CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment()) ? qyweixinUserDetailInfoRsp.getDepartment().get(0) : "0");
        }
        outEmployee.setDepartmentId(departmentId);
        if(departmentId.equals("0")  || !outDepartmentInfoDocMap.containsKey(departmentId)) {
            outEmployee.setDepartmentName(i18NStringManager.get(I18NStringEnum.s197,lang,null));
        } else {
            QyweixinDepartmentRsp qyweixinDepartmentRsp = new Gson().fromJson(outDepartmentInfoDocMap.get(departmentId).getOutDepartmentInfo(), QyweixinDepartmentRsp.class);
            log.info("ContactBindInnerServiceImpl.getOutEmployee,qyweixinDepartmentRsp={}", qyweixinDepartmentRsp);
            if(ObjectUtils.isEmpty(qyweixinDepartmentRsp)) {
                outEmployee.setDepartmentId("0");
                outEmployee.setDepartmentName(i18NStringManager.get(I18NStringEnum.s197,lang,null));
            } else {
                if(StringUtils.isNotEmpty(qyweixinDepartmentRsp.getName())) {
                    outEmployee.setDepartmentName(qyweixinDepartmentRsp.getName());
                } else {
                    outEmployee.setDepartmentName("D-FSQYWX-" + departmentId);
                }
            }
        }
        return outEmployee;
    }

    private Result<PageModel<List<EmployeeBindModel>>> getPagedBindData(List<EmployeeBindModel> employeeBindList, Integer pageSize) {
        PageModel<List<EmployeeBindModel>> pageModel = new PageModel<>();
        List<List<EmployeeBindModel>> pageList = PageUtils.getPageList(employeeBindList, pageSize);
        pageModel.setPageList(pageList);
        pageModel.setPageSize(pageSize);
        pageModel.setTotalPage(pageList.size());
        pageModel.setTotal(employeeBindList.size());
        return new Result<>(pageModel);
    }

    private Result<PageModel<List<EmployeeBindModel.OutEmployee>>> getPagedOutUnbind(List<EmployeeBindModel.OutEmployee> outEmployeeList,
                                                                                   Integer pageSize) {
        PageModel<List<EmployeeBindModel.OutEmployee>> pageModel = new PageModel<>();
        List<List<EmployeeBindModel.OutEmployee>> pageList = PageUtils.getPageList(outEmployeeList, pageSize);
        pageModel.setPageList(pageList);
        pageModel.setPageSize(pageSize);
        pageModel.setTotalPage(pageList.size());
        pageModel.setTotal(outEmployeeList.size());
        return new Result<>(pageModel);
    }

    @Override
    public Result<ExportDataResult> exportEmployeeBind(String fsEa, String outEa, Integer userId, String lang) {
        //查询企业是否有绑定关系
        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), fsEa, outEa, ConfigCenter.crm_domain);
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,mapping={}.", mapping);
        if (ObjectUtils.isEmpty(mapping)) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        outEa = mapping.getOutEa();
        String depId = mapping.getDepId();
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
        //查询企业已帮定的员工数据
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, mainAppId, outEa);
        if(CollectionUtils.isEmpty(qyweixinAccountEmployeeMappings)) {
            //正常返回
            return new Result<>();
        }
        List<Integer> accountBindEmployeeIds = qyweixinAccountEmployeeMappings.stream()
                .map(v -> Integer.valueOf(v.getFsAccount().substring(v.getFsAccount().lastIndexOf(".") + 1)))
                .collect(Collectors.toList());
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,accountBindEmployeeIds={}.", accountBindEmployeeIds);
        //查询纷享人员信息
        List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(fsEa, CrmConstants.SYSTEM_USER, accountBindEmployeeIds);
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,employeeInfos={}", employeeInfos);
        if(CollectionUtils.isEmpty(employeeInfos)) {
            return Result.newInstance(ErrorRefer.QUERY_CRM_USER_INFO_ERROR);
        }
        Map<Integer, EmployeeDto> employeeDtoMap = employeeInfos.stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, Function.identity()));
        //查询纷享部门信息
        Map<Integer, String> allCirclesMap = fsManager.getAllCircles(fsEa, CrmConstants.SYSTEM_USER);
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,allCirclesMap={}", allCirclesMap);
        //查询企微部门信息
        List<OutDepartmentInfoDoc> departmentInfoDocs = getOuDepartmentInfoDocs(outEa, depId);
        Map<String, OutDepartmentInfoDoc> outDepartmentInfoDocMap = new HashMap<>();
        for(OutDepartmentInfoDoc departmentInfoDoc : departmentInfoDocs) {
            if(!outDepartmentInfoDocMap.containsKey(departmentInfoDoc.getOutDepartmentId())) {
                outDepartmentInfoDocMap.put(departmentInfoDoc.getOutDepartmentId(),departmentInfoDoc);
            }
        }
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,outDepartmentInfoDocMap={}",outDepartmentInfoDocMap);
        //查询企微人员信息
        List<OutUserInfoDoc> outUserInfoDocs = getOutUserInfoDocs(outEa, depId, departmentInfoDocs);
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,outUserInfoDocs={}",outUserInfoDocs);
        Map<String, OutUserInfoDoc> outUserInfoDocMap = new HashMap<>();
        for(OutUserInfoDoc userInfoDoc : outUserInfoDocs) {
            if(!outUserInfoDocMap.containsKey(userInfoDoc.getOutUserId())) {
                outUserInfoDocMap.put(userInfoDoc.getOutUserId(),userInfoDoc);
            }
        }
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,outUserInfoDocMap={}", outUserInfoDocMap);
        //组装数据
        List<EmployeeBindFile> employeeBindFiles = new LinkedList<>();
        log.info("ContactBindInnerServiceImpl.exportEmployeeBind,lang={}", lang);
        String normal = i18NStringManager.get(I18NStringEnum.s190,lang,null);
        String stop = i18NStringManager.get(I18NStringEnum.s191,lang,null);
        String notInAvailableRange = i18NStringManager.get(I18NStringEnum.s192,lang,null);
        for(QyweixinAccountEmployeeMapping employeeMapping : qyweixinAccountEmployeeMappings) {
            EmployeeBindFile file = new EmployeeBindFile();
            List<String> accountList = Splitter.on(".").splitToList(employeeMapping.getFsAccount());
            Integer fsUserId = Integer.valueOf(accountList.get(2));
            String outUserId = employeeMapping.getOutAccount();
            file.setFsEa(fsEa);
            file.setOutEa(employeeMapping.getOutEa());
            file.setFsUserId(fsUserId);
            file.setOutUserId(outUserId);
            file.setBindStatus((employeeMapping.getStatus() == 0 ? normal : stop));
            file.setGmtCreate(employeeMapping.getGmtCreate());
            if(employeeDtoMap.containsKey(fsUserId)) {
                EmployeeDto employeeDto = employeeDtoMap.get(fsUserId);
                file.setFsUserName(employeeDto.getName());
                file.setFsDeptId(employeeDto.getMainDepartmentId());
                file.setFsUserStatus((employeeDto.getStatus().getValue() == 1 ? normal : stop));
                if(allCirclesMap.containsKey(employeeDto.getMainDepartmentId())) {
                    file.setFsDeptName(allCirclesMap.get(employeeDto.getMainDepartmentId()));
                }
            }

            if(outUserInfoDocMap.containsKey(outUserId)) {
                OutUserInfoDoc userInfoDoc = outUserInfoDocMap.get(outUserId);
                QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = new Gson().fromJson(userInfoDoc.getOutUserInfo(), QyweixinUserDetailInfoRsp.class);
                file.setOutUserName(qyweixinUserDetailInfoRsp.getName());
            } else {
                file.setOutUserName(notInAvailableRange);
            }

            employeeBindFiles.add(file);
        }

        ExportDataResult exportDataResult = sendExportResult(fsEa, userId, employeeBindFiles,lang);
        return new Result<>(exportDataResult);
    }

    private ExportDataResult sendExportResult(String fsEa, Integer userId, List<EmployeeBindFile> employeeBindFiles, String lang) {
        String tenantId = eieaConverter.enterpriseAccountToId(fsEa)+"";
        if (CollectionUtils.isNotEmpty(employeeBindFiles)) {
            // 创建Excel文件并写入数据
            CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager,tenantId,lang);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            EasyExcel.write(bos, EmployeeBindFile.class)
                    .sheet(i18NStringManager.get(I18NStringEnum.s193,lang,null))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(customCellWriteHandler)
                    .doWrite(employeeBindFiles);
            String tnFile = fileManager.uploadTnFile(fsEa, -10000, bos.toByteArray());
            String fileName = URLEncoder.encode(i18NStringManager.get(I18NStringEnum.s194,lang,null)) + ".xlsx";
            String downloadUrl = String.format(ConfigCenter.DOWNLOAD_FILE_PATH, tnFile + ".xlsx", fileName);
            String msg = i18NStringManager.get(I18NStringEnum.s195,lang,null) + downloadUrl;
            //发送企信消息
            SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
            sendTextNoticeArg.setTenantId(tenantId);
            sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
            sendTextNoticeArg.setMsg(msg);
            sendTextNoticeArg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s196,lang,null) + LocalDateTime.now().toString());
            notificationService.sendQYWXNotice(sendTextNoticeArg);

            ExportDataResult exportDataResult = new ExportDataResult();
            exportDataResult.setSuccess(true);
            exportDataResult.setDownloadUrl(downloadUrl);
            exportDataResult.setPrintMsg("");
            return exportDataResult;
        }
        return null;
    }

    @Override
    public Result<Void> addUserList(String appId, String outEa, List<String> outUserIdList) {
        return contactsService.addUserList(appId, outEa, outUserIdList);
    }

    @Override
    public Result<Void> addUser(String appId, String outEa, String outUserId) {
        return contactsService.addUser(appId, outEa, outUserId);
    }
}
