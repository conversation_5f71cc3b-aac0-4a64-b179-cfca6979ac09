package com.facishare.open.qywx.accountsync.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinEmployeeAccountModel;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.model.CreateConnectorArg;
import com.facishare.open.qywx.accountinner.model.FsAccountModel;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.arg.BatchCreateQywxConnectorArg;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.dao.*;
import com.facishare.open.qywx.accountsync.entity.ErpConnectInfoEntity;
import com.facishare.open.qywx.accountsync.manager.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpInfoBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.accountsync.utils.IdGenerator;
import com.facishare.open.qywx.accountsync.utils.SecurityUtil;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.result.ActionChangeOwnerResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service("toolsServiceImpl")
@Slf4j
public class ToolsServiceImpl implements ToolsService {
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CorpManager corpManager;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private QyweixinCorpBindDao qyweixinCorpBindDao;
    @Autowired
    private QyweixinOrderInfoDao qyweixinOrderInfoDao;
    @Autowired
    private QyweixinCorpInfoDao qyweixinCorpInfoDao;
    @Autowired
    private QyweixinExternalContactDao qyweixinExternalContactDao;
    @Autowired
    private QyweixinUserDao qyweixinUserDao;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private EIEAConverter eieaConverter;
    @ReloadableProperty("repAppId")
    private String repAppId;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private FsManager fsManager;
    @Autowired
    private QyweixinIdToOpenidManager qyweixinIdToOpenidManager;
    @Autowired
    private OutUserInfoManger outUserInfoManger;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Resource
    private FsObjServiceProxy fsObjServiceProxy;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EventCloudProxyManager eventCloudProxyManager;
    @Autowired
    private ErpdssManager erpdssManager;
    @Autowired
    private IdGenerator idGenerator;

    @Override
    public Result<AppLicenseInfo> getAppLicenseInfo(String corpId,String appId) {
        if(StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        com.facishare.open.qywx.accountinner.result.Result<AppLicenseInfo> appLicenseInfoResult = qyWeixinManager.getAppLicenseInfo(corpId, appId);
        if(appLicenseInfoResult.isSuccess()) {
            return new Result<>(appLicenseInfoResult.getData());
        }
        return new Result<>(appLicenseInfoResult.getCode(), appLicenseInfoResult.getMsg(),null);
    }

    @Override
    public Result<List<String>> getUserListInAppVisibleRange(String corpId, String appId) {
        if(StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        com.facishare.open.qywx.accountinner.result.Result<List<String>> userIdListResult = qyWeixinManager.getUserListInAppVisibleRange(corpId, appId);
        if(!userIdListResult.isSuccess() || ObjectUtils.isEmpty(userIdListResult.getData())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), userIdListResult.getMsg(),null);
        }
        return new Result<>(userIdListResult.getData());
    }

    @Override
    public Result<String> corpId2OpenCorpId(String corpId) {
        String openCorpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        return new Result<>(openCorpId);
    }

    @Deprecated
    @Override
    public Result<String> userId2OpenUserId(String corpId, String userId) {
        return userId2OpenUserId2(corpId, ConfigCenter.crmAppId, userId);
    }

    @Override
    public Result<String> userId2OpenUserId2(String corpId, String appId, String userId) {
        appId = StringUtils.isEmpty(appId) ? ConfigCenter.crmAppId : appId;
        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> listResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId, appId);
        String openUserId = userId;
        if(listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
            openUserId = listResult.getData().get(0).getOpen_userid();
        }
        return new Result<>(openUserId);
    }

    @Override
    public Result<String> userId2OpenUserId3(String corpId, String appId, String userId) {
        appId = StringUtils.isEmpty(appId) ? ConfigCenter.crmAppId : appId;
        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> result = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId, appId);
        String openUserId = userId;
        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            openUserId = result.getData().get(0).getOpen_userid();
        }
        if(StringUtils.equalsIgnoreCase(result.getCode(),"0")) {
            return new Result<>(openUserId);
        }
        return new Result<>(result.getCode(),result.getMsg(),openUserId);
    }

    @Override
    public Result<Integer> refreshEmployeeTable(String corpId) {
        new Thread(() -> this.refreshEmployeeTable2(corpId)).start();
        return new Result<>();
    }

    private Result<Integer> refreshEmployeeTable2(String corpId) {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.selectAll(corpId);
        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        if(CollectionUtils.isEmpty(result.getData())) {
            if(StringUtils.isNotEmpty(corpId2)) {
                result = qyweixinAccountBindService.selectAll(corpId2);
                if(CollectionUtils.isNotEmpty(result.getData())) {
                    corpId = corpId2;
                }
            }
        }
        log.info("ToolsServiceImpl.refreshEmployeeTable,corpId={},result={}",corpId,result);
        if(CollectionUtils.isEmpty(result.getData())) return new Result<>(0);

        List<QyweixinAccountEmployeeMapping> totalList = new ArrayList<>();
        for(QyweixinAccountEmployeeMapping mapping : result.getData()) {
            String userId = mapping.getOutAccount();
            try {
                if(!corpId2.equals(mapping.getOutEa())) {
                    mapping.setOutEa(corpId2);
                }
                //先查询下企微用户详情，获取到才继续
                com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfoResult = qyWeixinManager.getUserInfo(ConfigCenter.crmAppId, corpId, userId);
                log.info("ToolsServiceImpl.refreshEmployeeTable,userInfoResult={}",userInfoResult);
                if(!userInfoResult.isSuccess()) {
                    continue;
                }
                com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> listResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId);
                log.info("ToolsServiceImpl.refreshEmployeeTable,list={}",listResult);
                if(listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    mapping.setOutAccount(listResult.getData().get(0).getOpen_userid());
                    mapping.setIsvAccount(mapping.getOutAccount());
                    totalList.add(mapping);
                }
            } catch (Exception e) {
                log.info("ToolsServiceImpl.refreshEmployeeTable,exception={}",e.getMessage());
            } finally {
                //停一会再调用
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    log.info("ToolsServiceImpl.refreshEmployeeTable,exception={}",e.getMessage());
                }
            }
        }

        log.info("ToolsServiceImpl.refreshEmployeeTable,totalList={},totalList.size={},result.data.size={}",
                totalList,totalList.size(),result.getData().size());
        com.facishare.open.qywx.accountbind.result.Result<Integer> result2 = qyweixinAccountBindService.batchUpdateEmployeeMapping(totalList);
        return new Result<>(result2.getData());
    }

    @Override
    public Result<Integer> refreshEnterpriseTable(String corpId) {
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = null;
        //明文的corpId获取到的企业信息
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindService.selectEnterpriseBind("qywx", corpId);
        //刷企业表的数据，只用刷out_ea字段，明文转密文，对于isv_out_ea字段，有值不用管，没值直接插入密文
        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        if(CollectionUtils.isNotEmpty(result.getData()) && StringUtils.isNotEmpty(corpId2)) {
            enterpriseMappingList = result.getData();
            for(QyweixinAccountEnterpriseMapping enterpriseMapping : enterpriseMappingList) {
                enterpriseMapping.setOutEa(corpId2);
                enterpriseMapping.setIsvOutEa(corpId2);
            }
        } else {
            result = qyweixinAccountBindService.selectEnterpriseBind("qywx", corpId2);
            if(CollectionUtils.isNotEmpty(result.getData()) && StringUtils.isNotEmpty(corpId2)) {
                enterpriseMappingList = result.getData();
                for(QyweixinAccountEnterpriseMapping enterpriseMapping : enterpriseMappingList) {
                    enterpriseMapping.setIsvOutEa(corpId2);
                }
            }
        }
        log.info("ToolsServiceImpl.refreshEnterpriseTable,corpId={},enterpriseMappingList={}",corpId,enterpriseMappingList);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) return new Result<>(0);
        int count = 0;
        for(QyweixinAccountEnterpriseMapping enterpriseMapping : enterpriseMappingList) {
            com.facishare.open.qywx.accountbind.result.Result<Integer> result2 = qyweixinAccountBindService.updateQyweixinAccountEnterpriseMapping(enterpriseMapping);
            count+=result2.getData();
        }
        return new Result<>(count);
    }

    @Override
    public Result<Integer> refreshApplicationTable(String corpId) {
        int count = 0;
        QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
        qyweixinCorpBindBo.setCorpId(corpId);
        List<QyweixinCorpBindBo> entity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        qyweixinCorpBindBo.setCorpId(corpId2);
        List<QyweixinCorpBindBo> entity2 = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
        List<QyweixinCorpBindBo> entity3 = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(entity)) entity3.addAll(entity);
        if(CollectionUtils.isNotEmpty(entity2)) entity3.addAll(entity2);
        if(CollectionUtils.isEmpty(entity3)) return new Result<>();
        for (int i = 0; i < entity3.size(); i++) {
            //无论原先明文还是密文，直接刷成密文
            entity3.get(i).setCorpId(corpId2);
            entity3.get(i).setIsvCorpId(corpId2);
            int update = qyweixinCorpBindDao.update(entity3.get(i));
            count += update;
        }
        return new Result<>(count);
    }

    @Override
    public Result refreshEnterpriseAccount(String corpId, String agentId) {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.enterpriseBind2OpenEnterpriseBind(corpId, agentId);
        if(StringUtils.isNotEmpty(result.getData()) && JSONPath.read(result.getData(), "$.errcode").equals(0)) {
            return new Result<>().addError("s120050000", JSONPath.read(result.getData(), "$.errmsg").toString(),null);
        }
        return new Result<>().addError(JSONPath.read(result.getData(), "$.errcode").toString(), JSONPath.read(result.getData(), "$.errmsg").toString(),null);
    }

    @Override
    public Result<Void> getEnterpriseAccountMigration(String corpId, String appId) {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getEnterpriseAccountMigration(corpId, appId);
        if(StringUtils.isNotEmpty(result.getData()) && JSONPath.read(result.getData(), "$.errcode").equals(0)) {
            return new Result<Void>().addError("s120050000", JSONPath.read(result.getData(), "$.migration_info").toString(),null);
        }
        return new Result<Void>().addError(JSONPath.read(result.getData(), "$.errcode").toString(), JSONPath.read(result.getData(), "$.errmsg").toString(),null);
    }

    @Override
        public Result<Integer> refreshDepartmentTable(String corpId) {
            String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
            if(StringUtils.isEmpty(corpId2)) return new Result<>();
            com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.batchUpdateDepartmentBind(corpId, corpId2);
            return new Result<>(result.getData());
    }

    @Override
    public Result<Integer> refreshOrderTable(String corpId) {
        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        if(StringUtils.isEmpty(corpId2)) return new Result<>();
        int count = qyweixinOrderInfoDao.BatchUpdateDepartmentBind(corpId, corpId2);
        return new Result<>(count);
    }

    @Override
    public Result<Integer> refreshApplicationInfoTable(String corpId) {
        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        if(StringUtils.isEmpty(corpId2)) return new Result<>();
        QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
        qyweixinCorpInfoBo.setCorpId(corpId);
        List<QyweixinCorpInfoBo> result = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
        log.info("ToolsServiceImpl.refreshApplicationInfoTable,corpId={},result={}",corpId,result);
        if(CollectionUtils.isEmpty(result)) return new Result<>(0);
        int count = 0;
        for(QyweixinCorpInfoBo mapping : result) {
            String userId = mapping.getUserId();
            try {
                com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> listResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), corpId2);
                log.info("ToolsServiceImpl.refreshApplicationInfoTable,list={}",listResult);
                //安装CRM的管理员会离职，但是没有进行更新操作，会导致取不到值,取不到值直接使用原userId
                count = qyweixinCorpInfoDao.batchUpdateApplicationInfoBind(corpId, corpId2, userId, (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) ? listResult.getData().get(0).getOpen_userid() : userId);
            } catch (Exception e) {
                log.info("ToolsServiceImpl.refreshApplicationInfoTable,exception={}",e.getMessage());
            }
        }
        return new Result<>(count);
    }

    @Override
    public Result<Integer> deleteExternalContactTable(String corpId) {
        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        int count = qyweixinExternalContactDao.deleteAllExternalUserIds(corpId, corpId2);
        return new Result<>(count);
    }

    @Override
    public Result<Integer> deleteUserTable(String corpId) {
        String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        int count = qyweixinUserDao.deleteAllUserIds(corpId, corpId2);
        return new Result<>(count);
    }

    @Override
    public Result<Map<String, Object>> refreshAllEnterpriseAccount(List<String> corpIds) {
        Map<String, Object> mapResult = new HashMap<>();
        for (int i = 0; i < corpIds.size(); i++) {
            Map<String, Object> map = new HashMap<>();
            String corpId = corpIds.get(i);
            //刷新企业绑定表：enterprise_account_bind
            Result<Integer> enterpriseAccountBind = this.refreshEnterpriseTable(corpId);
            map.put("enterpriseAccountBind", enterpriseAccountBind);
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},enterpriseAccountBind={}",corpId, enterpriseAccountBind);
            //刷新员工绑定表：employee_account_bind
            Result<Integer> employeeAccountBind = this.refreshEmployeeTable(corpId);
            map.put("employeeAccountBind", employeeAccountBind);
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},employeeAccountBind={}",corpId, employeeAccountBind);
            //刷新应用绑定表：qyweixin_corp_bind
            Result<Integer> qyweixinCorpBind = this.refreshApplicationTable(corpId);
            map.put("qyweixinCorpBind", qyweixinCorpBind);
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},qyweixinCorpBind={}",corpId, qyweixinCorpBind);
            //刷新部门绑定表：department_account_bind
            Result<Integer> departmentAccountBind = this.refreshDepartmentTable(corpId);
            map.put("departmentAccountBind", departmentAccountBind);
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},departmentAccountBind={}",corpId, departmentAccountBind);
            //刷新订单表：qyweixin_order_info
            Result<Integer> qyweixinOrderInfo = this.refreshOrderTable(corpId);
            map.put("qyweixinOrderInfo", qyweixinOrderInfo);
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},qyweixinOrderInfo={}",corpId, qyweixinOrderInfo);
            //刷新应用信息表：qyweixin_corp_info
            Result<Integer> qyweixinCorpInfo = this.refreshApplicationInfoTable(corpId);
            map.put("qyweixinCorpInfo", qyweixinCorpInfo);
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},qyweixinCorpInfo={}",corpId, qyweixinCorpInfo);
            //迁移企业账号,第三方下的所有应用
            com.facishare.open.qywx.accountinner.result.Result<String> refreshCRM = qyWeixinManager.enterpriseBind2OpenEnterpriseBind(corpId, "");
            map.put("refreshCRM", refreshCRM.getData());
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},refreshCRM={}",corpId, refreshCRM);
            String corpId2 = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
            //迁移代开发应用
            QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
            qyweixinCorpBindBo.setCorpId(corpId);
            qyweixinCorpBindBo.setAppId(repAppId);
            List<QyweixinCorpBindBo> entity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
            if(CollectionUtils.isEmpty(entity)) {
                qyweixinCorpBindBo.setCorpId(corpId2);
                entity = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
            }
            com.facishare.open.qywx.accountinner.result.Result<String> refreshRep = null;
            if(CollectionUtils.isNotEmpty(entity)) {
                refreshRep = qyWeixinManager.enterpriseBind2OpenEnterpriseBind(corpId, entity.get(0).getAgentId());
            }
            map.put("refreshRep", refreshRep.getData());
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},refreshRep={}",corpId, refreshRep);
            //查询企业CRM迁移状态,迁移完成企业后，需要使用密文获取token
            com.facishare.open.qywx.accountinner.result.Result<String> queryCRMRefreshStatus = qyWeixinManager.getEnterpriseAccountMigration(corpId2, ConfigCenter.crmAppId);
            map.put("queryCRMRefreshStatus", queryCRMRefreshStatus.getData());
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},queryCRMRefreshStatus={}",corpId, queryCRMRefreshStatus);
            //查询企业代开发迁移状态,迁移完成企业后，需要使用密文获取token
            com.facishare.open.qywx.accountinner.result.Result<String> queryRepRefreshStatus = qyWeixinManager.getEnterpriseAccountMigration(corpId2, repAppId);
            map.put("queryRepRefreshStatus", queryRepRefreshStatus.getData());
            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},queryRepRefreshStatus={}",corpId, queryRepRefreshStatus);

            //有团队依赖这两个表，不能直接删除这两个表的数据
//            //删除外部联系人缓存表：qyweixin_external_contact
//            Result<Integer> result10 = this.deleteExternalContactTable(corpId);
//            map.put("result10", result10);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},result10={}",corpId, result10);
//            //删除用户缓存表：qyweixin_user
//            Result<Integer> result11 = this.deleteUserTable(corpId);
//            map.put("result11", result11);
//            log.info("ToolsServiceImpl.refreshAllEnterpriseAccount,corpId={},result11={}",corpId, result11);

            mapResult.put(corpId, map);
        }
        return new Result<>(mapResult);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> updateServiceProviderEmployeeBind(String fsEa, String appId, String outEa) {
        List<QyweixinAccountEmployeeMapping> errorUpdateEmpResult = new LinkedList<>();
        if(StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, appId, outEa);
        log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,qyweixinAccountEmployeeMappings={}", qyweixinAccountEmployeeMappings);
        if(CollectionUtils.isEmpty(qyweixinAccountEmployeeMappings)) {
            return new Result<>(errorUpdateEmpResult);
        }
        for(QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping : qyweixinAccountEmployeeMappings) {
            if(qyweixinAccountEmployeeMapping.getIsvAccount().length() == 32) {
                log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,qyweixinAccountEmployeeMapping={}", qyweixinAccountEmployeeMapping);
                continue;
            }
            qyweixinAccountEmployeeMapping.setOutAccount(qyweixinAccountEmployeeMapping.getIsvAccount());
            try {
                int i = qyweixinAccountBindInnerService.updateQyweixinAccountEmployee(qyweixinAccountEmployeeMapping);
            } catch (Exception e) {
                log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,qyweixinAccountEmployeeMapping1={}", qyweixinAccountEmployeeMapping);
                errorUpdateEmpResult.add(qyweixinAccountEmployeeMapping);
            }
        }
        log.info("ToolsServiceImpl.updateServiceProviderEmployeeBind,errorUpdateEmpResult={}", errorUpdateEmpResult);
        return new Result<>(errorUpdateEmpResult);
    }

    @Override
    public Result<List<FsAccountModel>> getAccountInfo(int bindType, List<String> accountLevelList) {
        String key = "key_account_bind_info_list";
        String value = redisDataSource.getRedisClient().get(key);
        List<FsAccountModel> fsAccountModelList = null;
        if(StringUtils.isNotEmpty(value)) {
            fsAccountModelList = JSONObject.parseArray(value,FsAccountModel.class);
            return new Result<>(fsAccountModelList);
        }
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindService.queryEnterpriseMappingByBindType(1);
        if(CollectionUtils.isEmpty(result.getData())) {
            log.info("ToolsServiceImpl.getAccountInfo,result is empty");
            return new Result<>();
        }
        List<String> fsEaList = result.getData().stream().map(QyweixinAccountEnterpriseMapping::getFsEa).collect(Collectors.toList());
        log.info("ToolsServiceImpl.getAccountInfo,fsEaList={}",fsEaList);
        fsAccountModelList = queryAccountInfoList(fsEaList);
        log.info("ToolsServiceImpl.getAccountInfo,fsAccountModelList={}",fsAccountModelList);
        List<FsAccountModel> dataList = new ArrayList<>();
        for(FsAccountModel model : fsAccountModelList) {
            if(accountLevelList.contains(model.getAccountLevel())) {
                String accountLevel = model.getAccountLevel();
                if(StringUtils.equalsIgnoreCase(accountLevel,"1")) {
                    accountLevel = "SVIP";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"2")) {
                    accountLevel = "VIP";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"3")) {
                    accountLevel = "重要";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"4")) {
                    accountLevel = "标准";
                } else if(StringUtils.equalsIgnoreCase(accountLevel,"5")) {
                    accountLevel = "小微";
                } else {
                    accountLevel = "其它";
                }
                model.setAccountLevel(accountLevel);
                dataList.add(model);
            }
        }
        String jsonData = JSONObject.toJSONString(dataList);
        log.info("ToolsServiceImpl.getAccountInfo,dataList={}",jsonData);
        redisDataSource.getRedisClient().set(key,jsonData);
        redisDataSource.getRedisClient().expire(key,60 * 60L);
        return new Result<>(dataList);
    }

    @Override
    public List<FsAccountModel> queryAccountInfoList(List<String> fsEaList) {
        String eaFieldApiName = "UDSText6__c";
        String accountLevelFieldApiName = "UDSSel31__c";
        String csmFieldApiName = "field_80ZX1__c";

//        String eaFieldApiName = "field_1kyP6__c";
//        String accountLevelFieldApiName = "field_4Nsb1__c";
//        String csmFieldApiName = "field_44M24__c";

        Filter filter=new Filter();
        filter.setFieldName(eaFieldApiName);//企业帐号字段apiName
        filter.setOperator("IN");
        filter.setFieldValues(fsEaList);

        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(10000);
        searchQuery.setFilters(Lists.newArrayList(filter));

        ControllerListArg controllerListArg=new ControllerListArg();
        controllerListArg.setObjectDescribeApiName("AccountObj");
        controllerListArg.setSearchQuery(searchQuery);

        int ei = eieaConverter.enterpriseAccountToId("fs");
        HeaderObj headerObj = new HeaderObj(ei, CrmConstants.SYSTEM_USER);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> listResult = metadataControllerService.list(headerObj,
                "AccountObj",
                controllerListArg);
        log.info("ToolsServiceImpl.queryAccountInfoList,dataList.size={},listResult={}",listResult.getData().getDataList().size(),JSONObject.toJSONString(listResult));

        List<FsAccountModel> fsAccountModelList = new ArrayList<>();
        if(listResult.isSuccess() && listResult.getData().getDataList().size()>0) {
            for(ObjectData objectData : listResult.getData().getDataList()) {
                log.info("ToolsServiceImpl.queryAccountInfoList,objectData={}",JSONObject.toJSONString(objectData));
                String fsEa = objectData.getString(eaFieldApiName);//企业帐号字段apiName
                String accountName = objectData.getName();
                String accountLevel = objectData.getString(accountLevelFieldApiName);
                String accountOwner = objectData.getOwnerName();
                Map<String,Object> csmMap = (Map<String,Object>)objectData.get(csmFieldApiName+"__r");
                Object csm = null;
                if(csmMap!=null) {
                    csm = csmMap.get("name");
                } else {
                    log.info("ToolsServiceImpl.queryAccountInfoList,csmMap=null");
                }
                log.info("ToolsServiceImpl.queryAccountInfoList,fsEa={},accountLevel={},accountOwner={},csm={}",fsEa,accountLevel,accountOwner,csm);
                FsAccountModel fsAccountModel = new FsAccountModel();
                fsAccountModel.setFsEa(fsEa);
                fsAccountModel.setFsEn(accountName);
                fsAccountModel.setAccountLevel(accountLevel);
                fsAccountModel.setAccountOwner(accountOwner);
                fsAccountModel.setCsm(csm!=null ? csm.toString() : null);
                fsAccountModel.setBindType(1);
                fsAccountModelList.add(fsAccountModel);
            }
        }
        return fsAccountModelList;
    }

    @Override
    public Result<QyweixinAppStatusInfo> getQYWXAppBindInfo(String corpId, String appId) {
        if (StringUtils.isBlank(corpId) || StringUtils.isBlank(appId))
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        QyweixinAppStatusInfo info = new QyweixinAppStatusInfo();
        //先查数据库
        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        if(ObjectUtils.isEmpty(corpBindBo)) {
            return new Result<>();
        }
        info.setCorpId(corpId);
        info.setName(corpBindBo.getCorpName());
        info.setStatus(corpBindBo.getStatus());
        //如果停用了，就不用继续查了
        if(corpBindBo.getStatus() == 1) {
            return new Result<>(info);
        }
        //再查应用信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(corpId, appId);
        //如果查询为空或者状态异常，状态为1
        if(!appInfoResult.isSuccess() || ObjectUtils.isEmpty(appInfoResult.getData())) {
            info.setAppStatus(1);
        } else {
            AppInfo appInfo = appInfoResult.getData();
            info.setAppStatus(0);
            info.setClose(appInfo.getClose());
            info.setCustomizedPublishStatus(appInfo.getCustomized_publish_status());
        }
        return new Result<>(info);
    }

    @Override
    public Result<QyweixinAppAuthorityInfo> getQYWXRepAppAuthorityInfo(String corpId) {
        //查询代开发应用信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(corpId, repAppId);
        if(!appInfoResult.isSuccess() || ObjectUtils.isEmpty(appInfoResult.getData())) {
            return new Result<>();
        }
        AppInfo appInfo = appInfoResult.getData();
        QyweixinAppAuthorityInfo info = new QyweixinAppAuthorityInfo();
        info.setCorpId(corpId);
        info.setName(appInfo.getName());
        if(ObjectUtils.isNotEmpty(appInfo.getAllow_userinfos()) && CollectionUtils.isNotEmpty(appInfo.getAllow_userinfos().getUser())) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfoResult = qyWeixinManager.getUserInfo(repAppId, corpId, appInfo.getAllow_userinfos().getUser().get(0).getUserid());
            if(userInfoResult.isSuccess() && ObjectUtils.isNotEmpty(userInfoResult.getData())) {
                QyweixinUserDetailInfoRsp userInfo = userInfoResult.getData();
                if(StringUtils.isNotEmpty(userInfo.getName()) && !userInfo.getName().equals(userInfo.getUserid())) {
                    info.setUserStatus(0);
                } else {
                    info.setUserStatus(1);
                }
            } else {
                info.setUserStatus(1);
            }
        }
        if(ObjectUtils.isNotEmpty(appInfo.getAllow_partys()) && CollectionUtils.isNotEmpty(appInfo.getAllow_partys().getPartyid())) {
            if(info.getUserStatus() == null) {
                for(String depId : appInfo.getAllow_partys().getPartyid()) {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(repAppId,
                            corpId, depId);
                    if(departmentEmployeeListResult.isSuccess() && departmentEmployeeListResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListResult.getData().getUserlist())) {
                        QyweixinDepartmentEmployeeListRsp departmentEmployeeList = departmentEmployeeListResult.getData();
                        if(StringUtils.isNotEmpty(departmentEmployeeList.getUserlist().get(0).getName()) && !departmentEmployeeList.getUserlist().get(0).getName().equals(departmentEmployeeList.getUserlist().get(0).getUserid())) {
                            info.setUserStatus(0);
                        } else {
                            info.setUserStatus(1);
                        }
                    } else {
                        info.setUserStatus(1);
                    }
                    break;
                }
            }
            com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(repAppId, corpId, appInfo.getAllow_partys().getPartyid().get(0));
            if(departmentInfoResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
                QyweixinDepartmentInfoRsp departmentInfo = departmentInfoResult.getData();
                if(StringUtils.isNotEmpty(departmentInfo.getDepartment().getName()) && !departmentInfo.getDepartment().getName().equals(departmentInfo.getDepartment().getId())) {
                    info.setDepartmentStatus(0);
                } else {
                    info.setDepartmentStatus(1);
                }
            } else {
                info.setDepartmentStatus(1);
            }
        }
        if(info.getUserStatus() == null || info.getDepartmentStatus() == null) {
            if(ObjectUtils.isNotEmpty(appInfo.getAllow_tags()) && CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
                //查询标签
                for(String tagId : appInfo.getAllow_tags().getTagid()) {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = qyWeixinManager.getTagEmployeeList(repAppId, corpId, tagId);
                    if(tagEmployeeListRspResult.isSuccess() && tagEmployeeListRspResult.getData()!=null) {
                        QyweixinTagEmployeeListRsp tagEmployeeListRsp = tagEmployeeListRspResult.getData();
                        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(tagEmployeeListRsp.getUserlist())) {
                            //还需要调用人员详情接口
                            if(info.getUserStatus() == null) {
                                for(QyweixinUserDetailInfoRsp userDetailInfoRsp : tagEmployeeListRsp.getUserlist()) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> updateUserDetailInfoRspResult = qyWeixinManager.getUserInfo(repAppId, corpId, userDetailInfoRsp.getUserid());
                                    if(updateUserDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(updateUserDetailInfoRspResult.getData())) {
                                        QyweixinUserDetailInfoRsp updateUserDetailInfoRsp = updateUserDetailInfoRspResult.getData();
                                        if(StringUtils.isNotEmpty(updateUserDetailInfoRsp.getName()) && !updateUserDetailInfoRsp.getName().equals(updateUserDetailInfoRsp.getUserid())) {
                                            info.setUserStatus(0);
                                        } else {
                                            info.setUserStatus(1);
                                        }
                                    } else {
                                        info.setUserStatus(1);
                                    }
                                    break;
                                }
                            }
                        }
                        if(CollectionUtils.isNotEmpty(tagEmployeeListRsp.getPartylist())) {
                            if(info.getUserStatus() == null) {
                                for(String depId : tagEmployeeListRsp.getPartylist()) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(repAppId,
                                            corpId, depId);
                                    if(departmentEmployeeListResult.isSuccess() && departmentEmployeeListResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListResult.getData().getUserlist())) {
                                        QyweixinDepartmentEmployeeListRsp departmentEmployeeList = departmentEmployeeListResult.getData();
                                        if(StringUtils.isNotEmpty(departmentEmployeeList.getUserlist().get(0).getName()) && !departmentEmployeeList.getUserlist().get(0).getName().equals(departmentEmployeeList.getUserlist().get(0).getUserid())) {
                                            info.setUserStatus(0);
                                        } else {
                                            info.setUserStatus(1);
                                        }
                                    } else {
                                        info.setUserStatus(1);
                                    }
                                    break;
                                }
                            }
                            if(info.getDepartmentStatus() == null) {
                                for(String depId : tagEmployeeListRsp.getPartylist()) {
                                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(repAppId, corpId, depId);
                                    if(departmentInfoResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
                                        QyweixinDepartmentInfoRsp departmentInfo = departmentInfoResult.getData();
                                        if(StringUtils.isNotEmpty(departmentInfo.getDepartment().getName()) && !departmentInfo.getDepartment().getName().equals(departmentInfo.getDepartment().getId())) {
                                            info.setDepartmentStatus(0);
                                        } else {
                                            info.setDepartmentStatus(1);
                                        }
                                    } else {
                                        info.setDepartmentStatus(1);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        return new Result<>(info);
    }

    @Override
    public Result<AccountObjInfo> queryCrmAccountObjStatus(String corpId) {
        String queryFsObject = fsManager.queryFsObject(ConfigCenter.MASTER_EA, "field_fsj82__c", corpId, "AccountObj");
        if(StringUtils.isNotEmpty(queryFsObject)) {
            AccountObjInfo info = new AccountObjInfo();
            JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
            if(read.size()!=0){
                String name = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].name")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].name").toString() : null;
                String tenantId = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].tenant_id")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].tenant_id").toString() : null;
                String channel = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].channel")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].channel").toString() : null;
                String lifeStatus = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].life_status")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].life_status").toString() : null;
                info.setName(name);
                info.setChannel(channel);
                info.setLifeStatus(lifeStatus);
                info.setCorpId(corpId);
                return new Result<>(info);
            }
        }
        return new Result<>();
    }

    @Override
    public Result<SalesOrderObjInfo> queryCrmSalesOrderObjStatus(String orderId) {
        String queryFsObject = fsManager.queryFsObject(ConfigCenter.MASTER_EA, "_id", orderId, "SalesOrderObj");
        SalesOrderObjInfo info = new SalesOrderObjInfo();
        if(StringUtils.isNotEmpty(queryFsObject)) {
            JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
            if(read.size()!=0){
                String salesOrderName = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].name")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].name").toString() : null;
                String tenantId = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].UDSText4__c")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].UDSText4__c").toString() : null;
                String salesOrderId = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0]._id")) ? JSONPath.read(queryFsObject, "$.data.dataList[0]._id").toString() : null;
                String lifeStatus = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].life_status")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].life_status").toString() : null;
                String orderStatus = ObjectUtils.isNotEmpty(JSONPath.read(queryFsObject, "$.data.dataList[0].order_status")) ? JSONPath.read(queryFsObject, "$.data.dataList[0].order_status").toString() : null;
                info.setSalesOrderName(salesOrderName);
                info.setLifeStatus(lifeStatus);
                info.setSalesOrderId(salesOrderId);
                info.setOrderStatus(orderStatus);
            }
            return new Result<>(info);
        }
        return new Result<>();
    }

    @Override
    public Result<Integer> getEnterpriseRunStatus(String ea) {
        GetEnterpriseRunStatusResult runStatus = fsManager.getEnterpriseRunStatus(ea);
        if(ObjectUtils.isEmpty(runStatus) || runStatus.getRunStatus() == null) {
            return new Result<Integer>().addError(ErrorRefer.QUERRY_EMPTY.getCode(), "该企业不存在",null);
        }
        return new Result<>(runStatus.getRunStatus());
    }

    @Override
    public Result<Void> unbind(String corpId, String fsEa, String appId) {
        if(StringUtils.isNotEmpty(corpId) && StringUtils.isNotEmpty(fsEa)) {
            qyweixinAccountBindInnerService.deleteQYWXAccountBind(fsEa, appId, corpId);
        }
        return new Result<>();
    }

    @Override
    public Result<Void> updateAllCorpBind() {
        Thread thread = new Thread(this::updateAllCorpBind2);
        thread.start();
        return new Result<>();
    }

    private void updateAllCorpBind2() {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace("updateAllCorpBind-" + traceId);
        //数据较多，分页去修改会好点
        int pageSize = 1000;
        int pageNum = 1;
        boolean isHasNextPage = Boolean.TRUE;
        do {
            List<QyweixinCorpBindBo> corpBindBos = qyweixinCorpBindDao.queryAllCorpBind(((pageNum++) - 1) * pageSize, pageSize);
            if(CollectionUtils.isEmpty(corpBindBos) || corpBindBos.size() < pageSize) {
                isHasNextPage = Boolean.FALSE;
            }
            if(CollectionUtils.isNotEmpty(corpBindBos)) {
                for(QyweixinCorpBindBo bindBo : corpBindBos) {
                    int count = qyweixinCorpBindDao.update(bindBo);
                    log.info("ToolsServiceImpl.updateAllCorpBind2,count={}, id={}",count, bindBo.getId());
                }
            }
        } while(isHasNextPage);
    }

    @Override
    public Result<Void> updateOpenIds(List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos) {

        updateOpenIds2(qyweixinIdToOpenidBos);
        Thread thread = new Thread(() -> updateOpenIds2(qyweixinIdToOpenidBos));
        thread.start();
        return new Result<>();
    }

    public Result<Void> updateOpenIds2(List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos) {
        //查询出id，直接删除，保留一个
        for(QyweixinIdToOpenidBo bo : qyweixinIdToOpenidBos) {
            if(StringUtils.isAnyEmpty(bo.getCorpId(), bo.getOpenid(), bo.getPlaintextId())) {
                continue;
            }
            List<String> ids = qyweixinIdToOpenidManager.getByOpenidAndPlaintextId(bo.getCorpId(), bo.getOpenid(), bo.getPlaintextId());
            log.info("ToolsServiceImpl.updateOpenIds2,ids={}", ids);
            if(CollectionUtils.isEmpty(ids) || ids.size() <= 1) {
                continue;
            }
            for(int i = 1; i < ids.size(); i++) {
                int count = qyweixinIdToOpenidManager.deleteById(ids.get(i));
                log.info("ToolsServiceImpl.updateOpenIds2,id={}, count={}", i, count);
            }
        }
        return new Result<>();
    }



    @Override
    public Result<Void> updateAllCorpBindToCopy(Integer copyDirection) {
        Thread thread = new Thread(() -> this.updateAllCorpBindToCopy2(copyDirection));
        thread.start();
        return new Result<>();
    }

    @Override
    public Result<Long> deleteUserInfo(String outEa, Boolean isDelete) {
        DeleteResult result = outUserInfoManger.deleteUserInfoByOutEa(outEa);
        log.info("deleteUserInfo,result={}",result);
        return new Result<>(result.getDeletedCount());
    }

    private void updateAllCorpBindToCopy2(Integer copyDirection) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace("updateAllCorpBindToCopy2-" + traceId);
        //数据较多，分页去修改会好点
        int pageSize = 1000;
        int pageNum = 1;
        boolean isHasNextPage = Boolean.TRUE;
        do {
            log.info("ToolsServiceImpl.updateAllCorpBindToCopy2,copyDirection={}",copyDirection);
            if(copyDirection == 0) {
                //原始字段复制到复制字段
                List<String> getPermanentCodes = qyweixinCorpBindDao.getPermanentCode(((pageNum++) - 1) * pageSize, pageSize);
                if(CollectionUtils.isEmpty(getPermanentCodes) || getPermanentCodes.size() < pageSize) {
                    isHasNextPage = Boolean.FALSE;
                }
                getPermanentCodes = getPermanentCodes.stream()
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(getPermanentCodes)) {
                    for(String code : getPermanentCodes) {
                        int count = qyweixinCorpBindDao.updateCopyPermanentCode(SecurityUtil.decryptStr(code), code);
                        log.info("ToolsServiceImpl.updateAllCorpBindToCopy2,count={}",count);
                    }
                }
            } else if(copyDirection == 1) {
                //复制字段复制到原始字段
                List<String> getCopyPermanentCodes = qyweixinCorpBindDao.getCopyPermanentCode(((pageNum++) - 1) * pageSize, pageSize);
                if(CollectionUtils.isEmpty(getCopyPermanentCodes) || getCopyPermanentCodes.size() < pageSize) {
                    isHasNextPage = Boolean.FALSE;
                }
                getCopyPermanentCodes = getCopyPermanentCodes.stream()
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(getCopyPermanentCodes)) {
                    for(String code : getCopyPermanentCodes) {
                        int count = qyweixinCorpBindDao.updatePermanentCode1(code, SecurityUtil.decryptStr(code));
                        log.info("ToolsServiceImpl.updateAllCorpBindToCopy2,count={}",count);
                    }
                }
            } else {
                isHasNextPage = Boolean.FALSE;
            }
        } while(isHasNextPage);
    }

    @Override
    public Result<String> queryFsEnterpriseOpen(String outEa) {
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        //明文的corpId获取到的企业信息
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindService.selectEnterpriseBind("qywx", outEa);
        if(CollectionUtils.isEmpty(result.getData())) {
            return new Result<>("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = result.getData().stream().filter(v -> v.getBindType() == 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return new Result<>("该企业没有自动创建的纷享企业");
        }

        if(enterpriseMappingList.get(0).getStatus() == 100) {
            return new Result<>(String.format("该企业创建中，企业ea=%s，创建时间=%s", enterpriseMappingList.get(0).getFsEa(), enterpriseMappingList.get(0).getGmtCreate()));
        }
        return new Result<>(String.format("该企业已创建成功，企业ea=%s，创建时间=%s", enterpriseMappingList.get(0).getFsEa(), enterpriseMappingList.get(0).getGmtCreate()));
    }

    @Override
    public Result<String> queryFsEmployeeOpen(String outEa, String outUserId) {
        if(StringUtils.isAnyEmpty(outEa, outUserId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.employeeToOutAccount("qywx", outEa, ConfigCenter.crmAppId, Lists.newArrayList(outUserId));
        if(CollectionUtils.isEmpty(result.getData())) {
            return new Result<>("该人员创建失败，请联系集成平台相关研发人员排查");
        }
        if(result.getData().get(0).getStatus() == 100) {
            return new Result<>(String.format("该人员创建中，纷享人员id=%s，创建时间=%s", result.getData().get(0).getFsAccount(), result.getData().get(0).getGmtCreate()));
        }
        return new Result<>(String.format("该人员已创建成功，纷享人员id=%s，创建时间=%s", result.getData().get(0).getFsAccount(), result.getData().get(0).getGmtCreate()));
    }

    @Override
    public Result<String> queryEnterpriseBindType(String fsEa, String outEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
                fsEa, outEa);
        if(ObjectUtils.isEmpty(result.getData())) {
            return new Result<>("该企业没有绑定的账号，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }

        if(result.getData().getBindType() == 0) {
            return new Result<>("应用开通的纷享企业");
        }
        return new Result<>("反绑定的纷享企业");
    }

    @Override
    public Result<String> queryFsEmployeeStatus(String outEa, String outUserId) {
        if(StringUtils.isAnyEmpty(outEa, outUserId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.employeeToOutAccount("qywx", outEa, ConfigCenter.crmAppId, Lists.newArrayList(outUserId));
        if(CollectionUtils.isEmpty(result.getData())) {
            return new Result<>("该人员没有绑定关系，请联系集成平台相关研发人员排查");
        }
        List<String> accountList = Splitter.on(".").splitToList(result.getData().get(0).getFsAccount());
        Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(accountList.get(1), 1000, Lists.newArrayList(Integer.parseInt(accountList.get(2))));
        if(ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
            return new Result<>(String.format("该人员未创建，纷享人员id=%s", result.getData().get(0).getFsAccount()));
        }
        if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
            return new Result<>(String.format("该人员已被停用，纷享人员id=%s", result.getData().get(0).getFsAccount()));
        }
        return new Result<>(String.format("该人员状态正常，纷享人员id=%s", result.getData().get(0).getFsAccount()));
    }

    @Override
    public Result<Void> dealRepeatEmployees() {
        Thread thread = new Thread(() -> this.dealRepeatEmployees2());
        thread.start();
        return new Result<>();
    }

    @Override
    public Result<Void> queryRepeatEmployees() {
        Thread thread = new Thread(() -> this.queryRepeatEmployees2());
        thread.start();
        return new Result<>();
    }

    public Result<Void> dealRepeatEmployees2() {
        TraceUtil.initTrace("dealRepeatEmployees2" + System.currentTimeMillis());
        Integer sum = 0;
        try {
            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinEmployeeAccountModel>> queryRepeatEmployeesResult = qyweixinAccountBindService.queryRepeatEmployees();
            if(!queryRepeatEmployeesResult.isSuccess() || CollectionUtils.isEmpty(queryRepeatEmployeesResult.getData())) {
                log.info("ToolsServiceImpl.dealRepeatEmployees2,queryRepeatEmployeesResult={}",queryRepeatEmployeesResult);
                return new Result<>();
            }
            Map<String, List<QyweixinEmployeeAccountModel>> groupedByOutEaMap =
                    queryRepeatEmployeesResult.getData().stream().collect(Collectors.groupingBy(QyweixinEmployeeAccountModel::getOutEa));

            List<String> dealMsg = new LinkedList<>();
            for (Map.Entry<String, List<QyweixinEmployeeAccountModel>> entry : groupedByOutEaMap.entrySet()) {
                try {
                    String outEa = entry.getKey();
                    log.info("ToolsServiceImpl.dealRepeatEmployees2,outEa={}",outEa);
                    List<QyweixinEmployeeAccountModel> employeeMappings = entry.getValue();
                    String fsEa = employeeMappings.get(0).getFsEa();
                    Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
                    dealMsg.add(String.format("执行，fsEa=%s,outEa=%s\n", fsEa, outEa));
                    //企业过期/停用/删除/不存在，直接处理
                    GetEnterpriseRunStatusArg statusArg = new GetEnterpriseRunStatusArg();
                    statusArg.setEnterpriseAccount(fsEa);
                    GetEnterpriseRunStatusResult enterpriseRunStatus = enterpriseEditionService.getEnterpriseRunStatus(statusArg);
                    if(ObjectUtils.isEmpty(enterpriseRunStatus)
                            || enterpriseRunStatus.getRunStatus() == null
                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_INVALIDATE
                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_STOP
                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_DELETE) {
                        log.info("ToolsServiceImpl.queryRepeatEmployees,del ea,outEa={},fsEa={},GetEnterpriseRunStatusArg={},", outEa, fsEa, statusArg);
                        for (QyweixinEmployeeAccountModel model : employeeMappings) {
                            String outUserId = model.getOutAccount();
                            log.info("ToolsServiceImpl.dealRepeatEmployees2,del ea,outUserId={}", outUserId);
                            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount("qywx", fsEa, ConfigCenter.crmAppId, outUserId);
                            //随机删除一个,这个直接删除库就行
                            for (int i = 1; i < accountResult.getData().size(); i++) {
                                int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ fsEa +".1000", Lists.newArrayList(accountResult.getData().get(i).getFsAccount()), ConfigCenter.crmAppId, outEa);
                                log.info("ToolsServiceImpl.dealRepeatEmployees2,del ea,accountBind={}", accountBind);
                                if (accountBind > 0) {
                                    //企信通知
                                    String msg = String.format("企业过期/停用/删除/不存在，直接处理，已删除，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
                                    dealMsg.add(msg);
                                } else {
                                    //企信通知
                                    String msg = String.format("企业过期/停用/删除/不存在，直接处理，删除关系失败，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
                                    dealMsg.add(msg);
                                }
                            }
                        }
                        continue;
                    }

                    com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectDescribe>> listAllObjectsResult = fsObjServiceProxy.listAllObjects(ei);
                    List<String> apiNames = listAllObjectsResult.getData().stream()
                            .map(ObjectDescribe::getApiName)
                            .filter(apiName -> apiName != null && !apiName.isEmpty())
                            .collect(Collectors.toList());

                    for(QyweixinEmployeeAccountModel model : employeeMappings) {
                        String outUserId = model.getOutAccount();
                        log.info("ToolsServiceImpl.dealRepeatEmployees2,outUserId={}",outUserId);
                        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount("qywx", fsEa, ConfigCenter.crmAppId, outUserId);
                        if(outEa.length() < 32 || model.getOutAccount().length() < 32) {
                            //随机删除一个,这个直接删除库就行
                            for(int i = 1; i < accountResult.getData().size(); i++) {
                                int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("1000", Lists.newArrayList(accountResult.getData().get(i).getFsAccount()), ConfigCenter.crmAppId,model.getOutEa());
                                log.info("ToolsServiceImpl.dealRepeatEmployees2,old,accountBind={}",accountBind);
                                if(accountBind > 0) {
                                    //企信通知
                                    String msg = String.format("已删除，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
                                    dealMsg.add(msg);
                                } else {
                                    //企信通知
                                    String msg = String.format("删除关系失败，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, accountResult.getData().get(i).getFsAccount());
                                    dealMsg.add(msg);
                                }
                            }
                            continue;
                        }

                        List<String> userIds = new LinkedList<>();
                        for(QyweixinAccountEmployeeMapping mapping : accountResult.getData()) {
                            boolean flag = Boolean.FALSE;
                            List<String> empAccounts = Splitter.on(".").splitToList(mapping.getFsAccount());
                            String userId = empAccounts.get(2);
                            log.info("ToolsServiceImpl.dealRepeatEmployees2,userId={}",userId);
                            for(String apiName : apiNames) {
                                if(apiName.equals("PersonnelObj") || apiName.equals("DepartmentObj")) {
                                    continue;
                                }
                                String queryFsObject = fsManager.queryFsObject2(ei, "owner", userId, apiName);
                                if(StringUtils.isNotEmpty(queryFsObject)) {
                                    JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
                                    if(CollectionUtils.isNotEmpty(read)) {
                                        if (read.size() != 0) {
                                            log.info("ToolsServiceImpl.dealRepeatEmployees2,flag={}",flag);
                                            flag = Boolean.TRUE;
                                            userIds.add(userId);
                                            break;
                                        }
                                    }
                                }
                            }
                            if(flag == Boolean.FALSE) {
                                if("1000".equals(userId)) {
                                    //管理员不删
                                    log.info("ToolsServiceImpl.dealRepeatEmployees2,admin,outEa={},outUserId={},userIds={}", outEa, outUserId, userId);
                                    userIds.add(userId);
                                    continue;
                                }

                                //删除
                                int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ fsEa +".1000", Lists.newArrayList(mapping.getFsAccount()), ConfigCenter.crmAppId, outEa);
                                log.info("ToolsServiceImpl.dealRepeatEmployees2,accountBind={}",accountBind);
                                //停用
                                com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
                                        ei + "",
                                        userId,
                                        false,
                                        null,
                                        null);
                                log.info("ToolsServiceImpl.dealRepeatEmployees2,stopResult={}",stopResult);
                                //企信通知
                                String msg = String.format("已删除，outEa=%s,outUserId=%s,userId=%s\n", outEa, outUserId, userId);
                                dealMsg.add(msg);

                                //修改姓名
                                String updateUserId;
                                if(CollectionUtils.isNotEmpty(userIds)) {
                                    updateUserId = userIds.get(0);
                                } else {
                                    updateUserId = userId;
                                }
                                com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeDtoListResult = fsEmployeeServiceProxy.batchGetEmployeeDto(ei, Lists.newArrayList(Integer.valueOf(updateUserId)));
                                log.info("ToolsServiceImpl.dealRepeatEmployees2,employeeDtoListResult={}",employeeDtoListResult);
                                if(CollectionUtils.isNotEmpty(employeeDtoListResult.getData())
                                        && (employeeDtoListResult.getData().get(0).getFullName().startsWith("U-FSQYWX") || employeeDtoListResult.getData().get(0).getName().startsWith("U-FSQYWX"))) {
                                    log.info("ToolsServiceImpl.dealRepeatEmployees2,update name,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
                                    //修改
                                    FsEmpArg fsEmpArg = FsEmpArg.builder()
                                            .ei(ei + "")
                                            .id(updateUserId)
                                            .name("U-FSQYWX" + outUserId)
                                            .fullName("U-FSQYWX" + outUserId)
                                            .build();
                                    com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.update(fsEmpArg,
                                            null,
                                            null);
                                    log.info("ToolsServiceImpl.dealRepeatEmployees2,update name,outEa={},outUserId={},update result={}", outEa, outUserId, result);
                                }
                                break;
                            } else {
                                log.info("ToolsServiceImpl.dealRepeatEmployees2,need del,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
                            }
                        }
                        if(userIds.size() > 1) {
                            log.info("ToolsServiceImpl.dealRepeatEmployees2,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
                            //企信通知
                            SendTextNoticeArg arg = new SendTextNoticeArg();
                            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                            arg.setMsgTitle("--刷库信息--");
                            String msg = String.format("超过两条绑定关系有数据，fsEa=%s,ei=%s,outEa=%s,outUserId=%s,userIds=%s", fsEa, ei, outEa, outUserId, userIds);
                            arg.setMsg(msg);
                            notificationService.sendQYWXNotice(arg);
                        }
                    }
                    sum +=1;
                    if(sum == 30) {
                        //企信通知
                        SendTextNoticeArg arg = new SendTextNoticeArg();
                        arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                        List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                        arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                        arg.setMsgTitle("--刷库信息--");
                        arg.setMsg(dealMsg.toString());
                        notificationService.sendQYWXNotice(arg);
                        return new Result<>();
                    }
                } catch (Exception e) {
                    log.info("ToolsServiceImpl.dealRepeatEmployees2,error,", e);
                }
            }
            //企信通知
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("--刷库信息--");
            arg.setMsg(dealMsg.toString());
            notificationService.sendQYWXNotice(arg);
        } catch (Exception e) {
            log.info("ToolsServiceImpl.dealRepeatEmployees2,error1,", e);
        }
        return new Result<>();
    }

    public Result<Void> queryRepeatEmployees2() {
        TraceUtil.initTrace("queryRepeatEmployees2" + System.currentTimeMillis());
        try {
            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinEmployeeAccountModel>> queryRepeatEmployeesResult = qyweixinAccountBindService.queryRepeatEmployees();
            if(!queryRepeatEmployeesResult.isSuccess() || CollectionUtils.isEmpty(queryRepeatEmployeesResult.getData())) {
                log.info("ToolsServiceImpl.queryRepeatEmployees2,queryRepeatEmployeesResult={}",queryRepeatEmployeesResult);
                return new Result<>();
            }
            Map<String, List<QyweixinEmployeeAccountModel>> groupedByOutEaMap =
                    queryRepeatEmployeesResult.getData().stream().collect(Collectors.groupingBy(QyweixinEmployeeAccountModel::getOutEa));

            for (Map.Entry<String, List<QyweixinEmployeeAccountModel>> entry : groupedByOutEaMap.entrySet()) {
                try {
                    String outEa = entry.getKey();
                    log.info("ToolsServiceImpl.queryRepeatEmployees2,outEa={}",outEa);
                    List<QyweixinEmployeeAccountModel> employeeMappings = entry.getValue();
                    String fsEa = employeeMappings.get(0).getFsEa();
                    Integer ei = eieaConverter.enterpriseAccountToId(fsEa);

                    if(outEa.length() < 32) {
                        log.info("ToolsServiceImpl.queryRepeatEmployees,old ea,outEa={},fsEa={}",outEa, fsEa);
                        continue;
                    }

                    //企业过期/停用/删除/不存在，直接处理
                    GetEnterpriseRunStatusArg statusArg = new GetEnterpriseRunStatusArg();
                    statusArg.setEnterpriseAccount(fsEa);
                    GetEnterpriseRunStatusResult enterpriseRunStatus = enterpriseEditionService.getEnterpriseRunStatus(statusArg);
                    if(ObjectUtils.isEmpty(enterpriseRunStatus)
                            || enterpriseRunStatus.getRunStatus() == null
                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_INVALIDATE
                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_STOP
                            || enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_DELETE) {
                        log.info("ToolsServiceImpl.queryRepeatEmployees,del ea,outEa={},fsEa={},GetEnterpriseRunStatusArg={},",outEa, fsEa, statusArg);
                        continue;
                    }


                    com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectDescribe>> listAllObjectsResult = fsObjServiceProxy.listAllObjects(ei);
                    List<String> apiNames = listAllObjectsResult.getData().stream()
                            .map(ObjectDescribe::getApiName)
                            .filter(apiName -> apiName != null && !apiName.isEmpty())
                            .collect(Collectors.toList());

                    for(QyweixinEmployeeAccountModel model : employeeMappings) {
                        String outUserId = model.getOutAccount();
                        log.info("ToolsServiceImpl.queryRepeatEmployees2,outUserId={}",outUserId);
                        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.outAccountToFsAccount("qywx", fsEa, ConfigCenter.crmAppId, outUserId);
                        if(model.getOutAccount().length() < 32) {
                            log.info("ToolsServiceImpl.queryRepeatEmployees,old outAccount,outEa={},fsEa={},outUserId{},",outEa, fsEa, outUserId);
                            continue;
                        }

                        if(accountResult.getData().size() > 2) {
                            //人工处理
                            log.info("ToolsServiceImpl.queryRepeatEmployees,old,outEa={},fsEa={},outUserId{},fsUserIds={},",outEa, fsEa, outUserId, accountResult.getData());
                            continue;
                        }

                        List<String> userIds = new LinkedList<>();
                        for(QyweixinAccountEmployeeMapping mapping : accountResult.getData()) {
                            boolean flag = Boolean.FALSE;
                            List<String> empAccounts = Splitter.on(".").splitToList(mapping.getFsAccount());
                            String userId = empAccounts.get(2);
                            log.info("ToolsServiceImpl.queryRepeatEmployees2,userId={}",userId);
                            for(String apiName : apiNames) {
                                if(apiName.equals("PersonnelObj") || apiName.equals("DepartmentObj")) {
                                    continue;
                                }
                                String queryFsObject = fsManager.queryFsObject2(ei, "owner", userId, apiName);
                                if(StringUtils.isNotEmpty(queryFsObject)) {
                                    JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
                                    if(CollectionUtils.isNotEmpty(read)) {
                                        if (read.size() != 0) {
                                            log.info("ToolsServiceImpl.queryRepeatEmployees2,apiName={}",apiName);
                                            flag = Boolean.TRUE;
                                            userIds.add(userId);
                                            break;
                                        }
                                    }
                                }
                            }

                            if(flag == Boolean.FALSE) {
                                //无数据，删除第一条
                                log.info("ToolsServiceImpl.queryRepeatEmployees2,need del,outEa={},outUserId={},userId={}", outEa, outUserId, userId);
                                break;
                            } else {
                                log.info("ToolsServiceImpl.queryRepeatEmployees2,need save,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
                            }
                        }
                        if(userIds.size() > 1) {
                            log.info("ToolsServiceImpl.queryRepeatEmployees2,repeat account,outEa={},outUserId={},userIds={}", outEa, outUserId, userIds);
                            //企信通知
                            SendTextNoticeArg arg = new SendTextNoticeArg();
                            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                            arg.setMsgTitle("--刷库信息--");
                            String msg = String.format("超过两条绑定关系有数据，fsEa=%s,ei=%s,outEa=%s,outUserId=%s,userIds=%s", fsEa, ei, outEa, outUserId, userIds);
                            arg.setMsg(msg);
                            notificationService.sendQYWXNotice(arg);
                        }
                    }
                } catch (Exception e) {
                    log.info("ToolsServiceImpl.queryRepeatEmployees2,error,", e);
                }
            }
        } catch (Exception e) {
            log.info("ToolsServiceImpl.queryRepeatEmployees2,error1,", e);
        }
        return new Result<>();
    }

    @Override
    public Result<Void> stopEmployee(Integer ei, String userId, String outEa) {
        //删除
        int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ eieaConverter.enterpriseIdToAccount(ei) +".1000", Lists.newArrayList("E." + eieaConverter.enterpriseIdToAccount(ei) + "." + userId), ConfigCenter.crmAppId, outEa);
        log.info("ToolsServiceImpl.stopEmployee,accountBind={}",accountBind);
        //停用
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
                ei + "",
                userId,
                false,
                null,
                null);
        log.info("ToolsServiceImpl.stopEmployee,stopResult={}",stopResult);
        //企信通知
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
        List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
        arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
        arg.setMsgTitle("--刷库信息--");
        String msg = String.format("已删除，ei=%s,userId=%s", ei, userId);
        arg.setMsg(msg);
        notificationService.sendQYWXNotice(arg);
        return new Result<>();
    }

    @Override
    public Result<Void> dealEmpData(Integer ei, String userId, String newUserId, String outEa) {
        Thread thread = new Thread(() -> this.dealEmpData2(ei, userId, newUserId, outEa));
        thread.start();
        return new Result<>();
    }

    public Result<Void> dealEmpData2(Integer ei, String userId, String newUserId, String outEa) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        List<String> dealMsg = new LinkedList<>();
        //删除老的人员的绑定关系
        //删除
        int accountBind = qyweixinAccountBindInnerService.deleteAccountBind("E."+ fsEa +".1000", Lists.newArrayList("E."+ fsEa + "." + userId), ConfigCenter.crmAppId, outEa);
        log.info("ToolsServiceImpl.dealRepeatEmployees2,accountBind={}",accountBind);
        //停用
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
                ei + "",
                userId,
                false,
                null,
                null);
        log.info("ToolsServiceImpl.dealRepeatEmployees2,stopResult={}",stopResult);
        //企信通知
        String msg = String.format("已删除，fsEa=%s,userId=%s，删除结果：accountBind=%s，stopResult=%s\n", fsEa, userId, accountBind, stopResult);
        dealMsg.add(msg);
        //对象处理
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectDescribe>> listAllObjectsResult = fsObjServiceProxy.listAllObjects(ei);
        List<String> apiNames = listAllObjectsResult.getData().stream()
                .map(ObjectDescribe::getApiName)
                .filter(apiName -> apiName != null && !apiName.isEmpty())
                .collect(Collectors.toList());
        for(String apiName : apiNames) {
            if (apiName.equals("PersonnelObj") || apiName.equals("DepartmentObj")) {
                continue;
            }
            String queryFsObject = fsManager.queryFsObject2(ei, "owner", userId, apiName);
            if(StringUtils.isNotEmpty(queryFsObject)) {
                JSONArray read = (JSONArray) JSONPath.read(queryFsObject, "$.data.dataList");
                if(CollectionUtils.isNotEmpty(read)) {
                    if (read.size() != 0) {
                        log.info("ToolsServiceImpl.queryRepeatEmployees2,apiName={}",apiName);
                        for (int i = 0; i < read.size(); i++) {
                            // 获取每个对象
                            JSONObject item = read.getJSONObject(i);
                            // 获取该对象的 "_id" 字段值
                            String id = item.getString("_id");
                            com.fxiaoke.crmrestapi.common.result.Result<ActionChangeOwnerResult> actionChangeOwnerResultResult = fsManager.changeOwner(ei, apiName, id, Integer.valueOf(newUserId));
                            log.info("ToolsServiceImpl.queryRepeatEmployees2,actionChangeOwnerResultResult={}",actionChangeOwnerResultResult);
                            try {
                                Thread.sleep(200L);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            if(!actionChangeOwnerResultResult.isSuccess()) {
                                dealMsg.add(String.format("更换负责人失败，fsEa=%s,userId=%s,apiName=%s更换负责人结果：actionChangeOwnerResultResult=%s\n", fsEa, userId, apiName, actionChangeOwnerResultResult));
                            }
                        }
                    }
                }
            }
        }
        //企信通知
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
        List<String> receivers = new LinkedList<>(ConfigCenter.DEAL_EMP_DATA_MEMBERS);
        arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
        arg.setMsgTitle("--同步信息--");
        arg.setMsg(dealMsg.toString());
        notificationService.sendQYWXNotice(arg);
        return new Result<>();
    }

    @Override
    public Result<Integer> updateDeptBindStatus(String fsEa, String fsDeptId, Integer status, String appId) {
        com.facishare.open.qywx.accountbind.result.Result<Integer> integerResult = qyweixinAccountBindService.batchUpdateFsDepBindStatus(fsEa, Lists.newArrayList(fsDeptId), status, appId);
        return new Result<>(integerResult.getData());
    }

    @Override
    public Result<Void> pushCorpBindData2Cloud(String domain) {
        //迁移所有数据
        Thread thread = new Thread(() -> pushCorpBindData2Cloud2(domain));
        thread.start();
        return new Result<>();
    }

    private void pushCorpBindData2Cloud2(String domain) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace("pushCorpBindData2Cloud-" + traceId);
        //数据较多，分页
        int pageSize = 1000;
        int pageNum = 1;
        boolean isHasNextPage = Boolean.TRUE;
        Gson gson = new Gson();
        do {
            List<QyweixinCorpBindBo> corpBindBos = qyweixinCorpBindDao.queryAllCorpBind(((pageNum++) - 1) * pageSize, pageSize);
            if(CollectionUtils.isEmpty(corpBindBos) || corpBindBos.size() < pageSize) {
                isHasNextPage = Boolean.FALSE;
            }
            if(CollectionUtils.isNotEmpty(corpBindBos)) {
                String json = gson.toJson(corpBindBos);
                eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "qyweixinCorpBindDao", json, domain);
                try {
                    Thread.sleep(200L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        } while(isHasNextPage);
        log.info("ToolsServiceImpl.pushCorpBindData2Cloud,end");
    }

    @Override
    public Result<Void> pushEnterpriseData2Cloud(String fsEa, String domain) {
        Gson gson = new Gson();

        //查询企业绑定
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResultList(SourceTypeEnum.QYWX.getSourceType(),
                fsEa);
        if(enterpriseMappingResult.isSuccess() && ObjectUtils.isNotEmpty(enterpriseMappingResult.getData())) {
            String json = gson.toJson(enterpriseMappingResult.getData());
            eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "accountEnterpriseBindDao", json, domain);
        }

        //查询人员绑定
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, ConfigCenter.crmAppId, null);

        if(CollectionUtils.isNotEmpty(qyweixinAccountEmployeeMappings)) {
            String json = gson.toJson(qyweixinAccountEmployeeMappings);
            eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "qyweixinAccountEmployeeBindDao", json, domain);
        }
        return new Result<>();
    }

    @Override
    public Result<Integer> updateEnterpriseDomain(String fsEa, String domain) {
        Result<Integer> result = qyweixinAccountBindInnerService.updateEnterpriseDomain(fsEa, domain);
        log.info("ToolsServiceImpl,updateEnterpriseDomain={}",result);
        return result;
    }

    @Override
    public Result<Void> pushCorpInfoData2Cloud(String domain) {
        //迁移所有数据
        Thread thread = new Thread(() -> pushCorpInfoData2Cloud2(domain));
        thread.start();
        return new Result<>();
    }

    private String getDownstreamOutEa(List<ListAppShareInfoRsp.CorpInfo> downstreamCorpList, String corpName) {
        for(ListAppShareInfoRsp.CorpInfo corpInfo : downstreamCorpList) {
            if(StringUtils.equalsIgnoreCase(corpInfo.getCorp_name(),corpName)) {
                return corpInfo.getCorpid();
            }
        }
        return null;
    }

    @Override
    public Result<String> batchCreateQywxConnector(BatchCreateQywxConnectorArg arg) {
        log.info("batchCreateQywxConnector,arg={}",arg);
        int failedCount = 0;
        int exceptionCount = 0;
        String upstreamCorpId = arg.getUpstreamCorpId();
        String upstreamAppId = arg.getUpstreamAppId();
        String upstreamAgentId = arg.getUpstreamAgentId();

        if(StringUtils.isEmpty(upstreamCorpId) || StringUtils.isEmpty(upstreamAppId) || StringUtils.isEmpty(upstreamAgentId)) {
            return new Result<>(TraceUtils.getTraceId()+":参数错误");
        }

        log.info("batchCreateQywxConnector,upstreamCorpId={},upstreamAppId={},upstreamAgentId={}",upstreamCorpId,upstreamAppId,upstreamAgentId);
        com.facishare.open.qywx.accountinner.result.Result<List<ListAppShareInfoRsp.CorpInfo>> listAppShareInfo = qyWeixinManager.listAppShareInfo(upstreamCorpId,
                upstreamAppId,
                upstreamAgentId);
        List<ListAppShareInfoRsp.CorpInfo> downstreamCorpList = listAppShareInfo.getData();
        log.info("batchImportEnterpriseBind,downstreamCorpList={}",downstreamCorpList);

        if(CollectionUtils.isNotEmpty(downstreamCorpList)) {
            for(BatchCreateQywxConnectorArg.EnterpriseMapping enterpriseMapping : arg.getBindList()) {
                String outEa = getDownstreamOutEa(downstreamCorpList,enterpriseMapping.getOutEn());
                String outEn = enterpriseMapping.getOutEn();
                String fsEa = enterpriseMapping.getFsEa();
                if(StringUtils.isEmpty(outEa)) {
                    failedCount ++;
                    log.info("batchImportEnterpriseBind,outEn not match,enterpriseMapping={}",enterpriseMapping);
                    continue;
                }
                try {
                    String dataCenterId = idGenerator.get();
                    String tenantId = eieaConverter.enterpriseAccountToId(fsEa)+"";
                    String prefix = (System.currentTimeMillis()+"").substring(10);
                    String dataCenterName = arg.getDataCenterName();

                    QYWXConnectParam connectParam = new QYWXConnectParam();
                    connectParam.setFsEa(fsEa);
                    connectParam.setOutEa(outEa);
                    connectParam.setOutEn(outEn);
                    connectParam.setOutDepId("1");
                    connectParam.setDataCenterId(dataCenterId);
                    connectParam.setDataCenterName(dataCenterName);
                    connectParam.setBindType(1);


                    CreateConnectorArg createConnectorArg = new CreateConnectorArg();
                    createConnectorArg.setId(dataCenterId);
                    createConnectorArg.setChannel("CONNECTOR_QYWX");
                    createConnectorArg.setTenantId(tenantId);
                    createConnectorArg.setDataCenterName(dataCenterName);
                    createConnectorArg.setConnectParams(JSONObject.toJSONString(connectParam));

                    //创建企微连接器
                    Result<ErpConnectInfoEntity> result = erpdssManager.createConnector(createConnectorArg);
                    log.info("batchImportEnterpriseBind,createConnector,result={}",result);
                    if(!result.isSuccess()) {
                        failedCount ++;
                        log.info("batchImportEnterpriseBind,createConnector failed,enterpriseMapping={}",enterpriseMapping);
                        continue;
                    }

                    Result<Void> fsBindWithQywx = qyweixinGatewayInnerService.fsBindWithQywx(connectParam,false);
                    log.info("batchImportEnterpriseBind,fsBindWithQywx,result={}",fsBindWithQywx);
                    if(!fsBindWithQywx.isSuccess()) {
                        failedCount ++;
                        log.info("batchImportEnterpriseBind,fsBindWithQywx failed,enterpriseMapping={}",enterpriseMapping);
                        continue;
                    }
                } catch (Exception e) {
                    exceptionCount ++;
                    log.info("batchImportEnterpriseBind,exception,enterpriseMapping={}",enterpriseMapping,e);
                    continue;
                }
                log.info("batchImportEnterpriseBind,bind success,enterpriseMapping={}",enterpriseMapping);
            }
        } else {
            return new Result<>(TraceUtils.getTraceId()+":拉取企微下游共享应用企业列表为空");
        }
        log.info("batchImportEnterpriseBind,exception,failedCount={},exceptionCount={}",failedCount,exceptionCount);
        if(failedCount==0 && exceptionCount==0) {
            log.info("batchImportEnterpriseBind,all success");
        }
        Result<String> result = new Result<>();
        result.setData(failedCount+"");
        result.setErrorMsg(TraceUtils.getTraceId());
        return result;
    }

    private void pushCorpInfoData2Cloud2(String domain) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace("pushCorpInfoData2Cloud-" + traceId);
        //数据较多，分页
        int pageSize = 1000;
        int pageNum = 1;
        boolean isHasNextPage = Boolean.TRUE;
        Gson gson = new Gson();
        do {
            List<QyweixinCorpInfoBo> corpBindBos = qyweixinCorpInfoDao.queryAllCorpBind(((pageNum++) - 1) * pageSize, pageSize);
            if(CollectionUtils.isEmpty(corpBindBos) || corpBindBos.size() < pageSize) {
                isHasNextPage = Boolean.FALSE;
            }
            if(CollectionUtils.isNotEmpty(corpBindBos)) {
                String json = gson.toJson(corpBindBos);
                eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, null, "dataPush", null, "QyweixinCorpInfoBo", json, domain);
                try {
                    Thread.sleep(200L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        } while(isHasNextPage);
        log.info("ToolsServiceImpl.pushCorpInfoData2Cloud,end");
    }
}
