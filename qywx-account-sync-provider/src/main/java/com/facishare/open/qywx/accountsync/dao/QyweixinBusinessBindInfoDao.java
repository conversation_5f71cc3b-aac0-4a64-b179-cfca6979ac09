package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinBusinessInfoBindBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QyweixinBusinessBindInfoDao extends ICrudMapper<QyweixinBusinessInfoBindBo> {
    @Update("<script>" +
            "insert into qywexin_business_info_bind(id, fs_ea, out_ea, app_id, business_type, status, create_time, update_time) " +
            "values " +
            "<foreach collection=\"list\" item=\"item\" separator=\",\" >\n" +
            "    (\n" +
            "    #{item.id,jdbcType=INTEGER}, #{item.fsEa,jdbcType=INTEGER},\n" +
            "    #{item.outEa},\n" +
            "    #{item.appId}, #{item.businessType}, #{item.status},\n" +
            "    NOW(), NOW()\n" +
            "    )\n" +
            "</foreach>" +
            "on duplicate key update " +
            "status = values(status), " +
            "update_time = NOW() " +
            "</script>")
    int batchUpsertInfos(@Param("list") List<QyweixinBusinessInfoBindBo> businessInfoBindBos);

    @Select("<script>" + "select * from qywexin_business_info_bind where out_ea=#{outEa}" + "</script>")
    List<QyweixinBusinessInfoBindBo> findOutEa(@Param("outEa") String outEa);


}
