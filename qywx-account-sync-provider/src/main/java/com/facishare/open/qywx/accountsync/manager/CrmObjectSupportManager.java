package com.facishare.open.qywx.accountsync.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.NoCache;
import cn.hutool.cache.impl.TimedCache;
import com.facishare.open.qywx.accountsync.core.enums.CreateObjectEnum;
import com.fxiaoke.crmrestapi.arg.CreateObjectArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.CreateObjectResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.crmrestapi.service.ObjectService;
import com.fxiaoke.i18n.client.I18nClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/16
 */
@Component
@Slf4j
public class CrmObjectSupportManager {
    @Resource
    private ObjectService objectService;
    @Resource
    private ObjectDescribeService objectDescribeService;

    TimedCache<String, String> existObjCache = CacheUtil.newTimedCache(TimeUnit.MINUTES.toMillis(24*60L));

    @PostConstruct
    private void init() {
        //调度缓存清理任务，每60分钟执行一次
        existObjCache.schedulePrune(TimeUnit.MINUTES.toMillis(60L));
    }

    /**
     * 检查对象是否已创建，并在描述存在时将其加入缓存。
     *
     * @param tenantId 租户ID
     * @param objApiName 对象API名称
     * @return 对象是否已创建
     */
    public boolean isObjCreate(Integer tenantId, String objApiName) {
        String cacheKey = tenantId + objApiName;

        if (existObjCache.containsKey(cacheKey)) {
            return true;
        }
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, -10000);
        Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(headerObj, objApiName);
        log.info("isObjCreate,describeResult:{}", describeResult);

        if (describeResult.isSuccess()) {
            // 将描述结果加入缓存，值为1
            existObjCache.put(cacheKey, "1");
        }

        return describeResult.isSuccess();
    }


    /**
     * 创建预定义对象。
     * 存在则不创建，不存在时才会创建。
     *
     * @param tenantId
     * @param objApiName
     * @return true：创建。false：不创建
     */
    public boolean createDefineObject(Integer tenantId, String objApiName) {
        if (isObjCreate(tenantId, objApiName)) {
            log.info("createDefineObject,objApiName={} is exist", objApiName);
            return true;
        }
        try {
            CreateObjectEnum createObjectEnum = CreateObjectEnum.valueOf(objApiName);
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, -10000);
            CreateObjectArg createObjectArg = new CreateObjectArg();
            createObjectArg.setJsonData(createObjectEnum.getDescribe());
            createObjectArg.setIncludeLayout(true);
            createObjectArg.setJsonLayout(createObjectEnum.getDetailLayout());
            createObjectArg.setJsonListLayout(createObjectEnum.getMobileListLayout());
            createObjectArg.setLayoutType("detail");
            createObjectArg.setActive(true);
            InnerResult<CreateObjectResult> createResult = objectService.createObject(headerObj, createObjectArg);
            log.info("createDefineObject,createResult={}", createResult);
            return createResult.isSuccess();
        } catch (Exception e) {
            log.error("create Obj failed", e);
            return false;
        }
    }

}
