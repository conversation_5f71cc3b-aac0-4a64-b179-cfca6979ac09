package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountsync.model.EaMappingModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by l<PERSON><PERSON> on 2018/07/20
 */
@Repository
public interface QyweixinCorpBindDao extends ICrudMapper<QyweixinCorpBindBo> {

    @Update("<script>" + "update qyweixin_corp_bind set " + " permanent_code = #{permanentCode} where" + " corp_id=#{corpId}  and " + " app_id=#{appId}" +"</script>")
    int updatePermanentCode(@Param("corpId") String corpId, @Param("appId") String appId, @Param("permanentCode") String permanentCode);

    @Select("<script>" + "select * from qyweixin_corp_bind where (corp_id=#{corpId} or isv_corp_id=#{corpId}) and app_id=#{appId} " + "</script>")
    QyweixinCorpBindBo queryQyweixinCorpBind(@Param("corpId") String corpId, @Param("appId") String appId);

    @Select("<script>" + "select * from qyweixin_corp_bind where (corp_id=#{corpId} or isv_corp_id=#{corpId}) and agent_id=#{agentId} " + "</script>")
    QyweixinCorpBindBo findByAgentId(@Param("corpId") String corpId, @Param("agentId") String agentId);

    @Update("<script> " +
            "update enterprise_account_bind set open_authorization = #{flag} where fs_ea = #{fs_ea} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> " +
            "</script>")
    int openAuthorization(@Param("fs_ea") String fs_ea,
                          @Param("flag") int flag,
                          @Param("outEa") String outEa);

    @Update("<script> " +
            "update enterprise_account_bind set aut_bind = #{flag} where fs_ea = #{fs_ea} <if test='outEa != null and outEa !=\"\"'> and out_ea=#{outEa} </if>" +
            "</script>")
    int saveAutoBind(@Param("fs_ea") String fs_ea, @Param("outEa") String outEa, @Param("flag") int flag);

    @Select("<script> " +
            "select open_authorization from enterprise_account_bind where fs_ea = #{fs_ea} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> order by gmt_create ASC limit 1" +
            "</script>")
    int getAuthorization(@Param("fs_ea") String fs_ea,
                         @Param("outEa") String outEa,
                         @Param("domain") String domain);

    @Select("<script> " +
            "select aut_bind from enterprise_account_bind where fs_ea = #{fs_ea} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> " +
            "</script>")
    int getAutoBind(@Param("fs_ea") String fs_ea, @Param("outEa") String out_ea, @Param("domain") String domain);

    @Update("<script> " +
            "update enterprise_account_bind set aut_retention = #{flag} where fs_ea = #{fs_ea} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" +
            "</script>")
    int autRetention(@Param("fs_ea") String fs_ea,
                     @Param("flag") int flag,
                     @Param("outEa") String out_ea);

    @Select("<script> " +
            "select fs_ea from enterprise_account_bind where status = '0' and open_authorization = '1' <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> LIMIT #{pageNum},#{pageSize}" +
            "</script>")
    List<String> openAuthorizationByPage(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize, @Param("domain") String domain);

    @Select("<script> " +
            "select fs_ea from enterprise_account_bind where status = '0' and aut_retention = '1' <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> " +
            "</script>")
    List<String> getAutRetentionCorp(@Param("domain") String domain);

    @Select("<script> " +
            "select out_account from employee_account_bind where status = '0' and aut_retention = '1' LIMIT #{pageNum},#{pageSize}" +
            "</script>")
    List<String> autRetentionEmployeeBatch(@Param("fs_ea") String fs_ea, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    @Select("<script> " +
            "select corp_secret from message_generating where ea = #{ea} order by version desc" +
            "</script>")
    List<String> getSecret(@Param("ea") String ea);

    @Select("<script> " +
            "select secret from message_generating where ea = #{ea} <if test=\"outEa != null and outEa != ''\">and qywx_corp_id = #{outEa} </if> order by version desc" +
            "</script>")
    List<String> getConversionSecret(@Param("ea") String ea,
                                     @Param("outEa") String outEa);

    @Select("<script> " +
            "select out_ea from enterprise_account_bind where fs_ea = #{ea} <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> order by gmt_create ASC limit 1" +
            "</script>")
    String getOutEa(@Param("ea") String ea, @Param("domain") String domain);

    @Select("<script> " +
            "select distinct(fs_account) from employee_account_bind where fs_account like CONCAT('%',#{fsEa},'%') and out_account = #{out_account} and out_ea = #{outEa} and status = '0' and app_id = #{appId}" +
            "</script>")
    String getFsCountByOutCount(@Param("fsEa") String ea, @Param("appId") String appId, @Param("out_account") String outAccount, @Param("outEa")String outEa);

    @Select("<script> " +
            "select distinct(fs_account) from employee_account_bind where fs_account like CONCAT('%',#{fsEa},'%') and isv_account = #{isvAccount} and out_ea = #{outEa} and status = '0' and app_id = #{appId}" +
            "</script>")
    String getFsCountByIsvCount(@Param("fsEa") String ea, @Param("appId") String appId, @Param("isvAccount") String outAccount, @Param("outEa")String outEa);


    @Select("<script> " +
            "select fs_tenant_id from message_generating where ea = #{ea} limit 1" +
            "</script>")
    Integer getEiByEa(@Param("ea") String ea);

    @Select("<script> " +
            "select out_ea from enterprise_account_bind where fs_ea = #{ea} <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> limit 1" +
            "</script>")
    String getOutEaByFsEa(@Param("ea") String ea, @Param("domain") String domain);

    @Select("<script> " +
            "select count(*) from message_generating where ea = #{ea}" +
            "</script>")
    Integer getCorpSecret(@Param("ea") String ea);

    @Select("<script> " +
            "select fs_ea from enterprise_account_bind where status = '0' and aut_Bind = '1' <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> " +
            "</script>")
    List<String> getEaByContactBind(@Param("domain") String domain);

    @Select("<script> " +
            "select permanent_code from qyweixin_corp_bind where (corp_id=#{corpId} or isv_corp_id=#{corpId}) and app_id=#{appId} and status = '0' " +
            "</script>")
    String getCorpSecretCode(@Param("corpId") String corpId, @Param("appId") String appId);

    @Select("<script> " +
            "select fs_ea from enterprise_account_bind where out_ea = #{outEa} or isv_out_ea = #{outEa} <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> limit 1" +
            "</script>")
    String getFsEaByOutEa(@Param("outEa") String outEa, @Param("domain") String domain);

    @Select("<script> " +
            "select count(*) from qyweixin_corp_bind where (corp_id = #{corpId} or isv_corp_id = #{corpId}) and app_id = #{repAppId} and status = '0'" +
            "</script>")
    Integer queryEnterpriseReplaceApplication(@Param("corpId") String corpId, @Param("repAppId") String repAppId);

    @Select("<script> " +
            "select corp_id from qyweixin_corp_bind where corp_name = #{corpName} and app_id = #{repAppId} and status = '0'" +
            "</script>")
    String queryEnterpriseIdByName(@Param("corpName") String corpName, @Param("repAppId") String repAppId);

    @Select("<script> " +
            "select corp_name from qyweixin_corp_bind where (corp_id = #{outEa} or isv_corp_id = #{outEa}) limit 1" +
            "</script>")
    String queryEnterpriseNameByOutEa(@Param("outEa") String outEa);

    @Select("<script> " +
            "select fs_ea from enterprise_account_bind eab LEFT JOIN qyweixin_corp_bind qcb " +
            "on eab.out_ea = qcb.corp_id " +
            "where qcb.app_id = #{appId} and qcb.`status` = '0' <if test='domain != null and domain !=\"\"'> and eab.domain=#{domain} </if> " +
            "</script>")
    List<EaMappingModel> autoAccountBind(@Param("appId") String appId, @Param("domain") String domain);

    @Select("<script>" + "select * from qyweixin_corp_bind where " + " isv_corp_id=#{isvCorpId} and " + " app_id=#{appId}  " + "</script>")
    QyweixinCorpBindBo queryQyweixinCorpBindByIsv(@Param("isvCorpId") String isvCorpId, @Param("appId") String appId);

    @Select("<script>" + "select * from qyweixin_corp_bind limit #{pageNum},#{pageSize} " + "</script>")
    List<QyweixinCorpBindBo> queryAllCorpBind(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    @Select("<script>" + "select permanent_code from qyweixin_corp_bind limit #{pageNum},#{pageSize} " + "</script>")
    List<String> getPermanentCode(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    @Select("<script>" + "select permanent_code_copy from qyweixin_corp_bind limit #{pageNum},#{pageSize} " + "</script>")
    List<String> getCopyPermanentCode(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    @Update("<script>" + "update qyweixin_corp_bind set permanent_code = #{permanentCode} where permanent_code_copy = #{copyPermanentCode}" + "</script>")
    Integer updatePermanentCode1(@Param("copyPermanentCode") String copyPermanentCode, @Param("permanentCode") String permanentCode);

    @Update("<script>" + "update qyweixin_corp_bind set permanent_code_copy = #{copyPermanentCode} where permanent_code = #{permanentCode}" + "</script>")
    Integer updateCopyPermanentCode(@Param("copyPermanentCode") String copyPermanentCode, @Param("permanentCode") String permanentCode);

    @Select("<script> " +
            "select * from qyweixin_corp_bind where corp_id = #{outEa} and status = '0'" +
            "</script>")
    List<QyweixinCorpBindBo> getByOutEa(@Param("outEa") String outEa);
}
