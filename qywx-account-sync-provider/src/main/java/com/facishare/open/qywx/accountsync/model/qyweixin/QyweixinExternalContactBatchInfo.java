package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountinner.model.QyweixinExternalContact;
import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinExternalContactBatchInfo implements Serializable {
    private int errcode;
    private String errmsg;
    private QyweixinExternalContact external_contact;
    private List<QyweixinFollowUser> follow_info;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
