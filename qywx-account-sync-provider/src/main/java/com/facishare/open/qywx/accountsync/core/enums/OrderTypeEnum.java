package com.facishare.open.qywx.accountsync.core.enums;

/**
 * <AUTHOR>
 * Created on 2019/2/20
 */
public enum OrderTypeEnum {
    //    订单类型。0-普通订单，1-扩容订单，2-续期，3-版本变更，4-试用
    ORDINARY_ORDER(0, "普通订单"),
    EXPANSION_ORDER(1, "扩容订单"),
    RENEWAL_ORDER(2, "续期"),
    VERSION_CHANGE_ORDER(3, "版本变更"),
    TRYOUT(4, "试用");

    private Integer code;
    private String name;

    OrderTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
