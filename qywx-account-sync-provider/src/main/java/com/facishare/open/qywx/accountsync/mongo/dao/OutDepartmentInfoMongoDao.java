package com.facishare.open.qywx.accountsync.mongo.dao;

import com.facishare.open.qywx.accountsync.mongo.document.OutDepartmentInfoDoc;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.facishare.open.qywx.accountsync.mongo.store.OutDepartmentInfoMongoStore;
import com.google.common.collect.Lists;
import com.mongodb.MongoException;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.currentDate;

/**
 * 企微部门详情操作mongo的dao类封装
 * <AUTHOR>
 * @date 2023/8/23
 */
@Slf4j
@Repository
public class OutDepartmentInfoMongoDao {
    public static final String f_id = "_id";
    public static final String f_outEa = "outEa";
    public static final String f_outDepartmentId = "outDepartmentId";
    public static final String f_outDepartmentInfo = "outDepartmentInfo";
    public static final String f_createTime = "createTime";
    public static final String f_updateTime = "updateTime";

    private final OutDepartmentInfoMongoStore store;

    public OutDepartmentInfoMongoDao(OutDepartmentInfoMongoStore store) {
        this.store = store;
    }

    private List<ObjectId> convertObjectIds(Collection<String> ids) {
        List<ObjectId> objIds = ids.stream()
                .filter(ObjectId::isValid)
                .map(v -> new ObjectId(v)).collect(Collectors.toList());
        return objIds;
    }

    private static ObjectId getObjId(String id) {
        return new ObjectId(id);
    }

    private int update(List<Bson> filters, List<Bson> updates) {
        updates.add(currentDate(f_updateTime));
        Bson filter = and(filters);
        Bson update = combine(updates);
        UpdateResult updateResult = store.getOrCreateCollection().updateOne(filter, update);
        return (int) updateResult.getModifiedCount();
    }


    public List<OutDepartmentInfoDoc> listByTenantId(Integer offset, Integer limit) {
        List<OutDepartmentInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find().limit(limit).skip(offset).into(res);
        return res;
    }

    /**
     * 忽略结果
     *
     * @param outDepartmentInfoDoc
     * @return 1 成功 0 失败
     */

    public int insertIgnore(OutDepartmentInfoDoc outDepartmentInfoDoc) {
        MongoCollection<OutDepartmentInfoDoc> collection = store.getOrCreateCollection();
        try {
            collection.insertOne(outDepartmentInfoDoc);
        } catch (MongoException mongoException) {
            log.error("sync data insert exception", mongoException);
            return 0;
        }
        return 1;
    }


    public OutDepartmentInfoDoc getById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        OutDepartmentInfoDoc outDepartmentInfoDoc = store.getOrCreateCollection().find(eq(new ObjectId(id))).limit(1).first();
        return outDepartmentInfoDoc;
    }


    public OutDepartmentInfoDoc getSimpleById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        OutDepartmentInfoDoc outDepartmentInfoDoc = store.getOrCreateCollection()
                .find(eq(new ObjectId(id)))
                .limit(1).first();
        return outDepartmentInfoDoc;
    }


    public List<OutDepartmentInfoDoc> listByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<OutDepartmentInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }


    public List<OutDepartmentInfoDoc> listSimpleByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<OutDepartmentInfoDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public void save(OutDepartmentInfoDoc outDepartmentInfoDoc) {
        log.info("OutDepartmentInfoMongoDao.save,outDepartmentInfoDoc={}", outDepartmentInfoDoc);
        Bson filter = and(eq(f_id, outDepartmentInfoDoc.getId()));
        store.getOrCreateCollection().replaceOne(filter, outDepartmentInfoDoc, new ReplaceOptions().upsert(true));
    }

    /**
     * 批量插入或替换记录
     *
     * @param docList
     * @return
     */
    public BulkWriteResult batchReplace(Collection<OutDepartmentInfoDoc> docList) {
        log.info("OutDepartmentInfoMongoDao.batchReplace,docList={}", docList);
        List<WriteModel<OutDepartmentInfoDoc>> request = new ArrayList<>();
        for (OutDepartmentInfoDoc doc : docList) {
            Bson filter = and(eq(f_outEa, doc.getOutEa()), eq(f_outDepartmentId, doc.getOutDepartmentId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("OutDepartmentInfoMongoDao.batchReplace,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }


    /**
     * 批量更新，无则不插入。
     *
     * @param docList
     * @return insert count
     */
    public BulkWriteResult batchUpdate(Collection<OutDepartmentInfoDoc> docList) {
        log.info("OutDepartmentInfoMongoDao.batchUpdate,docList={}", docList);
        List<WriteModel<OutDepartmentInfoDoc>> request = new ArrayList<>();
        for (OutDepartmentInfoDoc doc : docList) {
            Bson filter = and(eq(f_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("OutDepartmentInfoMongoDao.batchUpdate,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }

    public List<OutDepartmentInfoDoc> queryDepartmentInfos(String outEa) {
        List<OutDepartmentInfoDoc> res = new ArrayList<>();
        Bson filter = and(eq(f_outEa, outEa));
        store.getOrCreateCollection().find(filter).into(res);
        Map<String, OutDepartmentInfoDoc> map = new HashMap<>();
        for(OutDepartmentInfoDoc doc : res) {
            if(!map.containsKey(doc.getOutDepartmentId())) {
                map.put(doc.getOutDepartmentId(),doc);
            }
        }
        return map.values().stream().collect(Collectors.toList());
    }

    public DeleteResult deleteDepartmentInfo(OutDepartmentInfoDoc doc) {
        Bson filter = and(eq(f_id, doc.getId()));
        return store.getOrCreateCollection().deleteMany(filter);
    }

    public DeleteResult deleteDepartmentInfoByUserId(String outEa, String outDepartmentId) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_outEa, outEa), Filters.eq(f_outDepartmentId, outDepartmentId));
        Bson filter = Filters.and(filters);
        return store.getOrCreateCollection().deleteOne(filter);
    }

    public DeleteResult deleteNotInCollectionDocs(String outEa, Collection<OutDepartmentInfoDoc> docList) {
        Bson filter = deleteNotInCollectionDocsBson(outEa, docList);
        return store.getOrCreateCollection().deleteMany(filter);
    }

    private Bson deleteNotInCollectionDocsBson(String outEa, Collection<OutDepartmentInfoDoc> docList) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_outEa, outEa));
        List<Bson> neOutDepartmentFilters = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(docList)) {
            for(OutDepartmentInfoDoc doc : docList) {
                List<Bson> eventTypeFilters = Lists.newArrayList(Filters.eq(f_outDepartmentId, doc.getOutDepartmentId()));
                Bson filterBson = Filters.and(eventTypeFilters);
                neOutDepartmentFilters.add(filterBson);
            }
            filters.add(Filters.nor(neOutDepartmentFilters));
        }
        return Filters.and(filters);
    }
}
