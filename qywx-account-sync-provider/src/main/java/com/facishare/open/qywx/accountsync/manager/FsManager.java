package com.facishare.open.qywx.accountsync.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oauth.model.enums.AccessTypeEnum;
import com.facishare.open.oauth.model.enums.EaAuthStatusEnum;
import com.facishare.open.oauth.result.GetFsUserIdsResult;
import com.facishare.open.oauth.service.AuthService;
import com.facishare.open.qywx.accountsync.crm.CrmUrlUtils;
import com.facishare.open.qywx.accountsync.limiter.CrmRateLimiter;
import com.facishare.open.qywx.accountsync.model.EnterpriseModel;
import com.facishare.open.qywx.accountsync.model.GetFsUserIdsByRestResult;
import com.facishare.open.qywx.accountsync.model.qyweixin.InnerSearchQueryInfo;
import com.facishare.open.qywx.accountsync.utils.HttpHelper;
import com.facishare.open.qywx.accountsync.utils.HttpResponseMessage;
import com.facishare.open.qywx.accountsync.utils.OkHttp3MonitorUtils;
import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetAllDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.result.GetDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.*;
import com.facishare.organization.api.model.employee.result.*;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.uc.api.exeception.UserCenterException;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.ActionChangeOwnerArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ActionChangeOwnerResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 处理纷享销客通讯录数据
 * Created by liuwei on 2018/09/26
 */
@Slf4j
@Component
public class FsManager {

    @Resource
    private DepartmentService departmentService;
    @Resource
    private EmployeeService employeeService;
    @Resource
    private AuthService authService;
    @ReloadableProperty("crmComponentId")
    private String crmComponentId;
    @ReloadableProperty("open.rest.auth.service.url")
    private String openRestAuthServiceUrl;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private EmployeeProviderService employeeProviderService;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private MetadataActionService metadataActionService;

    /**
     * 获取有CRM权限的用户列表
     * @param fsEa
     * @param employeeId
     * @return
     */
    public GetFsUserIdsResult getFsUserIdsBy(String fsEa, Integer employeeId) {
        GetFsUserIdsResult fsUserIds = authService.getFsUserIdsBy("E."+fsEa+"."+employeeId,
                null,
                crmComponentId,
                AccessTypeEnum.APP,
                EaAuthStatusEnum.NORMAL);
        log.info("trace getFsUserIdsBy fsEa:{} employeeId:{} result:{}", fsEa, employeeId, JSONObject.toJSONString(fsUserIds));
        return fsUserIds;
    }

    /**
     * 获取有CRM权限的用户列表
     * @param fsEa
     * @param employeeId
     * @return
     */
    public GetFsUserIdsByRestResult getFsUserIdsByRestService(String fsEa, Integer employeeId) {
        HttpHelper httpHelper = new HttpHelper();
        String url = openRestAuthServiceUrl + "?fsAdminUserId=" + "E."+fsEa+"."+employeeId+"&fsSig&componentId="+crmComponentId+"&accessTypeCode="+AccessTypeEnum.APP.getType()+"&eaAuthStatusCode="+EaAuthStatusEnum.NORMAL.getStatus();
        GetFsUserIdsByRestResult result = new GetFsUserIdsByRestResult();

        try {
            Map<String,String> header = new HashMap<>();
            header.put("x-fs-ei",eieaConverter.enterpriseAccountToId(fsEa)+"");
            //限速
            if(!CrmRateLimiter.isAllowed(null)) {
                return null;
            }
            String jsonResult = httpHelper.postJsonData(url,new HashMap<>(),header);
            log.info("FsManager.getFsUserIdsByRestService,fsEa={},jsonResult={}",fsEa,jsonResult);
            result = JSONObject.parseObject(jsonResult, GetFsUserIdsByRestResult.class);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            result.setErrCode(-1);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public String getFsUserId(String url, Map<String, String> header, Map<String, String> map) {
        HttpHelper httpHelper = new HttpHelper();
        String jsonResult = null;
        try {
            //限速
            if(!CrmRateLimiter.isAllowed(null)) {
                return null;
            }
            jsonResult = httpHelper.postJsonData(url,map,header);
            return jsonResult;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonResult;
    }

    public List<EmployeeDto> getEmployeeInfos(String fsEa, Integer employeeId, List<Integer> userList) {
        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(fsEa));
        batchGetEmployeeDtoArg.setEmployeeIds(userList);
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(batchGetEmployeeDtoArg.getEnterpriseId()+"");
        BatchGetEmployeeDtoResult employeeDtoResult = employeeService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("trace getEmployeeInfos fsEa:{} employeeId:{} userList:{} result:{}", fsEa, employeeId, JSONObject.toJSONString(userList), JSONObject.toJSONString(employeeDtoResult));
        return employeeDtoResult.getEmployees();
    }

    public Map<Integer, String> getAllCircles(String fsEa, Integer employeeId) {
        GetAllDepartmentArg departmentArg = new GetAllDepartmentArg();
        departmentArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(fsEa));
        departmentArg.setCurrentEmployeeId(-13);
        departmentArg.setRunStatus(com.facishare.organization.api.model.RunStatus .ALL);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(departmentArg.getEnterpriseId()+"");
        Map<Integer, String> result = departmentService.getAllDepartment(departmentArg).getDepartmentList().stream().collect(Collectors.toMap(Department::getDepartmentId, Department::getName));
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("trace getAllCircles fsEa:{} employeeId:{} result:{}", fsEa, employeeId, JSONObject.toJSONString(result));
        return result;
    }

    public List<Integer> getUserIdsByDepartmentId(String ea, int departmentId) {
        BatchGetEmployeesDtoByDepartmentIdArg arg = new BatchGetEmployeesDtoByDepartmentIdArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setDepartmentIds(Lists.newArrayList(departmentId));
        arg.setIncludeLowDepartment(true);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(arg.getEnterpriseId()+"");
        BatchGetEmployeesDtoByDepartmentIdResult result = employeeProviderService.batchGetEmployeesByDepartmentId(arg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        return result.getEmployeeDtos().stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
    }

    public List<EmployeeDto> getAllEmployees(String ea,
                                             com.facishare.organization.api.model.RunStatus status) {
        GetAllEmployeesDtoArg arg = new GetAllEmployeesDtoArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setRunStatus(status);
        GetAllEmployeesDtoResult result = employeeProviderService.getAllEmployees(arg);
        if(ObjectUtils.isEmpty(result) || CollectionUtils.isEmpty(result.getEmployeeDtoList())) {
            return null;
        }
        log.info("FsManager.getAllEmployees.result={}", result);
        return result.getEmployeeDtoList();
    }

    public GetAllEmployeeIdsResult getAllEmployeeIds(String ea) {
        GetAllEmployeeIdsArg arg = new GetAllEmployeeIdsArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ALL);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(arg.getEnterpriseId()+"");
        log.info("FsManager.getAllEmployeeIds.arg={}", arg);
        GetAllEmployeeIdsResult result = employeeProviderService.getAllEmployeeIds(arg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("FsManager.getAllEmployeeIds.ea={},result={}", ea, result);
        if(ObjectUtils.isEmpty(result) || CollectionUtils.isEmpty(result.getEmployeeIds())) {
            return null;
        }
        return result;
    }

    public List<EmployeeDto> batchGetEmployeesByName(String ea, List<String> names) {
        List<EmployeeDto> employees = new LinkedList<>();
        for(String name : names) {
            GetEmployeesDtoByNameResult result = this.getEmployeesByName(ea, name);
            if(ObjectUtils.isNotEmpty(result) && ObjectUtils.isNotEmpty(result.getEmployeeDto())) {
                employees.add(result.getEmployeeDto());
            }
        }
        return employees;
    }

    public GetEmployeesDtoByNameResult getEmployeesByName(String ea, String name) {
        GetEmployeesDtoByNameArg arg = new GetEmployeesDtoByNameArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setName(name);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(arg.getEnterpriseId()+"");
        log.info("FsManager.getEmployeesByName.arg={}", arg);
        GetEmployeesDtoByNameResult result = employeeProviderService.getEmployeesByName(arg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("FsManager.getEmployeesByName.ea={},result={}", ea, result);
        return result;
    }

    public EmployeeDto getEmployeesDtoByEnterpriseAndMobile(String ea, String mobile) {
        GetEmployeesDtoByEnterpriseAndMobileArg arg = new GetEmployeesDtoByEnterpriseAndMobileArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setMobile(mobile);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(arg.getEnterpriseId()+"");
        log.info("FsManager.getEmployeesDtoByEnterpriseAndMobile.arg={}", arg);
        GetEmployeesDtoByEnterpriseAndMobileResult result = employeeProviderService.getEmployeeByEnterpriseAndMobile(arg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("FsManager.getEmployeesDtoByEnterpriseAndMobile.ea={},result={}", ea, result);
        if(ObjectUtils.isEmpty(result) || ObjectUtils.isEmpty(result.getEmployeeDto())) {
            return null;
        }
        return result.getEmployeeDto();
    }

    public Department getDepartment(String ea, Integer departmentId) {
        GetDepartmentArg arg = new GetDepartmentArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setDepartmentId(departmentId);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(arg.getEnterpriseId()+"");
        log.info("FsManager.getEmployeesDtoByEnterpriseAndMobile.arg={}", arg);
        GetDepartmentResult result = departmentService.getDepartment(arg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("FsManager.getEmployeesDtoByEnterpriseAndMobile.ea={},result={}", ea, result);
        if(ObjectUtils.isEmpty(result) || ObjectUtils.isEmpty(result.getDepartment())) {
            return null;
        }
        return result.getDepartment();
    }

    public FindEmployeeDtoByFullNameResult getEmployeesByFullName(String ea, String name) {
        FindEmployeeDtoByFullNameArg arg = new FindEmployeeDtoByFullNameArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        arg.setFullName(name);

        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(arg.getEnterpriseId()+"");
        log.info("FsManager.getEmployeesByName.arg={}", arg);
        FindEmployeeDtoByFullNameResult result = employeeProviderService.findEmployeeDtoByFullName(arg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        log.info("FsManager.getEmployeesByName.ea={},result={}", ea, result);
        return result;
    }

    /**
     * 查询对象数据
     */
    public String queryFsObject(Integer enterpriseId, String filed, String filedValue, String obj){
        if(filedValue == null || filedValue.length() == 0) {
            return null;
        }
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);
        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, String> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", obj);
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return null;
        }
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/" + obj), hearsMap, JSONObject.toJSONString(queryMap));
        return httpResponseMessage.getContent();
//        JSONArray read = (JSONArray) JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList");
//        if(read.size()==0){
//            log.warn("query by phone failed,arg:{},result:{}",innerSearchQueryInfo,httpResponseMessage);
//            Map<String, String> objectMap = Maps.newHashMap();
//            return objectMap;
//        }
//        String userID = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0].user_id").toString();
//        Map<String, String> objectMap = Maps.newHashMap();
//        objectMap.put("user_id",userID);
    }

    public GetEnterpriseRunStatusResult getEnterpriseRunStatus(String ea) {
        if(StringUtils.isEmpty(ea)) return null;

        GetEnterpriseRunStatusArg arg = new GetEnterpriseRunStatusArg();
        arg.setEnterpriseAccount(ea);
        //rest接口支持跨云调用必传参数
        GetEnterpriseRunStatusResult result = null;
        try {
            TraceContext.get().setEa(ea);
            result = enterpriseEditionService.getEnterpriseRunStatus(arg);
            //移除上下文，避免跨云调用混乱
        } catch (Exception e) {
            //找不到企业
            log.info("FsManager.getEnterpriseRunStatus", e);
        }
        TraceContext.remove();
        return result;
    }

    public GetEnterpriseDataResult getEnterpriseData(String ea) {
        if(StringUtils.isEmpty(ea)) return null;

        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(ea);
        GetEnterpriseDataResult result = null;
        try {
            //rest接口支持跨云调用必传参数
            TraceContext.get().setEa(ea);
            result = enterpriseEditionService.getEnterpriseData(arg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        return result;
    }

    /**
     * 查询对象数据
     */
    public String queryFsObject2(Integer enterpriseId, String filed, String filedValue, String obj){
        if(filedValue == null || filedValue.length() == 0) {
            return null;
        }
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);
        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(1);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, String> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", obj);
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        //限速
        try {
            Thread.sleep(100L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/" + obj), hearsMap, JSONObject.toJSONString(queryMap));
        return httpResponseMessage.getContent();
    }

    /**
     * 查询对象数据
     */
    public String queryFsObject3(Integer enterpriseId, String filed, String filedValue, String obj){
        if(filedValue == null || filedValue.length() == 0) {
            return null;
        }
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);
        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, String> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", obj);
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        //限速
        try {
            Thread.sleep(100L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtils.sendOkHttp3Post(CrmUrlUtils.queryList("/" + obj), hearsMap, JSONObject.toJSONString(queryMap));
        return httpResponseMessage.getContent();
    }

    public com.fxiaoke.crmrestapi.common.result.Result<ActionChangeOwnerResult> changeOwner(Integer ei, String apiName, String id, Integer newOwner) {
        HeaderObj headerObj = HeaderObj.newInstance(ei, -10000);
        com.fxiaoke.crmrestapi.common.result.Result<ActionChangeOwnerResult> acoResult = metadataActionService.changeOwner(headerObj, apiName,false,false, new ActionChangeOwnerArg(id, newOwner));
        return acoResult;
    }
}
