package com.facishare.open.qywx.accountsync.handler;

import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.manager.OANewBaseManager;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.accountsync.utils.xml.QYWeiXinDataEventBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业微信第三方应用事件处理器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class ThirdDataEventHandler extends EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;
    @Resource
    private OANewBaseManager oANewBaseManager;

    @ReloadableProperty("crmAppId")
    private String crmAppId;
    @Autowired
    private AutoPullMessageService autoPullMessageService;
    private static List<String> supportedDataEvent = Lists.newArrayList(
            "subscribe","unsubscribe","program_notify"
    );

    @Override
    public void handle(String plainMsg,String appId) {
        super.handle(plainMsg,appId);
        //log.info("ThirdDataEventHandler.handle,eventProto={}",eventProto);

        try {
//            String plainMsg = decryptMsg(eventProto.getSignature(), eventProto.getTimestamp(),
//                    eventProto.getNonce(), eventProto.getData(),eventProto.getAppId());
            log.info("ThirdDataEventHandler.handle,plainMsg={}",plainMsg);

            //QYWeiXinDataEventBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QYWeiXinDataEventBaseXml.class);
            QYWeiXinDataEventBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QYWeiXinDataEventBaseXml.class);
            log.info("ThirdDataEventHandler.handle,baseMsgXml={}",baseMsgXml);

//            if(runInCurrentEnv(baseMsgXml.getToUserName(),plainMsg)==false) return;

            //是否执行在新基座
            if(oANewBaseManager.canRunInNewBase(baseMsgXml.getToUserName(), plainMsg)) {
                log.info("ThirdDataEventHandler.handle,runInNewBase,baseMsgXml.getToUserName()={},plainMsg={}", baseMsgXml.getToUserName(), plainMsg);
                return;
            }
            String event = baseMsgXml.getEvent();
            if(supportedDataEvent.contains(event)) {
                if("program_notify".equals(event)){
                    //处理数据智能返回的通知
                    log.info("ThirdDataEventHandler.program_notify,event={}",event);
                    autoPullMessageService.getMessageByCallBackToken(baseMsgXml.getToUserName(),appId,baseMsgXml.getNotifyId());
                }else{
                    log.info("ThirdDataEventHandler.handle,plainMsg2={}", plainMsg);
                    qyweixinGatewayInnerService.recvDataEvent(plainMsg, ConfigCenter.crmAppId);
                }
            } else {
                log.info("ThirdDataEventHandler.handle,not supported event,event={}",event);
            }

        } catch (Exception e) {
            log.error("ThirdDataEventHandler.handle,DecryptMsg,exception={}",e.getMessage(),e);
        }
    }
}
