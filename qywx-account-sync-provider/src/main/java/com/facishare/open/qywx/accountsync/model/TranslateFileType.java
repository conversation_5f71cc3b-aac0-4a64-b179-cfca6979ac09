package com.facishare.open.qywx.accountsync.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Create by max on 2020/07/17
 **/
public class TranslateFileType {

    public static final int SUCCESS_CODE = 0;

    /**
     * @see https://work.weixin.qq.com/api/doc/90001/90143/91883
     */
    @Data
    public static class UploadResult implements Serializable {

        private static final long serialVersionUID = 7874934532916571497L;

        private Integer errcode;
        private String errmsg;
        private String type;
        @SerializedName("media_id")
        private String mediaId;
        @SerializedName("created_at")
        private String createdAt;
    }

    @Data
    public static class GetJobIdResult implements Serializable {

        private static final long serialVersionUID = 2984421983982443782L;

        private Integer errcode;
        private String errmsg;
        @SerializedName("jobid")
        private String jobId;
    }

    @Data
    public static class GetResult implements Serializable {

        private static final long serialVersionUID = -2251122371359590661L;

        private Integer errcode;
        private String errmsg;
        /**
         * 任务状态，整型，1表示任务开始，2表示任务进行中，3表示任务已完成
         */
        private Integer status;
        private ContactIdTranslate result;

        public String getUrl() {
            if (result == null) return "";
            if (result.contactIdTranslate == null) return "";
            return result.contactIdTranslate.getUrl();
        }

        @Data
        private static class ContactIdTranslate implements Serializable {

            private static final long serialVersionUID = 2320773939421759297L;

            @SerializedName("contact_id_translate")
            private TranslateResult contactIdTranslate;
        }

        @Data
        private static class TranslateResult implements Serializable {

            private static final long serialVersionUID = 8428637814378743397L;

            private String url;
        }
    }
}
