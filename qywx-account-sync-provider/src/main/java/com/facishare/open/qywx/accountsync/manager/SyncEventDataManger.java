package com.facishare.open.qywx.accountsync.manager;

import com.facishare.open.qywx.accountsync.arg.QuerySyncEventDataArg;
import com.facishare.open.qywx.accountsync.mongo.dao.SyncEventDataMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.SyncEventDataDoc;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Data
@Component
public class SyncEventDataManger {
    @Autowired
    private SyncEventDataMongoDao syncEventDataMongoDao;

    public BulkWriteResult batchReplace(List<SyncEventDataDoc> docList) {
        return syncEventDataMongoDao.batchReplace(docList);
    }

    public BulkWriteResult batchUpdate(List<SyncEventDataDoc> docList) {
        return syncEventDataMongoDao.batchUpdate(docList);
    }

    public List<SyncEventDataDoc> pageByQuerySyncEventDataArg(QuerySyncEventDataArg arg) {
        return syncEventDataMongoDao.pageByQuerySyncEventDataArg(arg);
    }

    public DeleteResult deleteTableDataByDelArg(QuerySyncEventDataArg arg) {
        return syncEventDataMongoDao.deleteTableDataByDelArg(arg);
    }
}
