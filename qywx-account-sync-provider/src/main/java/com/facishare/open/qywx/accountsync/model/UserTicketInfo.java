package com.facishare.open.qywx.accountsync.model;

import lombok.Data;

import java.io.Serializable;


/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/23
 */
@Data
public class UserTicketInfo implements Serializable {

    private String userId;
    private long timestamp;
    private String appId;
    private String corpId;

    //OAuth授权获取到的信息 仅在用户同意snsapi_privateinfo授权时返回
    private String mobile; //手机号码
    private String email;  //邮箱
    private String avatar; //头像url。注：如果要获取小图将url最后的”/0”改成”/100”即可
    private String qr_code;//员工个人二维码（扫描可添加为外部联系人）

    public UserTicketInfo() {
    }

    public UserTicketInfo(String userId, long timestamp, String appId, String corpId) {
        this.userId = userId;
        this.timestamp = timestamp;
        this.appId = appId;
        this.corpId = corpId;
    }

    public UserTicketInfo(String userId, long timestamp, String appId, String corpId, String mobile, String email, String avatar, String qr_code) {
        this.userId = userId;
        this.timestamp = timestamp;
        this.appId = appId;
        this.corpId = corpId;
        this.mobile = mobile;
        this.email = email;
        this.avatar = avatar;
        this.qr_code = qr_code;
    }
}
