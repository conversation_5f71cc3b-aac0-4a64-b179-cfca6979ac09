package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/06/21
 */
@Data
public class QyweixinGetProviderAccessTokenRsp implements Serializable {

    private int errcode;
    private String errmsg;
    private String provider_access_token;
    private int expires_in;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
