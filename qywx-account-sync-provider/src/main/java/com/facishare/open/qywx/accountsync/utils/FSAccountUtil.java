package com.facishare.open.qywx.accountsync.utils;

import com.google.common.base.Splitter;

import java.util.List;

public class FSAccountUtil {

    public static String makeUserId(String enterpriseAccount, Integer employeeId) {
        return "E." + enterpriseAccount + "." + employeeId;
    }

    public static Integer getEmpIdFromFSAccount(String fsAccount) {
        return Integer.parseInt(fsAccount.substring(fsAccount.lastIndexOf('.') + 1));
    }

    public static String getFsEaFromFSAccount(String fsAccount) {
        List<String> items = Splitter.on(".").splitToList(fsAccount);
        return items.get(1);
    }
}
