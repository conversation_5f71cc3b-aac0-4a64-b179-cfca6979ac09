package com.facishare.open.qywx.accountsync.core.enums;

/**
 * <AUTHOR>
 * Created on 2019/2/18
 */
public enum ProcessingStatusEnum {
//    订单处理状态，0：无需推送已处理 :1：未推送未处理，2：已推送已处理，3：未推送已处理（企业开通失败并退款）
    NO_NEED_TO_PUSH_PROCESSED(0, "无需推送已处理"),
    NOT_PUSHED_UNPROCESSED(1, "未推送未处理"),
    PUSHED_PROCESSED(2, "已推送已处理"),
    NOT_PUSHED_PROCESSED(3, "未推送已处理");

    private Integer code;
    private String name;

    ProcessingStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
