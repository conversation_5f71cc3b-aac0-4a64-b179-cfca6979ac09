//package com.facishare.open.qywx.accountsync.utils;
//
//import com.facishare.converter.EIEAConverter;
//import com.facishare.organization.adapter.api.model.biz.department.arg.ModifyDepartmentNameArg;
//import com.facishare.organization.adapter.api.model.biz.department.result.ModifyDepartmentNameResult;
//import com.facishare.organization.adapter.api.model.biz.employee.arg.ModifyEmployeeNameArg;
//import com.facishare.organization.adapter.api.model.biz.employee.result.ModifyEmployeeNameResult;
//import com.facishare.organization.adapter.api.service.DepartmentService;
//import com.facishare.organization.adapter.api.service.EmployeeService;
//import com.facishare.organization.api.model.department.DepartmentDto;
//import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
//import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
//import com.facishare.organization.api.model.employee.EmployeeDto;
//import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
//import com.facishare.organization.api.service.DepartmentProviderService;
//import com.facishare.organization.api.service.EmployeeProviderService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * Create by max on 2020/06/28
// **/
//@Component
//@Slf4j
//public class OrganizationUtils {
//
//    private static DepartmentService departmentService;
//    private static DepartmentProviderService departmentProviderService;
//    private static EIEAConverter eIEAConverter;
//    private static EmployeeProviderService employeeProviderService;
//    private static EmployeeService employeeService;
//
//    @Autowired
//    private void setDepartmentService(DepartmentService departmentService) {
//        OrganizationUtils.departmentService = departmentService;
//    }
//
//    @Autowired
//    public void setDepartmentProviderService(DepartmentProviderService departmentProviderService) {
//        OrganizationUtils.departmentProviderService = departmentProviderService;
//    }
//
//    @Autowired
//    public void seteIEAConverter(EIEAConverter eIEAConverter) {
//        OrganizationUtils.eIEAConverter = eIEAConverter;
//    }
//
//    @Autowired
//    public void setEmployeeProviderService(EmployeeProviderService employeeProviderService) {
//        OrganizationUtils.employeeProviderService = employeeProviderService;
//    }
//
//    @Autowired
//    public void setEmployeeService(EmployeeService employeeService) {
//        OrganizationUtils.employeeService = employeeService;
//    }
//
//    /**
//     * 通过员工ID获取员工信息
//     */
//    public static EmployeeDto getEmployeeDto(String ea, int employeeId) {
//        GetEmployeeDtoArg argument = new GetEmployeeDtoArg();
//        argument.setEnterpriseId(getEnterpriseId(ea));
//        argument.setEmployeeId(employeeId);
//        EmployeeDto result = employeeProviderService.getEmployeeDto(argument).getEmployeeDto();
//        log.info("getEmployeeDto success. argument:{}, result:{}", argument, result);
//        return result;
//    }
//
//    /**
//     * 通过部门ID获取部门信息
//     */
//    public static DepartmentDto getDepartmentInfo(String ea, int departmentId) {
//        GetDepartmentDtoArg getDepartmentDtoArg = new GetDepartmentDtoArg();
//        getDepartmentDtoArg.setEnterpriseId(getEnterpriseId(ea));
//        getDepartmentDtoArg.setDepartmentId(departmentId);
//
//        GetDepartmentDtoResult departmentDto = departmentProviderService.getDepartmentDto(getDepartmentDtoArg);
//        log.info("getDepartmentInfo result success. ea:{}, departmentId:{}", ea, departmentId);
//        return departmentDto.getDepartment();
//    }
//
//    /**
//     * 修改员工名称
//     */
//    public static void modifyEmployeeName(String ea, int employeeId, String name) {
//        ModifyEmployeeNameArg argument = new ModifyEmployeeNameArg();
//        argument.setEnterpriseId(getEnterpriseId(ea));
//        argument.setEmployeeId(employeeId);
//        argument.setName(name);
//        argument.setLastUpdateTime(System.currentTimeMillis());
//
//        ModifyEmployeeNameResult result = employeeService.modifyEmployeeName(argument);
//        log.info("modifyEmployeeName success. argument:{}, result:{}", argument, result);
//    }
//
//    /**
//     * 修改部门名称
//     */
//    public static void modifyDeptName(String ea, int departmentId, String name) {
//        ModifyDepartmentNameArg argument = new ModifyDepartmentNameArg();
//        argument.setEnterpriseId(getEnterpriseId(ea));
//        argument.setDepartmentId(departmentId);
//        argument.setName(name);
//        argument.setCurrentEmployeeId(-10000);
//        argument.setLastUpdateTime(System.currentTimeMillis());
//
//        ModifyDepartmentNameResult result = departmentService.modifyDepartmentName(argument);
//        log.info("modifyDeptName success. argument:{}, result:{}", argument, result);
//    }
//
//    private static int getEnterpriseId(String ea) {
//        return eIEAConverter.enterpriseAccountToId(ea);
//    }
//}
