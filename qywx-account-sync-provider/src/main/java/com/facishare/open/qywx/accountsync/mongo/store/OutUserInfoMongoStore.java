package com.facishare.open.qywx.accountsync.mongo.store;

import com.facishare.open.qywx.accountsync.mongo.dao.OutUserInfoMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 企微人员详情 mongo store
 * <AUTHOR>
 * @date 2023/8/23
 */
@Repository
@Slf4j
public class OutUserInfoMongoStore {

    @Getter
    private final DatastoreExt store;

    private final static String CollectionPrefix = "out_user_info";
    private final String dbName;
    private final Set<String> collectionCache = Sets.newConcurrentHashSet();

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(OutUserInfoDoc.class)
                        .automatic(true).build()));
    }


    public OutUserInfoMongoStore(@Qualifier("qywxMongoStore") DatastoreExt store) {
        this.store = store;
        this.dbName = ConfigFactory.getInstance().getConfig("fs-open-qywx-app-config")
                .get("mongo.dbName", "fs-open-qywx");
        store.getMongo().getDatabase(dbName)
                .listCollectionNames().iterator().forEachRemaining(v -> {
                    if (v.startsWith(CollectionPrefix)) {
                        collectionCache.add(v);
                    }
                });
    }

    private String getCollectionName() {
        return CollectionPrefix;
    }

    /**
     * 创建集合，检查索引
     * 这里不移除索引，另外使用批量接口移除
     * 也不根据名称更新索引，同样，更新索引需要走批量接口更新
     */
    public synchronized MongoCollection<OutUserInfoDoc> getOrCreateCollection() {
        //dbName会从配置文件的mongo.servers解析
        String collectionName = getCollectionName();
        MongoCollection<OutUserInfoDoc> collection = getDatabase()
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(collectionName, OutUserInfoDoc.class);
        if (!collectionCache.add(collectionName)) {
            return collection;
        }

        List<IndexModel> indexList = Lists.newArrayList();
        //过期自动清理时间,365天
        Bson expireTimeBson = Indexes.descending(OutUserInfoMongoDao.f_createTime);
        indexList.add(new IndexModel(expireTimeBson, new IndexOptions()
                .name("index_expire_time")
                .expireAfter(365L, TimeUnit.DAYS)
                .background(true)));

        //根据fsEa字段创建索引,索引名称=index_outEa
        Bson outEaBson = Indexes.ascending(OutUserInfoMongoDao.f_outEa);
        indexList.add(new IndexModel(outEaBson, new IndexOptions()
                .name("index_outEa")
                .background(true)));

        Bson outEaOutUserIdBson = Indexes.compoundIndex(
                Indexes.ascending(OutUserInfoMongoDao.f_outEa),
                Indexes.ascending(OutUserInfoMongoDao.f_outUserId));
        indexList.add(new IndexModel(outEaOutUserIdBson, new IndexOptions()
                .name("index_outEa_outUserId")
                .background(true)));

        List<String> created = collection.createIndexes(indexList);
        log.info("OutUserInfoMongoStore.getOrCreateCollection created indexes: {}, wanted: {}, created: {}", created, indexList, created);

        return collection;
    }

    public MongoDatabase getDatabase() {
        return store.getMongo().getDatabase(dbName);
    }
}
