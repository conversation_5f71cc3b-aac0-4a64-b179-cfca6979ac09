package com.facishare.open.qywx.accountsync.model;

import cn.hutool.core.date.DateTime;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/10/30 17:08
 *
 * @desc
 */
@Data
public class CrmWechatInterfaceLicenseObj  implements Serializable {
    private String name;
    private String active_account;
    private Integer account_type;
    private Integer account_duration;
    private Integer account_status;
    private Long active_time;
    private Long expire_time;
    private String bind_wechat_userid;
    private String wechat_employee_id;
    private Long active_expire_time;
    private String to_active_code;
    private String from_active_code;
    private Long pay_time;
    private String order_id;
}
