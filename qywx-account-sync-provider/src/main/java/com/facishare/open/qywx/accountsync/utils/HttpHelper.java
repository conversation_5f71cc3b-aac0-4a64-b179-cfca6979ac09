package com.facishare.open.qywx.accountsync.utils;

import com.alibaba.fastjson.JSONArray;
import com.facishare.open.qywx.accountsync.notification.QyweixinMsgNotification;
import com.facishare.open.qywx.accountsync.upload.FsInputStreamBody;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
public final class HttpHelper {

    private QyweixinMsgNotification qyweixinMsgNotification;

    private static final int DEFAULT_MAX_CONNECTION = 200;
    private static final int DEFAULT_SOCKET_TIMEOUT = 120000;
    private static final int DEFAULT_CONNECTION_TIMEOUT = 20000;

    //protected static Logger log = LoggerFactory.getLogger(HttpHelper.class);
    private HttpClient httpClient;

    public HttpHelper() {
        PoolingHttpClientConnectionManager connectionManager
                = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(DEFAULT_MAX_CONNECTION);
        connectionManager.setDefaultMaxPerRoute(DEFAULT_MAX_CONNECTION);

        SocketConfig.Builder sb = SocketConfig.custom();
        sb.setSoKeepAlive(true);
        sb.setTcpNoDelay(true);
        connectionManager.setDefaultSocketConfig(sb.build());

        HttpClientBuilder hb = HttpClientBuilder.create();
        hb.setConnectionManager(connectionManager);

        RequestConfig.Builder rb = RequestConfig.custom();
        rb.setSocketTimeout(DEFAULT_SOCKET_TIMEOUT);
        rb.setConnectTimeout(DEFAULT_CONNECTION_TIMEOUT);

        hb.setDefaultRequestConfig(rb.build());

        httpClient = hb.build();
    }

    public void setQyweixinMsgNotification(QyweixinMsgNotification qyweixinMsgNotification) {
        this.qyweixinMsgNotification = qyweixinMsgNotification;
    }

    private   String invoke(RequestBuilder builder,
                            String url,
                            Map<String, String> headers,
                            HttpEntity httpEntity,
                            ResponseHandler<String> handler) throws  IOException {

        builder.setUri(url);
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                builder.addHeader(e.getKey(), e.getValue());
            }
        }

        builder.setEntity(httpEntity);

        try (CloseableHttpResponse response = (CloseableHttpResponse) httpClient.execute(builder.build())) {

            int httpCode = response.getStatusLine().getStatusCode();

            if (httpCode != HttpStatus.SC_OK) {
                String format = MessageFormat.format("HTTP Status Error {0} : {1}", httpCode,
                        EntityUtils.toString(response.getEntity()));
                throw new RemoteException(format);
            }

            return handler.handleResponse(response);

        }

    }

    /**
     * 发送 urlencode编码的form数据
     * @param url
     * @param form
     * @param headers
     * @return
     */
    public String postUrlEncodeData(String url, Map<String, String> form, Map<String, String> headers) throws RemoteException {

        List<NameValuePair> formParams = form.entrySet().stream()
                .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        UrlEncodedFormEntity entity;

        try {
            entity = new UrlEncodedFormEntity(formParams, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RemoteException("Encoding Error", e);
        }


        try {
            String rsp =  invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postUrlEncodeData param to url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        } catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData(String url , Map<String, String> form) throws RemoteException {
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            if(ObjectUtils.isNotEmpty(this.qyweixinMsgNotification)) {
                qyweixinMsgNotification.MsgNotification(rsp);
            }
            log.info("trace postJsonData param to url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData(String url , Map<String, String> form, Map<String, String> header) throws RemoteException {
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.putAll(header);

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            if(ObjectUtils.isNotEmpty(this.qyweixinMsgNotification)) {
                qyweixinMsgNotification.MsgNotification(rsp);
            }
            log.info("trace postJsonData param to url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postObjectData(String url , Map<String, Object> form) throws RemoteException {
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            if(ObjectUtils.isNotEmpty(this.qyweixinMsgNotification)) {
                qyweixinMsgNotification.MsgNotification(rsp);
            }
            log.info("trace postJsonData param to url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }



    public String postTextData(String url , String content) throws RemoteException {
        StringEntity entity = new StringEntity(content, "UTF-8");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "text/plain");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            if(ObjectUtils.isNotEmpty(this.qyweixinMsgNotification)) {
                qyweixinMsgNotification.MsgNotification(rsp);
            }
            log.info("trace postTextData param to url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public static void main(String[] args) {
//        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/set_session_info?suite_access_token=x6rGhe1dVnQMNfvfp6B7-VR3pQIQrDeXkkpB9Rv7oJ1qOvsNz7Ko30QTUxR9cXKMpBjpcGYURge5cfVCJNsobB8-tf1U0ZeMuX2Al65KMagmeRm6eLFx-W_7XHhluq8p";
//        Map<String, Object> form = new HashMap<>();
//        form.put("pre_auth_code", "c5bGVKENSBosOHmeY1Wq0n2PrJVmSVyK2QODsadMAnEo-38Az5A10F-EN4PDXBB-");
//        Map<String, Integer> sessionInfo = ImmutableMap.<String, Integer>builder()
//                .put("auth_type", 1).build();
//        form.put("session_info", JSONObject.toJSONString(sessionInfo));
//        try{
//            log.info("trace setSessionInfo set:{}", form);
//            String httpRsp = new HttpHelper().postJsonData2(url, form);
//            log.info("trace setSessionInfo httpRsp:{}", httpRsp);
//        } catch (Exception e) {
//        }


//        String url2 = "https://www.ceshi113.com/wechatproxy/notify/wechat/event?msg_signature=d04f750521ec8904b6ec308c7d3bc8d89bda066b&timestamp=1539325433&nonce=1540151656";
//        String content = "<xml><ToUserName><![CDATA[tjca5cd639fcb05ce0]]></ToUserName><Encrypt><![CDATA[K1OSqs8+qIQxQT2O87pFmACI3dWm3PeNckgVfW0QYWH02WyNfTWc3HlAMMw6BbRpmuOv1wqsDuyEdMcyL4omLKz73KT6EmIicOx0GF2WpuzcofoVmkFxG+OgEuvpmw9OskvY12mm2RL0QPgz0h1r1rd3Rj9b+yia8bVOM18xtaC0iy181CgmJVOfEcHJsQLNxR0qtXmFKvuZu7IMALGGR9Wi2FuvQlHhTY6SWKofhOXNNGBGp9brWR2xLv0m2mHi2EwwomEQ8ZlCUcRHko/EAeZh7YfFRFbls95hK6KFjU14yX5Qi/S97YiKgmMPOOiEv8u9sR2NUIj4qsPMkez8EkePTWVZ1wmvUOiGp2Ick70iU6er/24K/EtjiNMARFnh]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>";
//        try {
//            String postTextResult = new HttpHelper().postTextData(url2, content);
//            log.info("trace postTextResult httpRsp:{}", postTextResult);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }


        try {
            Map<String, String> historyHeadersMap = Maps.newHashMap();
            historyHeadersMap.put("x-fs-ei", "71854");
            historyHeadersMap.put("x-fs-userinfo", "-10000");

            String s = new HttpHelper().doGet("http://10.112.3.5:9120/crm/common/inithistorytodomessage?ei=74908", historyHeadersMap);
            log.info(s);
        } catch (RemoteException e) {
            e.printStackTrace();
        }


        Map<String, String> flowTaskMap = Maps.newHashMap();
        flowTaskMap.put("x-tenant-id", "71854");
        flowTaskMap.put("x-user-id", "-10000");
        flowTaskMap.put("Content-type", "application/json");
        try {
            String notifyFlowTask = new HttpHelper().postJsonData2("http://172.31.101.110:30147/flow/task/wechat/brush", null, flowTaskMap);
            log.info(notifyFlowTask);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public String postJsonData2(String url , Map<String, Object> form) throws RemoteException {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");
        return postJsonData2(url, form, headers);
    }

    public String postJsonData2(String url, Map<String, Object> form, Map<String, String> headers) throws RemoteException {
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            if(ObjectUtils.isNotEmpty(this.qyweixinMsgNotification)) {
                qyweixinMsgNotification.MsgNotification(rsp);
            }
            log.info("trace postJsonData param to url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String httpClientUploadFile(String url, File file) {
        String result = "";
        String boundary ="-------------------------acebdf13572468";
        try {
            String fileName = file.getName();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type","multipart/form-data; boundary="+boundary);

            //HttpEntity builder
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setContentType(ContentType.APPLICATION_OCTET_STREAM);
            builder.setCharset(StandardCharsets.UTF_8);
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            builder.setBoundary(boundary);
            builder.addPart("multipartFile",new FileBody(file));
            //其他参数
            builder.addTextBody("name", "media");
            builder.addTextBody("filename", fileName,  ContentType.create("text/plain", Consts.UTF_8));
            builder.addTextBody("filelength", file.length() + "");
            HttpEntity entity = builder.build();
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                // 将响应内容转换为字符串
                result = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public String httpClientUploadFile(String url, FsInputStreamBody body) {
        String result = "";
        String boundary ="-------------------------acebdf13572468";
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type","multipart/form-data; boundary="+boundary);

            //HttpEntity builder
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setContentType(ContentType.APPLICATION_OCTET_STREAM);
            builder.setCharset(StandardCharsets.UTF_8);
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            builder.setBoundary(boundary);
            builder.addPart("multipartFile",body);
            //其他参数
            builder.addTextBody("name", "media");
            builder.addTextBody("filename", body.getFilename(),  ContentType.create("text/plain", Consts.UTF_8));
            builder.addTextBody("filelength", body.getContentLength() + "");
            HttpEntity entity = builder.build();
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                // 将响应内容转换为字符串
                result = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public String doGet(String url) throws RemoteException {
        return doGet(url, null);
    }

    public String doGet(String url, Map<String, String> hearders) throws RemoteException {
        try {
            String rsp = invoke(RequestBuilder.get(), url, hearders, null, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace doGet param  url:{}, rsp:{}", url, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }
}