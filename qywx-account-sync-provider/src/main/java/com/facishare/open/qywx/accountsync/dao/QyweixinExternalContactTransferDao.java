package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinCustomer;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinExternalContactTransferInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinExternalDeleteInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinQueryTransferInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactTransferBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface QyweixinExternalContactTransferDao extends ICrudMapper<QyweixinExternalContactTransferBo> {

    @Select("<script>"+"SELECT DISTINCT ea, handover_user_id, takeover_user_id, handover_user_status FROM qyweixin_external_contact_transfer " +
            "where sync_status = 0 and transfer_result = 2 and create_time BETWEEN #{startTime} and #{endTime}"+"</script>")
    List<QyweixinExternalContactTransferBo> getHandoverAndTakeover(@Param("startTime") String startTime, @Param("endTime") String endTime);

    @Update("<script>"+"update qyweixin_external_contact_transfer set transfer_result = #{transferResult}, transfer_time = #{transferTime} " +
            "where ea = #{ea} and external_user_id = #{externalUserId} " +
            "and handover_user_id = #{handoverUserId} and takeover_user_id = #{takeoverUserId} " +
            " and sync_status = 0 and transfer_result = 2 "+"</script>")
    int updateTransferStatus(QyweixinExternalContactTransferBo externalContactTransferBo);

    @Select("<script>"+"SELECT * FROM qyweixin_external_contact_transfer " +
            "where ea = #{ea} " +
            "<if test=\"externalApiName != null and externalApiName != ''\"> and external_api_name = #{externalApiName} </if> " +
            "<if test=\"externalNickname != null and externalNickname != ''\"> and external_nickname like concat ('%',#{externalNickname},'%') </if> " +
            "<if test=\"externalUserName != null and externalUserName != ''\"> and external_user_name like concat ('%',#{externalUserName},'%') </if> " +
            "<if test=\"handoverUserName != null and handoverUserName != ''\"> and handover_user_name like concat ('%',#{handoverUserName},'%') </if> " +
            "<if test=\"handoverDeptName != null and handoverDeptName != ''\"> and handover_dept_name like concat ('%',#{handoverDeptName},'%') </if> " +
            "<if test=\"handoverUserStatus != -1\"> and handover_user_status = #{handoverUserStatus} </if> " +
            "<if test=\"takeoverUserName != null and takeoverUserName != ''\"> and takeover_user_name like concat ('%',#{takeoverUserName},'%') </if> " +
            "<if test=\"takeoverDeptName != null and takeoverDeptName != ''\"> and takeover_dept_name like concat ('%',#{takeoverDeptName},'%') </if> " +
            "<if test=\"syncStatus != -1\"> and sync_status = #{syncStatus} </if> " +
            "<if test=\"errorMsg != null and errorMsg != ''\"> and error_msg like concat ('%',#{errorMsg},'%') </if> " +
            "<if test=\"transferResult != -1\"> and transfer_result = #{transferResult} </if> " +
            "<if test=\"transferTime != null and transferTime != ''\"> and transfer_time like concat ('%',#{transferTime},'%') </if> " +
            "<if test=\"startTime != null and startTime != '' and endTime != null and endTime != ''\"> and create_time BETWEEN #{startTime} and #{endTime} </if> " +
            " and status = 0 " +
            " ORDER BY create_time DESC "+"</script>")
    List<QyweixinExternalContactTransferInfo> getExternalContactTransferMapping(QyweixinQueryTransferInfo qyweixinQueryTransferInfo);

    @Update("<script>" + "update qyweixin_external_contact_transfer set status = 1 where id in " +
            " <foreach collection='deleteIds' item='deleteId' open='(' close=')'  separator=','> " +
            " #{deleteId} </foreach> " +
            "</script>")
    int deleteByExternalUserIds(@Param("deleteIds") List<Integer> deleteIds);

}
