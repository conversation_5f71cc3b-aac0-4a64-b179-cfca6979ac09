package com.facishare.open.qywx.accountsync.manager;

import com.facishare.open.qywx.accountsync.mongo.dao.OutDepartmentInfoMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.OutDepartmentInfoDoc;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Data
@Component
public class OutDepartmentInfoManger {
    @Autowired
    private OutDepartmentInfoMongoDao outDepartmentInfoMongoDao;

    public BulkWriteResult batchReplace(List<OutDepartmentInfoDoc> docList) {
        return outDepartmentInfoMongoDao.batchReplace(docList);
    }

    public List<OutDepartmentInfoDoc> queryDepartmentInfos(String outEa) {
        return outDepartmentInfoMongoDao.queryDepartmentInfos(outEa);
    }

    public DeleteResult deleteDepartmentInfo(OutDepartmentInfoDoc doc) {
        return outDepartmentInfoMongoDao.deleteDepartmentInfo(doc);
    }

    public DeleteResult deleteDepartmentInfoByUserId(String outEa, String outDepartmentId) {
        return outDepartmentInfoMongoDao.deleteDepartmentInfoByUserId(outEa, outDepartmentId);
    }

    public DeleteResult deleteNotInCollectionDocs(String outEa, List<OutDepartmentInfoDoc> docList) {
        return outDepartmentInfoMongoDao.deleteNotInCollectionDocs(outEa, docList);
    }
}
