package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/10/25 19:11
 * 企业微信许可接口详情
 * @desc
 */
@Data
public class QywxPermissionOrderAccountDetail extends QywxBaseResult implements Serializable {

    private String next_cursor;
    private Integer has_more;// 0: 没有， 1: 有
    private List<OrderAccountList> account_list;

    @Data
    public static class OrderAccountList implements Serializable {

        //账号码，订单类型为购买账号时，返回该字段
        private String active_code;

        //订单类型为续期账号时，返回该字段。返回加密的userid
        private String userid;

        //1:基础账号，2:互通账号
        private Integer type;

    }

    @Data
    public static class OrderAccountActiveList implements Serializable {

        //账号码，订单类型为购买账号时，返回该字段
        private String active_code;

        //订单类型为续期账号时，返回该字段。返回加密的userid
        private String userid;

        //1:基础账号，2:互通账号
        private Integer type;

    }
}
