package com.facishare.open.qywx.accountsync.manager;

import com.facishare.open.qywx.accountsync.dao.QyweixinIdToOpenidDao;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class QyweixinIdToOpenidManager {
    @Resource
    private QyweixinIdToOpenidDao qyweixinIdToOpenidDao;

    public List<String> getByOpenidAndPlaintextId (String corpId, String openid, String plaintextId){
        return qyweixinIdToOpenidDao.getIdsByOpenidAndPlaintextId(corpId, openid, plaintextId);
    }

    public int deleteById (String id){
        return qyweixinIdToOpenidDao.deleteById(id);
    }
}
