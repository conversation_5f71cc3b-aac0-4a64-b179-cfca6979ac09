package com.facishare.open.qywx.accountsync.core.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/26
 */
public enum QyweixinUpdateEventTypeEnum {

    MODIFY_EMPLOYEE("employee_modify", "员工修改以及修改userId"),
    UPDATE_EMPLOYEE("employee", "员工变更"),   //新增员工，原是新增和修改为一体的，但分离后不修改次字段
    UPDATE_TAG("tag", "标签变更"),
    UPDATE_DEPARTMENT("department", "部门变更"),
    DELETE_DEPARTMENT("department_delete", "部门删除"),
    DELETE_EMPLOYEE("employee_delete", "员工删除"),
    UPDATE_APP_PRIVILEGE("app_privilege", "应用可见范围变更");

    private String eventType;
    private String name;

    QyweixinUpdateEventTypeEnum(String eventType, String name) {
        this.eventType = eventType;
        this.name = name;
    }

    public String getEventType() {
        return eventType;
    }

    public String getName() {
        return name;
    }
}
