package com.facishare.open.qywx.accountsync.aop;

import com.facishare.open.qywx.accountsync.limiter.CrmRateLimiter;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 调用crm接口限速
 */
@Aspect
@Component
@Order(2)
public class CrmRateLimiterAspect {
    private static final Logger RUN_LOG = LoggerFactory.getLogger(CrmRateLimiterAspect.class);

    @Around("execution(* com.facishare.converter.EIEAConverter.*(..)) || " +
            "execution(* com.facishare.uc.api.service.EnterpriseEditionService.*(..)) || " +
            "execution(* com.facishare.stone.sdk.StoneProxyApi.*(..)) || " +
            "execution(* com.facishare.fsi.proxy.service.NFileStorageService.*(..)) || " +
            "execution(* com.facishare.organization.adapter.api.service.DepartmentService.*(..)) || " +
            "execution(* com.facishare.organization.adapter.api.service.EmployeeService.*(..)) || " +
            "execution(* com.facishare.open.oauth.service.AuthService.*(..)) || " +
            "execution(* com.facishare.organization.api.service.EmployeeProviderService.*(..)) || " +
            "execution(* com.fxiaoke.otherrestapi.openmessage.service.SendMessageService.*(..)) || " +
            "execution(* com.fxiaoke.crmrestapi.service.MetadataControllerService.*(..))")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        String fullClassName = point.getTarget().getClass().getName();
        String methodName = point.getSignature().getName();
        Object result = null;
        if (CrmRateLimiter.isAllowed(null)) {
            // 调用接口
            RUN_LOG.info("CrmRimiterAspect.around,{}.{}",
                    fullClassName, methodName);
            result = point.proceed();
        } else {
            // 处理超限情况
            RUN_LOG.info("CrmRimiterAspect.around,{}.{} frequent calls",
                    fullClassName, methodName);
            return null;
        }
        return result;
    }
}

