package com.facishare.open.qywx.accountsync.aop;

import com.facishare.open.qywx.accountsync.utils.ErasePasswordUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.Arrays;

/**
 * Created by fengyh on 2018/3/3.
 */
@Aspect
@Component
@Order(1)
public class LogAspect {
    private static final Logger RUN_LOG = LoggerFactory.getLogger(LogAspect.class);

    @Around("execution(* com.facishare.open.qywx.accountsync.service.impl.*.*(..)) && !execution(* com.facishare.open.qywx.accountsync.manager.FsManager.*(..))")
    public Object around(ProceedingJoinPoint point) throws Throwable {

        Object result = null;
        StopWatch totalWatch = new StopWatch();
        StopWatch methodStopWatch = new StopWatch();
        totalWatch.start();

        String fullClassName = point.getTarget().getClass().getName();
        String methodName = point.getSignature().getName();
//        String args = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(point.getArgs()) ? Arrays.toString(point.getArgs()) : null);
        RUN_LOG.info("{}.{} leave, args: {} , {}, start",
                fullClassName, methodName, point.getArgs(), methodName);

        methodStopWatch.start();
        result = point.proceed();
        methodStopWatch.stop();
        totalWatch.stop();
//        String result2 = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(result) ? String.valueOf(result) : null);
        RUN_LOG.info("{}.{} leave, totalTime: {}ms, methodTime: {}ms, 【return】: {} , args: {} , {}, end",
                fullClassName, methodName, totalWatch.getTotalTimeMillis(),
                methodStopWatch.getTotalTimeMillis(), result, point.getArgs(), methodName);

        return result;
    }
}

