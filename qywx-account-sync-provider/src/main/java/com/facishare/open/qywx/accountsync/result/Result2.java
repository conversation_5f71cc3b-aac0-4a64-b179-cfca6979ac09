package com.facishare.open.qywx.accountsync.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class Result2<T> implements Serializable {
    protected T data;

    protected String errCode;
    protected String errMsg;
    @JsonIgnore
    @JSONField(serialize = false)
    protected String i18nKey;
    @JsonIgnore
    @JSONField(serialize = false)
    protected List<String> i18nExtra;
    protected String traceMsg;
    public boolean isSuccess() {
        return "s106240000".equals(this.errCode);
    }
}
