//package com.facishare.open.qywx.accountsync.model;
//
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * 添加Crm 角色需要的参数和结果
// * <p>
// * Create by max on 2019/07/08
// **/
//public class CrmRoleType {
//
//    @Data
//    public static class AddRole {
//        private String roleCode;
//        private String roleName;
//        private String description;
//        // 1:系统预置（不可删除） 2:自定义（可删除）
//        private String roleType = "2";
//        private AuthContext authContext = new AuthContext();
//
//        public void setTenantId(int enterpriseId) {
//            authContext.setTenantId(String.valueOf(enterpriseId));
//        }
//    }
//
//    @Data
//    public static class AuthContext {
//        private String tenantId;
//        private String appId = "CRM";
//        private int userId = 1000;
//    }
//
//    @Data
//    public static class AddRoleResult implements Serializable {
//
//        private static final long serialVersionUID = -6766835847558096670L;
//
//        private String errCode;
//        private String errMessage;
//        private String result;
//        private Boolean success;
//    }
//}
