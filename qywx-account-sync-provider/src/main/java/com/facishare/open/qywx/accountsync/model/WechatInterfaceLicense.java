package com.facishare.open.qywx.accountsync.model;

import cn.hutool.core.date.DateTime;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/10/26 19:05
 * @desc
 */
@Data
public class WechatInterfaceLicense  implements Serializable {
    private String name;
    private Integer account_type;
    private Integer account_duration;
    private Integer account_status;
    private DateTime active_time;
    private DateTime expire_time;
    private Integer remaining_time;
    private String bind_wechat_userid;
    private String wechat_employee_id;
    private String to_active_code;
    private String from_active_code;
    private DateTime pay_time;
    private String order_id;
}
