package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinContactBindBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/09/26
 */
@Repository
public interface QyweixinContactBindDao extends ICrudMapper<QyweixinContactBindBo> {

    @Insert("<script>" +"INSERT INTO `qyweixin_contact_bind` (corp_id, isv_corp_id, corp_name, fs_ea, status)" +
            " VALUES(#{corpId}, #{isvCorpId}, #{corpName}, #{fsEa}, #{status}) ON DUPLICATE KEY UPDATE " +
            " corp_id=#{corpId}, isv_corp_id = #{isvCorpId}, corp_name=#{corpName}, fs_ea=#{fsEa}, status=#{status};"+ "</script>")
    int saveOrUpdateEntity(QyweixinContactBindBo qyweixinContactBindBo);

    @Update("<script>" +"update qyweixin_contact_bind <set>" +
            " <if test='status != null'> status=#{status},</if>" +
            " <if test='corpName != null'> corp_name=#{corpName}</if>" +
            " </set> <where>" +
            " <if test='corpId != null'> corp_id=#{corpId} </if>" +
            " <if test='fsEa != null'> and fs_Ea=#{fsEa} </if>" +
            "</where></script>")
    int updateEntity(QyweixinContactBindBo qyweixinContactBindBo);

    @Update("<script>" +"update qyweixin_contact_bind <set>" +
            " <if test='status != null'> status=#{status},</if>" +
            " <if test='corpName != null'> corp_name=#{corpName},</if>" +
            " <if test='corpId != null'> corp_id=#{corpId}</if>" +
            " </set> where fs_ea=#{fsEa} </script>")
    int updateCorpInfoByFsEa(QyweixinContactBindBo qyweixinContactBindBo);

    @Update("<script>" +"update qyweixin_contact_bind <set>" +
            " <if test='status != null'> status=#{status},</if>" +
            " <if test='corpName != null'> corp_name=#{corpName},</if>" +
            " <if test='fsEa != null'> fs_ea=#{fsEa}</if>" +
            " </set> where corp_id=#{corpId} </script>")
    int updateFsEaByCorpId(QyweixinContactBindBo qyweixinContactBindBo);

    @Delete("<script>" + "delete from qyweixin_contact_bind where corp_id in "+
            "<foreach item='corpIds' index='index' collection='list' open='(' separator=',' close=')'> " +
            "  #{corpIds} " +
            " </foreach> </script>")
    int deleteByOutEa(List<String> corpIds);

    @Select("<script>" + "select * from enterprise_account_bind where " + "source=#{source} and " + " fs_ea=#{fsEa} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> order by gmt_create ASC limit 1 " + "</script>")
    QyweixinAccountEnterpriseMapping queryMappingFromFsEa(@Param("source") String source,
                                                          @Param("fsEa") String fsEa,
                                                          @Param("outEa") String outEa,
                                                          @Param("domain") String domain);
    @Select("<script>" + "select * from enterprise_account_bind where " + "source=#{source} and " + "out_ea=#{outEa} limit 1" + "</script>")
    QyweixinAccountEnterpriseMapping queryMappingByOutEa(@Param("source") String source, @Param("outEa") String outEa);

    @Select("<script>"+"select user_id userId from qyweixin_corp_info where corp_id=#{outEa}"+"</script>")
    Map<String,String> queryUserIdByCorpId(String outEa);

    @Select("<script>"+"select count(*) from qyweixin_contact_bind where isv_corp_id=#{corpId}"+"</script>")
    Integer getCorpId(@Param("corpId") String corpId);

    @Select("<script>"+"select fs_ea from qyweixin_contact_bind where isv_corp_id=#{corpId} limit 1 "+"</script>")
    String getFsEaByCorpId(@Param("corpId") String corpId);

    @Select("<script>"+"select isv_corp_id from qyweixin_contact_bind where corp_id=#{corpId} limit 1 "+"</script>")
    String getIsvCorpId(@Param("corpId") String corpId);

    @Select("<script>"+"select * from qyweixin_contact_bind where fs_ea=#{fsEa} limit 1 "+"</script>")
    QyweixinContactBindBo getContactBind(@Param("fsEa") String fsEa);
}
