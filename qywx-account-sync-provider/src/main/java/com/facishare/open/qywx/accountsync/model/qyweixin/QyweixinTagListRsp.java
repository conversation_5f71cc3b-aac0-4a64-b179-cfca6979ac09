package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/24.
 */
@Data
public class QyweixinTagListRsp implements Serializable {
    private Integer errcode;
    private String errmsg;
    private List<QyweixinTagRsp> taglist;

    @Data
    public class QyweixinTagRsp implements Serializable{
        private String tagid;
        private String tagname;
    }

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
