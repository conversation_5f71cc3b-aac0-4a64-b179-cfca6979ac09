package com.facishare.open.qywx.accountsync.core.enums;

public enum QyweixinExternalTransferEnum {

    INVALID_USER_ID(40003, "无效的UserID"),
    INVALID_EXTERNAL_USER_ID(40096, "不合法的外部联系人userid"),
    USER_NOT_RESIGNED(40097, "该成员尚未离职"),
    USER_NOT_REAL_NAME(40098, "成员尚未实名认证"),
    EXTERNAL_MAX(40099, "外部联系人的数量已达上限"),
    EXTERNAL_TRANSFER_PROCESS(40100, "此用户的外部联系人已经在转移流程中"),
    EXTERNAL_TRANSFER_FREQUENTLY(40128, "客户转接过于频繁（90个自然日内，在职成员的每位客户仅可被转接2次）"),
    EXTERNAL_TRANSFERRING(40129, "当前客户正在转接中"),
    SAME_USER(40130, "原跟进人与接手人一样，不可继承"),
    USER_NOT_EXTERNAL(40131, "handover_userid 并不是外部联系人的跟进人"),
    USER_NOT_EXIT(60111, "UserID不存在"),
    USER_NOT_BIND(1, "原跟进人或者接替人在纷享无绑定关系"),
    USER_EXTERNAL_NOT_RELATION(84061, "不存在外部联系人的关系");;

    private Integer code;
    private String name;

    QyweixinExternalTransferEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
