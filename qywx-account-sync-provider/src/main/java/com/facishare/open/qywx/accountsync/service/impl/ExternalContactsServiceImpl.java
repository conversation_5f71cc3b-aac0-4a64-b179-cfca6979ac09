package com.facishare.open.qywx.accountsync.service.impl;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountinner.service.ExternalContactsService;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.accountsync.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service("externalContactsService")
public class ExternalContactsServiceImpl implements ExternalContactsService {
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinCorpBindDao qyweixinCorpBindDao;
    @Autowired
    private QYWeixinManager qyWeixinManager;

    @ReloadableProperty("repAppId")
    private String repAppId;

    @Deprecated
    @Override
    public Result<QyweixinExternalContactRsp> getDetail(String fsEa, String externalUserId) {
        return getDetail2(fsEa, externalUserId, null);
    }

    @Override
    public Result<QyweixinExternalContactRsp> getDetail2(String fsEa, String externalUserId, String outEa) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
                fsEa,
                outEa);
        if(enterpriseMappingResult.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(enterpriseMappingResult.getData().getOutEa(), repAppId);
        if(corpBindBo==null) {
            return Result.newInstance(ErrorRefer.REP_APP_NOT_ENABLE);
        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactRsp> externalContactDetailResult = qyWeixinManager.getExternalContactDetail(corpBindBo.getPermanentCode(),
                enterpriseMappingResult.getData().getOutEa(),
                externalUserId, null);
        if(ObjectUtils.isNotEmpty(externalContactDetailResult.getData()) && externalContactDetailResult.isSuccess()) {
            return new Result<>(externalContactDetailResult.getData());
        }
        return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), externalContactDetailResult.getMsg(),null);
    }

    @Deprecated
    @Override
    public Result<List<String>> getExternalContactList(String fsEa, String fsUserId) {
        return getExternalContactListEx(fsEa, fsUserId, null);
    }

    @Override
    public Result<List<String>> getExternalContactListEx(String fsEa, String fsUserId, String outEa) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
                fsEa,
                outEa);
        if(enterpriseMappingResult.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEmployeeMapping> qywxEmployeeMapping = qyweixinAccountBindService.getQywxEmployeeMapping2(fsEa,
                fsUserId,
                enterpriseMappingResult.getData().getOutEa());
        if(qywxEmployeeMapping.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EMP_NOT_BIND);
        }
        com.facishare.open.qywx.accountinner.result.Result<List<String>> externalContactListResult = qyWeixinManager.getExternalContactList(enterpriseMappingResult.getData().getOutEa(),
                qywxEmployeeMapping.getData().getOutAccount());
        if(externalContactListResult.isSuccess() && CollectionUtils.isNotEmpty(externalContactListResult.getData())) {
            return new Result<>(externalContactListResult.getData());
        }
        return new Result<>(ErrorRefer.QYWX_NOT_DATA.getCode(), externalContactListResult.getMsg(),null);
    }

    @Deprecated
    @Override
    public Result<List<String>> getExternalContactList2(String fsEa, String outUserId) {
        return getExternalContactList21(fsEa, outUserId, null);
    }

    @Override
    public Result<List<String>> getExternalContactList21(String fsEa, String outUserId, String outEa) {
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
                fsEa,
                outEa);
        if(enterpriseMappingResult.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        com.facishare.open.qywx.accountinner.result.Result<List<String>> externalContactListResult = qyWeixinManager.getExternalContactList(enterpriseMappingResult.getData().getOutEa(),
                outUserId);
        if(externalContactListResult.isSuccess() && CollectionUtils.isNotEmpty(externalContactListResult.getData())) {
            return new Result<>(externalContactListResult.getData());
        }
        return new Result<>(externalContactListResult.getCode(), externalContactListResult.getMsg(),null);
    }
}
