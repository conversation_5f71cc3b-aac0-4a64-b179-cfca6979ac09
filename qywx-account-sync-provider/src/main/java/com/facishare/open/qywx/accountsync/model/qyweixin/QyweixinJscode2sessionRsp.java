package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 小程序 第三方登录凭证校验
 * Created by <PERSON><PERSON><PERSON> on 2018/08/16
 */
@Data
public class QyweixinJscode2sessionRsp implements Serializable {

    private Integer errcode;
    private String errmsg;

    /**
     * 用户所属企业的corpid
     */
    private String corpid;

    /**
     * 用户在企业内的UserID，对应管理端的帐号，企业内唯一。注意：如果用户所在企业并没有安装此小程序应用，则返回加密的userid
     */
    private String userid;

    /**
     * 会话密钥
     */
    private String session_key;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }

}
