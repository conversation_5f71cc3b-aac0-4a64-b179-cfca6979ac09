//package com.facishare.open.qywx.accountsync.manager;
//
//import com.facishare.open.qywx.accountsync.model.FsEaArg;
//import com.facishare.rest.proxy.annotation.Body;
//import com.facishare.rest.proxy.annotation.POST;
//import com.facishare.rest.proxy.annotation.RestResource;
//
///**
// * 发送需要转换external_userid的企业ea
// * <p>
// *
// **/
//@RestResource(value = "sendFsEa", contentType = "application/json")
//public interface SendFsEaApi {
//
//    /**
//     * 发送需要转换external_userid的企业ea
//     *"sendFsEa":{
//		"address":"**************:25745",
//		"socketTimeOut":20000,
//		"serviceName":"发送需要转换external_userid的企业ea",
//		"connectionTimeOut":5000
//	},
//     * @param arg 参数
//     * @return result
//     */
//    @POST(value = "/fs-crm-metadata/crmrest/enterprise_wechat/upgrade_id", desc = "发送需要转换的企业ea")
//    void sendFsEa(@Body FsEaArg arg);
//}