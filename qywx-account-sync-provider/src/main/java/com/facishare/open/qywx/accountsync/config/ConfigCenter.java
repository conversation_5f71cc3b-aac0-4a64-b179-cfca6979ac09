package com.facishare.open.qywx.accountsync.config;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * Created by l<PERSON><PERSON> on 2018/11/01
 */
@Component
@Data
public class ConfigCenter {

    public static String crmAppId;
    public static String repAppId;

    /**
     * 登录授权应用专用suiteId
     */
    public static String loginAuthSuiteId="wx867ad65506013329";

    public static Set<String> SERVICE_PROVIDER = Sets.newHashSet();
    public static String CRM_REST_OBJ_URL="http://172.31.101.246:17263/API/v1/rest/object";
    public static String ACTIVERECORDOBJ_URL="http://172.31.101.246:17263/API/v1/inner/object/ActiveRecordObj/action/Add";
    public static Set<String> AUTO_BIND_ACCOUNT_EA= Sets.newHashSet();
    public static String INTERFACE_PRODUCT_ID = "63635dfc56093b0001ac1b2c";
    public static String QYWX_GROUP_CHAT = "qywx_group_chat_";
    public static String EXTATTR_AUTO_BIND_EMP_ENTERPRISE = "{\"84883\":{\"QYWX_EXTATTR\":\"工号\",\"FS_API_NAME\":\"field_DouyE__c\"}}";

    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String PREVIEW_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/GetByPath?path=%s";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * erp数据同步appId，这里共用一下
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";

    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();

    /**
     * 处理人员数据
     */
    public static Set<String> DEAL_EMP_DATA_MEMBERS = Sets.newHashSet();

    /**
     * 企业开通接收告警人
     */
    public static Set<String> ENTERPRISE_OPEN_NOTIFICATION_MEMBERS = Sets.newHashSet();

    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "84883";

    /**
     * Xor加密密钥
     */
    public static String XOR_SECRET_KEY= "";


    // 初始化企业相关
    public static final Integer FIRST_EMPLOYEE_ID = 1000;
    //public static final PermissionSpecialOperator OPERATOR = PermissionSpecialOperator.KIS_TRANSFER;

    // 下单相关
    public static final Integer ENTERPRISEID;
    public static final Integer OPERATERID;


    //订单类型：线上下单
    public static final String CRM_ORDER_RECORD_TYPE;
    //线索的ApiName
    public static final String CRM_DETAIL_OBJECT_API_NAME;
    //线索的客户字段
    public static final String CRM_DETAIL_OBJECT_CUSTOMER_ID;

    //FS客户的ApiName
    public static final String CRM_CUSTOMER_OBJECT_API_NAME;
    //FS客户下的名字字段
    public static final String CRM_CUSTOMER_OBJECT_CUSTOMER_NAME;
    //FS客户下的企业帐号字段
    public static final String CRM_CUSTOMER_OBJECT_API_ENTERPRISE_ACCOUNT;
    //FS客户下的企业ID字段
    public static final String CRM_CUSTOMER_OBJECT_CUSTOMER_EID;


    //针对反绑定的企业，自动从企业微信创建人员
    public static Set<String> EMPLOYEE_SYNC_EA= Sets.newHashSet();

    /**
     * 主企业
     */
    public static Integer MASTER_EA = 76517;

    /**
     * 企微在线下单，销售订单保存ea字段
     */
    public static String SALES_ORDER_EA_FIELD_NAME = "UDSText4__c";

    /**
     * 企微在线下单，销售订单的订单类型字段
     */
    public static String SALES_ORDER_TYPE_FIELD_NAME = "UDSSel1__c";

    /**
     * 企微在线下单，销售订单产品的订单id字段
     */
    public static String SALES_ORDER_PRODUCT_ID_FIELD_NAME = "order_id";

    /**
     * 企微在线下单，销售订单产品的服务开始时间字段
     */
    public static String SALES_ORDER_PRODUCT_SERVICE_START_TIME_FIELD_NAME = "UDDate1__c";

    /**
     * 企微在线下单，销售订单产品的服务结束时间字段
     */
    public static String SALES_ORDER_PRODUCT_SERVICE_END_TIME_FIELD_NAME = "UDDate2__c";

    /**
     * 预设对象生命状态通用字段
     */
    public static String OBJ_LIFE_STATUS_FIELD_NAME = "life_status";

    /**
     * 使用标签同步通讯录功能的企业
     */
    public static Set<String> TAG_SYNC_CONTACT_OUT_EA = Sets.newHashSet();

    /**
     * 轮询企微事件分页每页最大数量
     */
    public static Integer EVENT_PAGE_SIZE = 1000;

    /**
     * inner标准接口调用的url前缀
     */
    public static String CRM_OBJECT_INNER_URL_PREFIX;

    //密钥
    public static String BASE64_SECRET = "";

    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    /**
     * 检查相关的license
     */
    public static String CHECK_ENTERPRISE_LICENSE = "{\"wechatScrm\":[\"scrm_wechat_marketing_private_industry\",\"scrm_wechat_marketing_service_private_industry\",\"wechat_scrm_private_30_industry\",\"wechat_scrm_private_100_industry\",\"wechat_scrm_private_200_industry\",\"wechat_scrm_private_500_industry\",\"wechat_scrm_private_500plus_industry\"]}";

    public static String CRM_CUSTOM_ENTERPRISE_ID_FILE = "UDSText2__c";

    public static Set<String> TEM_CLOUD_EA = Sets.newHashSet();

    public static String ERPDSS_OACONNECTOR_WEB_URL = "http://172.17.4.230:57098/inner/oaConnector";//集成平台线上gray web应用的URL

    //企微连接器产品ID
    public static String qywxConnectorProductId = "6603d171043f570001855f10";//线上企微连接器产品ID
    //企微连接器上线日期
    public static String qywxConnectorOnlineDate = "2024-05-17 23:00:00";//线上正式上线时间

    public static String CLOUD_DOMAIN_EA = "{\"84883\":\"https://www.ceshi112.com\",\"74860\":\"https://www.ceshi112.com\"}";

    public static String crm_domain;

    /**
     * 判断该环境是否是主环境
     */
    public static Boolean MAIN_ENV = true;

    public static Boolean IS_TEST = true;
    public static Integer createCrmAccount = 30;

    public static String DOWNLOAD_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/DownloadByPath?Path=%s&name=%s";

    /**
     * 不使用企微缓存的EA列表
     */
    public static String notUseCacheOutEAList = "";
//    //暂时先指定数据智能appid.后续应该放置在配置文件
//    public static String sessionAppId = "";

    public static Set<String> USE_QYWX_PERMISSION_EALISTS = Sets.newHashSet();
    public static String programId;

    static {
        ConfigFactory.getInstance().getConfig("fs-open-qywx-app-config", config -> {
            crmAppId = config.get("crmAppId", crmAppId);
            repAppId = config.get("repAppId", repAppId);
            SERVICE_PROVIDER = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("SERVICE_PROVIDER", "")));
            CRM_REST_OBJ_URL = config.get("CRM_REST_OBJ_URL", CRM_REST_OBJ_URL);
            ACTIVERECORDOBJ_URL = config.get("ACTIVERECORDOBJ_URL", ACTIVERECORDOBJ_URL);
            AUTO_BIND_ACCOUNT_EA = ImmutableSet.copyOf(Splitter.on(";").split(config.get("AUTO_BIND_ACCOUNT_EA", "")));
            INTERFACE_PRODUCT_ID = config.get("INTERFACE_PRODUCT_ID", INTERFACE_PRODUCT_ID);
            EXTATTR_AUTO_BIND_EMP_ENTERPRISE = config.get("EXTATTR_AUTO_BIND_EMP_ENTERPRISE", EXTATTR_AUTO_BIND_EMP_ENTERPRISE);
            PREVIEW_FILE_PATH = config.get("PREVIEW_FILE_PATH", PREVIEW_FILE_PATH);
            NOTICE_MAX_SIZE = config.getInt("NOTICE_MAX_SIZE", NOTICE_MAX_SIZE);
            ERP_SYNC_DATA_APP_ID = config.get("ERP_SYNC_DATA_APP_ID", ERP_SYNC_DATA_APP_ID);
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOTIFICATION_MEMBERS", "")));
            ENTERPRISE_OPEN_NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("ENTERPRISE_OPEN_NOTIFICATION_MEMBERS", "")));
            DEAL_EMP_DATA_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("DEAL_EMP_DATA_MEMBERS", "1021")));

            XOR_SECRET_KEY = config.get("XOR_SECRET_KEY", XOR_SECRET_KEY);
            EMPLOYEE_SYNC_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("EMPLOYEE_SYNC_EA", "")));
            MASTER_EA = config.getInt("MASTER_EA", MASTER_EA);
            SALES_ORDER_EA_FIELD_NAME = config.get("SALES_ORDER_EA_FIELD_NAME", SALES_ORDER_EA_FIELD_NAME);
            SALES_ORDER_TYPE_FIELD_NAME = config.get("SALES_ORDER_TYPE_FIELD_NAME", SALES_ORDER_TYPE_FIELD_NAME);
            SALES_ORDER_PRODUCT_ID_FIELD_NAME = config.get("SALES_ORDER_PRODUCT_ID_FIELD_NAME", SALES_ORDER_PRODUCT_ID_FIELD_NAME);
            SALES_ORDER_PRODUCT_SERVICE_START_TIME_FIELD_NAME = config.get("SALES_ORDER_PRODUCT_SERVICE_START_TIME_FIELD_NAME", SALES_ORDER_PRODUCT_SERVICE_START_TIME_FIELD_NAME);
            SALES_ORDER_PRODUCT_SERVICE_END_TIME_FIELD_NAME = config.get("SALES_ORDER_PRODUCT_SERVICE_END_TIME_FIELD_NAME", SALES_ORDER_PRODUCT_SERVICE_END_TIME_FIELD_NAME);
            OBJ_LIFE_STATUS_FIELD_NAME = config.get("OBJ_LIFE_STATUS_FIELD_NAME", OBJ_LIFE_STATUS_FIELD_NAME);
            TAG_SYNC_CONTACT_OUT_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("TAG_SYNC_CONTACT_OUT_EA", "")));
            USE_QYWX_PERMISSION_EALISTS = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("USE_QYWX_PERMISSION_EALISTS", "")));
            EVENT_PAGE_SIZE = config.getInt("EVENT_PAGE_SIZE", EVENT_PAGE_SIZE);
            CRM_OBJECT_INNER_URL_PREFIX = config.get("CRM_OBJECT_INNER_URL_PREFIX", CRM_OBJECT_INNER_URL_PREFIX);
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            CHECK_ENTERPRISE_LICENSE = config.get("CHECK_ENTERPRISE_LICENSE", CHECK_ENTERPRISE_LICENSE);
            CRM_CUSTOM_ENTERPRISE_ID_FILE = config.get("CRM_CUSTOM_ENTERPRISE_ID_FILE", CRM_CUSTOM_ENTERPRISE_ID_FILE);
            TEM_CLOUD_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("TEM_CLOUD_EA", "")));
            ERPDSS_OACONNECTOR_WEB_URL = config.get("ERPDSS_OACONNECTOR_WEB_URL", ERPDSS_OACONNECTOR_WEB_URL);
            qywxConnectorProductId = config.get("QYWX_CONNECTOR_PRODUCT_ID", qywxConnectorProductId);
            qywxConnectorOnlineDate = config.get("QYWX_CONNECTOR_ONLINE_DATE", qywxConnectorOnlineDate);
            CLOUD_DOMAIN_EA = config.get("CLOUD_DOMAIN_EA", CLOUD_DOMAIN_EA);
            crm_domain = config.get("domainPrefix", crm_domain);
            MAIN_ENV = config.getBool("MAIN_ENV", MAIN_ENV);
            IS_TEST = config.getBool("IS_TEST", IS_TEST);
            createCrmAccount = config.getInt("createCrmAccount", 30);
            DOWNLOAD_FILE_PATH = config.get("DOWNLOAD_FILE_PATH", DOWNLOAD_FILE_PATH);
            notUseCacheOutEAList = config.get("notUseCacheOutEAList", notUseCacheOutEAList);
        });



        IChangeableConfig config = ConfigFactory.getConfig("fs-webhook-config");

        ENTERPRISEID = config.getInt("ENTERPRISEID", 1);
        OPERATERID = config.getInt("OPERATERID", -10000);

        CRM_ORDER_RECORD_TYPE = config.get("CRM_ORDER_RECORD_TYPE", "record_2nocy__c");
        CRM_DETAIL_OBJECT_API_NAME = config.get("CRM_DETAIL_OBJECT_API_NAME", "object_94b5Q__c");
        CRM_DETAIL_OBJECT_CUSTOMER_ID = config.get("CRM_DETAIL_OBJECT_CUSTOMER_ID", "field_zCYt5__c");
        CRM_CUSTOMER_OBJECT_API_NAME = config.get("CRM_CUSTOMER_OBJECT_API_NAME", "AccountObj");
        CRM_CUSTOMER_OBJECT_CUSTOMER_NAME = config.get("CRM_CUSTOMER_OBJECT_CUSTOMER_NAME", "name");
        CRM_CUSTOMER_OBJECT_API_ENTERPRISE_ACCOUNT = config.get("CRM_CUSTOMER_OBJECT_API_ENTERPRISE_ACCOUNT", "UDSText6__c");
//        sessionAppId = config.get("sessionAppId", "sessionAppId");
        CRM_CUSTOMER_OBJECT_CUSTOMER_EID = config.get("CRM_CUSTOMER_OBJECT_CUSTOMER_EID", "field_fsj82__c");
        ConfigFactory.getInstance().getConfig("fs-open-qywx-message-save", configSave -> {
            programId = configSave.get("programId");

        });

    }
}
