package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2018/4/24.
 */
@Data
public class QyweixinDepartmentListRsp implements Serializable {
    private Integer errcode;
    private String errmsg;
    List<QyweixinDepartmentRsp> department;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
