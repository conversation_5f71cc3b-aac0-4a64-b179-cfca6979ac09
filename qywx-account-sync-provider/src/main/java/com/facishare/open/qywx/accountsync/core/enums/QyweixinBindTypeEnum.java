package com.facishare.open.qywx.accountsync.core.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/09/25
 */
public enum QyweixinBindTypeEnum {

    NEW_CORP_BIND(0, "新建企业并绑定"),
    OLD_CORP_BIND(1, "绑定已有纷享企业");

    private Integer code;
    private String name;

    QyweixinBindTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
