package com.facishare.open.qywx.accountsync.mongo.dao;

import com.facishare.open.qywx.accountsync.arg.QuerySyncEventDataArg;
import com.facishare.open.qywx.accountsync.mongo.document.SyncEventDataDoc;
import com.facishare.open.qywx.accountsync.mongo.store.SyncEventDataMongoStore;
import com.google.common.collect.Lists;
import com.mongodb.MongoException;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.currentDate;

/**
 * 企微事件推送操作mongo的dao类封装
 * <AUTHOR>
 * @date 2023/8/23
 */
@Slf4j
@Repository
public class SyncEventDataMongoDao {
    public static final String f_id = "_id";
    public static final String f_outEa = "outEa";
    public static final String f_appId = "appId";
    public static final String f_eventType = "eventType";
    public static final String f_event = "event";
    public static final String f_status = "status";
    public static final String f_createTime = "createTime";
    public static final String f_updateTime = "updateTime";

    private final SyncEventDataMongoStore store;

    public SyncEventDataMongoDao(SyncEventDataMongoStore store) {
        this.store = store;
    }

    private List<ObjectId> convertObjectIds(Collection<String> ids) {
        List<ObjectId> objIds = ids.stream()
                .filter(ObjectId::isValid)
                .map(v -> new ObjectId(v)).collect(Collectors.toList());
        return objIds;
    }

    private static ObjectId getObjId(String id) {
        return new ObjectId(id);
    }

    private int update(List<Bson> filters, List<Bson> updates) {
        updates.add(currentDate(f_updateTime));
        Bson filter = and(filters);
        Bson update = combine(updates);
        UpdateResult updateResult = store.getOrCreateCollection().updateOne(filter, update);
        return (int) updateResult.getModifiedCount();
    }


    public List<SyncEventDataDoc> listByTenantId(Integer offset, Integer limit) {
        List<SyncEventDataDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find().limit(limit).skip(offset).into(res);
        return res;
    }

    /**
     * 忽略结果
     *
     * @param syncEventDataDoc
     * @return 1 成功 0 失败
     */

    public int insertIgnore(SyncEventDataDoc syncEventDataDoc) {
        MongoCollection<SyncEventDataDoc> collection = store.getOrCreateCollection();
        try {
            collection.insertOne(syncEventDataDoc);
        } catch (MongoException mongoException) {
            log.error("sync data insert exception", mongoException);
            return 0;
        }
        return 1;
    }


    public SyncEventDataDoc getById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        SyncEventDataDoc syncEventDataDoc = store.getOrCreateCollection().find(eq(new ObjectId(id))).limit(1).first();
        return syncEventDataDoc;
    }


    public SyncEventDataDoc getSimpleById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        SyncEventDataDoc syncEventDataDoc = store.getOrCreateCollection()
                .find(eq(new ObjectId(id)))
                .limit(1).first();
        return syncEventDataDoc;
    }


    public List<SyncEventDataDoc> listByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<SyncEventDataDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }


    public List<SyncEventDataDoc> listSimpleByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<SyncEventDataDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public void save(SyncEventDataDoc syncEventDataDoc) {
        log.info("SyncEventDataMongoDao.save,syncEventDataDoc={}", syncEventDataDoc);
        Bson filter = and(eq(f_id, syncEventDataDoc.getId()));
        store.getOrCreateCollection().replaceOne(filter, syncEventDataDoc, new ReplaceOptions().upsert(true));
    }

    /**
     * 批量插入或替换记录
     *
     * @param docList
     * @return
     */
    public BulkWriteResult batchReplace(Collection<SyncEventDataDoc> docList) {
        log.info("SyncEventDataMongoDao.batchReplace,docList={}", docList);
        List<WriteModel<SyncEventDataDoc>> request = new ArrayList<>();
        for (SyncEventDataDoc doc : docList) {
            Bson filter = and(eq(f_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("SyncEventDataMongoDao.batchReplace,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }


    /**
     * 批量更新，无则不插入。
     *
     * @param docList
     * @return insert count
     */
    public BulkWriteResult batchUpdate(Collection<SyncEventDataDoc> docList) {
        log.info("SyncEventDataMongoDao.batchUpdate,docList={}", docList);
        List<WriteModel<SyncEventDataDoc>> request = new ArrayList<>();
        for (SyncEventDataDoc doc : docList) {
            Bson filter = and(eq(f_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("SyncEventDataMongoDao.batchUpdate,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }

    public List<SyncEventDataDoc> pageByQuerySyncEventDataArg(QuerySyncEventDataArg arg) {
        List<SyncEventDataDoc> res = new ArrayList<>();
        Bson filter = buildFilterByQuerySyncEventDataArg(arg);
        int offset=(arg.getPageNum()-1) * arg.getPageSize();
        store.getOrCreateCollection().find(filter)
                .skip(offset)
                .limit(arg.getPageSize())
                .into(res);
        return res;
    }

    private Bson buildFilterByQuerySyncEventDataArg(QuerySyncEventDataArg arg) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_appId, arg.getAppId()),
                Filters.eq(f_status, arg.getStatus()));
        if(StringUtils.isNotEmpty(arg.getOutEa())) {
            List<Bson> outEaFilters = Lists.newArrayList(Filters.eq(f_outEa, arg.getOutEa()));
            filters.add(Filters.and(outEaFilters));
        }
        List<Bson> eventTypeAllFilters = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(arg.getEventType())) {
            for(String eventType : arg.getEventType()) {
                List<Bson> eventTypeFilters = Lists.newArrayList(Filters.eq(f_eventType, eventType));
                Bson filterBson = Filters.and(eventTypeFilters);
                eventTypeAllFilters.add(filterBson);
            }
            filters.add(Filters.or(eventTypeAllFilters));
        }

        return Filters.and(filters);
    }

    public DeleteResult deleteTableData(SyncEventDataDoc doc) {
        Bson filter = and(eq(f_id, doc.getId()));
        DeleteResult deleteResult = store.getOrCreateCollection().deleteMany(filter);
        log.info("SyncEventDataMongoDao.deleteTableData,deleteResult={}", deleteResult);
        return deleteResult;
    }

    public DeleteResult deleteTableDataByDelArg(QuerySyncEventDataArg arg) {
        Bson filter = buildFilterByQuerySyncEventDataArg(arg);
        DeleteResult deleteResult = store.getOrCreateCollection().deleteMany(filter);
        log.info("SyncEventDataMongoDao.deleteTableDataByDelArg,deleteResult={}", deleteResult);
        return deleteResult;
    }
}
