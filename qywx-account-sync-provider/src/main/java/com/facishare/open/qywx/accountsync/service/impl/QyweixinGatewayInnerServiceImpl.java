package com.facishare.open.qywx.accountsync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.model.*;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.arg.UploadFileArg;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.core.enums.*;
import com.facishare.open.qywx.accountsync.dao.*;
import com.facishare.open.qywx.accountsync.entity.ErpConnectInfoEntity;
import com.facishare.open.qywx.accountsync.handler.RepEventHandler;
import com.facishare.open.qywx.accountsync.handler.ThirdCmdEventHandler;
import com.facishare.open.qywx.accountsync.handler.ThirdDataEventHandler;
import com.facishare.open.qywx.accountsync.limiter.CrmRateLimiter;
import com.facishare.open.qywx.accountsync.manager.*;
import com.facishare.open.qywx.accountsync.model.*;
import com.facishare.open.qywx.accountsync.model.login.UserTicketModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.*;
import com.facishare.open.qywx.accountsync.mongo.document.OutDepartmentInfoDoc;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.facishare.open.qywx.accountsync.mongo.document.SyncEventDataDoc;
import com.facishare.open.qywx.accountsync.mq.MQSender;
import com.facishare.open.qywx.accountsync.network.ProxyOkHttpClient;
import com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.result.UploadFileResult;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.accountsync.utils.*;
import com.facishare.open.qywx.accountsync.utils.xml.*;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.facishare.open.qywx.i18n.I18NStringManager;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/07/18
 */
@Slf4j
@Data
@Service
public class QyweixinGatewayInnerServiceImpl implements QyweixinGatewayInnerService {

    @Autowired
    private QYWeixinManager qyWeixinManager;

    @Autowired
    private CorpManager corpManager;

    @Autowired
    private FsManager fsManager;

    @Autowired
    private FsFileManager fsFileManager;

    @Resource(name = "qywxEventNotifyMQSender")
    private AutoConfRocketMQProducer qywxEventNotifyMQSender;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private QyweixinOrderInfoDao qyweixinOrderInfoDao;

    @Autowired
    private QyweixinCorpBindDao qyweixinCorpBindDao;

    @Autowired
    private QyweixinCorpInfoDao qyweixinCorpInfoDao;

    @Autowired
    private QyweixinExternalContactDao qyweixinExternalContactDao;

    @Autowired
    private QyweixinMiniprogramSessionKeyDao qyweixinMiniprogramSessionKeyDao;

    @Autowired
    private QyweixinContactBindDao qyweixinContactBindDao;
    @Autowired
    private QyweixinConfigDao qyweixinConfigDao;
    @Autowired
    private QyweixinAccountSyncServiceImpl qyweixinAccountSyncService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private ContactsService contactsService;

    @Autowired
    private OrderManager orderManager;

    @Autowired
    private ProxyAppUtils proxyAppUtils;

    @ReloadableProperty("contactAppId")
    private String contactAppId;

    /**
     * 订货通appId
     */
    @ReloadableProperty("orderAppId")
    private String orderAppId;
    /**
     * 服务通在企业微信的appId
     */
    @ReloadableProperty("eserviceAppId")
    private String eserviceAppId;

    @ReloadableProperty("loginDomain")
    private String loginDomain;

    @ReloadableProperty("redirectUri")
    private String redirectUri;

    @Autowired
    DataPersistorManager dataPersistorManager;

    @Autowired
    ContactBindInnerService contactBindInnerService;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Resource
    private ThirdCmdEventHandler thirdCmdEventHandler;
    @Resource
    private ThirdDataEventHandler thirdDataEventHandler;
    @Resource
    private RepEventHandler repEventHandler;
    @Resource
    private MQSender mqSender;
    @Resource
    private SyncEventDataManger syncEventDataManger;

    @Autowired
    private ProxyOkHttpClient proxyOkHttpClient;

    @Autowired
    private OutUserInfoManger outUserInfoManger;

    @Autowired
    private OutDepartmentInfoManger outDepartmentInfoManger;

    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ErpdssManager erpdssManager;
    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private EventCloudProxyManager eventCloudProxyManager;

    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private AutoPullMessageService autoPullMessageService;

    private static final List<String> proxyCloudEvent = Lists.newArrayList("cancel_auth", "change_contact", "change_external_contact");

    private static final List<String> repProxyCloudEvent = Lists.newArrayList("cancel_auth", "change_auth", "change_contact", "change_external_chat", "enter_agent");

    @Override
    public String onMsgEvent(EnterpriseWeChatEventProto eventProto) {
        log.info("QyweixinGatewayInnerServiceImpl.onMsgEvent,eventProto={}", eventProto);
        try {
            String plainMsg = thirdCmdEventHandler.decryptMsg(eventProto.getSignature(),
                    eventProto.getTimestamp(),
                    eventProto.getNonce(),
                    eventProto.getData(),
                    eventProto.getAppId());
            log.info("QyweixinGatewayInnerServiceImpl.onMsgEvent,plainMsg={}", plainMsg);
            recvMsgEvent(plainMsg,eventProto.getAppId());
        } catch (Exception e) {
            log.info("QyweixinGatewayInnerServiceImpl.onMsgEvent,exception={}", e.getMessage(),e);
        }
        return "success";
    }

    @Override
    public String onDataEvent(EnterpriseWeChatEventProto eventProto) {
        log.info("QyweixinGatewayInnerServiceImpl.onDataEvent,eventProto={}", eventProto);
            try {
                String plainMsg = thirdDataEventHandler.decryptMsg(eventProto.getSignature(),
                        eventProto.getTimestamp(),
                        eventProto.getNonce(),
                        eventProto.getData(),
                        eventProto.getAppId());
                log.info("QyweixinGatewayInnerServiceImpl.onDataEvent,plainMsg={}", plainMsg);
                //非紧急事件，发送MQ，等待后面消费
                mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_DATA_EVENT_4_THIRD,plainMsg,eventProto.getAppId());
            } catch (Exception e) {
                log.info("QyweixinGatewayInnerServiceImpl.onDataEvent,exception={}", e.getMessage(),e);
            }
        return "success";
    }

    @Override
    public String onRepEvent(EnterpriseWeChatEventProto eventProto) {
        log.info("QyweixinGatewayInnerServiceImpl.onRepEvent,eventProto={}", eventProto);
            try {
                String plainMsg = repEventHandler.decryptMsg(eventProto.getSignature(),
                        eventProto.getTimestamp(),
                        eventProto.getNonce(),
                        eventProto.getData(),
                        eventProto.getAppId());
                log.info("QyweixinGatewayInnerServiceImpl.onRepEvent,plainMsg={}", plainMsg);
                repMsgEvent(plainMsg,eventProto.getAppId());
            } catch (Exception e) {
                log.info("QyweixinGatewayInnerServiceImpl.onRepEvent,exception={}", e.getMessage(),e);
            }
        return "success";
    }

    @Override
    public String onSystemData(EnterpriseWeChatEventProto eventProto) {
        log.info("QyweixinGatewayInnerServiceImpl.onDataEvent,eventProto={}", eventProto);
        try {
            String plainMsg = thirdDataEventHandler.decryptMsg(eventProto.getSignature(),
                    eventProto.getTimestamp(),
                    eventProto.getNonce(),
                    eventProto.getData(),
                    eventProto.getAppId());
            log.info("QyweixinGatewayInnerServiceImpl.onDataEvent,plainMsg={}", plainMsg);
            //非紧急事件，发送MQ，等待后面消费
            mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_SYSTEM_EVENT_NOTIFY,plainMsg,eventProto.getAppId());
        } catch (Exception e) {
            log.info("QyweixinGatewayInnerServiceImpl.onDataEvent,exception={}", e.getMessage(),e);
        }
        return "success";
    }
    @Override
    public String recvMsgEvent(String plainMsg,String appId) {
        //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
        QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
        log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent,baseMsgXml={}", baseMsgXml);
        String infoType = baseMsgXml.getInfoType();
        log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent,infoType={}", infoType);
        //紧急ticket事件，立即处理
        if ("suite_ticket".equalsIgnoreCase(infoType)) {
            //接收推送ticket
            //SuiteAuthXml suiteAuthXml = XmlParser.fromXml(plainMsg, SuiteAuthXml.class);
            SuiteAuthXml suiteAuthXml = XStreamUtils.parseXml(plainMsg, SuiteAuthXml.class);
            log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent,infoType={},suiteAuthXml={}", infoType,suiteAuthXml);
            qyWeixinManager.saveQyweixinTicketToken(suiteAuthXml, Boolean.TRUE);
        } else {
            //判断是否需要跨云
            if(proxyCloudEvent.contains(infoType)) {
                eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, baseMsgXml.getSuiteId(), EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD, baseMsgXml.getAuthCorpId(), infoType, plainMsg, null);
            }

            //如果是非紧急事件，发送MQ，等待后面消费
            mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD,plainMsg,appId);
        }
        return "success";
    }
    public String recvMsgEvent2(String plainMsg) {
        try {
            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
            log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent2,baseMsgXml={}", baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent2,infoType={}", infoType);
//            if ("suite_ticket".equals(infoType)) {
//
//                //接收推送ticket
//                SuiteAuthXml suiteAuthXml = XmlParser.fromXml(plainMsg, SuiteAuthXml.class);
//                log.info("trace_appId:{} recvMsg get suite_ticket: {}", baseMsgXml.getSuiteId(), suiteAuthXml.getSuiteTicket());
//                qyWeixinManager.saveQyweixinTicketToken(suiteAuthXml);
//            } else
            if ("create_auth".equals(infoType)) {

                //接收授权成功
                //AuthCompanyXml authCompanyXml = XmlParser.fromXml(plainMsg, AuthCompanyXml.class);
                AuthCompanyXml authCompanyXml = XStreamUtils.parseXml(plainMsg, AuthCompanyXml.class);
                this.getAuthInfoDoInitCorp(authCompanyXml.getAuthCode(), authCompanyXml.getSuiteId(), null);

            } else if ("change_auth".equals(infoType)) {

                //接收授权变更
                //AuthCompanyXml authCompanyXml = XmlParser.fromXml(plainMsg, AuthCompanyXml.class);
                AuthCompanyXml authCompanyXml = XStreamUtils.parseXml(plainMsg, AuthCompanyXml.class);
                log.info("trace_appId:{} recvMsgEvent2  get change_auth corpId:{}", baseMsgXml.getSuiteId(), authCompanyXml.getAuthCorpId());
                //迁移企业账号信息
                String corpId = authCompanyXml.getAuthCorpId();//this.refreshEnterpriseAccount(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId(), null);
                log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent2,change_auth,corpId={}", corpId);
                this.updateCorpInfo(corpId, authCompanyXml.getSuiteId());

            } else if ("cancel_auth".equals(infoType)) {

                //接收取消授权
                //AuthCompanyXml authCompanyXml = XmlParser.fromXml(plainMsg, AuthCompanyXml.class);
                AuthCompanyXml authCompanyXml = XStreamUtils.parseXml(plainMsg, AuthCompanyXml.class);
                log.info("trace_appId:{} recvMsgEvent2  get cancel_auth corpId:{}", baseMsgXml.getSuiteId(), authCompanyXml.getAuthCorpId());
                //通讯录取消授权事件不处理
                if (!Objects.equals(contactAppId, authCompanyXml.getSuiteId())) {
                    this.cancelCorpAuth(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId());
                }

            } else if ("change_contact".equals(infoType)) {
                //通讯录变更通知通讯录变更事件(员工、部门、标签)
                String suiteId = baseMsgXml.getSuiteId();
                log.info("trace_appId:{} recvMsgEvent2  get change_contact changeType:{} corpId:{}", suiteId, baseMsgXml.getChangeType(), baseMsgXml.getAuthCorpId());
                // 通讯录套件 通讯录变更事件不处理
                if (!Objects.equals(contactAppId, suiteId)) {
                    String finalPlainMsg = plainMsg;
                    corpManager.changeContacts(finalPlainMsg, baseMsgXml.getChangeType());
                }

            } else if ("open_order".equals(infoType)) {

                //接收下单成功通知消息
                //OrderXml orderXml = XmlParser.fromXml(plainMsg, OrderXml.class);
                OrderXml orderXml = XStreamUtils.parseXml(plainMsg, OrderXml.class);
                log.info("trace_appId:{} recvMsgEvent2 get open_order orderId:{}", baseMsgXml.getSuiteId(), orderXml.getOrderId());
                orderManager.openOrderEvent(orderXml.getSuiteId(), orderXml.getOrderId());

            } else if ("change_order".equals(infoType)) {

                //接收改单通知消息
                //OrderXml orderXml = XmlParser.fromXml(plainMsg, OrderXml.class);
                OrderXml orderXml = XStreamUtils.parseXml(plainMsg, OrderXml.class);
                log.info("trace_appId:{} recvMsgEvent2 get change_order oldOrderId:{}, newOrderId:{}", baseMsgXml.getSuiteId(), orderXml.getOldOrderId(), orderXml.getNewOrderId());
                orderManager.changeOrderEvent(orderXml.getSuiteId(), orderXml.getNewOrderId(), orderXml.getOldOrderId());

            } else if ("pay_for_app_success".equals(infoType)) {

                //接收支付成功通知消息
                //OrderXml orderXml = XmlParser.fromXml(plainMsg, OrderXml.class);
                OrderXml orderXml = XStreamUtils.parseXml(plainMsg, OrderXml.class);
                log.info("trace_appId:{} recvMsgEvent2 get pay_for_app_success orderId:{}", baseMsgXml.getSuiteId(), orderXml.getOrderId());
                orderManager.payForAppSuccessEvent(orderXml.getSuiteId(), orderXml.getOrderId());

            } else if ("refund".equals(infoType)) {

                //接收退款事件回调
                //OrderXml orderXml = XmlParser.fromXml(plainMsg, OrderXml.class);
                OrderXml orderXml = XStreamUtils.parseXml(plainMsg, OrderXml.class);
                log.info("trace_appId:{} recvMsgEvent2 get refund orderId:{}", baseMsgXml.getSuiteId(), orderXml.getOrderId());
                orderManager.refundEvent(orderXml.getSuiteId(), orderXml.getOrderId());

            } else if ("agree_external_userid_migration".equals(infoType)) {
                //同意授权转换external_userid事件
                //externalMigrationXml externalMigrationXml = XmlParser.fromXml(plainMsg, externalMigrationXml.class);
                externalMigrationXml externalMigrationXml = XStreamUtils.parseXml(plainMsg, externalMigrationXml.class);
                log.info("trace_appId:{} recvMsgEvent2  get agree_external_userid_migration externalMigrationXml:{}", baseMsgXml.getSuiteId(), externalMigrationXml);
                //this.externalMigration(externalMigrationXml.getAuthCorpId());
            } else if ("change_external_contact".equals(infoType)) {
                //ExternalContactEventXml externalContactEventXml = XmlParser.fromXml(plainMsg, ExternalContactEventXml.class);
                ExternalContactEventXml externalContactEventXml = XStreamUtils.parseXml(plainMsg, ExternalContactEventXml.class);
                changeExternalContact(externalContactEventXml);
            }  else if ("change_external_chat".equals(infoType)) {
//                ExternalChatEventXml externalChatEventXml = XStreamUtils.parseXml(plainMsg, ExternalChatEventXml.class);
//                log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent,externalChatEventXml={}", externalChatEventXml);
//                changeExternalChat(externalChatEventXml);
            } else {
                log.info("QyweixinGatewayInnerServiceImpl.recvMsgEvent2,appId={},not supported event={}",
                        baseMsgXml.getSuiteId(),
                        infoType);
                return "fail";
            }

        } catch (Exception e) {
            log.warn("QyweixinGatewayInnerServiceImpl.recvMsgEvent2,exception={}", e);
            return "fail";
        }
        return (plainMsg != null) ? "success" : "fail";
    }

    /**
     * 1、外部联系人新增或更新，发送MQ通知北研
     * 2、删除外部联系人或者被外部联系人删除，发送MQ通知北研
     * @param eventXml
     */
    private void changeExternalContact(ExternalContactEventXml eventXml) {
        if(QyweixinChangeExternalEventTypeEnum.ADD_EXTERNAL_CONTACT.getEventType().equals(eventXml.getChangeType())
                || QyweixinChangeExternalEventTypeEnum.EDIT_EXTERNAL_CONTACT.getEventType().equals(eventXml.getChangeType())) {
            //外部联系人新增或更新，发送MQ通知北研
            this.addOrUpdateExternalContact(eventXml);
        } else if(QyweixinChangeExternalEventTypeEnum.DEL_EXTERNAL_CONTACT.getEventType().equals(eventXml.getChangeType())
                || QyweixinChangeExternalEventTypeEnum.DEL_FOLLOW_USER.getEventType().equals(eventXml.getChangeType())) {
            //删除外部联系人或者被外部联系人删除，发送MQ通知北研
            this.deleteExternalContact(eventXml);
        }
    }

    private void addOrUpdateExternalContact(ExternalContactEventXml eventXml) {
        log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,eventXml={}",eventXml);
        String outEa = StringUtils.isEmpty(eventXml.getAuthCorpId()) ? eventXml.getToUserName() : eventXml.getAuthCorpId();
        log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,outEa={}",outEa);
        String validRepAppId = getValidRepAppId(outEa).getData();
        log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,validRepAppId={}",validRepAppId);

        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(outEa, validRepAppId);
        if(qyweixinCorpBindBo==null) {
            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,没有启用代开发,eventXml={}",eventXml);
            return;
        }

        ExternalContactEvent event = new ExternalContactEvent();
        event.setAppId(eventXml.getSuiteId());
        event.setCorpId(outEa);
        event.setChangeType(eventXml.getChangeType());
        event.setExternalUserId(eventXml.getExternalUserID());
        event.setUserId(eventXml.getUserID());

        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(event.getCorpId());
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,enterprise not bind.corpId={}",event.getCorpId());
            return;
        }

        //查询外部联系人详情
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactRsp> externalContactRspResult = qyWeixinManager.getExternalContactDetail(qyweixinCorpBindBo.getPermanentCode(),
                event.getCorpId(), event.getExternalUserId(), null);
        log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,externalContactRsp={}",externalContactRspResult);
        if(externalContactRspResult.getData()==null || !externalContactRspResult.isSuccess()) {
            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,企业微信外部联系人查询失败");
            return;
        }
        event.setExternalContactDetail(JSONObject.toJSONString(externalContactRspResult.getData()));

        QyweixinExternalContactBo contactBo = QyweixinExternalContactBo.builder()
                .outEa(event.getCorpId())
                .outUserId(event.getUserId())
                .externalUserId(event.getExternalUserId())
                .build();
        List<QyweixinExternalContactBo> entityList = qyweixinExternalContactDao.findByEntity(contactBo);
        log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,entityList={}",entityList);
        if(CollectionUtils.isEmpty(entityList)) {
            //现看下是否已经初始化过了
            int exists = qyweixinExternalContactDao.existsByOutEaAndUserId(outEa, event.getUserId());
            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,exists={}",exists);
            if(exists == 0) {
                String corpId = qyWeixinManager.getAllExternalContact(event.getCorpId(),null,event.getUserId(),validRepAppId).getData();
                contactBo.setOutEa(corpId);
            } else {
                QyweixinExternalContactBo newContactBo = QyweixinExternalContactBo.builder()
                        .outEa(outEa)
                        .outUserId(event.getUserId())
                        .externalUserId(event.getExternalUserId())
                        .externalName(externalContactRspResult.getData().getExternal_contact().getName())
                        .avatar(externalContactRspResult.getData().getExternal_contact().getAvatar())
                        .build();
                int insert = qyweixinExternalContactDao.insert(newContactBo);
                log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,insert={}",insert);
            }

            entityList = qyweixinExternalContactDao.findByEntity(contactBo);
            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,entityList2={}",entityList);
        }
        if(CollectionUtils.isEmpty(entityList)) {
            return;
        }

        log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,event={}",event);
        for(QyweixinAccountEnterpriseMapping mapping : enterpriseMappingList) {
            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,mapping={}",mapping);
            event.setFsEa(mapping.getFsEa());
            Result<String> result = qyweixinAccountBindInnerService.outAccountToFsAccount("qywx",event.getCorpId(),event.getUserId(),mapping.getFsEa());
            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,result={}",result);
            if(StringUtils.isNotEmpty(result.getData())) {
                event.setFsUserId(result.getData());
                List<String> accountList = Splitter.on(".").splitToList(result.getData());
                event.setUserIdMap(ImmutableMap.of(event.getUserId(), accountList.get(2)));
            }

            log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,event={}",event);

            Message msg = new Message();
            msg.setTags(CorpManager.TAG_EXTERNAL_CONTACT);
            msg.setBody(event.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(mapping.getFsEa())) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.qywxEventNotifyMQSender.name());
                cloudMessageProxyProto.setCorpId(event.getCorpId());
                cloudMessageProxyProto.setFsEa(mapping.getFsEa());
                cloudMessageProxyProto.setMessage(msg);
                log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,cloudMessageProxyProto={}",cloudMessageProxyProto);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(mapping.getFsEa()), cloudMessageProxyProto);
            } else {
                log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,msg={}",msg);
                SendResult sendResult = qywxEventNotifyMQSender.send(msg);
                log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,ea={},sendResult={}", mapping.getFsEa(), sendResult);
            }
        }
    }

    private void deleteExternalContact(ExternalContactEventXml eventXml) {
        String outEa = StringUtils.isEmpty(eventXml.getAuthCorpId()) ? eventXml.getToUserName() : eventXml.getAuthCorpId();
        ExternalContactEvent event = new ExternalContactEvent();
        event.setAppId(eventXml.getSuiteId());
        event.setCorpId(outEa);
        event.setChangeType(eventXml.getChangeType());
        event.setExternalUserId(eventXml.getExternalUserID());
        event.setUserId(eventXml.getUserID());

        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(event.getCorpId());
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            log.info("QyweixinGatewayInnerServiceImpl.deleteExternalContact,enterprise not bind.corpId={}",event.getCorpId());
            return;
        }

        //直接删除表里的数据
        int deleteCount = qyweixinExternalContactDao.deleteExternalUserIds(event.getCorpId(), event.getUserId(), event.getExternalUserId());
        log.info("QyweixinGatewayInnerServiceImpl.deleteExternalContact,contactBo={},deleteCount={}",event, deleteCount);
        for(QyweixinAccountEnterpriseMapping mapping : enterpriseMappingList) {
            event.setFsEa(mapping.getFsEa());
            Result<String> result = qyweixinAccountBindInnerService.outAccountToFsAccount("qywx",event.getCorpId(),event.getUserId(),mapping.getFsEa());
            log.info("QyweixinGatewayInnerServiceImpl.deleteExternalContact,result={}",result);
            if(StringUtils.isNotEmpty(result.getData())) {
                event.setFsUserId(result.getData());
                List<String> accountList = Splitter.on(".").splitToList(result.getData());
                event.setUserIdMap(ImmutableMap.of(event.getUserId(), accountList.get(2)));
            }

            Message msg = new Message();
            msg.setTags(CorpManager.TAG_DELETE_EXTERNAL_CONTACT);
            msg.setBody(event.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(mapping.getFsEa())) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.qywxEventNotifyMQSender.name());
                cloudMessageProxyProto.setCorpId(event.getCorpId());
                cloudMessageProxyProto.setFsEa(mapping.getFsEa());
                cloudMessageProxyProto.setMessage(msg);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(mapping.getFsEa()), cloudMessageProxyProto);
            } else {
                SendResult sendResult = qywxEventNotifyMQSender.send(msg);
                log.info("QyweixinGatewayInnerServiceImpl.deleteExternalContact,ea={},sendResult={}", mapping.getFsEa(), sendResult);
            }
        }
    }

    /**
     * 接收企微推送过来的消息，包括单聊消息和事件等
     *
     * @param plainMsg
     * @param appId
     * @return
     */

    @Override
    public String recvDataEvent(String plainMsg, String appId) {
        //QYWeiXinDataEventBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QYWeiXinDataEventBaseXml.class);
        QYWeiXinDataEventBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QYWeiXinDataEventBaseXml.class);
        log.info("QyweixinGatewayInnerServiceImpl.recvDataEvent,baseMsgXml={}", baseMsgXml);

        String event = baseMsgXml.getEvent();

        if ("subscribe".equalsIgnoreCase(event) || "unsubscribe".equalsIgnoreCase(event)) {
            corpManager.changeContacts2(plainMsg, event, appId);
        } else {
            log.info("QyweixinGatewayInnerServiceImpl.recvDataEvent,not supported event type,event={}", event);
        }
        return null;
    }

    @Override
    public String repMsgEvent(String plainMsg,String appId) {
        log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent,plainMsg={}", plainMsg);

        try {
            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
            log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent,baseMsgXml={}", baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent,infoType={}", infoType);
            TagChangeEventXml tagChangeEventXml = null;
            if (StringUtils.isEmpty(infoType)) {
                //tagChangeEventXml = XmlParser.fromXml(plainMsg, TagChangeEventXml.class);
                tagChangeEventXml = XStreamUtils.parseXml(plainMsg, TagChangeEventXml.class);
                infoType = tagChangeEventXml.getEvent();
                log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent,infoType2={}", infoType);
            }
            //紧急ticket事件，立即处理
            if ("suite_ticket".equals(infoType)) {
                //接收推送ticket
                //SuiteAuthXml suiteAuthXml = XmlParser.fromXml(plainMsg, SuiteAuthXml.class);
                SuiteAuthXml suiteAuthXml = XStreamUtils.parseXml(plainMsg, SuiteAuthXml.class);
                log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent,infoType={},suiteAuthXml={}", infoType,suiteAuthXml);
                qyWeixinManager.saveQyweixinTicketToken(suiteAuthXml, Boolean.TRUE);
            } else {
//                if(tagChangeEventXml!=null && StringUtils.equalsIgnoreCase(tagChangeEventXml.getEvent(),"change_external_contact")) {
//                    //代开发应用不支持change_external_contact事件
//                    log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent,rep app not support change_external_contact event");
//                    return "fail";
//                }

                //判断是否需要跨云
                if(repProxyCloudEvent.contains(infoType)) {
                    eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, baseMsgXml.getSuiteId(), EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP, baseMsgXml.getAuthCorpId(), infoType, plainMsg, null);
                }

                //如果是非紧急事件，发送MQ，等待后面消费
                mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP,plainMsg,appId);
            }

        } catch (Exception e) {
            log.warn("QyweixinGatewayInnerServiceImpl.repMsgEvent,exception={}", e);
            return "fail";
        }
        return (plainMsg != null) ? "success" : "fail";
    }

    public String repMsgEvent2(String plainMsg,String appId) {
        log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,plainMsg={}", plainMsg);

        try {
            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
            log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,baseMsgXml={}", baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,infoType={}", infoType);
            TagChangeEventXml tagChangeEventXml = null;
            if (StringUtils.isEmpty(infoType)) {
                //tagChangeEventXml = XmlParser.fromXml(plainMsg, TagChangeEventXml.class);
                tagChangeEventXml = XStreamUtils.parseXml(plainMsg, TagChangeEventXml.class);
                infoType = tagChangeEventXml.getEvent();
                log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,infoType2={}", infoType);
            }
            if ("create_auth".equals(infoType)) {
                //接收授权成功
                //AuthCompanyXml authCompanyXml = XmlParser.fromXml(plainMsg, AuthCompanyXml.class);
                AuthCompanyXml authCompanyXml = XStreamUtils.parseXml(plainMsg, AuthCompanyXml.class);
                getAuthInfo(authCompanyXml.getAuthCode(), authCompanyXml.getSuiteId(), null);
            } else if ("cancel_auth".equals(infoType)) {
                //接收取消授权
                //AuthCompanyXml authCompanyXml = XmlParser.fromXml(plainMsg, AuthCompanyXml.class);
                AuthCompanyXml authCompanyXml = XStreamUtils.parseXml(plainMsg, AuthCompanyXml.class);
                log.info("trace_appId:{} repMsgEvent2  get cancel_auth corpId:{}", baseMsgXml.getSuiteId(), authCompanyXml.getAuthCorpId());
                cancelCorpAuth(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId());
            } else if ("change_auth".equals(infoType)) {
                //接收授权变更
                //AuthCompanyXml authCompanyXml = XmlParser.fromXml(plainMsg, AuthCompanyXml.class);
                AuthCompanyXml authCompanyXml = XStreamUtils.parseXml(plainMsg, AuthCompanyXml.class);
                log.info("trace_appId:{} repMsgEvent2  get change_auth corpId:{}", baseMsgXml.getSuiteId(), authCompanyXml.getAuthCorpId());
                //saveEmployeeAccountBind(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId());
                saveAppEvent(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId(), infoType, plainMsg);
                changeCorpInfo(authCompanyXml.getAuthCorpId(), authCompanyXml.getSuiteId());
                //代开发应用可见范围变更事件，触发CRM应用可见范围变更逻辑
                updateCorpInfo(authCompanyXml.getAuthCorpId(),ConfigCenter.crmAppId);
                //判断是不是有授权数据智能：
                com.facishare.open.qywx.accountinner.result.Result<List<String>> permissionInfo = qyWeixinManager.getPermissionInfo(authCompanyXml.getAuthCorpId(), appId);
                log.info("permission setting :{}.corpId:{}",JSONObject.toJSONString(permissionInfo),authCompanyXml.getAuthCorpId());
                if(permissionInfo.isSuccess()&&permissionInfo.getData().contains("datazone:data:chat")){
                    qyWeixinManager.setCallback(authCompanyXml.getAuthCorpId(),appId);
                }

            } else if ("reset_permanent_code".equals(infoType)) {
                //代开发更改secret事件回调
                //ResetPermanentCodeXml resetPermanentCodeXml = XmlParser.fromXml(plainMsg, ResetPermanentCodeXml.class);
                ResetPermanentCodeXml resetPermanentCodeXml = XStreamUtils.parseXml(plainMsg, ResetPermanentCodeXml.class);
                log.info("trace_appId:{} repMsgEvent2 get refund authCode:{}", baseMsgXml.getSuiteId(), resetPermanentCodeXml.getAuthCode());
                resetPermanentCode(resetPermanentCodeXml.getSuiteId(), resetPermanentCodeXml.getAuthCode());
            } else if ("change_contact".equals(infoType)) {
                //企业微信标签人员或部门变更通知事件
                updateTag(baseMsgXml, tagChangeEventXml, infoType, plainMsg);
                //通讯录变更事件，在代开发只针对指定企业的账号自动同步，还有账号绑定
                this.changeContacts(plainMsg);
            } else if ("change_external_contact".equals(infoType)) {
                //ExternalContactEventXml externalContactEventXml = XmlParser.fromXml(plainMsg, ExternalContactEventXml.class);
                ExternalContactEventXml externalContactEventXml = XStreamUtils.parseXml(plainMsg, ExternalContactEventXml.class);
                changeExternalContact(externalContactEventXml);
            } else if ("change_external_chat".equals(infoType)) {
                RepExternalChatEventXml externalChatEventXml = XStreamUtils.parseXml(plainMsg, RepExternalChatEventXml.class);
                log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,externalChatEventXml={}", externalChatEventXml);
                repChangeExternalChat(externalChatEventXml);
            } else if ("enter_agent".equals(infoType)) {
                EventXml eventXml = XStreamUtils.parseXml(plainMsg, EventXml.class);
                log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,enter_agent,eventXml={}", eventXml);
                onEnterAgent(eventXml);
            } else if ("share_chain_change".equals(infoType)) {
                EventXml eventXml = XStreamUtils.parseXml(plainMsg, EventXml.class);
                log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,share_chain_change,eventXml={}", eventXml);
                onShareChainChange(eventXml);
            } else if("program_notify".equals(infoType)){
                QYWeiXinDataEventBaseXml notifyXml = XStreamUtils.parseXml(plainMsg, QYWeiXinDataEventBaseXml.class);
                //处理数据智能返回的通知
                log.info("QyweixinGatewayInnerServiceImpl.program_notify,event={}",infoType);

                autoPullMessageService.getMessageByCallBackToken(notifyXml.getToUserName(),appId,notifyXml.getNotifyId());
            }
            else {
                log.info("QyweixinGatewayInnerServiceImpl.repMsgEvent2,appId={},not supported event={}",
                        baseMsgXml.getSuiteId(),
                        infoType);
                return "fail";
            }

        } catch (Exception e) {
            log.warn("QyweixinGatewayInnerServiceImpl.repMsgEvent2,exception={}", e);
            return "fail";
        }
        return (plainMsg != null) ? "success" : "fail";
    }

    public static void main(String[] args) {
        String data="{\"code\":\"0\",\"data\":[\"contact:sensitive:mobile\",\"contact:sensitive:avatar\",\"contact:sensitive:qrcode\",\"contact:sensitive:email\",\"contact:sensitive:user_name\",\"contact:sensitive:gender\",\"contact:sensitive:telephone\",\"contact:sensitive:department_name\",\"contact:sensitive:position\",\"contact:sensitive:address\",\"contact:sensitive:extattr\",\"contact:sensitive:external_profile\",\"contact:sensitive:external_position\",\"contact:sensitive:biz_mail\",\"externalcontact:base:base\",\"externalcontact:contact:tag\",\"externalcontact:contact:group_msg\",\"externalcontact:contact:welcome_msg\",\"externalcontact:contact:qrcode\",\"externalcontact:contact:stat\",\"externalcontact:contact:transfer\",\"externalcontact:contact:resigned\",\"externalcontact:contact:product_album\",\"externalcontact:contact:intercept_rule\",\"externalcontact:groupchat:welcome_msg\",\"externalcontact:groupchat:resigned\",\"externalcontact:groupchat:stat\",\"externalcontact:groupchat:transfer\",\"externalcontact:moment:list\",\"externalcontact:moment:post\",\"externalcontact:sensitive:mobile\",\"externalcontact:sensitive:avatar\",\"calendar:base:base\",\"living:base:base\",\"externalpay:base:base\",\"approval:base:base\",\"checkin:app:base\",\"checkin:hardware:base\",\"hardware:base:base\",\"customerservice:chat:manage\",\"customerservice:tool:upgrade\",\"customerservice:tool:stat\",\"customerservice:base:base\",\"wedrive:base:base\",\"corp_arch:base:base\",\"corp_arch:member:direct_leader\",\"doc:base:base\",\"email:base:base\",\"datazone:data:chat\",\"datazone:component:chat\"],\"msg\":\"成功\",\"success\":true,\"traceMsg\":\"0A78D3BC0001118D67C1293DAC21027D\"}";
        com.facishare.open.qywx.accountinner.result.Result<List<String>> dataString=JSONObject.parseObject(data, new TypeReference<com.facishare.open.qywx.accountinner.result.Result<List<String>>>() {});
    }
    public void onShareChainChange(EventXml eventXml) {
        log.info("QyweixinGatewayInnerServiceImpl.onShareChainChange,eventXml={}", eventXml);
        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.findByAgentId(eventXml.getToUserName(), eventXml.getAgentID());
        log.info("QyweixinGatewayInnerServiceImpl.onShareChainChange,corpBindBo={}", corpBindBo);
        if (corpBindBo == null) {
            return;
        }
        String upstreamCorpId = corpBindBo.getCorpId();
        String upstreamAppId = corpBindBo.getAppId();
        String upstreamAgentId = corpBindBo.getAgentId();
        com.facishare.open.qywx.accountinner.result.Result<List<ListAppShareInfoRsp.CorpInfo>> listAppShareInfo = qyWeixinManager.listAppShareInfo(upstreamCorpId,
                upstreamAppId,
                upstreamAgentId);
        log.info("QyweixinGatewayInnerServiceImpl.onShareChainChange,listAppShareInfo={}", listAppShareInfo);
        if(CollectionUtils.isEmpty(listAppShareInfo.getData())) return;
        //插入数据到企业信息表
        for(ListAppShareInfoRsp.CorpInfo corpInfo : listAppShareInfo.getData()) {
            QyweixinCorpInfoBo corpInfoBo =new QyweixinCorpInfoBo();
            corpInfoBo.setCorpId(corpInfo.getCorpid());
            List<QyweixinCorpInfoBo> list = qyweixinCorpInfoDao.findByEntity(corpInfoBo);
            log.info("QyweixinGatewayInnerServiceImpl.onShareChainChange,findCorpInfo,list={}", list);
            if(CollectionUtils.isNotEmpty(list)) continue;
            corpInfoBo.setCorpName(corpInfo.getCorp_name());
            int insert = qyweixinCorpInfoDao.insert(corpInfoBo);
            log.info("QyweixinGatewayInnerServiceImpl.onShareChainChange,insertCorpInfo,insert={}", insert);
        }
    }

    public void onEnterAgent(EventXml eventXml) {
        log.info("QyweixinGatewayInnerServiceImpl.onEnterAgent,eventXml={}", eventXml);

        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.findByAgentId(eventXml.getToUserName(), eventXml.getAgentID());
        log.info("QyweixinGatewayInnerServiceImpl.onEnterAgent,corpBindBo={}", corpBindBo);
        if (corpBindBo == null) {
            return;
        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> result = qyWeixinManager.getUserInfo(corpBindBo.getAppId(),
                eventXml.getToUserName(),
                eventXml.getFromUserName());
        log.info("QyweixinGatewayInnerServiceImpl.onEnterAgent,getUserInfo result={}", result);

        QyweixinConfigBo qyweixinConfigBo = qyweixinConfigDao.findByType(eventXml.getToUserName(),
                ConfigEnum.CONTACTS_USER_DEFINED_MOBILE_KEY.name());
        log.info("QyweixinGatewayInnerServiceImpl.onEnterAgent,qyweixinConfigBo={}", qyweixinConfigBo);
        if(qyweixinConfigBo==null || StringUtils.isEmpty(qyweixinConfigBo.getConfig())) return;

        String mobile = null;
        if(result.getData().getExtattr()!=null && CollectionUtils.isNotEmpty(result.getData().getExtattr().getAttrs())) {
            for(QyweixinUserDetailInfoRsp.AttrsInfo attr : result.getData().getExtattr().getAttrs()) {
                if(StringUtils.equals(qyweixinConfigBo.getConfig(), attr.getName())) {
                    if(attr.getText()!=null && StringUtils.isNotEmpty(attr.getText().getValue())) {
                        mobile = attr.getText().getValue();
                    }
                }
            }
        }

        EnterpriseWechatUserModel userModel = new EnterpriseWechatUserModel();
        userModel.setAppId(corpBindBo.getAppId());
        userModel.setCorpId(eventXml.getToUserName());
        userModel.setUserId(eventXml.getFromUserName());
        userModel.setMobile(mobile);
        sendEnterpriseWechatUserInfo(userModel);

    }

    @Override
    public void sendEnterpriseWechatUserInfo(EnterpriseWechatUserModel model) {
        proxyAppUtils.sendEnterpriseWechatUserInfo(model);
    }

    public void updateTag(QyweixinMsgBaseXml baseMsgXml, TagChangeEventXml tagChangeEventXml, String infoType, String plainMsg) {
        if(tagChangeEventXml!=null && StringUtils.equalsIgnoreCase(tagChangeEventXml.getChangeType(),"update_tag")) {
            saveAppEvent(StringUtils.isNotEmpty(baseMsgXml.getAuthCorpId())? baseMsgXml.getAuthCorpId() : tagChangeEventXml.getToUserName(), ConfigCenter.repAppId, infoType, plainMsg);
            String corpId = tagChangeEventXml.getToUserName();
            List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
            corpId = enterpriseMapping.getOutEa();

            String fsEa = enterpriseMapping.getFsEa();

            GetFsUserIdsByRestResult fsUserIds = fsManager.getFsUserIdsByRestService(fsEa,
                    1000);

            List<Integer> userIdList = fsUserIds.getData().stream()
                    .map(v -> Integer.valueOf(v.substring(v.lastIndexOf(".") + 1)))
                    .collect(Collectors.toList());

            List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(fsEa,
                    1000, userIdList);

            addTagUserList(tagChangeEventXml,fsEa,corpId,employeeInfos);
//            delTagUserList(tagChangeEventXml,fsEa);
            addTagDepUserList(tagChangeEventXml,fsEa,corpId,employeeInfos);
//            delTagDepUserList(tagChangeEventXml,fsEa,corpId);
        }
    }

    private void addTagUserList(TagChangeEventXml tagChangeEventXml,String fsEa,String corpId,List<EmployeeDto> employeeInfos) {
        List<QyweixinUserDetailInfoRsp> addUserList= new ArrayList<>();
        if(StringUtils.isNotEmpty(tagChangeEventXml.getAddUserItems())) {
            List<String> userList = Splitter.on(",").splitToList(tagChangeEventXml.getAddUserItems());
            log.info("QyweixinGatewayInnerServiceImpl.updateTag,add user,userList={}",userList);
            for(String userId : userList) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = qyWeixinManager.getUserInfo(ConfigCenter.repAppId,corpId,userId);
                if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                    addUserList.add(userDetailInfoRspResult.getData());
                }
            }
        }
        batchUpdateEmployeeBind(fsEa,corpId,employeeInfos,addUserList);
    }

    private void addTagDepUserList(TagChangeEventXml tagChangeEventXml,String fsEa,String corpId,List<EmployeeDto> employeeInfos) {
        List<QyweixinUserDetailInfoRsp> userList= new ArrayList<>();
        if(StringUtils.isNotEmpty(tagChangeEventXml.getAddPartyItems())) {
            List<String> depList = Splitter.on(",").splitToList(tagChangeEventXml.getAddPartyItems());
            log.info("QyweixinGatewayInnerServiceImpl.updateTag,add party,depList={}",depList);
            for(String depId : depList) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(ConfigCenter.repAppId,
                        corpId, depId);
                if(departmentEmployeeListResult.isSuccess() && departmentEmployeeListResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListResult.getData().getUserlist())) {
                    userList.addAll(departmentEmployeeListResult.getData().getUserlist());
                }
            }
        }
        batchUpdateEmployeeBind(fsEa,corpId,employeeInfos,userList);
    }


    private void batchUpdateEmployeeBind(String fsEa, String corpId, List<EmployeeDto> employeeInfos, List<QyweixinUserDetailInfoRsp> userList) {
        if(CollectionUtils.isNotEmpty(userList)) {
            List<QyweixinAccountEmployeeMapping> newMappingList = new ArrayList<>();
            for(QyweixinUserDetailInfoRsp userDetailInfoRsp : userList) {
                Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindInnerService.getEmployeeMapping(corpId,
                        userDetailInfoRsp.getUserid(),-1,fsEa);
                if(CollectionUtils.isNotEmpty(result.getData())) {
                    result.getData().get(0).setStatus(0);
                    //更新员工状态
                    int count = qyweixinAccountBindInnerService.updateQyweixinAccountEmployee(result.getData().get(0));
                    log.info("QyweixinGatewayInnerServiceImpl.updateTag,count={}",count);
                } else {
                    List<EmployeeDto> list = employeeInfos.stream()
                            .filter(item->StringUtils.equalsIgnoreCase(item.getMobile(),userDetailInfoRsp.getMobile()))
                            .collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(list)) continue;

                    QyweixinAccountEmployeeMapping accountEmployeeMapping = new QyweixinAccountEmployeeMapping();
                    accountEmployeeMapping.setSource("qywx");
                    accountEmployeeMapping.setFsAccount("E."+fsEa+"."+list.get(0).getEmployeeId());
                    accountEmployeeMapping.setIsvAccount(userDetailInfoRsp.getUserid());
                    accountEmployeeMapping.setOutAccount(userDetailInfoRsp.getUserid());//这个后面需要替换掉
                    //accountEmployeeMapping.setAppId(crmAppId);
                    accountEmployeeMapping.setOutEa(corpId);

                    newMappingList.add(accountEmployeeMapping);
                }
            }
            if(CollectionUtils.isNotEmpty(newMappingList)) {
                //批量绑定员工
                log.info("QyweixinGatewayInnerServiceImpl.updateTag,newMappingList={}",newMappingList);
                qyweixinAccountBindService.bindAccountEmployeeMapping(newMappingList);
            }

            //自动绑定账号
            Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
            }.getType());
            if(autoBindEmpEnterpriseMap.containsKey(fsEa)) {
                log.info("QyweixinGatewayInnerServiceImpl.updateTag,autoBindEmpEnterpriseMap,fsEa={}",fsEa);
                corpManager.autoBindEmpAccount(fsEa, corpId, userList);
            } else if(ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
                log.info("QyweixinGatewayInnerServiceImpl.updateTag,AUTO_BIND_ACCOUNT_EA,fsEa={}",fsEa);
                corpManager.autoBindAccountEnterprise2(fsEa, corpId, userList);
            }
        }
    }

    @Override
    public String getSuiteAccessToken(String appId) {
        return qyWeixinManager.getSuiteAccessTokenFromRedis(appId);
    }

    @Override
    public String getPreAuthCode(String appId) {
        com.facishare.open.qywx.accountinner.result.Result<String> preAuthCodeResult = qyWeixinManager.getPreAuthCode(appId);
        if(!preAuthCodeResult.isSuccess()) {
            return null;
        }
        String preAuthCode = preAuthCodeResult.getData();
        //设置预授权
        qyWeixinManager.setSessionInfo(appId, preAuthCode);
        return preAuthCode;
    }

    @Override
    public Result<String> doDial(String caller, String authCorpId){
        log.info("QyweixinGatewayInnerServiceImpl.doDial,caller={},authCorpId={}",caller,authCorpId);
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getDial(caller, authCorpId);//调用manager的方法去调用企业微信接口
        log.info("QyweixinGatewayInnerServiceImpl.doDial,result={}",result);
        if(!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
            return new Result<String>().addError(ErrorRefer.DIAL_OTHER_ERROR.getCode(), result.getMsg(),null);
        }
        QyweixinGetDialRsp getDialRsp = new Gson().fromJson(result.getData(), QyweixinGetDialRsp.class);
        log.info("QyweixinGatewayInnerServiceImpl.doDial,getDialRsp={}",getDialRsp);
        int qywxErrorCode=getDialRsp.getErrcode();
        if(qywxErrorCode == 0) {
            return new Result<>(result.getData());
        }
        //判断是否为公费电话的错误返回码，返回对应i18n平台的错误信息
        for(ErrorRefer error : ErrorRefer.values()){
            Integer nQywxErrorCode = null;
            try {
                nQywxErrorCode = Integer.valueOf(error.getQywxCode());
            } catch (Exception e) {
                continue;
            }
            if (qywxErrorCode == nQywxErrorCode){
                log.info("QyweixinGatewayInnerServiceImpl.doDial,qywxErrorCode={},getQywxCode={}",qywxErrorCode,error.getQywxCode());
//                String errorData=errorCodeUtil.getErrorString(error.getCode(), Locale.SIMPLIFIED_CHINESE.toLanguageTag());
//                log.info("QyweixinGatewayInnerServiceImpl.doDial,errorData={}",errorData);
                return new Result<String>().Result(error.getCode(), error.getQywxCode(), getDialRsp.getErrmsg());
            }
        }

//        String errorData=errorCodeUtil.getErrorString(ErrorRefer.DIAL_OTHER_ERROR.getCode(), Locale.SIMPLIFIED_CHINESE.toLanguageTag());
//        log.info("QyweixinGatewayInnerServiceImpl.doDial,errorData2={}",errorData);
        return new Result<String>().Result(ErrorRefer.DIAL_OTHER_ERROR.getCode(), ErrorRefer.DIAL_OTHER_ERROR.getQywxCode(), getDialRsp.getErrmsg());
    }

    @Override
    @Transactional
    public Result<String> getAuthInfoDoInitCorp(String authCode, String appId, String fsEa) {

        log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,fsEa={}, appId={}, authCode={}", fsEa, appId, authCode);
        //获取永久授权码、企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetPermenantCodeRsp> corpAuthInfoResult = qyWeixinManager.getPermanentCode(authCode, appId);
        log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,corpAuthInfo,corpAuthInfo={}", corpAuthInfoResult);
        if(!corpAuthInfoResult.isSuccess()){
            return new Result<String>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), corpAuthInfoResult.getMsg(),null);
        }
        QyweixinGetPermenantCodeRsp corpAuthInfo = corpAuthInfoResult.getData();
        //保存应用信息
        //投递
        eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, appId, EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD, null, "create_auth", new Gson().toJson(corpAuthInfo), null);
        corpManager.saveCorpInfoTask(corpAuthInfo,appId);

        //迁移企业账号信息
        String corpId = corpAuthInfo.getAuth_corp_info().getCorpid();//this.refreshEnterpriseAccount(corpAuthInfo.getAuth_corp_info().getCorpid(), appId, null);
        log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,corpId={}", corpId);

        //订单表一定为密文的corpId，这里需要做转换
        //代客下单，订单信息是没有operator_id的，所以，代客下单后，客户在企业微信安装完crm应用，需要更新代客下单的订单上的operator_id字段，因为俊文需要这个字段来创建企业管理员帐号
        QyweixinOrderInfoBo qyweixinOrderInfoBo = qyweixinOrderInfoDao.getLatestPaidOrder(corpId,appId,null);
        log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,qyweixinOrderInfoBo={}",qyweixinOrderInfoBo);
        if(qyweixinOrderInfoBo!=null && qyweixinOrderInfoBo.getOrderFrom()!=0 && StringUtils.isEmpty(qyweixinOrderInfoBo.getOperatorId())) {
            qyweixinOrderInfoBo.setOperatorId(corpAuthInfo.getAuth_user_info().getUserid());
            qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
            log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,saveOrUpdateOrder");
        }

        if(corpManager.isManualBinding(corpId))
            return new Result<String>().Result(ErrorRefer.MANUAL_BINDING_NOT_SUPPORTED.getCode(), "手动绑定不支持这种操作",null);

//        com.facishare.open.qywx.accountbind.result.Result<String> resultAccount = qyweixinAccountBindService.outEaToFsEa(SourceTypeEnum.QYWX.getSourceType(),
//                corpId,null);
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> accountBindResult =
                qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), corpId);
        log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,resultAccount={}",accountBindResult);
        boolean isOpen = Boolean.FALSE;
        if(CollectionUtils.isEmpty(accountBindResult.getData()) || accountBindResult.getData().stream()
                .anyMatch(bindInfo -> bindInfo.getStatus() == 100)) {
            isOpen = Boolean.TRUE;
        }

        if(isOpen){
            //没有绑定关系，或者以前开通失败的
            //创建一个线程，一分钟后检查企业是否创建成功
            new Thread(() -> enterpriseOpenMonitor(corpId, appId)).start();
            Result<String> result = corpManager.initCorpEvent(corpAuthInfo, appId, authCode);
            log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,initCorpEvent.result={}",result);
            if(result.isSuccess()){
                String ticket = genFsTicket(appId, corpId, corpAuthInfo.getAuth_user_info().getUserid(),null).getData();
                log.info("trace getAuthInfoDoInitCorp fsEa:{} corpId:{} appId:{} authCode:{} 发送消息开通新的纷享账号", fsEa,
                        corpId, appId, authCode);
                dataPersistorManager.installCRM(corpId,
                        corpAuthInfo.getAuth_corp_info().getCorp_name(), appId, true);
                return new Result<>(ticket);
            } else {
                log.error("trace getAuthInfoDoInitCorp fsEa:{} corpId:{} appId:{} authCode:{} error 初始化纷享企业信息异常",
                        fsEa, corpId, appId, authCode);
                return result;
            }
        } else {
            //存在企业绑定关系， 只保存企业微信信息，订单上线后判断是否存在未处理订单
            String ticket = genFsTicket(appId, corpId, corpAuthInfo.getAuth_user_info().getUserid(),null).getData();
            log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,ticket={}",ticket);
            log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,fsEa:{} corpId:{} appId:{}, authCode:{} 存在企业绑定关系, 保存企业微信信息", fsEa,
                    corpId, appId, authCode);
            dataPersistorManager.installCRM(corpId,
                    corpAuthInfo.getAuth_corp_info().getCorp_name(), appId, false);
            //原来是initUpdateCorpEvent,但在线订单上线后逻辑会和初次开通时一样，故再改initCorpEvent
            //现在调用此接口的都是企微回调事件（creat_auth），企业可能会重新安装，不需要再次调用
//            Result<String> result = corpManager.initCorpEvent(corpAuthInfo, appId, authCode);
//            log.info("QyweixinGatewayInnerServiceImpl.getAuthInfoDoInitCorp,initCorpEvent.result2={}",result);
//            if(!result.isSuccess()){
//                log.error("trace initCorpEvent for update fsEa:{} corpId:{} appId:{}, authCode:{} error " +
//                        "重新添加纷享CRM信息异常", fsEa, corpId, appId, authCode);
//            }
            // 在安装CRM应用时》检测安装了通讯录应用 》 全量给已绑定的用户发欢迎消息
            if(ConfigCenter.crmAppId.equals(appId)){
                corpManager.sendMsgAfterInstallCRM(corpId, accountBindResult.getData().get(0).getFsEa());
            }

            return new Result<>(ticket);
        }
    }

    @Override
    public Result<String> genFsTicket(String appId, String corpId, String userId, String fsEa) {
        log.info("genFsTicket,appId={},corpId={},userId={},fsEa={}",appId,corpId,userId,fsEa);
        if(StringUtils.containsIgnoreCase(appId,"@")) {
            List<String> items = Splitter.on("@").splitToList(appId);
            appId = items.get(0);
            log.info("genFsTicket,appId={}",appId);
        }
        String ticketFormat = "corpId=%s&appId=%s&userId=%s&timestamp=%s&fsEa=%s";
        long timestamp = System.currentTimeMillis();
        String ticket = String.format(ticketFormat, corpId, appId, userId, timestamp, fsEa);
        UserTicketModel ticketModel = new UserTicketModel(corpId, appId, userId, timestamp, fsEa);
        String ticketMd5 = "";
        try {
            ticketMd5 = MD5Helper.getStringMD5(ticket);
            log.info("trace createTicket key:{}", "qywx_user_ticket" + "_" + ticketMd5);
            redisDataSource.getRedisClient().set("qywx_user_ticket" + "_" + ticketMd5, JSONObject.toJSONString(ticketModel));
            redisDataSource.getRedisClient().expire("qywx_user_ticket" + "_" + ticketMd5, 10 * 60);  //10分钟有效
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            log.error("trace createTicket exception ,", e);
            return null;
        }
        return new Result<>(ticketMd5);
    }

    @Override
    public String getRegisterCode(String templateId) {
        return qyWeixinManager.getRegisterCode(templateId).getData();
    }

    @Override
    public Result<CorpTicketResult> loginAuth(String authCode, String state,String fsEa) {

        com.facishare.open.qywx.accountinner.result.Result<QyweixinLoginInfoRsp> loginInfoResult = qyWeixinManager.getWebLoginUserInfo(authCode);
        log.info("QyweixinGatewayInnerServiceImpl.loginAuth,loginInfo={}",loginInfoResult);
        if(!loginInfoResult.isSuccess() || ObjectUtils.isEmpty(loginInfoResult.getData())) {
            return new Result<CorpTicketResult>().addError(ErrorRefer.INTERNAL_ERROR.getCode(),
                    loginInfoResult.getMsg(),null);
        }
        return loginAuth(loginInfoResult.getData(),state,fsEa);
    }

    @Override
    public Result<CorpTicketResult> loginAuth(QyweixinLoginInfoRsp loginInfo, String state, String fsEa) {
        //拼接ticket
        String ticket = genFsTicket(state, loginInfo.getCorp_info().getCorpid(), loginInfo.getUser_info().getUserid(),fsEa).getData();

        ParallelUtils.createBackgroundTask().submit(() ->
        {
            updateProfileImage(loginInfo.getCorp_info().getCorpid(),
                    loginInfo.getUser_info().getUserid(),
                    loginInfo.getUser_info().getAvatar());
        }).run();
        CorpTicketResult corpTicketResult= CorpTicketResult.builder()
                .corpId(loginInfo.getCorp_info().getCorpid())
                .ticket(ticket).build();
        return new Result<>(corpTicketResult);
    }

    @Override
    public Result<QyweixinLoginInfoRsp> code2WebLoginUserInfo(String code) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinLoginInfoRsp> loginInfoResult = qyWeixinManager.getWebLoginUserInfo(code);
        log.info("QyweixinGatewayInnerServiceImpl.code2WebLoginUserInfo,loginInfo={}",loginInfoResult);
        if(!loginInfoResult.isSuccess() || ObjectUtils.isEmpty(loginInfoResult.getData())) {
            return new Result<QyweixinLoginInfoRsp>().addError(ErrorRefer.INTERNAL_ERROR.getCode(),
                    loginInfoResult.getMsg(),null);
        }
        return new Result<>(loginInfoResult.getData());
    }

    @Override
    public Result<Object> code2AppLoginUserInfo(String code, String appId, String outEa) {
        com.facishare.open.qywx.accountinner.result.Result<Object> userInfoResult = qyWeixinManager.getAppLoginUserInfo(code, appId, outEa);
        log.info("QyweixinGatewayInnerServiceImpl.code2CorpId,userInfo={},code={}", userInfoResult, code);
        if(!userInfoResult.isSuccess() || ObjectUtils.isEmpty(userInfoResult.getData())) {
            return new Result<>().addError(ErrorRefer.INTERNAL_ERROR.getCode(),
                    userInfoResult.getMsg(),null);
        }
        return new Result<>(userInfoResult.getData());
    }

    @Override
    public Result<QyweixinRepUserDetailInfoRsp> getRepAppLoginUserInfo(String appId, String corpId, String code) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinRepUserDetailInfoRsp> result = qyWeixinManager.getRepUserInfoByCode(appId, corpId, code);
        return new Result<>(result.getData());
    }

    /**
     * OAuth授权登录
     * @param code
     * @param appId  应用id
     * @return
     */
    @Override
    public Result<CorpTicketResult> appAuth(String code, String appId,String fsEa) {
        com.facishare.open.qywx.accountinner.result.Result<Object> userInfoResult = qyWeixinManager.getAppLoginUserInfo(code, appId, null);
        log.info("QyweixinGatewayInnerServiceImpl.appAuth,userInfo={},code={}", userInfoResult, code);
        if (!userInfoResult.isSuccess() || ObjectUtils.isEmpty(userInfoResult.getData())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(),
                    userInfoResult.getMsg(),null);
        }
        return appAuth(userInfoResult.getData(),appId,fsEa);
    }

    @Override
    public Result<CorpTicketResult> appAuth(Object userInfo,String appId,String fsEa) {
        CorpTicketResult corpTicketResult = new CorpTicketResult();
        //拼接ticket
        if (userInfo instanceof QyweixinUserDetailInfoRsp) {
            QyweixinUserDetailInfoRsp userDetailInfoRsp = (QyweixinUserDetailInfoRsp) userInfo;
            String ticket = genFsTicket(appId, userDetailInfoRsp.getCorpid(), userDetailInfoRsp.getUserid(), fsEa).getData();

            ParallelUtils.createBackgroundTask().submit(() -> updateProfileImage(userDetailInfoRsp.getCorpid(),
                    userDetailInfoRsp.getUserid(), userDetailInfoRsp.getAvatar())).run();

            dataPersistorManager.userAppLoginLog(userDetailInfoRsp.getCorpid(), appId, userDetailInfoRsp.getUserid());
            corpManager.saveQyweixinAdminMobile(userDetailInfoRsp, appId);

            log.info("trace login, appid:{}, corpid:{}, userid:{}, name:{}, ticket:{}",
                    appId, userDetailInfoRsp.getCorpid(), userDetailInfoRsp.getUserid(), userDetailInfoRsp.getName(), ticket);
            corpTicketResult.setCorpId(userDetailInfoRsp.getCorpid());
            corpTicketResult.setTicket(ticket);
            return new Result<>(corpTicketResult);

        } else if (userInfo instanceof QyweixinUserSimpleInfoRsp) {
            QyweixinUserSimpleInfoRsp userSimpleInfoRsp = (QyweixinUserSimpleInfoRsp) userInfo;

            String ticket = genFsTicket(appId, userSimpleInfoRsp.getCorpId(), userSimpleInfoRsp.getUserId(), fsEa).getData();

            dataPersistorManager.userAppLoginLog(userSimpleInfoRsp.getCorpId(), appId, userSimpleInfoRsp.getUserId());

            log.info("trace login, appid:{}, corpid:{}, userid:{}, ticket:{}",
                    appId, userSimpleInfoRsp.getCorpId(), userSimpleInfoRsp.getUserId(), ticket);
            corpTicketResult.setTicket(ticket);
            corpTicketResult.setCorpId(userSimpleInfoRsp.getCorpId());
            return new Result<>(corpTicketResult);
        }

        //String errMsg = String.format("%s,oauth code=%s", ErrorRefer.GET_AUTH_TICKET_FAILED.getQywxCode(), code);
        return Result.newInstance(ErrorRefer.GET_AUTH_TICKET_FAILED);
    }

    /**
     * 根据企业微信企业ID绑定的纷享企业所在的云环境，获取对应的纷享企业所在的云环境的域名，比如https://www.fxiaoke.com,https://crm.unicloudea.com/
     * @param corpId 企业微信企业ID
     * @return
     */
    @Override
    public String getDomainByCorpId(String corpId){
        log.info("QyweixinGatewayInnerServiceImpl.getDomainByCorpId,corpId={}",corpId);
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        log.info("QyweixinGatewayInnerServiceImpl.getDomainByCorpId,enterpriseMappingList={}",enterpriseMappingList);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            corpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
            log.info("QyweixinGatewayInnerServiceImpl.getDomainByCorpId,corpId2={}",corpId);
            enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
            if(CollectionUtils.isEmpty(enterpriseMappingList)) {
                throw new RuntimeException(i18NStringManager.get2(I18NStringEnum.s154.getI18nKey(),
                        null,
                        null,
                        String.format(I18NStringEnum.s154.getI18nValue(),corpId),
                        Lists.newArrayList(
                                corpId
                        )));
            }
        }

        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);

        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(enterpriseMapping.getFsEa());
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        Map<String, String> cloudDomainEa = new Gson().fromJson(ConfigCenter.CLOUD_DOMAIN_EA, new TypeToken<Map<String, String>>() {
        });
        if(ObjectUtils.isNotEmpty(result) && cloudDomainEa.containsKey(enterpriseMapping.getFsEa())) {
            log.info("QyweixinGatewayInnerServiceImpl.getDomainByCorpId,old={},new={}", result.getEnterpriseData().getDomain(), cloudDomainEa.get(enterpriseMapping.getFsEa()));
            result.getEnterpriseData().setDomain(cloudDomainEa.get(enterpriseMapping.getFsEa()));
        }
        log.info("QyweixinGatewayInnerServiceImpl.getDomainByCorpId,result={}",result);
        return result.getEnterpriseData().getDomain();
    }

    @Override
    public String sendMessageTest() {
        String data="{\"avatar\":\"http://p.qlogo.cn/bizmail/JiaQiaZOdbQicibtjvI4zINiazK01xGff3R1Al4ibYkYvar3iaWp3L1IZahLg/0\",\"corpId\":\"ww4ba39487c1f49492\",\"corpName\":\"测试小刘企业\",\"privilege\":{\"allowParty\":[1],\"allowTag\":[1],\"allowUser\":[\"6121728\",\"06221101\"]},\"appId\":\"wx4c7edab730f4fdc9\",\"userId\":\"LiuWei3\",\"userName\":\"刘威\"}";

        String TAG_ENTERPRISE = "qywx_enterprise_order";
        QyweixinEnterpriseOrder qyweixinAddEnterprise = new Gson().fromJson(data, QyweixinEnterpriseOrder.class);
        Message msg = new Message();
        msg.setTags(TAG_ENTERPRISE);
        msg.setBody(qyweixinAddEnterprise.toProto());
        SendResult sendResult = qywxEventNotifyMQSender.send(msg);
        log.info("trace installAuth from qywx send mq msg sendResult:{}, message:{}, body:{}", sendResult, msg, JSONObject.toJSONString(qyweixinAddEnterprise));
        return sendResult.getSendStatus().name();
    }

    /**
     * 获取企业微信平台上，一个企业对应用的授权信息。
     *
     * @param appID  : 企业微信开放平台分配的appid
     * @param corpId :企业微信平台分配给企业的账号
     * @return : @see   QywxCorpAuthorizeInfo
     */
    @Override
    public Result<QywxCorpAuthorizeInfo> getQywxCorpAuthorizeInfo(String appID, String corpId) {
        QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
        qyweixinCorpBindBo.setAppId(appID);
        qyweixinCorpBindBo.setCorpId(corpId);
        List<QyweixinCorpBindBo> qyweixinCorpBindBoList = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
        if(qyweixinCorpBindBoList.isEmpty()) {
            return new Result<>();
        }
        if(qyweixinCorpBindBoList.size() > 1) {
            log.error("get more than one bind records, appID:{}, corpId:{}, bind records:{} ", appID, corpId, qyweixinCorpBindBoList);
            return new Result<>();
        }

        QywxCorpAuthorizeInfo qywxCorpAuthorizeInfoResult = new QywxCorpAuthorizeInfo();
        qyweixinCorpBindBo = qyweixinCorpBindBoList.get(0);
        qywxCorpAuthorizeInfoResult.setAgent_id(qyweixinCorpBindBo.getAgentId());
        qywxCorpAuthorizeInfoResult.setApp_id(qyweixinCorpBindBo.getAppId());
        qywxCorpAuthorizeInfoResult.setCorp_id(qyweixinCorpBindBo.getCorpId());
        qywxCorpAuthorizeInfoResult.setCorp_name(qyweixinCorpBindBo.getCorpName());
        qywxCorpAuthorizeInfoResult.setPermanent_code(qyweixinCorpBindBo.getPermanentCode());
        qywxCorpAuthorizeInfoResult.setStatus(qyweixinCorpBindBo.getStatus());
        return new Result(qywxCorpAuthorizeInfoResult);
    }

    @Override
    public Result<QywxAccessTokenInfo> getAccessTokenInfo(String fsEnterpriseAccount, String appId) {
        log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,fsEnterpriseAccount={},appId={}",fsEnterpriseAccount,appId);
        QyweixinAccountEnterpriseMapping enterpriseMapping = qyweixinAccountBindService.fsEaToOutEaResult(SourceTypeEnum.QYWX.getSourceType(),
                fsEnterpriseAccount).getData();
        log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,enterpriseMapping={}",enterpriseMapping);
        if(enterpriseMapping==null){
            return Result.newInstance(ErrorRefer.CORP_NOT_AUTHORIZED);
        }
        return getAccessTokenInfo2(enterpriseMapping.getOutEa(),appId);
    }

    @Override
    public Result<QywxAccessTokenInfo> getAccessTokenInfo2(String corpId, String appId) {
        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId,appId);
        log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,corpBindBo={}",corpBindBo);
        if(corpBindBo==null) {
            corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId,ConfigCenter.repAppId);
            log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,corpBindBo.1={}",corpBindBo);
            if(corpBindBo!=null) {
                QyweixinCorpBindBo corpBindBo2 = qyweixinCorpBindDao.queryQyweixinCorpBind(corpBindBo.getCorpId(),appId);
                log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,corpBindBo2={}",corpBindBo2);
                if(corpBindBo2==null && StringUtils.isNotEmpty(corpBindBo.getIsvCorpId())) {
                    corpBindBo2 = qyweixinCorpBindDao.queryQyweixinCorpBind(corpBindBo.getIsvCorpId(),appId);
                    log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,corpBindBo2.1={}",corpBindBo2);
                }
                corpBindBo = corpBindBo2;
            }
        }

        if(corpBindBo==null){
            return Result.newInstance(ErrorRefer.CORP_NOT_AUTHORIZED);
        }

        log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,corpBindBo.2={}",corpBindBo);
        String agentId = corpBindBo.getAgentId();
        com.facishare.open.qywx.accountinner.result.Result<String> corpAccessTokenResult = null;
        try {
            log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,getCorpAccessToken,appId={},corpId={}",appId,corpBindBo.getCorpId());
            corpAccessTokenResult = qyWeixinManager.getCorpAccessToken(appId, corpBindBo.getCorpId(), false);
            log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,corpAccessToken={}",corpAccessTokenResult);
        } catch (Exception e) {
            log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,exception={}",e.getMessage());
        }

        if(StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newInstance(ErrorRefer.GET_ACCESS_TOKEN_FAILED);
        }

        QywxAccessTokenInfo qywxAccessTokenInfo = new QywxAccessTokenInfo();
        qywxAccessTokenInfo.setAgentId(agentId);
        qywxAccessTokenInfo.setCorpAccessToken(corpAccessTokenResult.getData());

        log.info("QyweixinGatewayInnerServiceImpl.getAccessTokenInfo,qywxAccessTokenInfo={}",qywxAccessTokenInfo);
        return new Result<>(qywxAccessTokenInfo);
    }

    @Override
    public Result<String> jscode2sessionService(String code, String appId) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinJscode2sessionRsp> jscode2sessionRsqResult = qyWeixinManager.jscode2sessionService(code, appId);
        if(jscode2sessionRsqResult.isSuccess()) {
            QyweixinJscode2sessionRsp jscode2sessionRsq = jscode2sessionRsqResult.getData();
            QyweixinMiniprogramSessionKeyBo qyweixinMiniprogramSessionKeyBo = new QyweixinMiniprogramSessionKeyBo();
            qyweixinMiniprogramSessionKeyBo.setAppId(appId);
            qyweixinMiniprogramSessionKeyBo.setCorpId(jscode2sessionRsq.getCorpid());
            qyweixinMiniprogramSessionKeyBo.setUserId(jscode2sessionRsq.getUserid());

            List<QyweixinMiniprogramSessionKeyBo> miniprogramSessionKeyBoCacheList = qyweixinMiniprogramSessionKeyDao.findByEntity(qyweixinMiniprogramSessionKeyBo);
            if(null == miniprogramSessionKeyBoCacheList || miniprogramSessionKeyBoCacheList.isEmpty()){
                qyweixinMiniprogramSessionKeyBo.setSessionKey(jscode2sessionRsq.getSession_key());
                qyweixinMiniprogramSessionKeyDao.save(qyweixinMiniprogramSessionKeyBo);
            } else {
                QyweixinMiniprogramSessionKeyBo miniprogramSessionKeyBoCache = miniprogramSessionKeyBoCacheList.get(0);
                miniprogramSessionKeyBoCache.setSessionKey(jscode2sessionRsq.getSession_key());
                qyweixinMiniprogramSessionKeyDao.update(miniprogramSessionKeyBoCache);
            }

            String key = jscode2sessionRsq.getCorpid() + jscode2sessionRsq.getUserid() + appId;
            String token = "";
            try {
                token = MD5Helper.getStringMD5(key);
                String value = JSONObject.toJSONString(jscode2sessionRsq);
                log.info("trace jscode2sessionService key:{} token:{} value:{}", key, token, value);
                redisDataSource.getRedisClient().set(token, JSONObject.toJSONString(jscode2sessionRsq));
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
            }
            return new Result<>(token);
        } else {
            return new Result<String>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), jscode2sessionRsqResult.getMsg(),null);
        }
    }

    @Override
    public Result<String> miniprogramEncryptData(String ticket, String encryptedData, String iv) {

        String jscode2sessionRsq = redisDataSource.getRedisClient().get(ticket);
        if(StringUtils.isBlank(jscode2sessionRsq)){
            return Result.newInstance(ErrorRefer.INVALID_TICKET);
        }

        QyweixinJscode2sessionRsp qyweixinJscode2sessionRsp = JSONObject.parseObject(jscode2sessionRsq, QyweixinJscode2sessionRsp.class);

        //TODO 配置小程序appId
        String miniprogramAppId = "";
        String result = QYWxCryptHelper.DecryptMiniprogramMsg(miniprogramAppId, encryptedData, qyweixinJscode2sessionRsp.getSession_key(), iv);
        if(StringUtils.isBlank(result)){
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        return new Result<>(result);
    }

    @Override
    public String getRedirectLoginUrl(String ticket, String appId,String corpId,String fromOrigin) {
        log.info("QyweixinGatewayInnerServiceImpl.getRedirectLoginUrl,ticket={},appId={},corpId={},fromOrigin={}",ticket,appId,corpId,fromOrigin);
        Map<String, String> redirectMap = new Gson().fromJson(redirectUri, new TypeToken<Map<String, String>>(){}.getType());
        String convertLoginDomain=fromOrigin;
        if(StringUtils.isEmpty(convertLoginDomain)) {
            convertLoginDomain=getDomainByCorpId(corpId);
        }
        log.info("QyweixinGatewayInnerServiceImpl.getRedirectLoginUrl,convertLoginDomain={}",convertLoginDomain);
//        if (StringUtils.isBlank(appId)) {
//            String redirectUri = redirectMap.get(crmAppId);
//            log.warn("getRedirectLoginUrl appId is null");
//            return convertLoginDomain + String.format(redirectUri.trim(), ticket);
//        }

        String redirectUri = redirectMap.get(appId);
        log.info("QyweixinGatewayInnerServiceImpl.getRedirectLoginUrl,redirectUri={}",redirectUri);
        if (StringUtils.isNotBlank(redirectUri)) {
            return convertLoginDomain + String.format(redirectUri.trim(), ticket);
        }
        String redirectUrl = qyWeixinManager.getAppMetaInfo().get(appId).getRedirectUrl();
        log.info("QyweixinGatewayInnerServiceImpl.getRedirectLoginUrl,redirectUrl={}",redirectUrl);
        return redirectUrl.contains("{ticket}") ? redirectUrl.replace("{ticket}", ticket) : redirectUrl;
    }

    @Override
    public void notifyHistoryToDoMessageGateway(List<String> eaList) {
        eaList.stream().forEach(v->{
            corpManager.notifyHistoryToDoMessage(v);
        });
    }

    public void updateCorpInfo(String corpId, String appId) {
        log.info("QyweixinGatewayInnerServiceImpl.updateCorpInfo,corpId={},appId={}",corpId,appId);
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
           return;
        }
        //更新企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> authInfoRspResult = qyWeixinManager.getCorpInfo(corpId, appId);
        if(!authInfoRspResult.isSuccess() || ObjectUtils.isEmpty(authInfoRspResult.getData())) {
            //目前发现已过期的应用也会收到change_auth事件，这时候调用获取企业信息接口就会返回错误，这里做特殊处理
            log.info("QyweixinGatewayInnerServiceImpl.updateCorpInfo,getCorpInfo exception = {}",authInfoRspResult.getMsg());
            log.info("QyweixinGatewayInnerServiceImpl.updateCorpInfo,调用企业微信获取企业信息接口失败");
            return ;
        }
        //保存应用信息
        corpManager.updateCorpInfo(authInfoRspResult.getData(), corpId, appId);
    }

    public void cancelCorpAuth(String corpId, String appId) {
        //保存绑定关系或更新
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        log.info("QyweixinGatewayInnerServiceImpl.cancelCorpAuth,qyweixinCorpBindBo={}",qyweixinCorpBindBo);

        qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.DELETE_BIND.getCode());
        int count = qyweixinCorpBindDao.update(qyweixinCorpBindBo);
        log.info("QyweixinGatewayInnerServiceImpl.cancelCorpAuth,count={}",count);
        //使用代开发授权会话留存，删除代开发应用的时候，会话留存的corpSecret和agentId也要删除
        corpManager.updateCorpMessageGenerating(corpId, qyweixinCorpBindBo);
    }

    @Override
    public void payForAppSuccessEvent(String appId, String orderId) {
        orderManager.payForAppSuccessEvent(appId, orderId);
    }

    public void resetPermanentCode(String appId, String authCode) {
        log.info("QyweixinGatewayInnerServiceImpl.resetPermanentCode, appId={}, authCode={}", appId, authCode);
        //获取永久授权码、企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetPermenantCodeRsp> corpAuthInfoResult = qyWeixinManager.getPermanentCode(authCode, appId);
        log.info("QyweixinGatewayInnerServiceImpl.resetPermanentCode,corpAuthInfo={}", corpAuthInfoResult);
        if(corpAuthInfoResult.isSuccess()){
            eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, appId, EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP, corpAuthInfoResult.getData().getAuth_corp_info().getCorpid(), "reset_permanent_code", new Gson().toJson(corpAuthInfoResult.getData()), null);
            corpManager.saveRepCorpInfoTask(corpAuthInfoResult.getData(),appId);
            //updateCorpAndAccount(corpAuthInfo.getAuth_corp_info().getCorpid());
        } else {
            log.info("QyweixinGatewayInnerServiceImpl.resetPermanentCode failed.");
        }
    }

    /**
     * 代开发授权
     * @param authCode
     * @param appId
     * @param fsEa
     * @return
     */
    public Result<String> getAuthInfo(String authCode, String appId, String fsEa) {
        log.info("QyweixinGatewayInnerServiceImpl.getAuthInfo,fsEa={}, appId={}, authCode={}", fsEa, appId, authCode);
        //获取永久授权码、企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetPermenantCodeRsp> corpAuthInfoResult = qyWeixinManager.getPermanentCode(authCode, appId);
        log.info("QyweixinGatewayInnerServiceImpl.getAuthInfo,corpAuthInfo,corpAuthInfo={}", corpAuthInfoResult);
        if (!corpAuthInfoResult.isSuccess()) {
            return new Result<String>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), corpAuthInfoResult.getMsg(),null);
        }
        QyweixinGetPermenantCodeRsp corpAuthInfo = corpAuthInfoResult.getData();
        //保存应用信息

        eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, appId, EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP, corpAuthInfo.getAuth_corp_info().getCorpid(), "create_auth", new Gson().toJson(corpAuthInfo), null);

        corpManager.saveRepInfoTask(corpAuthInfo, appId);

        //迁移企业账号信息
        //this.refreshEnterpriseAccount(corpAuthInfo.getAuth_corp_info().getCorpid(), appId, String.valueOf(corpAuthInfo.getAuth_info().getAgent().get(0).getAgentid()));

        return new Result<>();
    }

//    public Result<String> externalMigration(String authCorpId) {
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(authCorpId);
//        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
//            List<String> fsEaList = enterpriseMappingList.stream()
//                    .map(QyweixinAccountEnterpriseMapping::getFsEa)
//                    .collect(Collectors.toList());
//            qyWeixinManager.senEaList(fsEaList);
//        }
//        return new Result<>();
//    }

    @Override
    public Result<String> getExternalUserId(String isvExternalUserId) {
        QyweixinExternalContactBo externalContactBo = new QyweixinExternalContactBo();
        externalContactBo.setIsvExternalUserId(isvExternalUserId);
        List<QyweixinExternalContactBo> list = qyweixinExternalContactDao.findByEntity(externalContactBo);
        if(CollectionUtils.isEmpty(list)) {
            externalContactBo.setIsvExternalUserId(null);
            externalContactBo.setExternalUserId(isvExternalUserId);
            list = qyweixinExternalContactDao.findByEntity(externalContactBo);
            if(CollectionUtils.isEmpty(list)) {
                return new Result<>(null);
            }
        }
        return new Result<>(list.get(0).getExternalUserId());
    }

    /**
     * 更新用户在纷享侧头像
     */
    private void updateProfileImage(String corpId, String outAccount, String profileImage) {
        log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,corpId={},outAccount={},profileImage={}",
                corpId,outAccount,profileImage);
        if (org.apache.commons.lang3.StringUtils.isBlank(profileImage)
                || profileImage.equals("https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar$73ba92b5.png")) return;
        try {
            List<QyweixinAccountEmployeeMapping> employeeMappingList = qyweixinAccountSyncService.getFsAccountByOutAccount(
                    corpId, outAccount,0,null);
            log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,employeeMappingList={}", employeeMappingList);
            if(CollectionUtils.isEmpty(employeeMappingList)) return;
            String userAccount = employeeMappingList.get(0).getFsAccount();
            log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,corpId={},outAccount={},userAccount={}",
                    corpId,outAccount,userAccount);

            List<String> accountList = Splitter.on(".").splitToList(userAccount);
            if(CollectionUtils.isEmpty(accountList) || accountList.size() < 3) {
                return;
            }
            String ea = accountList.get(1);
            //需要验证ea的正确性
            com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseResult = qyweixinAccountBindService.fsEaToOutEaResult(SourceTypeEnum.QYWX.getSourceType(), ea);
            log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,enterpriseResult={}", enterpriseResult);
            //反绑定企业不更新
            if(!enterpriseResult.isSuccess() || ObjectUtils.isEmpty(enterpriseResult) || enterpriseResult.getData().getBindType() != 0) {
                return;
            }
            String uid = accountList.get(2);
            int userId = 0;
            try {
                userId = Integer.parseInt(uid);
            } catch (Exception e) {
                log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,userAccount={},parseInt exception={}",userAccount,e.getMessage());
                return;
            }

            GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
            employeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            employeeDtoArg.setEmployeeId(userId);
            GetEmployeeDtoResult employeeDto = employeeService.getEmployeeDto(employeeDtoArg);
            log.info("updateProfileImage getEmployeeDto success. employeeDtoArg:{}, profileImage:{}, employeeDto:{}",
                    employeeDtoArg, profileImage, employeeDto);
            if (ObjectUtils.isNotEmpty(employeeDto.getEmployee()) && StringUtils.isEmpty(employeeDto.getEmployee().getProfileImage())) {
                //更新通讯录的方式去更新头像
                URL url = new URL(profileImage);
                InputStream inputStream = url.openStream();
                URLConnection connection = url.openConnection();
                connection.connect();
                String contentLength = connection.getHeaderField("Content-Length");
                long length = Long.parseLong(contentLength);
                UploadFileArg uploadFileArg = new UploadFileArg();
                uploadFileArg.setEa(ea);
                uploadFileArg.setFileExt("jpg");
                uploadFileArg.setFileName("avatar.jpg");
                uploadFileArg.setFileSize(length);
                uploadFileArg.setMessageType("image");
                UploadFileResult uploadFileResult = fsFileManager.uploadFile(inputStream, uploadFileArg);
                log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,uploadFileResult={}", uploadFileResult);
                if(ObjectUtils.isNotEmpty(uploadFileResult)) {
                    String restFulUrl = HttpUrlUtils.buildUpdateObjectDataUrl("PersonnelObj");
                    Map<String, String> headerMap =
                            HttpUrlUtils.buildHeaderMap(eieaConverter.enterpriseAccountToId(ea), -10000, HttpUrlUtils.FS_OPEN_QYWX);
                    Map<String, Object> form = new HashMap<>();
                    Map<String, Object> objectDataMqp = new HashMap<>();
                    Map<String, Object> profileImageMap = new HashMap<>();
                    profileImageMap.put("ext", "jpg");
                    profileImageMap.put("filename", "avatar.jpg");
                    profileImageMap.put("path", uploadFileResult.getNpath());//N_202309_12_27d6e7c1eaff46518c467efd35deff87
                    profileImageMap.put("size", uploadFileResult.getFileSize());
                    objectDataMqp.put("profile_image", Lists.newArrayList(profileImageMap));
                    objectDataMqp.put("_id", String.valueOf(userId));
                    form.put("object_data", objectDataMqp);
                    String httpRsp = null;
                    if(CrmRateLimiter.isAllowed(null)) {
                        httpRsp = proxyOkHttpClient.postUrl(restFulUrl, form, headerMap);
                    }
                    log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,restFulUrl={},headerMap={},form={},httpRsp={}", restFulUrl, headerMap, form, httpRsp);
                }
            }
        }catch (Exception e) {
            log.info("updateProfileImage failed,exception={}", e.getMessage());
        }
    }

    private void changeCorpInfo(String corpId, String appId) {
        //更新企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> authInfoRspResult = qyWeixinManager.getCorpInfo(corpId, appId);
        if(!authInfoRspResult.isSuccess() || ObjectUtils.isEmpty(authInfoRspResult.getData())) {
            //目前发现已过期的应用也会收到change_auth事件，这时候调用获取企业信息接口就会返回错误，这里做特殊处理
            log.info("QyweixinGatewayInnerServiceImpl.updateCorpInfo,getCorpInfo exception = {}",authInfoRspResult.getMsg());
            log.info("QyweixinGatewayInnerServiceImpl.updateCorpInfo,调用企业微信获取企业信息接口失败");
            return ;
        }
        QyweixinGetAuthInfoRsp authInfoRsp = authInfoRspResult.getData();
        //迁移企业账号信息
//        corpId = this.refreshEnterpriseAccount(authInfoRsp.getAuth_corp_info().getCorpid(), appId, String.valueOf(authInfoRsp.getAuth_info().getAgent().get(0).getAgentid()));

        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return;
        }
        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
        String fsEa = enterpriseMapping.getFsEa();
        log.info("QyweixinGatewayInnerServiceImpl.changeCorpInfo,enterpriseMappingList={}", enterpriseMappingList);
        //异步自动绑定账号
        Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());
        if(autoBindEmpEnterpriseMap.containsKey(fsEa)) {
            log.info("QyweixinGatewayInnerServiceImpl.changeCorpInfo,autoBindEmpEnterpriseMap,fsEa={}",fsEa);
            contactBindInnerService.autoBindEmpAccount(fsEa, corpId);
        } else if(ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
            log.info("QyweixinGatewayInnerServiceImpl.changeCorpInfo,AUTO_BIND_ACCOUNT_EA,fsEa={}",fsEa);
            contactBindInnerService.changAuthEventToBindAccount(fsEa, corpId);
        }
    }

    @Deprecated
    @Override
    public Result<QyweixinExternalContactRsp> getExternalContactDetail(String fsEa, String externalUserId) {
        return getExternalContactDetail2(fsEa, externalUserId ,null);
    }

    @Override
    public Result<QyweixinExternalContactRsp> getExternalContactDetail2(String fsEa, String externalUserId, String outEa) {
        Result<QyweixinAccountEnterpriseMapping> enterpriseMapping = qyweixinAccountBindInnerService.getEnterpriseMapping2(fsEa, outEa);
        if(enterpriseMapping.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        outEa = enterpriseMapping.getData().getOutEa();
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(outEa,
                ConfigCenter.repAppId);
        if(qyweixinCorpBindBo==null) {
            return Result.newInstance(ErrorRefer.REP_APP_NOT_ENABLE);
        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactRsp> externalContactDetailResult = qyWeixinManager.getExternalContactDetail(qyweixinCorpBindBo.getPermanentCode(),
                outEa, externalUserId, null);
        if(!externalContactDetailResult.isSuccess() || ObjectUtils.isEmpty(externalContactDetailResult.getData())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), externalContactDetailResult.getMsg(),null);
        }
        return new Result<>(externalContactDetailResult.getData());
    }

    @Override
    public Result<QyweixinGroupChatResult> getGroupChat(QyweixinGroupChatInfo groupChatInfo) {
        if(ObjectUtils.isEmpty(groupChatInfo)) {
            return new Result<>();
        }
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
                groupChatInfo.getEa(),
                groupChatInfo.getOutEa());
        if(!result.isSuccess() && ObjectUtils.isEmpty(result)) {
            return new Result<>();
        }
        String corpId = result.getData().getOutEa();
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGroupChatResult> groupChatListResult = qyWeixinManager.getGroupChatList(groupChatInfo, corpId);
        if(!groupChatListResult.isSuccess() || ObjectUtils.isEmpty(groupChatListResult.getData())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), groupChatListResult.getMsg(),null);
        }
        return new Result<>(groupChatListResult.getData());
    }

    @Override
    public Result<GroupChatListResult> getGroupChatList(QyweixinGroupChatInfo groupChatInfo) {
        GroupChatListResult groupChatListResult = null;
        if(redisDataSource.getRedisClient().exists(ConfigCenter.QYWX_GROUP_CHAT + groupChatInfo.getEa())) {
            log.info("QyweixinAccountSyncServiceImpl.getGroupChatList,ea={}", groupChatInfo.getEa());
            String getGroupChatList = redisDataSource.getRedisClient().get(ConfigCenter.QYWX_GROUP_CHAT + groupChatInfo.getEa());
            log.info("QyweixinAccountSyncServiceImpl.getGroupChatList,getGroupChatList={}", getGroupChatList);
            if(StringUtils.isNotEmpty(getGroupChatList)) {
                groupChatListResult = new Gson().fromJson(getGroupChatList, GroupChatListResult.class);
                //判断用户是否在该群聊
                if(StringUtils.isNotEmpty(groupChatInfo.getUserId())) {
                    groupChatListResult = this.getGroupChatListByUsrId(groupChatListResult, groupChatInfo.getUserId());
                }
            }
        } else {
            Thread thread = new Thread(() -> this.getGroupChatDetailList(groupChatInfo));
            thread.start();
        }
        return new Result<>(groupChatListResult);
    }

    private void getGroupChatDetailList(QyweixinGroupChatInfo groupChatInfo) {
        GroupChatListResult groupChatListResult = new GroupChatListResult();
        List<QyweixinGroupChatDetail.GroupChat> groupChatList = new ArrayList<>();
        String nextCursor;
        do {
            Result<QyweixinGroupChatResult> result = getGroupChat(groupChatInfo);
            log.info("QyweixinAccountSyncServiceImpl.getGroupChatDetailList,result={}", result);
            if(result.isSuccess() && result.getData()!=null) {
                for(QyweixinGroupChat groupChat : result.getData().getGroupChatList()) {
                    Result<QyweixinGroupChatDetail> groupChatDetailResult = getGroupChatDetail2(groupChatInfo.getEa(),
                            groupChat.getChatId(),
                            ConfigCenter.repAppId,
                            groupChatInfo.getOutEa());
                    log.info("QyweixinAccountSyncServiceImpl.getGroupChatDetailList,groupChatDetailResult={}", groupChatDetailResult);
                    if(groupChatDetailResult!=null
                            && ObjectUtils.isNotEmpty(groupChatDetailResult.getData())
                            && ObjectUtils.isNotEmpty(groupChatDetailResult.getData().getGroup_chat())) {
                        groupChatList.add(groupChatDetailResult.getData().getGroup_chat());
                    }
                }
                nextCursor = result.getData().getNextCursor();
            } else {
                nextCursor = null;
            }
            log.info("QyweixinAccountSyncServiceImpl.getGroupChatDetailList,groupChatListResult={}", groupChatListResult);
        } while (StringUtils.isNotEmpty(nextCursor));
        if(CollectionUtils.isNotEmpty(groupChatList)) {
            groupChatListResult.setGroupChatList(groupChatList);
            redisDataSource.getRedisClient().set(ConfigCenter.QYWX_GROUP_CHAT + groupChatInfo.getEa(), JSONObject.toJSONString(groupChatListResult));
            redisDataSource.getRedisClient().expire(ConfigCenter.QYWX_GROUP_CHAT + groupChatInfo.getEa(), 60*60*6);
        }
    }

    private GroupChatListResult getGroupChatListByUsrId(GroupChatListResult groupChatListResult, String userId) {
        log.info("QyweixinAccountSyncServiceImpl.getGroupChatListByUsrId,userId={},groupChatListResult={}", userId,  groupChatListResult);
        GroupChatListResult groupChatListByUserIdResult = new GroupChatListResult();
        if(ObjectUtils.isEmpty(groupChatListResult) || StringUtils.isEmpty(userId)) {
            return groupChatListResult;
        }
        List<QyweixinGroupChatDetail.GroupChat> groupChatList = new ArrayList<>();
        for(QyweixinGroupChatDetail.GroupChat groupChat : groupChatListResult.getGroupChatList()) {
            if(CollectionUtils.isNotEmpty(groupChat.getMember_list())) {
                for(QyweixinGroupChatDetail.GroupChat.MemberList memberList : groupChat.getMember_list()) {
                    if(userId.equalsIgnoreCase(memberList.getUserid())) {
                        groupChatList.add(groupChat);
                        break;
                    }
                }
            }
            if(!groupChatList.contains(groupChat) && CollectionUtils.isNotEmpty(groupChat.getAdmin_list())) {
                for(QyweixinGroupChatDetail.GroupChat.UserIdModel userIdModel : groupChat.getAdmin_list()) {
                    if(userId.equalsIgnoreCase(userIdModel.getUserid())) {
                        groupChatList.add(groupChat);
                        break;
                    }
                }
            }
        }
        log.info("QyweixinAccountSyncServiceImpl.getGroupChatListByUsrId,userId={},groupChatList={}", userId,  groupChatList);
        if(CollectionUtils.isNotEmpty(groupChatList)) {
            groupChatListByUserIdResult.setGroupChatList(groupChatList);
        }
        return groupChatListByUserIdResult;
    }

    @Deprecated
    @Override
    public Result<QyweixinGroupChatDetail> getGroupChatDetail(String fsEa, String chatId,String appId) {
        return getGroupChatDetail2(fsEa, chatId, appId, null);
    }

    @Override
    public Result<QyweixinGroupChatDetail> getGroupChatDetail2(String fsEa, String chatId, String appId, String outEa) {
        Result<QyweixinAccountEnterpriseMapping> enterpriseMapping = qyweixinAccountBindInnerService.getEnterpriseMapping2(fsEa,outEa);
        if(enterpriseMapping.getData()==null) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }
        outEa = enterpriseMapping.getData().getOutEa();
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(outEa,
                appId);
        if(qyweixinCorpBindBo==null) {
            return Result.newInstance(ErrorRefer.REP_APP_NOT_ENABLE);
        }
        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getCorpAccessToken(appId, outEa, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return new Result<>(ErrorRefer.TOKEN_ERROR.getCode(),tokenResult.getMsg(),null);
        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGroupChatDetail> qyweixinGroupChatDetailResult = qyWeixinManager.getGroupChatDetail(tokenResult.getData(), chatId);
        log.info("QyweixinGatewayInnerServiceImpl.getGroupChatDetail,qyweixinGroupChatDetail={}", qyweixinGroupChatDetailResult);
        QyweixinGroupChatDetail qyweixinGroupChatDetail = qyweixinGroupChatDetailResult.getData();
        if(qyweixinGroupChatDetailResult.isSuccess()
                && ObjectUtils.isNotEmpty(qyweixinGroupChatDetail)
                && ObjectUtils.isNotEmpty(qyweixinGroupChatDetail.getGroup_chat())) {
            //获取外部联系人头像
            //1、筛选出type为2的群成员，即为 外部联系人
            //2、查询数据库有无该外部联系人或者头像，如果没有，就更新再获取
            //3、添加到群聊详情
            List<String> externalUserIdList = qyweixinGroupChatDetail.getGroup_chat().getMember_list().stream()
                    .filter(v -> v.getType() == 2)
                    .map(QyweixinGroupChatDetail.GroupChat.MemberList::getUserid)
                    .collect(Collectors.toList());
            List<String> updateExternalUserIdList = new LinkedList<>();
            if(CollectionUtils.isNotEmpty(externalUserIdList)) {
                List<QyweixinExternalContactBo> externalUsers = qyweixinExternalContactDao.findByExternalUserIds(outEa, externalUserIdList);
                log.info("QyweixinGatewayInnerServiceImpl.getGroupChatDetail,externalUserIdList={},externalUsers={}", externalUserIdList, externalUsers);
                List<QyweixinExternalContactBo> distinctExternalUserList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(externalUsers)) {
                    //去查
                    for (QyweixinExternalContactBo externalDetailInfoRsp : externalUsers) {
                        List<QyweixinExternalContactBo> list = distinctExternalUserList.stream()
                                .filter(rsp -> StringUtils.isEmpty(rsp.getAvatar()))
                                .filter(rsp -> StringUtils.equalsIgnoreCase(externalDetailInfoRsp.getExternalUserId(), rsp.getExternalUserId()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(list)) {
                            distinctExternalUserList.add(externalDetailInfoRsp);
                        }
                    }
                    log.info("QyweixinGatewayInnerServiceImpl.getGroupChatDetail,distinctExternalUserList={}", distinctExternalUserList);
                    Map<String, QyweixinExternalContactBo> externalUserMap = distinctExternalUserList.stream()
                            .collect(Collectors.toMap(QyweixinExternalContactBo::getExternalUserId, Function.identity(), (v1, v2) -> v1));
                    for(QyweixinGroupChatDetail.GroupChat.MemberList member : qyweixinGroupChatDetail.getGroup_chat().getMember_list()) {
                        if(member.getType() == 2) {
                            if(ObjectUtils.isNotEmpty(externalUserMap.get(member.getUserid()))
                                    && StringUtils.isNotEmpty(externalUserMap.get(member.getUserid()).getAvatar())) {
                                member.setAvatar(externalUserMap.get(member.getUserid()).getAvatar());
                            } else {
                                updateExternalUserIdList.add(member.getUserid());
                            }
                        }
                    }
                } else {
                    updateExternalUserIdList.addAll(externalUserIdList);
                }
            } else {
                updateExternalUserIdList.addAll(externalUserIdList);
            }
            if(CollectionUtils.isNotEmpty(updateExternalUserIdList)) {
                log.info("QyweixinGatewayInnerServiceImpl.getGroupChatDetail,updateExternalUserIdList={}", updateExternalUserIdList);
                String outEa2 = outEa;
                //异步更新客户信息
                Thread thread = new Thread(() -> qyWeixinManager.getDetailByExternalUserIds(outEa2, updateExternalUserIdList));
                thread.start();
            }
            return new Result<>(qyweixinGroupChatDetail);
        }
        return new Result<>(qyweixinGroupChatDetail);
    }

    @Deprecated
    @Override
    public Result<GetPermitUserListResult> getPermitUserList(String fsEa) {
        return getPermitUserList2(fsEa, null);
    }

    @Override
    public Result<GetPermitUserListResult> getPermitUserList2(String fsEa, String outEa) {
        List<String> secret = qyweixinCorpBindDao.getConversionSecret(fsEa, outEa);
        if(CollectionUtils.isEmpty(secret)) {
            return null;
        }
        log.info("QyweixinGatewayInnerServiceImpl.getPermitUserList secret={}, ea={}", secret, fsEa);
        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
        if(StringUtils.isEmpty(outEa)) {
            outEa = qyweixinCorpBindDao.getOutEa(fsEa, ConfigCenter.crm_domain);
        }
        //走自建应用或代开发
        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newInstance(ErrorRefer.TOKEN_ERROR);
        }
        com.facishare.open.qywx.accountinner.result.Result<GetPermitUserListResult> result = qyWeixinManager.getPermitUserList(tokenResult.getData());
        if(!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), result.getMsg(),null);
        }
        return new Result<>(result.getData());
    }

    @Override
    public Result<CheckRoomAgreeResult> checkRoomAgree(String fsEa, String roomId) {
        return checkRoomAgree2(fsEa, roomId, null);
    }

    @Override
    public Result<CheckRoomAgreeResult> checkRoomAgree2(String fsEa, String roomId, String outEa) {
        List<String> secret = qyweixinCorpBindDao.getConversionSecret(fsEa, outEa);
        if(CollectionUtils.isEmpty(secret)) {
            return null;
        }
        log.info("QyweixinGatewayInnerServiceImpl.checkRoomAgree secret={}, ea={}", secret, fsEa);
        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
        if(StringUtils.isEmpty(outEa)) {
            outEa = qyweixinCorpBindDao.getOutEa(fsEa, ConfigCenter.crm_domain);
        }
        //走自建应用或代开发
        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newInstance(ErrorRefer.TOKEN_ERROR);
        }
        log.info("QyweixinGatewayInnerServiceImpl.checkRoomAgree,token={}",tokenResult);
        com.facishare.open.qywx.accountinner.result.Result<CheckRoomAgreeResult> result = qyWeixinManager.checkRoomAgree(tokenResult.getData(),roomId);
        if(!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), result.getMsg(),null);
        }
        return new Result<>(result.getData());
    }

    @Override
    public Result<CheckSingleAgreeResult> checkSingleAgree(QyweixinCheckSingleAgreeArg arg) {
        List<String> secret = qyweixinCorpBindDao.getConversionSecret(arg.getFsEa(), arg.getOutEa());
        if(CollectionUtils.isEmpty(secret)) {
            return null;
        }
        log.info("QyweixinGatewayInnerServiceImpl.checkSingleAgree secret={}, ea={}", secret, arg.getFsEa());
        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
        String outEa = arg.getOutEa();
        if(StringUtils.isEmpty(arg.getOutEa())) {
            outEa = qyweixinCorpBindDao.getOutEa(arg.getFsEa(), ConfigCenter.crm_domain);
        }
        //走自建应用或代开发
        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return new Result<>(ErrorRefer.TOKEN_ERROR.getCode(), tokenResult.getMsg(),null);
        }
        log.info("QyweixinGatewayInnerServiceImpl.checkSingleAgree,token={}",tokenResult);
        com.facishare.open.qywx.accountinner.result.Result<CheckSingleAgreeResult> result = qyWeixinManager.checkSingleAgree(tokenResult.getData(), arg);
        if(!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
            return new Result<>(ErrorRefer.QUERRY_EMPTY.getCode(), result.getMsg(),null);
        }
        return new Result<>(result.getData());
    }

    /**
     * 代开发应用回调通讯录变更事件
     * @param plainMsg
     */
    private void changeContacts(String plainMsg) {
        Gson gson = new Gson();
        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,plainMsg={}", plainMsg);
        //RepChangeContactXml repChangeContactXml = XmlParser.fromXml(plainMsg, RepChangeContactXml.class);
        RepChangeContactXml repChangeContactXml = XStreamUtils.parseXml(plainMsg, RepChangeContactXml.class);
        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,repChangeContactXml={}", repChangeContactXml);
        String corpId = repChangeContactXml.getToUserName();
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = null;

        try {
            if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "update_party")) {
                //单独改这个逻辑是因为企微对于部门更新事件有以下几点问题
                //1、父部门在可见范围内，在组织架构，变更子部门的父部门，新的父部门不在可见范围，企微回调事件只有一个当前部门的变更事件，而且还能从企微那里获取到当前部门的数据，无法得知当前部门和当前部门的子部门和相关人员是否在可见范围
                //2、当前部门不在可见范围，在组织架构，变更当前部门的父部门，新的部门不在可见范围，企微回调事件只有当前部门和部门下人员的变更事件，没有当前部门下的子部门变更事件，无法得知当前部门下的子部门是否在可见范围
                saveAppEvent(corpId, ConfigCenter.repAppId, repChangeContactXml.getEvent(), plainMsg);
            } else {
                List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
                if(CollectionUtils.isEmpty(enterpriseMappingList)) {
                    return;
                }
                boolean isHasManual = enterpriseMappingList.stream()
                        .anyMatch(mapping -> mapping.getBindType() == 1);
                if(!isHasManual) {
                    return;
                }

                if(repChangeContactXml!=null && (StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "create_user")
                        || StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "update_user"))) {
                    List<OutUserInfoDoc> userInfoDocs = outUserInfoManger.queryUserInfos(corpId);
                    if(CollectionUtils.isEmpty(userInfoDocs)) {
                        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,init contacts,outEa={}", corpId);
                        saveAppEvent(corpId, ConfigCenter.repAppId, repChangeContactXml.getEvent(), plainMsg);
                    } else {
                        userDetailInfoRspResult = qyWeixinManager.getUserInfo(ConfigCenter.repAppId, corpId, repChangeContactXml.getUserID());
                        if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                            List<OutUserInfoDoc> outUserInfoDocs = new LinkedList<>();
                            QyweixinUserDetailInfoRsp rsp = userDetailInfoRspResult.getData();
                            OutUserInfoDoc outUserInfoDoc = new OutUserInfoDoc();
                            //outUserInfoDoc.setId(ObjectId.get());
                            outUserInfoDoc.setOutEa(corpId);
                            outUserInfoDoc.setOutUserId(rsp.getUserid());
                            outUserInfoDoc.setOutUserInfo(gson.toJson(rsp));
                            outUserInfoDoc.setCreateTime(System.currentTimeMillis());
                            outUserInfoDoc.setUpdateTime(System.currentTimeMillis());
                            outUserInfoDocs.add(outUserInfoDoc);
                            BulkWriteResult bulkWriteResult = outUserInfoManger.batchReplace(outUserInfoDocs);
                            log.info("QyweixinGatewayInnerServiceImpl.changeContacts,user,bulkWriteResult={}", bulkWriteResult);
                        } else {
                            //更改人员的时候，如果人员不在可见范围，也需要移除
                            if(userDetailInfoRspResult.getCode().equals("60011")) {
                                DeleteResult deleteResult = outUserInfoManger.deleteUserInfoByUserId(corpId, repChangeContactXml.getUserID());
                                log.info("QyweixinGatewayInnerServiceImpl.changeContacts,update user,deleteResult={}", deleteResult);
                            }
                        }
                    }
                }
                if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "delete_user")) {
                    DeleteResult deleteResult = outUserInfoManger.deleteUserInfoByUserId(corpId, repChangeContactXml.getUserID());
                    log.info("QyweixinGatewayInnerServiceImpl.changeContacts,user,deleteResult={}", deleteResult);
                }
            }

            if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "create_party")) {
                List<OutDepartmentInfoDoc> outDepartmentInfoDocs = outDepartmentInfoManger.queryDepartmentInfos(corpId);
                if(CollectionUtils.isEmpty(outDepartmentInfoDocs)) {
                    log.info("QyweixinGatewayInnerServiceImpl.changeContacts,init contacts,outEa={}", corpId);
                    saveAppEvent(corpId, ConfigCenter.repAppId, repChangeContactXml.getEvent(), plainMsg);
                } else {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(ConfigCenter.repAppId, corpId, repChangeContactXml.getId());
                    if(departmentInfoResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
                        List<OutDepartmentInfoDoc> departmentInfoDocs = new LinkedList<>();
                        OutDepartmentInfoDoc departmentInfoDoc = new OutDepartmentInfoDoc();
                        //departmentInfoDoc.setId(ObjectId.get());
                        departmentInfoDoc.setOutEa(corpId);
                        departmentInfoDoc.setOutDepartmentId(departmentInfoResult.getData().getDepartment().getId());
                        departmentInfoDoc.setOutDepartmentInfo(gson.toJson(departmentInfoResult.getData().getDepartment()));
                        departmentInfoDoc.setCreateTime(System.currentTimeMillis());
                        departmentInfoDoc.setUpdateTime(System.currentTimeMillis());
                        departmentInfoDocs.add(departmentInfoDoc);
                        BulkWriteResult bulkWriteResult = outDepartmentInfoManger.batchReplace(departmentInfoDocs);
                        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,department,bulkWriteResult={}", bulkWriteResult);
                    }
                }
            }

            if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "delete_party")) {
                DeleteResult deleteResult = outDepartmentInfoManger.deleteDepartmentInfoByUserId(corpId, repChangeContactXml.getId());
                log.info("QyweixinGatewayInnerServiceImpl.changeContacts,department,deleteResult={}", deleteResult);
            }
        } catch (Exception e) {
            log.info("QyweixinGatewayInnerServiceImpl.changeContacts,change contacts error,plainMsg={}", plainMsg);
        }

        //区别于其他
        if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "create_user")) {
            //有名称和id，直接绑定
            List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
            String fsEa = enterpriseMapping.getFsEa();
            Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
            }.getType());
            if(autoBindEmpEnterpriseMap.containsKey(fsEa)) {
                List<QyweixinUserDetailInfoRsp> allEmployeeListVisible = new LinkedList<>();
                if(ObjectUtils.isEmpty(userDetailInfoRspResult)) {
                    userDetailInfoRspResult = qyWeixinManager.getUserInfo(ConfigCenter.repAppId, corpId, repChangeContactXml.getUserID());
                }
                if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                    allEmployeeListVisible.add(userDetailInfoRspResult.getData());
                    log.info("QyweixinGatewayInnerServiceImpl.changeContacts,fsEa={},allEmployeeListVisible={}", fsEa, allEmployeeListVisible);
                    corpManager.autoBindEmpAccount(fsEa, corpId, allEmployeeListVisible);
                }
            } else if(ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
                List<QyweixinUserDetailInfoRsp> allEmployeeListVisible = new LinkedList<>();
                QyweixinUserDetailInfoRsp userDetailInfoRsp = QyweixinUserDetailInfoRsp.builder()
                        .name(repChangeContactXml.getName())
                        .userid(repChangeContactXml.getUserID())
                        .corpid(repChangeContactXml.getToUserName())
                        .build();
                allEmployeeListVisible.add(userDetailInfoRsp);
                log.info("QyweixinGatewayInnerServiceImpl.changeContacts,fsEa={},allEmployeeListVisible1={}", fsEa, allEmployeeListVisible);
                corpManager.autoBindAccountEnterprise2(fsEa, corpId, allEmployeeListVisible);
            }
        }
    }

    @Override
    public Result<CorpInfoModel> getCorpInfo(String corpId, String appId) {
        if(StringUtils.isEmpty(corpId)) {
            return Result.newInstance(ErrorRefer.QUERRY_EMPTY);
        }
        corpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        if(StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> corpInfoResult = qyWeixinManager.getCorpInfo(corpId, appId);
        log.info("QyweixinGatewayInnerServiceImpl.getCorpInfo,corpId={},corpInfo={}", corpId, corpInfoResult);
        CorpInfoModel corpInfoModel = new CorpInfoModel();
        if(corpInfoResult.isSuccess() && corpInfoResult.getData()!=null && corpInfoResult.getData().getAuth_corp_info()!=null) {
            BeanUtils.copyProperties(corpInfoResult.getData().getAuth_corp_info(),corpInfoModel);
            QyweixinCorpBindBo qyweixinCorpBindBo = new QyweixinCorpBindBo();
            qyweixinCorpBindBo.setCorpId(corpId);
            List<QyweixinCorpBindBo> appBindList = qyweixinCorpBindDao.findByEntity(qyweixinCorpBindBo);
            if(CollectionUtils.isNotEmpty(appBindList)) {
                for(QyweixinCorpBindBo corpBindBo : appBindList) {
                    if(StringUtils.equalsIgnoreCase(corpBindBo.getAppId(),ConfigCenter.crmAppId)) {
                        corpInfoModel.getFrom().add("企业微信-CRM");
                    } else if(StringUtils.equalsIgnoreCase(corpBindBo.getAppId(),eserviceAppId)) {
                        corpInfoModel.getFrom().add("企业微信-服务通");
                    } else if(StringUtils.equalsIgnoreCase(corpBindBo.getAppId(),orderAppId)) {
                        corpInfoModel.getFrom().add("企业微信-订货通");
                    } else {
                        continue;
                    }
                }
            }
        }
        return new Result<>(corpInfoModel);
    }

    @Override
    public com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> getUserInfo(String corpId,
                                                                                                     String appId,
                                                                                                     String userId) {
        return qyWeixinManager.getUserInfo(appId, corpId, userId);
    }

    @Override
    public com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> getUserInfoByPhone(String corpId,
                                                                                                            String appId,
                                                                                                            String phone) {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getUserIdByPhone(corpId, appId, phone);
        if(!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
            return new com.facishare.open.qywx.accountinner.result.Result<>(result.getCode(), result.getMsg(),null);
        }
        return qyWeixinManager.getUserInfo(appId, corpId, result.getData());
    }

    @Override
    public Result<QyweixinCorpBindBo> getCorpBindInfo(String corpId, String appId) {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.corpId2OpenCorpId(corpId);
        if(StringUtils.isNotEmpty(result.getData())) {
            corpId = result.getData();
        }
        return new Result<>(qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId));
    }

    @Override
    public Result<QYWXConnectParam> queryConnectInfo(String fsEa, String dataCenterId) {
        if(StringUtils.isNotEmpty(dataCenterId)) {
            Result<ErpConnectInfoEntity> result = erpdssManager.getDcInfo(fsEa, dataCenterId);
            if(result.getData()==null) {
                //兼容专属云的dataCenterId纷享云查不到的场景，企微连接器在专属云发布前，专属云不支持多数据中心
                log.info("queryConnectInfo,set dataCenterId = ''");
                dataCenterId = "";
            }
        }

        if(StringUtils.isEmpty(dataCenterId)) {
            Result<QyweixinAccountEnterpriseMapping> enterpriseMapping2 = qyweixinAccountBindInnerService.getEnterpriseMapping2(fsEa,
                    null);
            if(enterpriseMapping2.getData()==null) {
                return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
            }
            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMapping2.getData();
            QYWXConnectParam connectParam = new QYWXConnectParam();
            connectParam.setDataCenterId("");
            connectParam.setDataCenterName("企业微信");
            connectParam.setFsEa(fsEa);
            connectParam.setOutEa(enterpriseMapping.getOutEa());
            connectParam.setOutDepId(enterpriseMapping.getDepId());
            connectParam.setBindType(enterpriseMapping.getBindType());


            Result<String> queryCorpName2 = contactBindInnerService.queryCorpName2(fsEa,
                    connectParam.getOutEa(),
                    connectParam.getOutDepId());
            if(queryCorpName2.isSuccess()) {
                connectParam.setOutEn(queryCorpName2.getData());
            }
            return new Result<>(connectParam);
        }
        Result<ErpConnectInfoEntity> result = erpdssManager.getDcInfo(fsEa, dataCenterId);
        if(!result.isSuccess()) {
            return new Result<>(result.getErrorCode(),result.getErrorMsg(),null);
        }

        ErpConnectInfoEntity entity = result.getData();
        if(entity==null) {
            return Result.newInstance(ErrorRefer.CONNECT_PARAMS_IS_EMPTY);
        }
        if(StringUtils.isEmpty(entity.getConnectParams())) {
            entity.setConnectParams("{}");
        }

        QYWXConnectParam connectParam = JSONObject.parseObject(entity.getConnectParams(),QYWXConnectParam.class);
        if(connectParam!=null) {
            connectParam.setDataCenterId(dataCenterId);
            connectParam.setDataCenterName(entity.getDataCenterName());
            if(StringUtils.isNotEmpty(connectParam.getOutEa())) {
                Result<String> queryCorpName2 = contactBindInnerService.queryCorpName2(fsEa,
                        connectParam.getOutEa(),
                        connectParam.getOutDepId());
                if(queryCorpName2.isSuccess()) {
                    connectParam.setOutEn(queryCorpName2.getData());
                }
            }
            if(StringUtils.isNotEmpty(connectParam.getOutEa())) {
                Result<QyweixinAccountEnterpriseMapping> enterpriseMapping2 = qyweixinAccountBindInnerService.getEnterpriseMapping2(fsEa,
                        connectParam.getOutEa());
                if(enterpriseMapping2.getData()!=null) {
                    connectParam.setBindType(enterpriseMapping2.getData().getBindType());
                }
            }
        }
        return new Result<>(connectParam);
    }

    @Override
    public Result<Void> fsBindWithQywx(QYWXConnectParam connectParam,boolean checkRepApp) {
        com.facishare.open.qywx.accountinner.result.Result<String> corpId2OpenCorpId = qyWeixinManager.corpId2OpenCorpId(connectParam.getOutEa());
        connectParam.setOutEa(corpId2OpenCorpId.getData());

        Result<QyweixinAccountEnterpriseMapping> corpBindByOutEa = qyweixinAccountBindInnerService.queryCorpBindByOutEa(connectParam.getOutEa(),
                null);
        log.info("fsBindWithQywx,queryCorpBindByOutEa={}",corpBindByOutEa);
        if(corpBindByOutEa.getData()!=null && corpBindByOutEa.getData().getBindType()==0) {
            return Result.newInstance(ErrorRefer.OUT_EA_IS_AUTO_BIND_CANNOT_MANUAL_BIND);
        }

        GetDcBindArg getDcBindArg = new GetDcBindArg();
        getDcBindArg.setFsEa(connectParam.getFsEa());
        getDcBindArg.setOutEa(connectParam.getOutEa());
        getDcBindArg.setChannel("CONNECTOR_QYWX");
        //1.获取企微连接器信息
        Result<ErpConnectInfoEntity> dcBind = erpdssManager.getDcBind(getDcBindArg);
        if(dcBind!=null
                && dcBind.getData()!=null
                && StringUtils.isNotEmpty(connectParam.getDataCenterId())
                && !StringUtils.equalsIgnoreCase(dcBind.getData().getId(),connectParam.getDataCenterId())) {
            QYWXConnectParam connectParam2 = JSONObject.parseObject(dcBind.getData().getConnectParams(),QYWXConnectParam.class);
            String errMsg = String.format(ErrorRefer.QYWX_CORPID_HAS_BIND.getQywxCode(),connectParam2.getFsEa());
            return new Result<>(ErrorRefer.QYWX_CORPID_HAS_BIND.getCode(),errMsg,null);
        }

        String validRepAppId = getValidRepAppId(connectParam.getOutEa()).getData();
        if(checkRepApp) {
            //2.检查代开发应用是否安装或停用
            QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(connectParam.getOutEa(), validRepAppId);
            if(corpBindBo==null) {
                return Result.newInstance(ErrorRefer.REP_APP_NOT_ENABLE);
            } else {
                if(corpBindBo.getStatus()!=0) {
                    return Result.newInstance(ErrorRefer.REP_APP_IS_STOP);
                }
            }
        }

        //3.更新或建立企业绑定关系
        Result<QyweixinAccountEnterpriseMapping> enterpriseMapping2 = qyweixinAccountBindInnerService.getEnterpriseMapping2(connectParam.getFsEa(),
                connectParam.getOutEa());
        if(enterpriseMapping2.getData()==null) {
            com.facishare.open.qywx.accountbind.result.Result<Boolean> enterpriseAccountBind = corpManager.saveEnterpriseAccountBind(connectParam.getOutEa(),
                    connectParam.getOutDepId(),
                    connectParam.getFsEa(),
                    null,
                    connectParam.getOutEa());
            if(!enterpriseAccountBind.isSuccess() || (enterpriseAccountBind.getData()!=null && enterpriseAccountBind.getData()==false)) {
                return Result.newInstance(ErrorRefer.ENTERPRISE_BIND_FAILED);
            }
        } else {
            Result<Integer> result = qyweixinAccountBindInnerService.updateEnterpriseOutInfo(enterpriseMapping2.getData().getId(),
                    connectParam.getOutEa(),
                    connectParam.getOutDepId());
            log.info("fsBindWithQywx,updateEnterpriseOutInfo={}",result);
        }

        //4.更新集成平台企微连接器连接参数
        int tenantId = eieaConverter.enterpriseAccountToId(connectParam.getFsEa());
        UpdateConnectParamsArg arg = new UpdateConnectParamsArg();
        arg.setTenantId(tenantId+"");
        arg.setDataCenterId(connectParam.getDataCenterId());
        arg.setDataCenterName(connectParam.getDataCenterName());
        arg.setConnectParams(JSONObject.toJSONString(connectParam));
        Result<Void> result = erpdssManager.updateConnectParams(arg);
        log.info("fsBindWithQywx,updateConnectParams,result={}",result);
        return new Result<>();
    }

    @Override
    public Result<Void> fsUnBindWithQywx(QYWXConnectParam connectParam) {
        int tenantId = eieaConverter.enterpriseAccountToId(connectParam.getFsEa());
        UpdateConnectParamsArg arg = new UpdateConnectParamsArg();
        arg.setTenantId(tenantId+"");
        arg.setDataCenterId(connectParam.getDataCenterId());
        arg.setConnectParams("{}");
        Result<Void> result = erpdssManager.updateConnectParams(arg);
        log.info("fsUnBindWithQywx,updateConnectParams={}",result);
        result = qyweixinAccountBindInnerService.deleteQYWXAccountBind(connectParam.getFsEa(),
                ConfigCenter.crmAppId,
                connectParam.getOutEa());
        log.info("fsUnBindWithQywx,deleteQYWXBindByFsEa2={}",result);
        return result;
    }

    @Override
    public Result<Void> fsUnBindWithQywx2(String outEa, String outDepId) {
        com.facishare.open.qywx.accountinner.result.Result<String> corpId2OpenCorpId = qyWeixinManager.corpId2OpenCorpId(outEa);
        log.info("fsUnBindWithQywx2,corpId2OpenCorpId={}",corpId2OpenCorpId);
        outEa = corpId2OpenCorpId.getData();

        Result<QyweixinAccountEnterpriseMapping> corpBindByOutEa = qyweixinAccountBindInnerService.queryCorpBindByOutEa(outEa,
                outDepId);
        log.info("fsUnBindWithQywx2,queryCorpBindByOutEa={}",corpBindByOutEa);

        String fsEa = null;
        if(corpBindByOutEa.isSuccess() && corpBindByOutEa.getData()!=null) {
            fsEa = corpBindByOutEa.getData().getFsEa();

            GetDcBindArg arg = new GetDcBindArg();
            arg.setFsEa(fsEa);
            arg.setOutEa(outEa);
            arg.setChannel("CONNECTOR_QYWX");
            Result<ErpConnectInfoEntity> dcBind = erpdssManager.getDcBind(arg);
            log.info("fsUnBindWithQywx2,arg={},dcBind={}", arg, dcBind);
            Result<Void> result;
            if (dcBind.isSuccess() && dcBind.getData() != null) {
                QYWXConnectParam connectParam = new QYWXConnectParam();
                connectParam.setFsEa(fsEa);
                connectParam.setOutEa(outEa);
                connectParam.setOutDepId(outDepId);
                connectParam.setDataCenterId(dcBind.getData().getId());
                result = fsUnBindWithQywx(connectParam);
                log.info("fsUnBindWithQywx2,result={}", result);
                return result;
            } else {
                result = qyweixinAccountBindInnerService.deleteQYWXAccountBind(fsEa, ConfigCenter.crmAppId, outEa);
                log.info("fsUnBindWithQywx2,deleteQYWXBindByFsEa2={}", result);
                return result;
            }
        } else {
            return Result.newInstance(ErrorRefer.QYWX_UNBIND_FAILED);
        }
    }

    @Override
    public Result<Void> checkAndInitConnector(String fsEa, String dataCenterId) {
        String tenantId = eieaConverter.enterpriseAccountToId(fsEa)+"";
        //Long nowTime = System.currentTimeMillis();

        Result<QyweixinAccountEnterpriseMapping> enterpriseMapping2 = qyweixinAccountBindInnerService.getEnterpriseMapping2(fsEa, null);
        log.info("checkAndInitConnector,enterpriseMapping2={}",enterpriseMapping2);

        //ProductVersionPojo productVersionPojo = null;
//        if(enterpriseMapping2.getData()==null) {
//            log.info("checkAndInitConnector,fsEa not bind");
//            return new Result<>();
////            productVersionPojo = orderManager.queryFirstValidSCRMLicense(tenantId);
////            log.info("checkAndInitConnector,scrm,productVersionPojo={}",productVersionPojo);
////            if(productVersionPojo==null) {
////                return new Result<>();
////            }
//        }
        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMapping2.getData();

        if(StringUtils.isNotEmpty(dataCenterId)) {
            log.info("checkAndInitConnector,manual bind,qywx connector exist, return only");
            return new Result<>();
        }

        //如果是手动绑定企业并且企业的绑定时间小于企微连接器上线时间（qywxConnectorOnlineDate）
//        if(enterpriseMapping.getBindType()==1) {
//            if(StringUtils.isNotEmpty(dataCenterId)) {
//                log.info("checkAndInitConnector,manual bind,qywx connector exist, return only");
//                return new Result<>();
//            }
//
//            Long onlineTime = DateUtils.parseDate(ConfigCenter.qywxConnectorOnlineDate,DateUtils.FORMAT_yyyyMMddhhmmss).getTime();
//            log.info("checkAndInitConnector,manual bind,paid,onlineTime={},qywxConnectorOnlineDate={}",onlineTime,
//                    ConfigCenter.qywxConnectorOnlineDate);
//            if(enterpriseMapping.getGmtCreate().getTime() >= onlineTime) {
//                log.info("checkAndInitConnector,manual bind,paid,enterpriseMapping.getGmtCreate().getTime() >= onlineTime");
//                return new Result<>();
//            }
//            //手动绑定企业，默认替客户下一个有效期3年的0元订单，3年过期后，如果客户还要继续使用，需要购买企微连接器
//            long endTime = nowTime + 3 * 365 * 24 * 60 * 60 * 1000L;
////            if(productVersionPojo!=null) {
////                endTime = productVersionPojo.getExpiredTime();
////            }
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector = orderManager.buyConnector(fsEa,
//                    "0",
//                    nowTime,
//                    endTime);
//            log.info("checkAndInitConnector,manual bind,buyConnector={}",buyConnector);
//            if(!buyConnector.isSuccess()) {
//                return new Result<>(ErrorRefer.QYWX_CONNECTOR_ORDER_FAILED.getCode(),
//                        ErrorRefer.QYWX_CONNECTOR_ORDER_FAILED.getQywxCode());
//            }
//        }
//
//        QyweixinOrderInfoBo latestPaidOrder = qyweixinOrderInfoDao.getLatestPaidOrder(enterpriseMapping.getOutEa(),
//                crmAppId,
//                null);
//        log.info("checkAndInitConnector,latestPaidOrder={}",latestPaidOrder);
//        //自动绑定的企业，企微连接器0元
//        if(enterpriseMapping.getBindType()==0) {
//            //企微试用企业没有订单，帮客户默认下一年的企微连接器订单
//            if(latestPaidOrder==null) {
//                Long endTime = nowTime + 1 * 365 * 24 * 60 * 60 * 1000L;
//                com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector = orderManager.buyConnector(fsEa,
//                        "0",
//                        nowTime,
//                        endTime);
//                log.info("checkAndInitConnector,auto bind,try,buyConnector={}",buyConnector);
//                if(!buyConnector.isSuccess()) {
//                    return new Result<>(ErrorRefer.QYWX_CONNECTOR_ORDER_FAILED.getCode(),
//                            ErrorRefer.QYWX_CONNECTOR_ORDER_FAILED.getQywxCode());
//                }
//            } else {
//                Long orderEndTime = latestPaidOrder!=null && latestPaidOrder.getEndTime()!=null && latestPaidOrder.getEndTime()!=0L ? latestPaidOrder.getEndTime() * 1000 : null;
//                if(StringUtils.isNotEmpty(dataCenterId)) {
//                    //自动绑定场景，集成平台已经有企微连接器
//                    ModuleFlag moduleFlag = orderManager.judgeQywxConnectorModule(tenantId);
//                    log.info("checkAndInitConnector,nowTime={},orderEndTime={},moduleFlag={}",nowTime,
//                            orderEndTime,moduleFlag);
//                    //检查客户的license是否过期，如果客户已经重新下企微版CRM订单，则自动帮客户下0元的企微连接器订单
//                    if(orderEndTime!=null
//                            && moduleFlag!=null
//                            && !moduleFlag.isFlag()
//                            && orderEndTime > nowTime) {
//                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector = orderManager.buyConnector(fsEa,
//                                "0",
//                                nowTime,
//                                orderEndTime);
//                        log.info("checkAndInitConnector,auto bind,repaid,buyConnector={}",buyConnector);
//                        return buyConnector.isSuccess() ? new Result<>() : new Result<>(buyConnector.getCode()+"",buyConnector.getMsg());
//                    }
//                } else {
//                    //自动绑定场景，集成平台没有企微连接器，客户已经在企微侧下单购买了企微版CRM，自动帮客户购买一个0元的企微连接器
//                    if(orderEndTime!=null && orderEndTime > nowTime) {
//                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector = orderManager.buyConnector(fsEa,
//                                "0",
//                                nowTime,
//                                orderEndTime);
//                        log.info("checkAndInitConnector,auto bind,paid,buyConnector={}",buyConnector);
//                        if(!buyConnector.isSuccess()) {
//                            return new Result<>(ErrorRefer.QYWX_CONNECTOR_ORDER_FAILED.getCode(),
//                                    ErrorRefer.QYWX_CONNECTOR_ORDER_FAILED.getQywxCode());
//                        }
//                    }
//                }
//            }
//        }

        String dataCenterName = "企业微信";
        dataCenterId = idGenerator.get();

        String outEa = enterpriseMapping!=null ? enterpriseMapping.getOutEa() : null;
        String outDepId = enterpriseMapping!=null ? enterpriseMapping.getDepId() : null;
        Integer bindType = enterpriseMapping!=null ? enterpriseMapping.getBindType() : 1;


        QYWXConnectParam connectParam = new QYWXConnectParam();
        connectParam.setDataCenterId(dataCenterId);
        connectParam.setDataCenterName(dataCenterName);
        connectParam.setFsEa(fsEa);
        connectParam.setOutEa(outEa);
        connectParam.setOutDepId(outDepId);
        connectParam.setBindType(bindType);


        CreateConnectorArg arg = new CreateConnectorArg();
        arg.setId(dataCenterId);
        arg.setChannel("CONNECTOR_QYWX");
        arg.setTenantId(tenantId);
        arg.setDataCenterName(dataCenterName);
        arg.setConnectParams(JSONObject.toJSONString(connectParam));

        //创建企微连接器
        Result<ErpConnectInfoEntity> result = erpdssManager.createConnector(arg);
        log.info("checkAndInitConnector,createConnector,result={}",result);
        return new Result<>(result.getErrorCode(),result.getErrorMsg(),null);
    }

    /**
     * 获取登录用户身份，仅用于登录授权应用
     * @param code
     * @return
     */
    @Override
    public Result<QyweixinUserInfo3rdRsp> getUserInfoByLoginAuthApp(String code) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserInfo3rdRsp> result = qyWeixinManager.getUserInfoByLoginAuthApp(code);
        if(result.isSuccess()) {
            return new Result<>(result.getData());
        }
        return new Result<>(result.getCode(),result.getMsg(),null);
    }

    public Result<String> getValidRepAppId(String outEa) {
        List<QyweixinCorpBindBo> repAppList = getRepAppList(outEa).getData();
        repAppList.removeIf((item)->StringUtils.equalsIgnoreCase(ConfigCenter.repAppId,item.getAppId()));
        String validRepAppId = ConfigCenter.repAppId;
        if(CollectionUtils.isNotEmpty(repAppList)) {
            validRepAppId = repAppList.get(0).getAppId();
        }
        log.info("getValidRepAppId,default,outEa={},validRepAppId={}",outEa,validRepAppId);
        return new Result<>(validRepAppId);
    }

    @Override
    public Result<String> getMainAppId(String outEa) {
        List<QyweixinCorpBindBo> list = qyweixinCorpBindDao.getByOutEa(outEa);
        log.info("getMainAppId,list={}",list);
        String mainAppId = ConfigCenter.crmAppId;
        list.removeIf((item)->StringUtils.equalsIgnoreCase(ConfigCenter.repAppId,item.getAppId()));

        if(CollectionUtils.isNotEmpty(list)) {
            String appId = null;
            //检查已安装应用列表，是否有CRM应用，如果有，默认使用CRM应用
            for(QyweixinCorpBindBo corpBindBo : list) {
                if(StringUtils.equalsIgnoreCase(corpBindBo.getAppId(),ConfigCenter.crmAppId)) {
                    appId = corpBindBo.getAppId();
                    break;
                }
            }
            log.info("getMainAppId,appId={}",appId);
            if(StringUtils.isEmpty(appId)) {
                mainAppId = list.get(0).getAppId();
            }
        }
        log.info("getMainAppId,outEa={},mainAppId={}",outEa,mainAppId);
        return new Result<>(mainAppId);
    }

    @Override
    public Result<List<QyweixinCorpBindBo>> getRepAppList(String outEa) {
        List<QyweixinCorpBindBo> list = qyweixinCorpBindDao.getByOutEa(outEa);

        List<QyweixinCorpBindBo> repList = new ArrayList<>();
        for(QyweixinCorpBindBo corpBindBo : list) {
            if(corpBindBo.getAppId().startsWith("dk")) {
                repList.add(corpBindBo);
            }
        }
        log.info("getRepAppList,repList={}",repList);
        return new Result<>(repList);
    }

    /**
     * 外部群变更事件
     * 1、发mq给北研
     * @param repExternalChatEventXml xml
     */
    public void repChangeExternalChat(RepExternalChatEventXml repExternalChatEventXml) {
        String corpId = repExternalChatEventXml.getToUserName();

        String validRepAppId = getValidRepAppId(corpId).getData();

        ExternalChatEvent event = new ExternalChatEvent();
        event.setAppId(validRepAppId);
        event.setCorpId(corpId);
        event.setChangeType(repExternalChatEventXml.ChangeType);
        event.setChatId(repExternalChatEventXml.getChatId());
        event.setJoinScene(repExternalChatEventXml.getJoinScene());
        event.setQuitScene(repExternalChatEventXml.getQuitScene());
        event.setUpdateDetail(repExternalChatEventXml.getUpdateDetail());
        event.setMemChangeCnt(repExternalChatEventXml.getMemChangeCnt());
        //通过corpId查询fsEa
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(event.getCorpId());
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            log.info("QyweixinGatewayInnerServiceImpl.repChangeExternalChat,enterprise not bind.corpId={}",event.getCorpId());
            return;
        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGroupChatDetail> qyweixinGroupChatDetailResult = null;
        if(QyweixinChangeExternalChatEventTypeEnum.CREATE.getEventType().equals(repExternalChatEventXml.getChangeType()) ||
                QyweixinChangeExternalChatEventTypeEnum.UPDATE.getEventType().equals(repExternalChatEventXml.getChangeType())) {
            //使用代开发获取
            com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getCorpAccessToken(validRepAppId, corpId, false);
            if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
                log.info("QyweixinGatewayInnerServiceImpl.repChangeExternalChat,enterprise not repAppId.corpId={}",event.getCorpId());
                return;
            }
            qyweixinGroupChatDetailResult = qyWeixinManager.getGroupChatDetail(tokenResult.getData(), repExternalChatEventXml.getChatId());
            log.info("QyweixinGatewayInnerServiceImpl.repChangeExternalChat,qyweixinGroupChatDetail={}", qyweixinGroupChatDetailResult);
            if(!qyweixinGroupChatDetailResult.isSuccess() || ObjectUtils.isEmpty(qyweixinGroupChatDetailResult.getData())) {
                log.info("QyweixinGatewayInnerServiceImpl.repChangeExternalChat,qyweixinGroupChatDetail is null.corpId={}",event.getCorpId());
                return;
            }
            event.setExternalChatDetail(new Gson().toJson(qyweixinGroupChatDetailResult.getData()));
        }
        for(QyweixinAccountEnterpriseMapping mapping : enterpriseMappingList) {
            event.setFsEa(mapping.getFsEa());
            if (ObjectUtils.isNotEmpty(qyweixinGroupChatDetailResult)) {
                Result<String> result = qyweixinAccountBindInnerService.outAccountToFsAccount("qywx",event.getCorpId(),qyweixinGroupChatDetailResult.getData().getGroup_chat().getOwner(),mapping.getFsEa());
                log.info("QyweixinGatewayInnerServiceImpl.addOrUpdateExternalContact,result={}",result);
                if(StringUtils.isNotEmpty(result.getData())) {
                    List<String> accountList = Splitter.on(".").splitToList(result.getData());
                    event.setUserIdMap(ImmutableMap.of(qyweixinGroupChatDetailResult.getData().getGroup_chat().getOwner(), accountList.get(2)));
                }
            }

            log.info("QyweixinGatewayInnerServiceImpl.repChangeExternalChat,event={}", event);
            Message msg = new Message();
            if(QyweixinChangeExternalChatEventTypeEnum.CREATE.getEventType().equals(repExternalChatEventXml.getChangeType())) {
                msg.setTags(CorpManager.TAG_CREATE_EXTERNAL_CHAT);
            } else if(QyweixinChangeExternalChatEventTypeEnum.UPDATE.getEventType().equals(repExternalChatEventXml.getChangeType())) {
                msg.setTags(CorpManager.TAG_UPDATE_EXTERNAL_CHAT);
            } else if(QyweixinChangeExternalChatEventTypeEnum.DISMISS.getEventType().equals(repExternalChatEventXml.getChangeType())) {
                msg.setTags(CorpManager.TAG_DISMISS_EXTERNAL_CHAT);
            }
            if(StringUtils.isEmpty(msg.getTags())) {
                log.info("QyweixinGatewayInnerServiceImpl.repChangeExternalChat,changeType not suppot");
                return;
            }
            msg.setBody(event.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(mapping.getFsEa())) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.qywxEventNotifyMQSender.name());
                cloudMessageProxyProto.setCorpId(event.getCorpId());
                cloudMessageProxyProto.setFsEa(mapping.getFsEa());
                cloudMessageProxyProto.setMessage(msg);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(mapping.getFsEa()), cloudMessageProxyProto);
            } else {
                SendResult sendResult = qywxEventNotifyMQSender.send(msg);
                log.info("QyweixinGatewayInnerServiceImpl.repChangeExternalChat,ea={},sendResult={}", mapping.getFsEa(), sendResult);
            }
        }
    }

    private void saveAppEvent(String outEa, String appId, String infoType, String plainMsg) {
        log.info("QyweixinGatewayInnerServiceImpl.saveAppEvent,outEa={}, appId={}, infoType={}, plainMsg={}", outEa, appId, infoType, plainMsg);
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(outEa);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return;
        }
        boolean isHasManual = enterpriseMappingList.stream()
                .anyMatch(mapping -> mapping.getBindType() == 1);
        if(!isHasManual) {
            return;
        }

        SyncEventDataDoc dataDoc = new SyncEventDataDoc();
        dataDoc.setId(ObjectId.get());
        dataDoc.setAppId(appId);
        dataDoc.setOutEa(outEa);
        dataDoc.setEventType(infoType);
        dataDoc.setEvent(plainMsg);
        dataDoc.setStatus(0);
        dataDoc.setCreateTime(System.currentTimeMillis());
        dataDoc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = syncEventDataManger.batchReplace(Lists.newArrayList(dataDoc));
        log.info("QyweixinGatewayInnerServiceImpl.saveAppEvent,bulkWriteResult={}", bulkWriteResult);
    }

    private void enterpriseOpenMonitor(String outEa, String appId) {
        log.info("QyweixinGatewayInnerServiceImpl.enterpriseOpenMonitor,outEa={},appId={}", outEa, appId);
        //睡眠一分钟
        try {
            Thread.sleep(60 * 1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //查库
        Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindInnerService.queryCorpBindByOutEa(outEa, null);
        if(enterpriseMappingResult.isSuccess() && ObjectUtils.isNotEmpty(enterpriseMappingResult.getData())) {
            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingResult.getData();
            if(enterpriseMapping.getStatus() == 100) {
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .ea(enterpriseMapping.getFsEa())
                        .appId(appId)
                        .channelId(ChannelEnum.qywx.name())
                        .dataTypeId(QYWXDataTypeEnum.ENTERPRISE_CREATE.getDataType())
                        .corpId(outEa)
                        .errorCode("100")
                        .errorMsg("超过一分钟，该企业还未创建成功，请及时关注！")
                        .build();
                oaConnectorOpenDataManager.send(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("企微企业开通失败告警");
                String msg = String.format("超过一分钟，该企业还未创建成功\n纷享企业ea=%s\n请及时关注！", enterpriseMapping.getFsEa());
                arg.setMsg(msg);
                notificationService.sendQYWXNotice(arg);
            }
        } else {
            //没有记录，自己接收告警
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.qywx.name())
                    .dataTypeId(QYWXDataTypeEnum.ENTERPRISE_CREATE.getDataType())
                    .corpId(outEa)
                    .errorCode("101")
                    .errorMsg("超过一分钟，该企业没有绑定记录，请及时关注！")
                    .build();
            oaConnectorOpenDataManager.send(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("企微企业开通失败告警");
            String msg = String.format("超过一分钟，该企业没有绑定记录\n企微企业ea=%s\n请及时关注！", outEa);
            arg.setMsg(msg);
            notificationService.sendQYWXNotice(arg);
        }
    }
}
