//package com.facishare.open.qywx.accountsync.notify;
//
///**
// * Created by fengyh on 2018/4/27.
// */
//
//import com.alibaba.rocketmq.client.exception.MQClientException;
//import com.alibaba.rocketmq.client.producer.DefaultMQProducer;
//import com.alibaba.rocketmq.client.producer.SendResult;
//import com.alibaba.rocketmq.client.producer.SendStatus;
//import com.alibaba.rocketmq.common.message.Message;
//import com.facishare.common.rocketmq.AutoConfRocketMQ;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.UUID;
//
//
///**
// * 支持自动化配置的RocketMQ同步消息发送者 Created by liyiguang on 16/1/4.
// */
//public class AutoConfRocketMQSender extends AutoConfRocketMQ {
//
//    private static final Logger LOG = LoggerFactory.getLogger(AutoConfRocketMQSender.class);
//
//    public static final String KEY_GROUP_PROVIDER = "GROUP_PROVIDER";
//
//
//    private volatile DefaultMQProducer sender;
//
//    public AutoConfRocketMQSender(String configName, String nameServerKey, String groupKey, String topicKey) {
//        super(configName, nameServerKey, groupKey, topicKey);
//    }
//
//    public AutoConfRocketMQSender(String configName) {
//        this(configName, KEY_NAME_SERVER, KEY_GROUP_PROVIDER, KEY_TOPICS);
//    }
//
//    public DefaultMQProducer getSender() {
//        return sender;
//    }
//
//    /**
//     * 配置发生变化时加载
//     */
//    @Override
//    protected void doReload() {
//
//        if (sender == null) {
//            createProducer();
//        } else {
//            shutDownProducer();
//            createProducer();
//        }
//    }
//
//    /**
//     * 同步消息发送
//     */
//    public SendResult send(Message message) {
//        if (message.getTopic() == null && getTopic() != null) {
//            message.setTopic(getTopic());
//            return doSend(message);
//        } else if(message.getTopic() != null ) {
//            return doSend(message);
//        } else {
//            LOG.warn("DiscardNoTopicMessage");
//            return null;
//        }
//    }
//
//    private boolean isValidTopic(String topic) {
//        if (this.topics == null || topics.isEmpty()) {
//            return false;
//        }
//        return topics.containsKey(topic);
//    }
//
//    private SendResult doSend(Message msg) {
//        try {
//            SendResult result = sender.send(msg);
//            SendStatus status = result.getSendStatus();
//            if (status.equals(SendStatus.SEND_OK)) {
//                LOG.info("send succ, msgId={}, status={}", result.getMsgId(), status);
//            } else {
//                LOG.error("send fail, msgId={}, status={}", result.getMsgId(), status);
//            }
//            return result;
//        } catch (Exception e) {
//            LOG.error("SendError,message={}", msg, e);
//            return null;
//        }
//    }
//
//    private void createProducer() {
//        try {
//            sender = new DefaultMQProducer(groupName);
//            sender.setNamesrvAddr(nameServer);
//            sender.setMaxMessageSize(maxMessageSize);
//            sender.setInstanceName(UUID.randomUUID().toString());
//            sender.start();
//        } catch (MQClientException e) {
//            LOG.error("CanNotCreateProducer nameServer={} group={} ", nameServer, groupName, e);
//        }
//    }
//
//    private void shutDownProducer() {
//        if (this.sender != null) {
//            try {
//                this.sender.shutdown();
//                this.sender = null;
//            } catch (Exception e) {
//                LOG.error("ShutRocketMQDownProducer Error,nameServer={} group={}", sender.getNamesrvAddr(),
//                        sender.getProducerGroup(), e);
//            }
//        }
//    }
//
//    @Override
//    public void shutDown() {
//        shutDownProducer();
//    }
//
//
//}
//
