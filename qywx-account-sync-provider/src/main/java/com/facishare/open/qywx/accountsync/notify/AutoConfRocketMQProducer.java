package com.facishare.open.qywx.accountsync.notify;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

public class AutoConfRocketMQProducer extends AutoConfMQProducer {
    public AutoConfRocketMQProducer(String config) {
        super(config);
    }

    public AutoConfRocketMQProducer(String configName, String sectionNames) {
        super(configName, sectionNames);
    }

    public SendResult send(Message msg) {
        if(StringUtils.isEmpty(msg.getTopic())) {
            msg.setTopic(this.getDefaultTopic());
        }
        return super.send(msg);
    }
}
