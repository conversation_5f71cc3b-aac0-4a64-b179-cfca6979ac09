package com.facishare.open.qywx.accountsync.dao;

import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpInfoBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/20
 */
@Repository
public interface QyweixinCorpInfoDao extends ICrudMapper<QyweixinCorpInfoBo> {

    @Select("<script>"+"SELECT aTable.out_ea corpId, aTable.fs_ea fsEa, aTable.bind_type bindType, bTable.corp_name corpName, bTable.user_id userId  FROM enterprise_account_bind aTable LEFT JOIN qyweixin_corp_info bTable on aTable.out_ea = bTable.corp_id where aTable.out_ea=#{corpId}"+"</script>")
    List<Map<String,String>> queryCorpInfoByCorpId(String corpId);

    @Update("<script>" + "update qyweixin_corp_info set corp_id=#{openCorpId}, user_id=#{openUserId} where corp_id=#{corpId} and user_id=#{userId}</script>")
    int batchUpdateApplicationInfoBind(@Param("corpId") String corpId, @Param("openCorpId") String openCorpId, @Param("userId") String userId, @Param("openUserId") String openUserId);

    @Select("<script>" + "select * from qyweixin_corp_info limit #{pageNum},#{pageSize} " + "</script>")
    List<QyweixinCorpInfoBo> queryAllCorpBind(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize);
}
