package com.facishare.open.qywx.accountsync.utils;

import com.facishare.open.order.contacts.proxy.api.model.contacts.OutDepModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutUpdateTagModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinDepartmentRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinUserDetailInfoRsp;
import com.facishare.open.qywx.accountsync.utils.xml.ContactsXml;
import com.facishare.open.qywx.accountsync.utils.xml.TagChangeEventXml;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 通讯录工具类
 * <AUTHOR>
 * @date 2023.02.16
 */
public class ContactsUtils {
    public static final String dep_prefix="D-FSQYWX-";
    public static final String emp_prefix="U-FSQYWX-";
    public static OutDepModel getOutDepModel(QyweixinDepartmentRsp departmentRsp) {
        OutDepModel model = new OutDepModel();
        model.setId(departmentRsp.getId());
        model.setName(departmentRsp.getName());
        model.setParentId(departmentRsp.getParentid());
        model.setOrder(departmentRsp.getOrder());

        return model;
    }

    public static List<OutDepModel> getOutDepModelList(List<QyweixinDepartmentRsp> departmentRspList) {
        List<OutDepModel> list = new ArrayList<>();
        for(QyweixinDepartmentRsp departmentRsp : departmentRspList) {
            list.add(getOutDepModel(departmentRsp));
        }
        return list;
    }

    public static OutEmpModel getOutEmpModel(QyweixinUserDetailInfoRsp userDetailInfoRsp) {
        OutEmpModel model = OutEmpModel.builder()
                .id(userDetailInfoRsp.getUserid())
                .name(userDetailInfoRsp.getName())
                .nickname(userDetailInfoRsp.getName())
                .code(userDetailInfoRsp.getUserid())
                .avatar(userDetailInfoRsp.getAvatar())
                .gender(userDetailInfoRsp.getGender())
                .mobile(userDetailInfoRsp.getMobile())
                .email(userDetailInfoRsp.getEmail())
                .leaderId(null)
                .status(userDetailInfoRsp.getStatus())
                .depIdList(userDetailInfoRsp.getDepartment())
                .build();

        return model;
    }

    public static List<OutEmpModel> getOutEmpModelList(List<QyweixinUserDetailInfoRsp> userDetailInfoRspList) {
        List<OutEmpModel> list = new ArrayList<>();
        for(QyweixinUserDetailInfoRsp userDetailInfoRsp : userDetailInfoRspList) {
            list.add(getOutEmpModel(userDetailInfoRsp));
        }
        return list;
    }

    public static OutUpdateTagModel getOutUpdateTagModel(TagChangeEventXml xml) {
        OutUpdateTagModel model = new OutUpdateTagModel();
        model.setTagId(xml.getTagId()+"");
        model.setAddEmpList(getItemList(xml.getAddUserItems()));
        model.setRemoveEmpList(getItemList(xml.getDelUserItems()));

        model.setAddDepList(getItemList(xml.getAddPartyItems()));
        model.setRemoveDepList(getItemList(xml.getDelPartyItems()));

        return model;
    }

    public static OutUpdateTagModel getOutUpdateTagModel(ContactsXml xml) {
        OutUpdateTagModel model = new OutUpdateTagModel();
        model.setTagId(xml.getTagId());
        model.setAddEmpList(getItemList(xml.getAddUserItems()));
        model.setRemoveEmpList(getItemList(xml.getDelUserItems()));

        model.setAddDepList(getItemList(xml.getAddPartyItems()));
        model.setRemoveDepList(getItemList(xml.getDelPartyItems()));

        return model;
    }

    private static List<String> getItemList(String items) {
        if(StringUtils.isEmpty(items)) return new ArrayList<>();
        List<String> itemList = Splitter.on(",").splitToList(items);
        return itemList;
    }
}
