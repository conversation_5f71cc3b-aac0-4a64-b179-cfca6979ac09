package com.facishare.open.qywx.accountsync.core.enums;

/**
 * <AUTHOR>
 * Created on 2019/2/21
 */
public enum AppStatusEnum {
    /**
     * 付费状态。
     * 0-没有付费;
     * 1-限时试用;
     * 2-试用过期;
     * 3-付费版本有效期内;
     * 4-付费版本过期;
     * 5-不限时试用;
     * 6-付费版本有效期内，但是实际使用人数超标，且超标未超过7天;
     * 7-付费版本有效期内，但是实际使用人数超标，且持续超标超过7天
     */
    NO_PAYMENT(0, "没有付费"),
    LIMITED_TIME_TRIAL(1, "限时试用"),
    TRIAL_EXPIRED(2, "试用过期"),
    PAID_VERSION_VALIDITY_PERIOD(3, "付费版本有效期内"),
    PAID_VERSION_EXPIRED(4, "付费版本过期"),
    UNLIMITED_TRIAL(5, "不限时试用"),
    EXCEEDED_SITUATION_ONE(6, "付费版本有效期内，但是实际使用人数超标，且超标未超过7天"),
    EXCEEDED_SITUATION_TWO(7, "付费版本有效期内，但是实际使用人数超标，且持续超标超过7天");

    private Integer code;
    private String name;

    AppStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
