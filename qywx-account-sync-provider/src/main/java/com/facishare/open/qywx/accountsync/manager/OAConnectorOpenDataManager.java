package com.facishare.open.qywx.accountsync.manager;

import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.ckDao.BizLogOaconnectoropendataDistDao;
import com.facishare.open.qywx.accountsync.core.enums.QYWXDataTypeEnum;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.OAConnectorOpenDataModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.ckBo.BizLogOaconnectoropendataDistBo;
import com.facishare.open.qywx.accountsync.utils.DateUtils;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.fxiaoke.common.IpUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.OAConnectorOpenDataLog;
import com.fxiaoke.log.dto.OAConnectorOpenDataLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class OAConnectorOpenDataManager {
    @Autowired
    private BizLogOaconnectoropendataDistDao bizLogOaconnectoropendataDistDao;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    public void send(OAConnectorOpenDataModel model) {
        if(model.getCreateTime() == null) {
            model.setCreateTime(System.currentTimeMillis());
        }

        //人员登陆失败区分
        if(StringUtils.isNotEmpty(model.getDataTypeId())
                && model.getDataTypeId().equals(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())) {
            //安装失败，无记录
            try {
                if(model.getErrorCode().equals("100")) {
                    List<BizLogOaconnectoropendataDistBo> distBos = bizLogOaconnectoropendataDistDao.findEnterpriseCreateError(ChannelEnum.qywx.name(), QYWXDataTypeEnum.ENTERPRISE_CREATE.getDataType(), model.getCorpId());
                    if(CollectionUtils.isNotEmpty(distBos)) {

                        model.setDataTypeId(QYWXDataTypeEnum.NEW_EMPLOYEE_LOGIN.getDataType());
                    }
                } else {
                    //安装失败，有记录
                    if(model.getErrorCode().equals("101") || model.getErrorCode().equals("102")) {
                        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseMappingResult = qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), model.getCorpId());
                        if(enterpriseMappingResult.isSuccess() && CollectionUtils.isNotEmpty(enterpriseMappingResult.getData())) {
                            Date date = DateUtils.parseDate("2024-02-01", "yyyy-MM-dd");
                            assert date != null;
                            if(enterpriseMappingResult.getData().get(0).getGmtCreate().getTime() > date.getTime()) {
                                model.setDataTypeId(QYWXDataTypeEnum.NEW_EMPLOYEE_LOGIN.getDataType());
                            }
                        }
                    }
                }
            } finally {
                log.info("OAConnectorOpenDataManager.send,model.dataTypeId={}", model.getDataTypeId());
            }
        }

        OAConnectorOpenDataLogDTO dto = OAConnectorOpenDataLogDTO.builder()
                .appName(ConfigHelper.getProcessInfo().getName())
                .traceId(TraceUtil.get())
                .ea(model.getEa())
                .createTime(model.getCreateTime())
                .serverIp(IpUtil.getSiteLocalIp())
                .appId(model.getAppId())
                .channelId(model.getChannelId())
                .corpId(model.getCorpId())
                .dataTypeId(model.getDataTypeId())
                .outUserId(model.getOutUserId())
                .errorCode(model.getErrorCode())
                .errorMsg(model.getErrorMsg())
                .build();

        log.info("OAConnectorOpenDataManager.send,logDTO={}", dto);
        try {
            BizLogClient.send("biz-log-oaconnectoropendata", Pojo2Protobuf.toMessage(dto, OAConnectorOpenDataLog.class).toByteArray());
        } catch (Exception e) {
            log.info("OAConnectorOpenDataManager.send,exception={}",e.getMessage());
        }
    }
}
