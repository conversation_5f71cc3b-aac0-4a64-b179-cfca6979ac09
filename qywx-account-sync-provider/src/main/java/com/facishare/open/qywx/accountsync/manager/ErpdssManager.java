package com.facishare.open.qywx.accountsync.manager;

import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountinner.model.CreateConnectorArg;
import com.facishare.open.qywx.accountinner.model.GetDcBindArg;
import com.facishare.open.qywx.accountinner.model.UpdateConnectParamsArg;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.entity.ErpConnectInfoEntity;
import com.facishare.open.qywx.accountsync.network.ProxyOkHttpClient;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.result.Result2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * 集成平台服务管理器
 * <AUTHOR>
 * @date 2024.4.23
 */

@Slf4j
@Component
public class ErpdssManager {
    @Autowired
    private ProxyOkHttpClient proxyOkHttpClient;
    @Autowired
    private EIEAConverter eieaConverter;

    public Result<Void> updateConnectParams(UpdateConnectParamsArg arg) {
        String url = ConfigCenter.ERPDSS_OACONNECTOR_WEB_URL + "/updateConnectParams";
        log.info("ErpdssManager.updateConnectParams,url={},arg={}",url,arg);

        if(arg == null
                || StringUtils.isEmpty(arg.getTenantId())
                || StringUtils.isEmpty(arg.getDataCenterId())
                || StringUtils.isEmpty(arg.getConnectParams())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        Result2 response = proxyOkHttpClient.postUrl(url, arg, new HashMap<>(),new TypeReference<Result2>(){
        });
        log.info("ErpdssManager.updateConnectParams,response={}",response);
        return response.isSuccess() ? new Result<>() : new Result<>(response.getErrCode(),response.getErrMsg(),null);
    }

    public Result<ErpConnectInfoEntity> getDcInfo(String fsEa, String dataCenterId) {
        int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
        String url = ConfigCenter.ERPDSS_OACONNECTOR_WEB_URL + "/getDcInfo?tenantId="+tenantId+"&dataCenterId="+dataCenterId;
        log.info("ErpdssManager.getDcInfo,fsEa={},dataCenterId={},url={}",fsEa,dataCenterId,url);

        if(StringUtils.isEmpty(fsEa)
                || StringUtils.isEmpty(dataCenterId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        Result2<ErpConnectInfoEntity> response = proxyOkHttpClient.postUrl(url, new Object(), new HashMap<>(),new TypeReference<Result2<ErpConnectInfoEntity>>(){
        });
        log.info("ErpdssManager.getDcInfo,response={}",response);
        return response.isSuccess() ? new Result<>(response.getData()) : new Result<>(response.getErrCode(),response.getErrMsg(),null);
    }

    public Result<ErpConnectInfoEntity> getDcBind(GetDcBindArg arg) {
        String url = ConfigCenter.ERPDSS_OACONNECTOR_WEB_URL + "/getDcBind";
        log.info("ErpdssManager.getDcBind,arg={}",arg);

        if(StringUtils.isEmpty(arg.getFsEa())
                || StringUtils.isEmpty(arg.getOutEa())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        Result2<ErpConnectInfoEntity> response = proxyOkHttpClient.postUrl(url, arg, new HashMap<>(),new TypeReference<Result2<ErpConnectInfoEntity>>(){
        });
        log.info("ErpdssManager.getDcBind,response={}",response);
        return response.isSuccess() ? new Result<>(response.getData()) : new Result<>(response.getErrCode(),response.getErrMsg(),null);
    }

    public Result<ErpConnectInfoEntity> createConnector(CreateConnectorArg arg) {
        String url = ConfigCenter.ERPDSS_OACONNECTOR_WEB_URL + "/createConnector";
        log.info("ErpdssManager.createConnector,arg={}",arg);

        if(arg == null
                || StringUtils.isEmpty(arg.getId())
                || StringUtils.isEmpty(arg.getTenantId())
                || StringUtils.isEmpty(arg.getDataCenterName())
                || StringUtils.isEmpty(arg.getConnectParams())
                || StringUtils.isEmpty(arg.getChannel())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        Result2<ErpConnectInfoEntity> response = proxyOkHttpClient.postUrl(url, arg, new HashMap<>(),new TypeReference<Result2<ErpConnectInfoEntity>>(){
        });
        log.info("ErpdssManager.createConnector,response={}",response);
        return response.isSuccess() ? new Result<>(response.getData()) : new Result<>(response.getErrCode(),response.getErrMsg(),null);
    }

    public static void main(String[] args) {
        Long time = 0L;
        if(time==0L) {
            System.out.println("ok");
        } else {
            System.out.println("no");
        }
    }
}
