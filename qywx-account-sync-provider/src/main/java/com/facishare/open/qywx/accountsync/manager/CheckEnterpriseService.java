//package com.facishare.open.qywx.accountsync.manager;
//
//import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
//import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
//import com.facishare.uc.api.model.fscore.RunStatus;
//import com.facishare.uc.api.service.EnterpriseEditionService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * Created by ma<PERSON><PERSON><PERSON> on 2019/9/12.
// */
//@Service
//public class CheckEnterpriseService {
//    @Autowired
//    private EnterpriseEditionService enterpriseEditionService;
//
//    public Boolean isActiveEnterprise(String enterpriseAccount) {
//        GetEnterpriseRunStatusArg getEnterpriseRunStatusArg = new GetEnterpriseRunStatusArg();
//        getEnterpriseRunStatusArg.setEnterpriseAccount(enterpriseAccount);
//        GetEnterpriseRunStatusResult enterpriseRunStatus = enterpriseEditionService.getEnterpriseRunStatus(getEnterpriseRunStatusArg);
//
//        return enterpriseRunStatus.getRunStatus() == RunStatus.RUN_STATUS_NORMAL;
//    }
//}
