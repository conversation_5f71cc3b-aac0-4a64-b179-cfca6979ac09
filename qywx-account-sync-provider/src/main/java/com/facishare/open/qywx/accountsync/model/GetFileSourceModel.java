package com.facishare.open.qywx.accountsync.model;

import com.facishare.open.qywx.accountsync.arg.FileUploadArg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetFileSourceModel implements Serializable {
    private String outEa;
    private String fileName;
    private FileUploadArg.FileTypeEnum fileType;
    private Long fileSize;
    private byte[] data;
}
