package com.facishare.open.qywx.accountsync.ckDao;

import com.facishare.open.qywx.accountsync.model.qyweixin.ckBo.BizLogOaconnectoropendataDistBo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 日志上报dao
 * <AUTHOR>
 * @date 2024-03-15
 */
@Repository
public interface BizLogOaconnectoropendataDistDao extends ICrudMapper<BizLogOaconnectoropendataDistBo> {

    @Select("<script>" + "select * from biz_log_oaconnectoropendata_dist where channelId=#{channelId} " +
            "and dataTypeId=#{dataTypeId} and corpId=#{corpId} " +"</script>")
    List<BizLogOaconnectoropendataDistBo> findEnterpriseCreateError(@Param("channelId") String channelId, @Param("dataTypeId") String dataTypeId, @Param("corpId") String corpId);
}
