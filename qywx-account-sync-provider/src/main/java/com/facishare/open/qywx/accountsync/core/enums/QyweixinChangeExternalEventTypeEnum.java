package com.facishare.open.qywx.accountsync.core.enums;

public enum QyweixinChangeExternalEventTypeEnum {
    ADD_EXTERNAL_CONTACT("add_external_contact", "添加企业客户事件"),
    EDIT_EXTERNAL_CONTACT("edit_external_contact", "编辑企业客户事件"),
    DEL_EXTERNAL_CONTACT("del_external_contact", "删除企业客户事件"),
    DEL_FOLLOW_USER("del_follow_user", "删除跟进成员事件"),
    ;

    private String eventType;
    private String name;

    QyweixinChangeExternalEventTypeEnum(String eventType, String name) {
        this.eventType = eventType;
        this.name = name;
    }

    public String getEventType() {
        return eventType;
    }

    public String getName() {
        return name;
    }
}
