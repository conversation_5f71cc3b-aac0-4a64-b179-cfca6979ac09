//package com.facishare.open.qywx.accountsync.manager;
//
//import com.alibaba.fastjson.JSON;
//import com.facishare.converter.EIEAConverter;
//import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
//import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
//import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
//import com.facishare.open.qywx.accountbind.result.Result;
//import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
//import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
//import com.facishare.open.qywx.accountsync.core.enums.QYWXBindTypeEnum;
//import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
//import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
//import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinDepartmentInfo;
//import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinEmployeeInfo;
//import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinTagEmployeeList;
//import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
//import com.facishare.open.qywx.accountsync.utils.OrganizationUtils;
//import com.facishare.open.qywx.accountsync.utils.UserRoleUtils;
//import com.facishare.open.qywx.accountsync.utils.WechatRegisterConfigHelper;
//import com.facishare.organization.adapter.api.exception.ErrorDepartmentNameNotExistException;
//import com.facishare.organization.adapter.api.exception.ErrorEmployeeAccountExistException;
//import com.facishare.organization.adapter.api.exception.ErrorEmployeeNameExistException;
//import com.facishare.organization.adapter.api.exception.ErrorEnterpriseEmployeeLimitFullException;
//import com.facishare.organization.adapter.api.model.PermissionSpecialOperator;
//import com.facishare.organization.adapter.api.model.biz.RunStatus;
//import com.facishare.organization.adapter.api.model.biz.department.ModifiedDepartment;
//import com.facishare.organization.adapter.api.model.biz.department.arg.*;
//import com.facishare.organization.adapter.api.model.biz.department.result.CreateDepartmentResult;
//import com.facishare.organization.adapter.api.model.biz.department.result.GetChildrenDepartmentResult;
//import com.facishare.organization.adapter.api.model.biz.department.result.ModifyDepartmentResult;
//import com.facishare.organization.adapter.api.model.biz.employee.*;
//import com.facishare.organization.adapter.api.model.biz.employee.arg.CreateEmployeeArg;
//import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
//import com.facishare.organization.adapter.api.model.biz.employee.arg.ModifyEmployeeArg;
//import com.facishare.organization.adapter.api.model.biz.employee.arg.ModifyEmployeeStopArg;
//import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
//import com.facishare.organization.adapter.api.model.biz.employee.result.ModifyEmployeeResult;
//import com.facishare.organization.adapter.api.permission.enums.role.SystemRoleEnum;
//import com.facishare.organization.adapter.api.permission.model.AddRoleWithDepartmentToEmployeesByAppId;
//import com.facishare.organization.adapter.api.permission.model.GetEmployeeIdsByRoleCodeAndAppId;
//import com.facishare.organization.adapter.api.permission.model.RoleCodeAndDepartmentIds;
//import com.facishare.organization.adapter.api.permission.service.PermissionService;
//import com.facishare.organization.adapter.api.service.DepartmentService;
//import com.facishare.organization.adapter.api.service.EmployeeService;
//import com.facishare.organization.adapter.api.util.Constant;
//import com.facishare.organization.api.exception.OrganizationException;
//import com.facishare.organization.api.model.employee.EmployeeDto;
//import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
//import com.facishare.organization.api.model.type.EmployeeEntityStatus;
//import com.facishare.organization.api.service.EmployeeProviderService;
//import com.facishare.webhook.common.dao.DepartmentDao;
//import com.facishare.webhook.common.dao.EmployeeDao;
//import com.facishare.webhook.common.dao.EnterpriseDao;
//import com.facishare.webhook.common.dao.model.DepartmentPo;
//import com.facishare.webhook.common.model.ResourceSource;
//import com.facishare.webhook.common.service.OrgNameService;
//import com.facishare.webhook.common.service.OrganizationService;
//import com.facishare.webhook.common.util.CommonConfigHelper;
//import com.facishare.webhook.common.util.FSAccountUtil;
//import com.facishare.webhook.common.util.StringHelper;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.collections4.ListUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.stream.Collectors;
//import java.util.stream.IntStream;
//import java.util.stream.Stream;
//
//import static com.facishare.webhook.common.util.Constant.OPERATOR;
//import static com.facishare.webhook.common.util.FSAccountUtil.makeUserId;
//import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
//
///**
// * Created by mawenrui on 2018/7/25.
// */
//@Service
//@Slf4j
//public class QiyeweixinOrganizationService implements OrganizationService {
//
//    @Autowired
//    private DepartmentService departmentService;
//    @Autowired
//    private EmployeeService employeeService;
//    @Autowired
//    private QyweixinAccountBindService qyweixinAccountBindService;
//    @Autowired
//    private QyweixinAccountSyncService qyweixinAccountSyncService;
//    @Autowired
//    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
//    @Autowired
//    private EIEAConverter eieaConverter;
//    @Autowired
//    private EnterpriseDao enterpriseDao;
//    @Autowired
//    private EmployeeDao employeeDao;
//    @Autowired
//    private DepartmentDao departmentDao;
//    @Autowired
//    private UserRoleUtils userRoleUtils;
//    @Autowired
//    private WechatMessageService wechatMessageService;
//    @Autowired
//    private PermissionService permissionService;
//    @Autowired
//    private OrgNameService orgNameService;
//    @Autowired
//    private EmployeeProviderService employeeProviderService;
//    @Autowired
//    private FsManager fsManager;
//
//    protected Random random = new Random();
//
//    private ResourceSource resourceSource = ResourceSource.SELF_WEIXIN;
//
//    public static final Integer ALL_COMPANY_DEPARTMENT = 999999;
//
//    @Override
//    public void createEmployee(String eid, Integer fsEI, String fsEA, String employeeInfoStr) {
//
//        if (isManualBinding(fsEA, eid)) {
//            return;
//        }
//
//        try {
//            QyweixinEmployeeInfo qyweixinEmployeeInfo = JSON.parseObject(employeeInfoStr, QyweixinEmployeeInfo.class);
//
//            // 如果员工状态是禁用，无需创建直接返回
//            if (Objects.equals(qyweixinEmployeeInfo.getEnable(), 0) || Objects.equals(qyweixinEmployeeInfo.getStatus(), 2)) {
//                return;
//            }
//            String appId = qyweixinEmployeeInfo.getAppId();
//
//
//            CreateEmployeeArg arg = new CreateEmployeeArg();
//            arg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//            arg.setEnterpriseId(fsEI);
//            arg.setGender("2".equalsIgnoreCase(qyweixinEmployeeInfo.getGender()) ? "F" : "M");
//            arg.setPost(qyweixinEmployeeInfo.getPosition());
//            arg.setProfileImage(qyweixinEmployeeInfo.getAvatar());
//            arg.setAccount(orgNameService.formatEmployeeAccount(qyweixinEmployeeInfo.getUserId()).toLowerCase());
//            arg.setName(orgNameService.formatEmployeeName(qyweixinEmployeeInfo.getName()));
//            arg.setFullName(orgNameService.formatEmployeeName(qyweixinEmployeeInfo.getName()));
//            arg.setMobile(StringUtils.isNotBlank(qyweixinEmployeeInfo.getMobile()) ? qyweixinEmployeeInfo.getMobile() : StringUtils.EMPTY);
//            arg.setMobileStatus(MobileStatus.PUBLIC);
//            arg.setInitialPassword(false);
//            arg.setPassword("a1" + StringHelper.getRandomCode(6));
//
//            // 设置部门
//            // 开通企业时创建员工，部门已经创建好
//            // 同步员工时创建员工，如果员工所在部门已经停用，需要自动启用，因为员工的消息可能在部门消息之前处理
//            arg.setDepartmentIds(Lists.newArrayList());
//            List<String> departmentList = qyweixinEmployeeInfo.getDepartment();
//            List<QyweixinAccountDepartmentMapping> accountDepartmentMappings = Lists.newArrayList();
//            log.info("createEmployee start. CreateEmployeeArg:{}, departmentList:{}", arg, departmentList);
//            if (CollectionUtils.isNotEmpty(departmentList)) {
//
//                qyweixinAccountBindService.deleteOrResumeDepartment(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, departmentList, false);
//                departmentList.forEach(departmentId -> departmentDao.deleteOrResumeDepartment(resourceSource.getWebhookSource(), eid, departmentId, false));
//
//                Result<List<QyweixinAccountDepartmentMapping>> bindResult = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(resourceSource.getWebhookSource(), eid, appId, departmentList);
//
//                if (!bindResult.isSuccess()) {
//                    throw new RuntimeException(bindResult.getErrorCode());
//                }
//
//                accountDepartmentMappings = bindResult.getData();
//
//                if (Objects.nonNull(accountDepartmentMappings)) {
//                    List<Integer> fsDepartmentIds = accountDepartmentMappings.stream()
//                            .filter(qyweixinAccountDepartmentMapping -> !ALL_COMPANY_DEPARTMENT.equals(qyweixinAccountDepartmentMapping.getFsDepartmentId()))
//                            .map(QyweixinAccountDepartmentMapping::getFsDepartmentId).collect(Collectors.toList());
//
//                    if (CollectionUtils.isNotEmpty(fsDepartmentIds)) {
//                        arg.setDepartmentIds(fsDepartmentIds);
//
//                        arg.setMainDepartmentId(fsDepartmentIds.get(0));
//
//                        fsDepartmentIds.forEach(id -> {
//                            ModifyDepartmentResumeArg modifyDepartmentResumeArg = new ModifyDepartmentResumeArg();
//                            modifyDepartmentResumeArg.setEnterpriseId(fsEI);
//                            modifyDepartmentResumeArg.setDepartmentId(id);
//                            modifyDepartmentResumeArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                            departmentService.modifyDepartmentResume(modifyDepartmentResumeArg);
//                        });
//                    }
//                }
//            }
//
//            Integer fsEmployeeId = 0;
//            try {
//                // 是否已经绑定过
//                Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.outAccountToFsAccount(SourceTypeEnum.QYWX.getSourceType(),
//                        fsEA, StringUtils.EMPTY, qyweixinEmployeeInfo.getUserId());
//                log.info("outAccountToFsAccount end. ea:{}, result:{}", fsEA, result);
//                if (result.getData().isEmpty()) {
//                    fsEmployeeId = employeeService.createEmployee(arg).getEmployee().getEmployeeId();
//                }else {
//                    Optional<QyweixinAccountEmployeeMapping> first = result.getData().stream().filter(mapping -> Objects.equals(mapping.getAppId(), appId)).findFirst();
//                    String fsAccount = result.getData().get(0).getFsAccount();
//                    log.info("check is current app end. ea:{}, first:{}, fsAccount:{}", fsEA, first, fsAccount);
//
//                    fsEmployeeId = FSAccountUtil.getEmployeeIdFromFSAccount(fsAccount);
//                    // 绑定了当前应用
//                    if (first.isPresent()) {
//                        qyweixinAccountBindService.deleteOrResumeEmployee(SourceTypeEnum.QYWX.getSourceType(), fsEA,
//                                appId, Lists.newArrayList(qyweixinEmployeeInfo.getUserId()), false);
//                    }
//                    resumeEmployee(fsEI, FSAccountUtil.getEmployeeIdFromFSAccount(fsAccount));
//
//                    //修改主属部门
//                    ModifyEmployee.Argument argument = new ModifyEmployee.Argument();
//                    argument.setEnterpriseId(fsEI);
//                    argument.setAccount(arg.getAccount());
//                    argument.setEmployeeId(fsEmployeeId);
//                    argument.setCurrentEmployeeId(arg.getCurrentEmployeeId());
//                    argument.setMainDepartmentId(arg.getMainDepartmentId());
//                    log.info("before modifyEmployeeV2. ea:{}, argument:{}", fsEA, argument);
//                    employeeService.modifyEmployeeV2(argument);
//
//                    fsEmployeeId = first.isPresent() ? 0 : fsEmployeeId;
//                }
//            } catch (ErrorEmployeeNameExistException e) {
//                // 员工停用之后，再添加同名员工，纷享侧会重名，所以重命名后创建员工
//                FindEmployeeByNames.Argument argument = new FindEmployeeByNames.Argument();
//                argument.setEnterpriseId(fsEI);
//                argument.setNickName(Lists.newArrayList(arg.getName()));
//                argument.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                argument.setRunStatus(RunStatus.ALL);
//                FindEmployeeByNames.Result result = employeeService.findEmployeeByNames(argument);
//
//                if (Objects.nonNull(result.getEmployeeDtos()) && result.getEmployeeDtos().size() == 1) {
//                    EmployeeDto oldItem = result.getEmployeeDtos().get(0);
//                    Integer employeeId; // 可能用旧的，也可能是新建
//                    if (oldItem.getAccount().equalsIgnoreCase(arg.getAccount())) {
//                        //账号已存在，重新建立绑定关系即可
//                        employeeId = oldItem.getEmployeeId();
//                    } else {
//                        //帐号不相等，只是名字相等，给与随机名字
//                        arg.setName(arg.getName() + (int) ((Math.random() * 9 + 1) * 1000));
//                        employeeId = employeeService.createEmployee(arg).getEmployee().getEmployeeId();
//                    }
//                    fsEmployeeId = employeeId;
//                }
//            } catch (ErrorEmployeeAccountExistException e) {
//                // 账号被他人使用
//                GetEmployeeDtoByAccount.Argument argument = new GetEmployeeDtoByAccount.Argument();
//                argument.setAccount(arg.getAccount());
//                argument.setEnterpriseId(fsEI);
//                argument.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                GetEmployeeDtoByAccount.Result result = employeeService.getEmployeeDtoByAccount(argument);
//
//                if (Objects.nonNull(result.getEmployeeDto())) {
//                    fsEmployeeId = result.getEmployeeDto().getEmployeeId();
//                }
//            } catch (ErrorEnterpriseEmployeeLimitFullException e) {
//                log.warn("企业微信配额异常" + e);
//                return;
//            }
//
//            log.info("check fsEmployeeId. ea:{}, fsEmployeeId:{}", fsEA, fsEmployeeId);
//            // 其他通讯录未知错误，fsEmployeeId仍然为0
//            if (fsEmployeeId > 0) {
//                // 绑定员工
//                QyweixinAccountEmployeeMapping accountEmployeeMapping = new QyweixinAccountEmployeeMapping();
//                accountEmployeeMapping.setSource(resourceSource.getWebhookSource());
//                accountEmployeeMapping.setFsAccount(makeUserId(fsEA, fsEmployeeId));
//                accountEmployeeMapping.setOutAccount(qyweixinEmployeeInfo.getUserId());
//                accountEmployeeMapping.setAppId(appId);
//                accountEmployeeMapping.setOutEa(eid);
//                List<QyweixinAccountEmployeeMapping> accountEmployeeMappings = Lists.newArrayList(accountEmployeeMapping);
//
//                qyweixinAccountBindService.bindAccountEmployeeMapping(accountEmployeeMappings);
//
//                // 写入纷享mongo
//                employeeDao.findAndModifyEmployee(
//                        resourceSource.getWebhookSource(),
//                        eid,
//                        qyweixinEmployeeInfo.getUserId(),
//                        employeeInfoStr
//                );
//
//                // 只有安装了通讯录应用，才能获取到部门上级
//                if (isNotEmpty(qyweixinEmployeeInfo.getIsLeaderInDept())) {
//                    // 如果员工是部门负责人，为部门设置负责人
//                    Map<String, Integer> deptLeaderMap = getLeaderDeptMap(qyweixinEmployeeInfo.getDepartment(), qyweixinEmployeeInfo.getIsLeaderInDept());
//
//                    for (int i = 0; i < accountDepartmentMappings.size(); i++) {
//                        if (Objects.nonNull(deptLeaderMap.get(accountDepartmentMappings.get(i).getOutDepartmentId()))) {
//                            setFSDepartmentPrinciple(fsEI, accountDepartmentMappings.get(i).getFsDepartmentId(), fsEmployeeId);
//                        }
//                    }
//                }
//            }
//        } catch (Exception ex) {
//            // 可能会有未知错误, 防止中断处理流程
//            log.error("createEmployee Exception. eid:{}, employeeInfoStr:{}", eid, employeeInfoStr, ex);
//        }
//    }
//
//    private void setFSDepartmentPrinciple(Integer fsEI, Integer fsDepartmentId, Integer fsEmployeeId) {
//        ModifyDepartmentArg modifyDepartmentArg = new ModifyDepartmentArg();
//
//        modifyDepartmentArg.setEnterpriseId(fsEI);
//        modifyDepartmentArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//
//        ModifiedDepartment modifiedDepartment = new ModifiedDepartment();
//        modifiedDepartment.setDepartmentId(fsDepartmentId);
//        modifiedDepartment.setDepartmentPrincipalId(fsEmployeeId);
//        modifyDepartmentArg.setModifiedDepartment(modifiedDepartment);
//
//        departmentService.modifyDepartment(modifyDepartmentArg);
//    }
//
//    private Map<String, Integer> getLeaderDeptMap(List<String> deptIds, List<Integer> isLeaders) {
//        Map<String, Integer> leaderDeptMap = new HashMap<>();
//
//        IntStream.range(0, deptIds.size()).forEach(i -> {
//            if (isLeaders.get(i) == 1) {
//                leaderDeptMap.put(deptIds.get(i), isLeaders.get(i));
//            }
//        });
//
//        return leaderDeptMap;
//    }
//
//    @Override
//    public void createDepartment(String eid, Integer fsEI, String fsEA, String departmentInfoStr) {
//        if (isManualBinding(fsEA, eid)) {
//            return;
//        }
//
//        try {
//            QyweixinDepartmentInfo qyweixinDepartmentInfo = JSON.parseObject(departmentInfoStr, QyweixinDepartmentInfo.class);
//            Integer fsDepartmentId = 0;
//            String appId = qyweixinDepartmentInfo.getAppId();
//
//            // 父部门id为0的是根部门，只有一个
//            if ("0".equalsIgnoreCase(qyweixinDepartmentInfo.getParentId())) {
//                // 此部门为0级部门，修改"全公司"为部门名称
//                ModifyCompanyArg modifyCompanyArg = new ModifyCompanyArg();
//                modifyCompanyArg.setEnterpriseId(fsEI);
//                modifyCompanyArg.setDepartmentName(qyweixinDepartmentInfo.getName());
//                modifyCompanyArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                departmentService.modifyCompany(modifyCompanyArg);
//
//                fsDepartmentId = ALL_COMPANY_DEPARTMENT;
//            } else {
//                Result<List<QyweixinAccountDepartmentMapping>> result = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(
//                        SourceTypeEnum.QYWX.getSourceType(), eid, StringUtils.EMPTY, -1, Lists.newArrayList(qyweixinDepartmentInfo.getId()));
//                // 没有该部门信息或全部停用去创建
//                if (CollectionUtils.isEmpty(result.getData())) {
//                    CreateDepartmentArg arg = new CreateDepartmentArg();
//
//                    arg.setEnterpriseId(fsEI);
//                    arg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                    arg.setObserverIds(Lists.newArrayList());
//
//                    // 创建部门的时候父部门可能还没有创建，父部门先统一设置为全公司
//                    arg.setParentId(Constant.COMPANY_DEPARTMENT_ID);
//
//                    String finalDepartmentName = orgNameService.formatDepartmentName(qyweixinDepartmentInfo.getName());
//
//                    arg.setName(finalDepartmentName);
//
//                    try {
//                        CreateDepartmentResult ret = departmentService.createDepartment(arg);
//                        fsDepartmentId = ret.getDepartment().getDepartmentId();
//                    } catch (OrganizationException ex) {
//                        // 部门名称已存在, 改为部门名称+部门id
//                        arg.setName(finalDepartmentName + "(" + qyweixinDepartmentInfo.getId() + ")");
//                        CreateDepartmentResult ret = departmentService.createDepartment(arg);
//                        fsDepartmentId = ret.getDepartment().getDepartmentId();
//                    }
//                }else {
//                    Result<List<QyweixinAccountDepartmentMapping>> listResult = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(
//                            SourceTypeEnum.QYWX.getSourceType(), eid, appId, -1, Lists.newArrayList(qyweixinDepartmentInfo.getId()));
//                    // 存在关系但是不是此应用的绑定关系
//                    if (listResult.getData().isEmpty()) {
//                        fsDepartmentId = result.getData().get(0).getFsDepartmentId();
//                    }else {
//                        qyweixinAccountBindService.deleteOrResumeDepartment(SourceTypeEnum.QYWX.getSourceType(), eid,
//                                    appId, Lists.newArrayList(qyweixinDepartmentInfo.getId()), false);
//                    }
//                    resumeDepartment(fsEI, result.getData().get(0).getFsDepartmentId());
//                }
//            }
//
//            if (fsDepartmentId > 0) {
//                // 创建成功后立即绑定，而不是批量绑定，避免创建中失败，一部分创建成功却没有绑定的问题
//                List<QyweixinAccountDepartmentMapping> accountDepartmentMappings = Lists.newArrayList();
//                QyweixinAccountDepartmentMapping accountDepartmentMapping = new QyweixinAccountDepartmentMapping();
//                accountDepartmentMapping.setSource(resourceSource.getWebhookSource());
//                accountDepartmentMapping.setFsEa(fsEA);
//                accountDepartmentMapping.setFsDepartmentId(fsDepartmentId);
//                accountDepartmentMapping.setOutEa(eid);
//                accountDepartmentMapping.setOutDepartmentId(String.valueOf(qyweixinDepartmentInfo.getId()));
//                accountDepartmentMapping.setAppId(qyweixinDepartmentInfo.getAppId());
//
//                accountDepartmentMappings.add(accountDepartmentMapping);
//
//                qyweixinAccountBindService.bindAccountDepartmentMapping(accountDepartmentMappings);
//
//                // 保存到mongo
//                departmentDao.findAndModifyDepartment(
//                        resourceSource.getWebhookSource(),
//                        eid,
//                        String.valueOf(qyweixinDepartmentInfo.getId()),
//                        departmentInfoStr);
//            }
//        } catch (Exception ex) {
//            // 可能会有未知错误, 防止中断处理流程
//            log.error("create department error. eid:{}, departmentInfoStr:{}", eid, departmentInfoStr, ex);
//        }
//    }
//
//    @Override
//    public void modifyEmployee(String eid, Integer fsEI, String fsEA, String employeeInfoStr) {
//
//        if (isManualBinding(fsEA, eid)) {
//            return;
//        }
//
//        Boolean shouldUpdateMongoEmployee = true;
//        String wxUserId = StringUtils.EMPTY;
//        try {
//            QyweixinEmployeeInfo qyweixinEmployeeInfo = JSON.parseObject(employeeInfoStr, QyweixinEmployeeInfo.class);
//            wxUserId = qyweixinEmployeeInfo.getUserId();
//            String appId = qyweixinEmployeeInfo.getAppId();
//            String corpId = qyweixinEmployeeInfo.getCorpId();
//
//            Result<Map<String, String>> employeeMappingResult = qyweixinAccountBindService.outAccountToFsAccountBatch(
//                    resourceSource.getWebhookSource(), fsEA, appId,
//                    Lists.newArrayList(qyweixinEmployeeInfo.getUserId()));
//
//            if (!employeeMappingResult.isSuccess()) {
//                throw new RuntimeException(employeeMappingResult.getErrorCode());
//            }
//
//            String fsAccount = employeeMappingResult.getData().get(qyweixinEmployeeInfo.getUserId());
//            Integer employeeId = FSAccountUtil.getEmployeeIdFromFSAccount(fsAccount);
//
//            // 停用员工, 停用后直接返回
//            if (Objects.equals(qyweixinEmployeeInfo.getEnable(), 0) || Objects.equals(qyweixinEmployeeInfo.getStatus(), 2)) {
//                // 是否存在正常态非当前应用的应用。所有应用都停用才会停用员工
//                long count = qyweixinAccountBindService.queryFsAccountBindByOldOutAccount(SourceTypeEnum.QYWX.getSourceType(),
//                        Lists.newArrayList(wxUserId), appId, corpId).getData().parallelStream()
//                        .filter(mapping -> !Objects.equals(mapping.getAppId(), appId))
//                        .filter(mapping -> mapping.getStatus() == 0).count();
//                if (count <= 0) {
//                    ModifyEmployeeStopArg modifyEmployeeStopArg = new ModifyEmployeeStopArg();
//                    modifyEmployeeStopArg.setEmployeeId(employeeId);
//                    modifyEmployeeStopArg.setStop(true);
//                    modifyEmployeeStopArg.setLastUpdateTime(System.currentTimeMillis());
//                    modifyEmployeeStopArg.setEnterpriseId(fsEI);
//                    modifyEmployeeStopArg.setCurrentEmployeeId(PermissionSpecialOperator.KIS_TRANSFER.getOperatorId());
//                    employeeService.modifyEmployeeStop(modifyEmployeeStopArg);
//                    employeeDao.deleteOrResumeEmployee(resourceSource.getWebhookSource(), eid, qyweixinEmployeeInfo.getUserId(), true);
//                }
//                qyweixinAccountBindService.deleteOrResumeEmployee(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, Lists.newArrayList(qyweixinEmployeeInfo.getUserId()), true);
//                employeeDao.updateEmployeeInfo(resourceSource.getWebhookSource(), eid, wxUserId, employeeInfoStr);
//                return;
//            } else {
//                // 如果员工是已经停用状态，先启用再修改
//                GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
//                getEmployeeDtoArg.setEnterpriseId(fsEI);
//                getEmployeeDtoArg.setEmployeeId(employeeId);
//                getEmployeeDtoArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                GetEmployeeDtoResult getEmployeeDtoResult = employeeService.getEmployeeDto(getEmployeeDtoArg);
//
//                log.info("getEmployeeDto success. ea:{}, arg:{} ,result:{}",fsEA, getEmployeeDtoArg, getEmployeeDtoResult);
//                // 更新为正常态
//                employeeDao.deleteOrResumeEmployee(resourceSource.getWebhookSource(), eid, qyweixinEmployeeInfo.getUserId(), false);
//                qyweixinAccountBindService.deleteOrResumeEmployee(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, Lists.newArrayList(qyweixinEmployeeInfo.getUserId()), false);
//
//                if (getEmployeeDtoResult.getEmployee().getStatus() == EmployeeEntityStatus.STOP) {
//                    ModifyEmployeeStopArg modifyEmployeeStopArg = new ModifyEmployeeStopArg();
//                    modifyEmployeeStopArg.setEmployeeId(employeeId);
//                    modifyEmployeeStopArg.setStop(false);
//                    modifyEmployeeStopArg.setLastUpdateTime(System.currentTimeMillis());
//                    modifyEmployeeStopArg.setEnterpriseId(fsEI);
//                    modifyEmployeeStopArg.setCurrentEmployeeId(PermissionSpecialOperator.KIS_TRANSFER.getOperatorId());
//
//                    try {
//                        employeeService.modifyEmployeeStop(modifyEmployeeStopArg);
//                        userRoleUtils.setUserRole(fsEA, appId,  Lists.newArrayList(String.valueOf(employeeId)));
//                    } catch (ErrorEnterpriseEmployeeLimitFullException ex) {
//                        ex.printStackTrace();
//                        log.error("启用员工超出配额限制: ", ex);
//                        return;
//                    }
//                }
//            }
//
//            ModifiedEmployee arg = new ModifiedEmployee();
//            arg.setEmployeeId(employeeId);
//            arg.setGender("2".equalsIgnoreCase(qyweixinEmployeeInfo.getGender()) ? "F" : "M");
//            arg.setPost(qyweixinEmployeeInfo.getPosition());
//            arg.setProfileImage(qyweixinEmployeeInfo.getAvatar());
//
//            // 修改员工的时候，如果姓名不合法，就使用原来的名字
//            String name = qyweixinEmployeeInfo.getName().replaceAll(" ", "");
//            log.warn("modify employee name :{}", name);
//            if (CommonConfigHelper.getEmployeeNameRegex().matcher(name).find() || name.length() > 20) {
//                Result<Map<String, String>> employeeResult = qyweixinAccountBindService.outAccountToFsAccountBatch(
//                        resourceSource.getWebhookSource(), fsEA, appId,Lists.newArrayList(qyweixinEmployeeInfo.getUserId()));
//                if (employeeResult.isSuccess()) {
//                    GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
//                    getEmployeeDtoArg.setEnterpriseId(fsEI);
//                    getEmployeeDtoArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                    fsAccount = employeeResult.getData().get(qyweixinEmployeeInfo.getUserId());
//                    getEmployeeDtoArg.setEmployeeId(FSAccountUtil.getEmployeeIdFromFSAccount(fsAccount));
//                    GetEmployeeDtoResult getEmployeeDtoResult = employeeService.getEmployeeDto(getEmployeeDtoArg);
//
//                    name = getEmployeeDtoResult.getEmployee().getName();
//                }
//            }
//            name = StringUtils.substring(name, 0, 20);
//            arg.setName(name);
//            arg.setFullName(name);
//
//            EmployeeDto employeeDto = OrganizationUtils.getEmployeeDto(fsEA, employeeId);
//            if (!Objects.equals(name, employeeDto.getName())) {
//                arg.setName(employeeDto.getName());
//                arg.setFullName(employeeDto.getFullName());
//            }
//
//            arg.setAccount(orgNameService.formatEmployeeAccount(qyweixinEmployeeInfo.getUserId()));
//
//            arg.setDepartmentIds(Lists.newArrayList());
//            log.info("check department start. ea:{}, arg:{}, departments:{}", fsEA, arg, qyweixinEmployeeInfo.getDepartment());
//            if (isNotEmpty(qyweixinEmployeeInfo.getDepartment())) {
//                Result<List<QyweixinAccountDepartmentMapping>> departmentMappingResult =
//                        qyweixinAccountBindService.queryDepartmentBindByOutDepartment(resourceSource.getWebhookSource(), eid, appId, qyweixinEmployeeInfo.getDepartment());
//
//                if (!departmentMappingResult.isSuccess()) {
//                    throw new RuntimeException(departmentMappingResult.getErrorCode());
//                }
//
//                List<QyweixinAccountDepartmentMapping> qyweixinAccountDepartmentMappings = departmentMappingResult.getData();
//                log.info("check qyweixinAccountDepartmentMappings. ea:{}, mappings:{}", fsEA, qyweixinAccountDepartmentMappings);
//                if (Objects.nonNull(qyweixinAccountDepartmentMappings)) {
//                    List<Integer> fsDepartmentIds = qyweixinAccountDepartmentMappings.stream()
//                            .filter(qyweixinAccountDepartmentMapping -> !ALL_COMPANY_DEPARTMENT.equals(qyweixinAccountDepartmentMapping.getFsDepartmentId()))
//                            .map(QyweixinAccountDepartmentMapping::getFsDepartmentId).collect(Collectors.toList());
//
//                    log.info("get fsDepartmentIds. ea:{}, fsDepartmentIds:{}", fsEA, fsDepartmentIds);
//                    if (CollectionUtils.isNotEmpty(fsDepartmentIds)) {
//                        arg.setDepartmentIds(fsDepartmentIds);
//                        arg.setMainDepartmentId(fsDepartmentIds.get(0));
//
//                        // 只有安装了通讯录应用，才能获取到部门上级
//                        if (CollectionUtils.isNotEmpty(qyweixinEmployeeInfo.getIsLeaderInDept())) {
//                            // 如果员工是部门负责人，为部门设置负责人
//                            Map<String, Integer> deptLeaderMap = getLeaderDeptMap(qyweixinEmployeeInfo.getDepartment(), qyweixinEmployeeInfo.getIsLeaderInDept());
//
//                            qyweixinAccountDepartmentMappings.stream().forEach(qyweixinAccountDepartmentMapping -> {
//                                if (Objects.nonNull(deptLeaderMap.get(qyweixinAccountDepartmentMapping.getOutDepartmentId()))) {
//                                    setFSDepartmentPrinciple(fsEI, qyweixinAccountDepartmentMapping.getFsDepartmentId(), employeeId);
//                                }
//                            });
//                        }
//                    }
//                }
//            }
//
//            arg.setMobileStatus(MobileStatus.PUBLIC.getValue());
//            arg.setMobile(qyweixinEmployeeInfo.getMobile());
//
//            ModifyEmployeeArg employeeArg = new ModifyEmployeeArg();
//            employeeArg.setModifiedEmployee(arg);
//            employeeArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//            employeeArg.setEnterpriseId(fsEI);
//
//            try {
//                ModifyEmployeeResult employeeResult = employeeService.modifyEmployee(employeeArg);
//                log.info("modifyEmployee success. argument:{}, result:{}", employeeArg, employeeResult);
//            } catch (OrganizationException e) {
//                // 员工重名,加上随机数
//                employeeArg.getModifiedEmployee().setName(StringUtils.substring(arg.getName() + (random.nextInt(8999) + 1000), 0, 20));
//                employeeService.modifyEmployee(employeeArg);
//            }
//        } catch (Exception ex) {
//            // 可能会有未知错误, 防止中断处理流程
//            log.error("modifyEmployee Exception. eid:{}, employeeInfoStr:{}", eid, employeeInfoStr, ex);
//            shouldUpdateMongoEmployee = false;
//        }
//
//        if (shouldUpdateMongoEmployee) {
//            employeeDao.updateEmployeeInfo(resourceSource.getWebhookSource(), eid, wxUserId, employeeInfoStr);
//        }
//    }
//
//    @Override
//    public void modifyDepartment(String eid, Integer fsEI, String fsEA, String departmentInfoStr) {
//
//        if (isManualBinding(fsEA, eid)) {
//            return;
//        }
//
//        try {
//            QyweixinDepartmentInfo qyweixinDepartmentInfo = JSON.parseObject(departmentInfoStr, QyweixinDepartmentInfo.class);
//            String appId = qyweixinDepartmentInfo.getAppId();
//            Result<List<QyweixinAccountDepartmentMapping>> mappingResult = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(
//                    resourceSource.getWebhookSource(),
//                    eid,
//                    appId,
//                    -1,
//                    Lists.newArrayList(qyweixinDepartmentInfo.getId()));
//
//            if (!mappingResult.isSuccess()) {
//                throw new RuntimeException(mappingResult.getErrorCode());
//            }
//
//            if (Objects.isNull(mappingResult.getData()) || mappingResult.getData().size() == 0) {
//                return;
//            }
//
//            Integer departmentId = mappingResult.getData().get(0).getFsDepartmentId();
//
//            if (Objects.isNull(departmentId)) {
//                return;
//            }
//
//            // 修改的是0级部门, 只修改名称
//            if (ALL_COMPANY_DEPARTMENT.equals(departmentId)) {
//                ModifyCompanyArg modifyCompanyArg = new ModifyCompanyArg();
//                modifyCompanyArg.setEnterpriseId(fsEI);
//                modifyCompanyArg.setDepartmentName(qyweixinDepartmentInfo.getName());
//                modifyCompanyArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//                departmentService.modifyCompany(modifyCompanyArg);
//
//                return;
//            }
//
//            // 部门停用后，修改部门表示企业微信已经启用了部门
//            ModifyDepartmentResumeArg modifyDepartmentResumeArg = new ModifyDepartmentResumeArg();
//            modifyDepartmentResumeArg.setEnterpriseId(fsEI);
//            modifyDepartmentResumeArg.setDepartmentId(departmentId);
//            modifyDepartmentResumeArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//            departmentService.modifyDepartmentResume(modifyDepartmentResumeArg);
//
//            qyweixinAccountBindService.deleteOrResumeDepartment(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, Lists.newArrayList(qyweixinDepartmentInfo.getId()), false);
//            departmentDao.deleteOrResumeDepartment(resourceSource.getWebhookSource(), eid, qyweixinDepartmentInfo.getId(), false);
//
//            String finalDepartmentName = orgNameService.formatDepartmentName(qyweixinDepartmentInfo.getName());
//
//            ModifiedDepartment department = new ModifiedDepartment();
//
//            department.setObserverIds(Lists.newArrayList());
//            department.setDepartmentId(departmentId);
//            department.setName(finalDepartmentName);
//            String currentName = OrganizationUtils.getDepartmentInfo(fsEA, departmentId).getName();
//            if (!Objects.equals(finalDepartmentName, currentName)) {
//                department.setName(currentName);
//            }
//
//            ModifyDepartmentArg arg = new ModifyDepartmentArg();
//            arg.setEnterpriseId(fsEI);
//            arg.setModifiedDepartment(department);
//            arg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//
//            // 根部门不需要拖动
//            if (!"0".equalsIgnoreCase(qyweixinDepartmentInfo.getParentId())) {
//                // 拖动部门
//                Integer dragParentId = Constant.COMPANY_DEPARTMENT_ID;
//                if (StringUtils.isNotEmpty(qyweixinDepartmentInfo.getParentId())) {
//                    // 父部门可能被停用了，也可能不可见
//                    Result<List<QyweixinAccountDepartmentMapping>> result = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType()
//                            , eid, appId, -1, Lists.newArrayList(qyweixinDepartmentInfo.getParentId()));
//                    List<QyweixinAccountDepartmentMapping> data = result.getData();
//
//                    if (CollectionUtils.isNotEmpty(data) && (data.get(0).getStatus() == 0)) {
//                        Result<List<QyweixinAccountDepartmentMapping>> parentMappingResult =
//                                qyweixinAccountBindService.queryDepartmentBindByOutDepartment(
//                                        resourceSource.getWebhookSource(),
//                                        eid,
//                                        appId,
//                                        Lists.newArrayList(qyweixinDepartmentInfo.getParentId()));
//
//                        if (!parentMappingResult.isSuccess()) {
//                            throw new RuntimeException(parentMappingResult.getErrorCode());
//                        }
//
//                        if (CollectionUtils.isNotEmpty(parentMappingResult.getData()) && parentMappingResult.getData().size() > 0) {
//                            dragParentId = parentMappingResult.getData().get(0).getFsDepartmentId();
//                        }
//                    }
//                }
//
//                DragDepartmentArg dragDepartmentArg = new DragDepartmentArg();
//                dragDepartmentArg.setEnterpriseId(fsEI);
//                dragDepartmentArg.setParentID(dragParentId);
//                dragDepartmentArg.setDepartmentId(departmentId);
//                dragDepartmentArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//
//                departmentService.dragDepartment(dragDepartmentArg);
//            }
//
//            // 修改部门
//            try {
//                ModifyDepartmentResult modifyDepartmentResult = departmentService.modifyDepartment(arg);
//                log.info("modifyDepartment success. argument:{}, result:{}", arg, modifyDepartmentResult);
//            } catch (ErrorDepartmentNameNotExistException ex) {
//                // 重名部门，在重命名后重新修改
//                log.warn("rename, modifyDepartment exception. ", ex);
//                arg.getModifiedDepartment().setName(finalDepartmentName + "(" + qyweixinDepartmentInfo.getId() + ")");
//                departmentService.modifyDepartment(arg);
//            }
//
//            departmentDao.updateDepartmentInfo(resourceSource.getWebhookSource(), eid, qyweixinDepartmentInfo.getId(), departmentInfoStr);
//        } catch (Exception ex) {
//            log.error("modifyDepartment exception. eid:{}, departmentInfoStr:{}", eid, departmentInfoStr, ex);
//        }
//    }
//
//    private Boolean isManualBinding(String fsEa, String eid) {
//        boolean isBinding = false;
//
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(eid);
//        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
//            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
//            Integer type = enterpriseMapping.getBindType();
//            log.debug("isManualBinding start. fsEa:{}, eid:{}, enterpriseMapping:{}", fsEa, eid, enterpriseMapping);
//            if (QYWXBindTypeEnum.OLD_CORP_BIND.getCode().equals(type)) {
//                isBinding = true;
//            }
//        }
//        return isBinding;
//    }
//
//    @Override
//    public void syncEmployee(String eid, String appId, String fsEA, String openId) {
//        int fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//
//        // 拿到企业微信侧员工信息
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> employeeInfoResult =
//                qyweixinAccountSyncService.getEmployeeInfoBatch(fsEA, appId, Lists.newArrayList(openId));
//
//        if (!employeeInfoResult.isSuccess()) {
//            throw new RuntimeException(employeeInfoResult.getErrorCode());
//        }
//
//        if (CollectionUtils.isEmpty(employeeInfoResult.getData())) {
//            return;
//        }
//
//        // 肯定有员工信息
//        QyweixinEmployeeInfo qyweixinEmployeeInfo = employeeInfoResult.getData().stream().findFirst()
//                .orElseThrow(() ->
//                        new RuntimeException(String.format("employeeNotFound eid:%s appId:%s fsEA:%s openId:%s", eid, appId, fsEA, openId))
//                );
//
//        // 查询绑定关系
//        Integer fsEmployeeId = 0;
//        qyweixinEmployeeInfo.setAppId(appId);
//        Result<Map<String, String>> employeeBindingResult = qyweixinAccountBindService.outAccountToFsAccountBatch(
//                resourceSource.getWebhookSource(), fsEA, appId, Lists.newArrayList(openId));
//        if (!employeeBindingResult.isSuccess()) {
//            throw new RuntimeException(employeeBindingResult.getErrorCode());
//        }
//
//        Map<String, String> employeeBindings = employeeBindingResult.getData();
//
//        // 没有绑定的创建，绑定的同步
//        if (Objects.isNull(employeeBindings) || Objects.isNull(employeeBindings.get(openId))) {
//            createEmployee(eid, fsEI, fsEA, JSON.toJSONString(qyweixinEmployeeInfo));
//
//            // 创建员工有可能失败，此时再查询一次绑定关系, 没有绑定关系就返回
//            employeeBindingResult = qyweixinAccountBindService.outAccountToFsAccountBatch(
//                    resourceSource.getWebhookSource(), fsEA, appId, Lists.newArrayList(openId));
//            employeeBindings = employeeBindingResult.getData();
//
//            if (Objects.isNull(employeeBindings) || Objects.isNull(employeeBindings.get(openId))) {
//                return;
//            }
//
//            // 新加员工需要发送欢迎消息
//            fsEmployeeId = FSAccountUtil.getEmployeeIdFromFSAccount(employeeBindings.get(openId));
//            List<String> receiverIds = Lists.newArrayList(String.valueOf(fsEmployeeId));
//            WechatRegisterConfigHelper.getWechatMessageValue().forEach((key, value) -> {
//                wechatMessageService.sendMessage(
//                        fsEA,
//                        value.getTitle(),
//                        value.getUrl(),
//                        value.getPicUrl(),
//                        receiverIds
//                );
//            });
//            // 添加角色
//            fsEmployeeId = FSAccountUtil.getEmployeeIdFromFSAccount(employeeBindings.get(openId));
//            userRoleUtils.setUserRole(fsEA, appId,  Lists.newArrayList(String.valueOf(fsEmployeeId)));
//        } else {
//            modifyEmployee(eid, fsEI, fsEA, JSON.toJSONString(qyweixinEmployeeInfo));
//        }
//    }
//
//
//    private List<Integer> getAdminIds(Integer enterpriseId) {
//        GetEmployeeIdsByRoleCodeAndAppId.Argument argument = new GetEmployeeIdsByRoleCodeAndAppId.Argument();
//        argument.setCurrentEmployeeId(-8);
//        argument.setEnterpriseId(enterpriseId);
//        argument.setAppId("facishare-system");
//        argument.setRoleCode("99");
//        GetEmployeeIdsByRoleCodeAndAppId.Result result = permissionService.getEmployeeIdsByRoleCodeAndAppId(argument);
//
//        BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
//        arg.setEnterpriseId(enterpriseId);
//        arg.setEmployeeIds(result.getEmployeeIds());
//        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
//        return employeeProviderService.batchGetEmployeeDto(arg).getEmployeeDtos().stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
//    }
//
//    /**
//     * 企业微信可以修改员工ID，此时修改员工信息时，也要修改员工绑定关系
//     *
//     * @param eid
//     * @param appId
//     * @param fsEA
//     * @param openId
//     * @param fsAccount
//     */
//    public void syncEmployeeWithNewUserId(String eid, String appId, String fsEA, String openId, String fsAccount) {
//        Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//
//        // 拿到员工信息
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> employeeInfoResult =
//                qyweixinAccountSyncService.getEmployeeInfoBatch(fsEA, appId, Lists.newArrayList(openId));
//        if (!employeeInfoResult.isSuccess()) {
//            throw new RuntimeException(employeeInfoResult.getErrorCode());
//        }
//
//        if (CollectionUtils.isEmpty(employeeInfoResult.getData())) {
//            return;
//        }
//
//        // 通过fsAccount,即employeeId，获取老的微信userId
//        Result<Map<String, String>> oldEmployeeResult = qyweixinAccountBindService.fsAccountToOutAccountBatch(
//                        resourceSource.getWebhookSource(), appId, Lists.newArrayList(fsAccount));
//
//        if (!oldEmployeeResult.isSuccess()) {
//            throw new RuntimeException(oldEmployeeResult.getErrorCode());
//        }
//
//        if (Objects.isNull(oldEmployeeResult.getData()) || oldEmployeeResult.getData().size() == 0) {
//            return;
//        }
//
//        String oldAccount = oldEmployeeResult.getData().get(fsAccount);
//
//        // 更新mongo数据的userID
//        employeeDao.updateEmployeeId(resourceSource.getWebhookSource(), eid, oldAccount, openId);
//
//        // 修改绑定关系(所有应用)
//        qyweixinAccountBindService.updateByNewOutAccount(openId, oldAccount, StringUtils.EMPTY, eid);
//
//        // 肯定有员工信息, 修改员工
//        QyweixinEmployeeInfo qyweixinEmployeeInfo = employeeInfoResult.getData().get(0);
//        modifyEmployee(eid, fsEI, fsEA, JSON.toJSONString(qyweixinEmployeeInfo));
//
//        // 设置角色
//        Integer fsEmployeeId = FSAccountUtil.getEmployeeIdFromFSAccount(fsAccount);
//        userRoleUtils.setUserRole(fsEA, appId, Lists.newArrayList(String.valueOf(fsEmployeeId)));
//    }
//
//    @Override
//    public void deleteEmployee(String eid, String appId, String fsEA, String openId) {
//
//        Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//
//        // 查询绑定关系
//        Result<Map<String, String>> employeeBindingResult = qyweixinAccountBindService.outAccountToFsAccountBatch(
//                resourceSource.getWebhookSource(), fsEA, appId, Lists.newArrayList(openId));
//        if (!employeeBindingResult.isSuccess()) {
//            throw new RuntimeException(employeeBindingResult.getErrorCode());
//        }
//
//        Map<String, String> employeeBindings = employeeBindingResult.getData();
//
//        if (Objects.isNull(employeeBindings) || Objects.isNull(employeeBindings.get(openId))) {
//            return;
//        }
//        // 是否存在正常态非当前应用的应用。所有应用都停用才会停用员工
//        long count = qyweixinAccountBindService.queryFsAccountBindByOldOutAccount(SourceTypeEnum.QYWX.getSourceType(),
//                Lists.newArrayList(openId), StringUtils.EMPTY, eid).getData().parallelStream()
//                .filter(mapping -> !Objects.equals(mapping.getAppId(), appId))
//                .filter(mapping -> mapping.getStatus() == 0).count();
//        Integer fsEmployeeId = FSAccountUtil.getEmployeeIdFromFSAccount(employeeBindings.get(openId));
//        if (count <= 0) {
//            //删除的员工查不到具体信息，只能直接停用
//
//            // 停用员工
//            ModifyEmployeeStopArg modifyEmployeeStopArg = new ModifyEmployeeStopArg();
//            modifyEmployeeStopArg.setEmployeeId(fsEmployeeId);
//            modifyEmployeeStopArg.setStop(true);
//            modifyEmployeeStopArg.setLastUpdateTime(System.currentTimeMillis());
//            modifyEmployeeStopArg.setEnterpriseId(fsEI);
//            modifyEmployeeStopArg.setCurrentEmployeeId(PermissionSpecialOperator.KIS_TRANSFER.getOperatorId());
//            employeeService.modifyEmployeeStop(modifyEmployeeStopArg);
//            // 把mongo数据设置为删除
//            employeeDao.deleteOrResumeEmployee(resourceSource.getWebhookSource(), eid, openId, true);
//        }
//
//        // 移除角色
//        userRoleUtils.removeUserRole(fsEA, appId, Lists.newArrayList(String.valueOf(fsEmployeeId)));
//
//        qyweixinAccountBindService.deleteOrResumeEmployee(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, Lists.newArrayList(openId), true);
//    }
//
//    /**
//     * 单独变更部门的消息，部门下不会有员工，无需创建员工
//     * 批量变更部门和员工的消息，部门消息和员工消息会单独发出，无需创建员工
//     *
//     * @param eid
//     * @param fsEA
//     * @param openId
//     */
//    @Override
//    public void syncDepartment(String eid, String appId, String fsEA, String openId) {
//
//        Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//
//        // 拿到部门信息
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinDepartmentInfo>> departmentResult =
//                qyweixinAccountSyncService.getDepartmentInfoList(fsEA, appId, openId);
//
//        if (!departmentResult.isSuccess() || Objects.isNull(departmentResult.getData())) {
//            throw new RuntimeException(departmentResult.getErrorCode());
//        }
//
//        // 企业微信的部门和子部门的顺序是乱的, 先找到openId的部门
//        // 企业微信在更新可见范围的时候，可能会同时删除部门
//        // 企业微信变更父部门的时候，会发出同步部门的消息，但可能父部门不在可见范围里，获取不到部门信息
//        Optional<QyweixinDepartmentInfo> qyweixinDepartmentInfoOptional = departmentResult.getData().stream()
//                .filter(qyweixinDepartmentInfo -> qyweixinDepartmentInfo.getId().equalsIgnoreCase(openId))
//                .findFirst();
//
//        Optional.ofNullable(qyweixinDepartmentInfoOptional).ifPresent(qyweixinDepartmentInfo -> {
//            // 查询部门绑定关系
//            Result<List<QyweixinAccountDepartmentMapping>> departmentBindingResult =
//                    qyweixinAccountBindService.queryDepartmentBindByOutDepartment(resourceSource.getWebhookSource(),
//                            eid, appId, -1, Lists.newArrayList(openId));
//
//            if (!departmentBindingResult.isSuccess()) {
//                throw new RuntimeException(departmentBindingResult.getErrorCode());
//            }
//
//            // 没有绑定部门则创建新部门，创建后必须修改
//            if (Objects.isNull(departmentBindingResult.getData()) || CollectionUtils.isEmpty(departmentBindingResult.getData())) {
//                createDepartment(eid, fsEI, fsEA, JSON.toJSONString(qyweixinDepartmentInfo));
//                modifyDepartment(eid, fsEI, fsEA, JSON.toJSONString(qyweixinDepartmentInfo));
//            } else {
//                DepartmentPo departmentPo = departmentDao.getDepartment(resourceSource.getWebhookSource(), eid, openId);
//                // 有可能用户把企业微信和已经有的纷享企业进行绑定，没有走webhook，虽然有绑定关系，但是mongo没有数据
//                if (Objects.isNull(departmentPo)) {
//                    departmentDao.findAndModifyDepartment(resourceSource.getWebhookSource(), eid, openId, JSON.toJSONString(qyweixinDepartmentInfo));
//                    modifyDepartment(eid, fsEI, fsEA, JSON.toJSONString(qyweixinDepartmentInfo));
//                } else if (!departmentPo.getDepartmentInfo().equalsIgnoreCase(JSON.toJSONString(qyweixinDepartmentInfo)) || departmentPo.getIsDelete()) {
//                    modifyDepartment(eid, fsEI, fsEA, JSON.toJSONString(qyweixinDepartmentInfo));
//                }
//                qyweixinAccountBindService.deleteOrResumeDepartment(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId,
//                        Lists.newArrayList(openId), false);
//            }
//        });
//    }
//
//    /**
//     * 添加部门的也是，也要添加子部门
//     *
//     * @param eid
//     * @param appId
//     * @param fsEA
//     * @param openId
//     */
//    @Override
//    public void  addAllowDepartment(String eid, String appId, String fsEA, String openId) {
//
//        // 拿到部门信息
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinDepartmentInfo>> departmentResult =
//                qyweixinAccountSyncService.getDepartmentInfoList(fsEA, appId, openId);
//
//        if (!departmentResult.isSuccess()) {
//            throw new RuntimeException(departmentResult.getErrorCode());
//        }
//
//        if (Objects.nonNull(departmentResult.getData())) {
//            departmentResult.getData().forEach(qyweixinDepartmentInfo -> {
//                // 对部门及其子部门进行处理
//                syncDepartment(eid, appId, fsEA, openId);
//            });
//
//            Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//            departmentResult.getData().forEach(qyweixinDepartmentInfo -> {
//                // 上面创建部门的时候，可能父部门还没有创建
//                modifyDepartment(eid, fsEI, fsEA, JSON.toJSONString(qyweixinDepartmentInfo));
//            });
//        }
//
//        Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//
//        // 对部门下的员工进行处理
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> departmentEmployeeInfoResult =
//                qyweixinAccountSyncService.getDepartmentEmployeeInfoList(fsEA, appId, openId);
//
//        if (!departmentEmployeeInfoResult.isSuccess()) {
//            throw new RuntimeException("qyweixinAccountSyncService.getDepartmentEmployeeInfoList error: " + departmentEmployeeInfoResult.getErrorCode());
//        }
//
//        log.info("addAllowDepartment get department employee info. ea:{}, result:{}", fsEA, departmentEmployeeInfoResult);
//        List<String> fsEmployeeList = Lists.newArrayList();
//        if (Objects.nonNull(departmentEmployeeInfoResult.getData())) {
//            departmentEmployeeInfoResult.getData().forEach(qyweixinEmployeeInfo -> {
//                String userId = qyweixinEmployeeInfo.getUserId();
//
//                // 查询绑定关系
//                Result<Map<String, String>> employeeBindingResult = qyweixinAccountBindService.outAccountToFsAccountBatch(
//                        resourceSource.getWebhookSource(), fsEA, appId, Lists.newArrayList(userId));
//                if (!employeeBindingResult.isSuccess()) {
//                    throw new RuntimeException(employeeBindingResult.getErrorCode());
//                }
//
//                Map<String, String> employeeBindings = employeeBindingResult.getData();
//
//                log.info("addAllowDepartment check employee bind. ea:{}, userId:{}, employeeBindings:{}", fsEA, userId,employeeBindings);
//                // 没有绑定的创建，绑定的同步
//                if (Objects.isNull(employeeBindings.get(userId))) {
//                    employeeDao.findAndModifyEmployee(resourceSource.getWebhookSource(), eid, userId, JSON.toJSONString(qyweixinEmployeeInfo));
//                    createEmployee(eid, fsEI, fsEA, JSON.toJSONString(qyweixinEmployeeInfo));
//
//                    employeeBindingResult = qyweixinAccountBindService.outAccountToFsAccountBatch(
//                            resourceSource.getWebhookSource(), fsEA, Lists.newArrayList(userId));
//
//                    employeeBindings = employeeBindingResult.getData();
//                } else {
//                    // 已经绑定的员工，进行修改
//                    employeeDao.updateEmployeeInfo(resourceSource.getWebhookSource(), eid, userId, JSON.toJSONString(qyweixinEmployeeInfo));
//                    qyweixinAccountBindService.deleteOrResumeEmployee(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, Lists.newArrayList(openId), false);
//                    modifyEmployee(eid, fsEI, fsEA, JSON.toJSONString(qyweixinEmployeeInfo));
//                }
//
//                Integer fsEmployeeId = FSAccountUtil.getEmployeeIdFromFSAccount(employeeBindings.get(userId));
//
//                fsEmployeeList.add(String.valueOf(fsEmployeeId));
//            });
//        }
//
//        // 加入对应的角色中
//        userRoleUtils.setUserRole(fsEA, appId, fsEmployeeList);
//    }
//
//    @Override
//    public void reduceAllowDepartment(String eid, String appId, String fsEA, String openId) {
//        // 对部门下的员工进行处理
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> departmentEmployeeInfoResult =
//                qyweixinAccountSyncService.getDepartmentEmployeeInfoList(fsEA, appId, openId);
//
//        if (!departmentEmployeeInfoResult.isSuccess()) {
//            throw new RuntimeException("qyweixinAccountSyncService.getDepartmentEmployeeInfoList error: " + departmentEmployeeInfoResult.getErrorCode());
//        }
//
//        if (Objects.nonNull(departmentEmployeeInfoResult.getData())) {
//            departmentEmployeeInfoResult.getData().forEach(qyweixinEmployeeInfo -> {
//                deleteEmployee(eid, appId, fsEA, qyweixinEmployeeInfo.getUserId());
//            });
//        }
//
//        // 对部门进行处理
//        deleteDepartment(eid, appId, fsEA, openId);
//    }
//
//    /**
//     * 可见范围变更
//     *
//     * @param fsEA
//     * @param eid
//     * @param appId
//     * @param allowParty
//     * @param allowUser
//     * @param allowTag
//     */
//    public void syncPrevilege(String fsEA, String eid, String appId, List<String> allowParty, List<String> allowUser, List<String> allowTag) {
//
//        // 所有可见的部门，包含可见范围里的部门及其子部门，可见范围里tag下的部门及其子部门
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinDepartmentInfo>>
//                allAvailableDepartmentResult = qyweixinAccountSyncService.getDepartmentInfoList(fsEA, appId, StringUtils.EMPTY);
//
//        log.info("syncPrevilege fsEA:{},eid:{},appId:{},allowUser:{}",fsEA,eid,appId,allowUser);
//
//        if (!allAvailableDepartmentResult.isSuccess()) {
//            throw new RuntimeException("qyweixinAccountSyncService.getDepartmentInfoList error:" + allAvailableDepartmentResult.getErrorMsg());
//        }
//
//        // 转化成所有可见部门id
//        List<String> allAvailableDepartmentIds = allAvailableDepartmentResult.getData().stream().map(QyweixinDepartmentInfo::getId).collect(Collectors.toList());
//
//        // 所有可见员工id，即可见范围里的员工+可见tag下的员工+可见部门下的员工
//        List<String> allUserIds = getUserIds(fsEA, appId, allowTag, allAvailableDepartmentIds, allowUser);
//
//        log.info("syncPrevilege getUserIds success. ea:{}, eid:{}, allUserIds:{}", fsEA, eid, allUserIds);
//        // 同步部门
//        syncPrevilegeDepartment(fsEA, eid, appId, allAvailableDepartmentIds);
//
//        // 同步员工
//        syncPrevilegeEmployee(fsEA, eid, appId, allUserIds);
//
//        Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//        //如果没有系统管理员了，就设置一个
//        if (CollectionUtils.isEmpty(getAdminIds(fsEI))) {
//            Integer fsEmployeeId = FSAccountUtil.getEmployeeIdFromFSAccount(qyweixinAccountBindService.outAccountToFsAccountBatch(
//                    resourceSource.getWebhookSource(), fsEA, appId, Lists.newArrayList(allUserIds.get(0))).getData().get(allUserIds.get(0)));
//            log.info("Set system admin start. ea:{}, admin usrId:{}", fsEA, fsEmployeeId);
//            AddRoleWithDepartmentToEmployeesByAppId.Argument argument = new AddRoleWithDepartmentToEmployeesByAppId.Argument();
//            argument.setEnterpriseId(fsEI);
//            argument.setEmployeeIds(Lists.newArrayList(fsEmployeeId));
//
//            argument.setCurrentEmployeeId(OPERATOR.getOperatorId());
//            argument.setAppId(SystemRoleEnum.SYSTEM_MANAGEMENT.getAppId());
//
//            RoleCodeAndDepartmentIds roleCodeAndDepartmentIds = new RoleCodeAndDepartmentIds();
//            roleCodeAndDepartmentIds.setRoleCode(SystemRoleEnum.SYSTEM_MANAGEMENT.getRoleCode());
//            roleCodeAndDepartmentIds.setDepartmentIds(Lists.newArrayList(ALL_COMPANY_DEPARTMENT));
//            argument.setRoleCodeAndDepartmentIds(roleCodeAndDepartmentIds);
//
//            permissionService.addRoleWithDepartmentToEmployeesByAppId(argument);
//        }
//    }
//
//    private void syncPrevilegeDepartment(String fsEA, String eid, String appId, List<String> allAvailableDepartmentIds) {
//        List<String> dbActivePartyIds = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(), eid, appId
//                , Collections.emptyList()).getData().stream()
//                .map(QyweixinAccountDepartmentMapping::getOutDepartmentId)
//                .collect(Collectors.toList());
//
//        // 不在可见范围的部门
//        List<String> removeAllDepartmentList = ListUtils.removeAll(dbActivePartyIds, allAvailableDepartmentIds);
//
//        // 停用不可见的部门
//        removeAllDepartmentList.stream().distinct().forEach(partyId -> deleteDepartment(eid, appId, fsEA, partyId));
//
//        // 增加或更新可见的部门
//        allAvailableDepartmentIds.stream().distinct().forEach(partyId -> syncDepartment(eid, appId, fsEA, partyId));
//    }
//
//    public void syncPrevilegeEmployee(String fsEA, String eid, String appId, List<String> allUserIds) {
//        List<String> dbActiveUserIds =
//                qyweixinAccountBindService.outAccountToFsAccountBatch(SourceTypeEnum.QYWX.getSourceType(),
//                fsEA, appId, Lists.newArrayList(),-1).getData().parallelStream()
//                .map(QyweixinAccountEmployeeMapping::getOutAccount).collect(Collectors.toList());
//
//        // 不在可见范围的员工
//        List<String> removeAllEmployeeList = ListUtils.removeAll(dbActiveUserIds, allUserIds);
//
//        // 停用不可见的员工
//        removeAllEmployeeList.stream().distinct().forEach(outEmployeeId -> deleteEmployee(eid, appId, fsEA, outEmployeeId));
//
//        // 增加或更新可见的员工
//        allUserIds.stream().distinct().forEach(userId -> syncEmployee(eid, appId, fsEA, userId));
//    }
//
//    /**
//     * 获取tag下的员工+部门下的员工+可见范围的员工
//     * tag下的部门，可以通过获取部门的接口查出来，没必要再查一次，只需要查询tag下的员工
//     *
//     * @param fsEA
//     * @param appId
//     * @param allowTag
//     * @return
//     */
//    private List<String> getUserIds(String fsEA, String appId, List<String> allowTag, List<String> allowParty, List<String> allowUser) {
//
//        return Stream.concat(
//                Stream.concat(
//                        // 部门下的员工
//                        ListUtils.emptyIfNull(allowParty).stream().map(partyId -> {
//                            com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>>
//                                    employeeResult = qyweixinAccountSyncService.getDepartmentEmployeeInfoList(fsEA, appId, partyId);
//
//                            if (!employeeResult.isSuccess()) {
//                                throw new RuntimeException("qyweixinAccountSyncService.getDepartmentEmployeeInfoList error: " + employeeResult.getErrorCode());
//                            }
//
//                            return employeeResult.getData();
//                        }),
//
//                        // tag下的员工
//                        ListUtils.emptyIfNull(allowTag).stream().map(tagId -> {
//                            com.facishare.open.qywx.accountsync.result.Result<QyweixinTagEmployeeList>
//                                    tagResult = qyweixinAccountSyncService.getTagEmployeeInfoList(fsEA, appId, tagId);
//                            if (!tagResult.isSuccess()) {
//                                throw new RuntimeException("qyweixinAccountSyncService.getTagEmployeeInfoList error: " + tagResult.getErrorCode());
//                            }
//
//                            return tagResult.getData();
//                        }).filter(Objects::nonNull).map(QyweixinTagEmployeeList::getUserList)
//                ).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).map(QyweixinEmployeeInfo::getUserId),
//
//                // 可见范围里的员工
//                ListUtils.emptyIfNull(allowUser).stream()
//        ).distinct().collect(Collectors.toList());
//    }
//
//    /**
//     * 删除部门，部门下肯定没有员工
//     *
//     * @param eid
//     * @param fsEA
//     * @param openId
//     */
//    @Override
//    public void deleteDepartment(String eid, String appId, String fsEA, String openId) {
//        Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//
//        // 查询绑定关系
//        Result<List<QyweixinAccountDepartmentMapping>> departmentBindingResult = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(
//                resourceSource.getWebhookSource(), eid, appId, Lists.newArrayList(openId));
//
//        log.info("QiyeweixinOrganizationService.deleteDepartment,departmentBindingResult={}",departmentBindingResult);
//
//        if (!departmentBindingResult.isSuccess()) {
//            throw new RuntimeException(departmentBindingResult.getErrorCode());
//        }
//
//        if (CollectionUtils.isEmpty(departmentBindingResult.getData())) {
//            return;
//        }
//
//        Integer fsDepartmentId = departmentBindingResult.getData().get(0).getFsDepartmentId();
//
//        if (ALL_COMPANY_DEPARTMENT.equals(fsDepartmentId)) {
//            return;
//        }
//
//        // 查询是否有其他正常应用使用该部门
//        long count = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(), eid,
//                StringUtils.EMPTY, Lists.newArrayList(openId)).getData().stream().filter(mapping -> !mapping.getAppId().equals(appId))
//                .count();
//        // 没有其他应用到到，停用
//        if (count <= 0) {
//            // 删除部门前，把子部门移动到根部门下
//            GetChildrenDepartmentArg getChildrenDepartmentArg = new GetChildrenDepartmentArg();
//            getChildrenDepartmentArg.setDepartmentId(fsDepartmentId);
//            getChildrenDepartmentArg.setSelf(false);
//            getChildrenDepartmentArg.setEnterpriseId(fsEI);
//            getChildrenDepartmentArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//
//            GetChildrenDepartmentResult childrenDepartment = departmentService.getChildrenDepartment(getChildrenDepartmentArg);
//            if (Objects.nonNull(childrenDepartment)) {
//                childrenDepartment.getDepartments().forEach(department -> {
//                    DragDepartmentArg dragDepartmentArg = new DragDepartmentArg();
//                    dragDepartmentArg.setEnterpriseId(fsEI);
//                    dragDepartmentArg.setParentID(Constant.COMPANY_DEPARTMENT_ID);
//                    dragDepartmentArg.setDepartmentId(department.getDepartmentId());
//                    dragDepartmentArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//
//                    departmentService.dragDepartment(dragDepartmentArg);
//                });
//            }
//
//            // 停用部门
//            ModifyDepartmentStopArg arg = new ModifyDepartmentStopArg();
//            arg.setEnterpriseId(fsEI);
//            arg.setDepartmentId(fsDepartmentId);
//            arg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//            departmentService.modifyDepartmentStop(arg);
//        }
//
//        qyweixinAccountBindService.deleteOrResumeDepartment(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, Lists.newArrayList(openId), true);
//        departmentDao.deleteOrResumeDepartment(resourceSource.getWebhookSource(), eid, openId, true);
//
//        // 拿到部门信息
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinDepartmentInfo>> departmentResult =
//                qyweixinAccountSyncService.getDepartmentInfoList(fsEA, appId, openId);
//
//        if (!departmentResult.isSuccess()) {
//            throw new RuntimeException(departmentResult.getErrorCode());
//        }
//
//        if (Objects.nonNull(departmentResult.getData())) {
//            departmentResult.getData().forEach(qyweixinDepartmentInfo -> {
//                // 更新mongo里的部门信息
//                departmentDao.updateDepartmentInfo(resourceSource.getWebhookSource(), eid, openId, JSON.toJSONString(qyweixinDepartmentInfo));
//            });
//        }
//
//        // 不需要移除的人需要重新启用
//        {
//            List<String> outAccounts = Lists.newArrayList();
//            fsManager.getUserIdsByDepartmentId(fsEA, fsDepartmentId).forEach(userId -> outAccounts.add(FSAccountUtil.makeUserId(fsEA, userId)));
//            getDontDeleteEmployeeIds(appId, outAccounts).forEach(fsAccount -> resumeEmployee(fsEI, FSAccountUtil.getEmployeeIdFromFSAccount(fsAccount)));
//        }
//
//        // 获取部门下的人员移出角色
//        List<String> userIds = fsManager.getUserIdsByDepartmentId(fsEA, fsDepartmentId).stream().map(String::valueOf).collect(Collectors.toList());
//        userRoleUtils.removeUserRole(fsEA, appId, userIds);
//        log.info("QiyeweixinOrganizationService.deleteDepartment, success");
//    }
//
//    /**
//     * 删除部门，部门下肯定没有员工
//     * 删除部门工具专用
//     *
//     * @param eid
//     * @param fsEA
//     * @param openId
//     */
//    public void deleteDepartment2(String eid, String appId, String fsEA, String openId) {
//        Integer fsEI = eieaConverter.enterpriseAccountToId(fsEA);
//
//        // 查询绑定关系
//        Result<List<QyweixinAccountDepartmentMapping>> departmentBindingResult = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(
//                resourceSource.getWebhookSource(), eid, appId,-1, Lists.newArrayList(openId));
//
//        log.info("QiyeweixinOrganizationService.deleteDepartment,departmentBindingResult={}",departmentBindingResult);
//
//        if (!departmentBindingResult.isSuccess()) {
//            throw new RuntimeException(departmentBindingResult.getErrorCode());
//        }
//
//        if (CollectionUtils.isEmpty(departmentBindingResult.getData())) {
//            throw new RuntimeException("库里没有绑定的部门信息");
//        }
//
//        Integer fsDepartmentId = departmentBindingResult.getData().get(0).getFsDepartmentId();
//
//        if (ALL_COMPANY_DEPARTMENT.equals(fsDepartmentId)) {
//            throw new RuntimeException("部门ID错误，纷享根部门无法删除");
//        }
//
//        // 查询是否有其他正常应用使用该部门
//        long count = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(), eid,
//                StringUtils.EMPTY, Lists.newArrayList(openId)).getData().stream().filter(mapping -> !mapping.getAppId().equals(appId))
//                .count();
//        // 没有其他应用到到，停用
//        if (count <= 0) {
//            // 删除部门前，把子部门移动到根部门下
//            GetChildrenDepartmentArg getChildrenDepartmentArg = new GetChildrenDepartmentArg();
//            getChildrenDepartmentArg.setDepartmentId(fsDepartmentId);
//            getChildrenDepartmentArg.setSelf(false);
//            getChildrenDepartmentArg.setEnterpriseId(fsEI);
//            getChildrenDepartmentArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//
//            GetChildrenDepartmentResult childrenDepartment = departmentService.getChildrenDepartment(getChildrenDepartmentArg);
//            if (Objects.nonNull(childrenDepartment)) {
//                childrenDepartment.getDepartments().forEach(department -> {
//                    DragDepartmentArg dragDepartmentArg = new DragDepartmentArg();
//                    dragDepartmentArg.setEnterpriseId(fsEI);
//                    dragDepartmentArg.setParentID(Constant.COMPANY_DEPARTMENT_ID);
//                    dragDepartmentArg.setDepartmentId(department.getDepartmentId());
//                    dragDepartmentArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//
//                    departmentService.dragDepartment(dragDepartmentArg);
//                });
//            }
//
//            // 停用部门
//            ModifyDepartmentStopArg arg = new ModifyDepartmentStopArg();
//            arg.setEnterpriseId(fsEI);
//            arg.setDepartmentId(fsDepartmentId);
//            arg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//            departmentService.modifyDepartmentStop(arg);
//        }
//
//        qyweixinAccountBindService.deleteOrResumeDepartment(SourceTypeEnum.QYWX.getSourceType(), fsEA, appId, Lists.newArrayList(openId), true);
//        departmentDao.deleteOrResumeDepartment(resourceSource.getWebhookSource(), eid, openId, true);
//
//        // 拿到部门信息
//        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinDepartmentInfo>> departmentResult =
//                qyweixinAccountSyncService.getDepartmentInfoList(fsEA, appId, openId);
//
//        if (!departmentResult.isSuccess()) {
//            throw new RuntimeException(departmentResult.getErrorCode());
//        }
//
//        if (Objects.nonNull(departmentResult.getData())) {
//            departmentResult.getData().forEach(qyweixinDepartmentInfo -> {
//                // 更新mongo里的部门信息
//                departmentDao.updateDepartmentInfo(resourceSource.getWebhookSource(), eid, openId, JSON.toJSONString(qyweixinDepartmentInfo));
//            });
//        }
//
//        // 不需要移除的人需要重新启用
//        {
//            List<String> outAccounts = Lists.newArrayList();
//            fsManager.getUserIdsByDepartmentId(fsEA, fsDepartmentId).forEach(userId -> outAccounts.add(FSAccountUtil.makeUserId(fsEA, userId)));
//            getDontDeleteEmployeeIds(appId, outAccounts).forEach(fsAccount -> resumeEmployee(fsEI, FSAccountUtil.getEmployeeIdFromFSAccount(fsAccount)));
//        }
//
//        // 获取部门下的人员移出角色
//        List<String> userIds = fsManager.getUserIdsByDepartmentId(fsEA, fsDepartmentId).stream().map(String::valueOf).collect(Collectors.toList());
//        userRoleUtils.removeUserRole(fsEA, appId, userIds);
//        log.info("QiyeweixinOrganizationService.deleteDepartment, success");
//    }
//
//    public List<String> getDontDeleteEmployeeIds(String appId, List<String> fsAccounts) {
//        if (CollectionUtils.isEmpty(fsAccounts)) return Lists.newArrayList();
//        return qyweixinAccountBindService.fsAccountToOutAccountBatchV2(SourceTypeEnum.QYWX.getSourceType(),
//                StringUtils.EMPTY, 0, fsAccounts)
//                .getData().stream()
//                .filter(mapping -> !Objects.equals(mapping.getAppId(), appId))
//                .map(QyweixinAccountEmployeeMapping::getFsAccount)
//                .collect(Collectors.toList());
//    }
//
//    public void updateOrganizationForReOpen(String updateData) {
//        QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(updateData, QyweixinEnterpriseOrder.class);
//
//        String eid = qyweixinAddEnterprise.getCorpId();
//
//        Result<String> eaResult = qyweixinAccountBindService.outEaToFsEa(resourceSource.getWebhookSource(), eid,null);
//
//        if (!eaResult.isSuccess()) {
//            return;
//        }
//
//        String fsEa = eaResult.getData();
//
//        syncPrevilege(
//                fsEa,
//                qyweixinAddEnterprise.getCorpId(),
//                qyweixinAddEnterprise.getAppId(),
//                qyweixinAddEnterprise.getPrivilege().getAllowParty(),
//                qyweixinAddEnterprise.getPrivilege().getAllowUser(),
//                qyweixinAddEnterprise.getPrivilege().getAllowTag()
//        );
//    }
//
//    public void resumeEmployee(int ei, int employeeId) {
//        ModifyEmployeeStopArg modifyEmployeeStopArg = new ModifyEmployeeStopArg();
//        modifyEmployeeStopArg.setEmployeeId(employeeId);
//        modifyEmployeeStopArg.setStop(false);
//        modifyEmployeeStopArg.setLastUpdateTime(System.currentTimeMillis());
//        modifyEmployeeStopArg.setEnterpriseId(ei);
//        modifyEmployeeStopArg.setCurrentEmployeeId(PermissionSpecialOperator.KIS_TRANSFER.getOperatorId());
//
//        try {
//            employeeService.modifyEmployeeStop(modifyEmployeeStopArg);
//        } catch (ErrorEnterpriseEmployeeLimitFullException ex) {
//            ex.printStackTrace();
//            log.warn("启用员工超出配额限制: ", ex);
//        }
//    }
//
//    public void resumeDepartment(int ei, int departmentId) {
//        ModifyDepartmentResumeArg modifyDepartmentResumeArg = new ModifyDepartmentResumeArg();
//        modifyDepartmentResumeArg.setEnterpriseId(ei);
//        modifyDepartmentResumeArg.setDepartmentId(departmentId);
//        modifyDepartmentResumeArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//        departmentService.modifyDepartmentResume(modifyDepartmentResumeArg);
//    }
//}
