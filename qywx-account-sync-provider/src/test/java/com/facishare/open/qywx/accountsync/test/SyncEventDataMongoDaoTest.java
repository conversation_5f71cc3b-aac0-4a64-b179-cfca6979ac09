package com.facishare.open.qywx.accountsync.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.qywx.accountinner.model.FsAccountModel;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.arg.QuerySyncEventDataArg;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.mongo.dao.SyncEventDataMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.SyncEventDataDoc;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.utils.HttpHelper;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class SyncEventDataMongoDaoTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private SyncEventDataMongoDao syncEventDataMongoDao;

    @ReloadableProperty("crmAppId")
    private String crmAppId;
    @ReloadableProperty("repAppId")
    private String repAppId;

    @Test
    public void testMongoDao() {
        for (int i = 0; i < 10; i++) {
            List<SyncEventDataDoc> dataDocs = new LinkedList<>();
            SyncEventDataDoc dataDoc = new SyncEventDataDoc();
            dataDoc.setId(ObjectId.get());
            dataDoc.setOutEa("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g");
            dataDoc.setEventType("change_auth");
            dataDoc.setEvent("哈哈哈");
            dataDoc.setAppId(repAppId);
            dataDoc.setStatus(0);
            dataDoc.setCreateTime(System.currentTimeMillis());
            dataDoc.setUpdateTime(System.currentTimeMillis());
            dataDocs.add(dataDoc);
            //插入
            BulkWriteResult result = syncEventDataMongoDao.batchReplace(dataDocs);
            System.out.println(result);
            //查询
            SyncEventDataDoc messageSaveDoc = syncEventDataMongoDao.getById(dataDoc.getId().toString());
            System.out.println(messageSaveDoc);
        }
        for (int i = 0; i < 10; i++) {
            List<SyncEventDataDoc> dataDocs = new LinkedList<>();
            SyncEventDataDoc dataDoc = new SyncEventDataDoc();
            dataDoc.setId(ObjectId.get());
            dataDoc.setOutEa("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
            dataDoc.setEventType("change_auth");
            dataDoc.setEvent("哈哈哈");
            dataDoc.setAppId(repAppId);
            dataDoc.setStatus(0);
            dataDoc.setCreateTime(System.currentTimeMillis());
            dataDoc.setUpdateTime(System.currentTimeMillis());
            dataDocs.add(dataDoc);
            //插入
            BulkWriteResult result = syncEventDataMongoDao.batchReplace(dataDocs);
            System.out.println(result);
            //查询
            SyncEventDataDoc messageSaveDoc = syncEventDataMongoDao.getById(dataDoc.getId().toString());
            System.out.println(messageSaveDoc);
        }
        for (int i = 0; i < 10; i++) {
            List<SyncEventDataDoc> dataDocs = new LinkedList<>();
            SyncEventDataDoc dataDoc = new SyncEventDataDoc();
            dataDoc.setId(ObjectId.get());
            dataDoc.setOutEa("wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw");
            dataDoc.setEventType("change_auth");
            dataDoc.setEvent("哈哈哈");
            dataDoc.setAppId(repAppId);
            dataDoc.setStatus(0);
            dataDoc.setCreateTime(System.currentTimeMillis());
            dataDoc.setUpdateTime(System.currentTimeMillis());
            dataDocs.add(dataDoc);
            //插入
            BulkWriteResult result = syncEventDataMongoDao.batchReplace(dataDocs);
            System.out.println(result);
            //查询
            SyncEventDataDoc messageSaveDoc = syncEventDataMongoDao.getById(dataDoc.getId().toString());
            System.out.println(messageSaveDoc);
        }

        //查询
        int pageNum = 0;
        int size = 0;
        Set<String> outEas = new HashSet<>();
        QuerySyncEventDataArg arg = new QuerySyncEventDataArg();
        arg.setAppId(repAppId);
        //arg.setEventType(Lists.newArrayList("change_auth", "change_contact"));
        arg.setStatus(0);
        arg.setPageSize(9);
        do {
            arg.setPageNum(pageNum ++);
            List<SyncEventDataDoc> messageSaveDocs = syncEventDataMongoDao.pageByQuerySyncEventDataArg(arg);
            System.out.println(messageSaveDocs);
            if(CollectionUtils.isNotEmpty(messageSaveDocs)) {
                Set<String> outEaSet = messageSaveDocs.stream().map(SyncEventDataDoc::getOutEa).collect(Collectors.toSet());
                outEas.addAll(outEaSet);
            }
            size = messageSaveDocs.size();
        } while(size == 9);
        //删除，
        if(CollectionUtils.isEmpty(outEas)) {
            return;
        }
        for(String outEa : outEas) {
            QuerySyncEventDataArg delArg = new QuerySyncEventDataArg();
            delArg.setAppId(repAppId);
           // delArg.setEventType(Lists.newArrayList("change_auth", "change_contact"));
            delArg.setStatus(0);
            delArg.setOutEa(outEa);
            DeleteResult result1 = syncEventDataMongoDao.deleteTableDataByDelArg(delArg);
            System.out.println(result1);
        }
    }

    @Test
    public void batchReplace() {
        List<SyncEventDataDoc> dataDocs = new LinkedList<>();
        SyncEventDataDoc dataDoc = new SyncEventDataDoc();
        dataDoc.setId(ObjectId.get());
        dataDoc.setOutEa("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        dataDoc.setEventType("change_auth");
        dataDoc.setEvent("哈哈哈");
        dataDoc.setAppId(repAppId);
        dataDoc.setStatus(0);
        dataDoc.setCreateTime(System.currentTimeMillis());
        dataDoc.setUpdateTime(System.currentTimeMillis());
        dataDocs.add(dataDoc);
        //插入
        BulkWriteResult result = syncEventDataMongoDao.batchReplace(dataDocs);
        System.out.println(result);
    }
}
