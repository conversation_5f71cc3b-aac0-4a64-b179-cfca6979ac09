package com.facishare.open.qywx.accountsync.service

import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService
import com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl
import com.facishare.open.qywx.accountsync.utils.xml.RepExternalChatEventXml
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class QyweixinGatewayInnerServiceTest extends Specification {
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService

    @Autowired
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerServiceImpl

    def "repMsgEvent2"() {
        given:
        String xml = xmlCase
        expect:
        def result = qyweixinGatewayInnerServiceImpl.repMsgEvent2(xml,null)
        println result;
        where:
        type  |  xmlCase
        "create_user OR update_user"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>**********</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[create_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAbzvHo74G_tsq7Kj_deI9Lw]]></UserID><Name><![CDATA[撒个加萨佛自己来]]></Name></xml>"
        "delete_user"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1700472605</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[delete_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAbzvHo74G_tsq7Kj_deI9Lw]]></UserID></xml>"
        "create_party OR update_party"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1700473298</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_party]]></ChangeType><Id>6</Id><Name><![CDATA[其他嘿嘿（待设置部门）]]></Name></xml>"
        "delete_party"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1700460099</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[delete_party]]></ChangeType><Id>6</Id></xml>"
        "create_user OR update_user"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1701259051</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAT_Zt89-HkoCPIVNJfcwtWA]]></UserID><Alias><![CDATA[涂秋平测试]]></Alias></xml>"
        "create_party OR update_party"  |  "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1701259112</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_party]]></ChangeType><Id>6</Id><Name><![CDATA[其他嘿嘿哟哟（待设置部门）]]></Name></xml>"
    }

    @Test
    def "repChangeExternalChat"() {
        given:
        RepExternalChatEventXml eventXml = new RepExternalChatEventXml()
        eventXml.setToUserName("wpwx1mDAAA-tNOhTG2bfbmqGfoYiBtuw")
        eventXml.setEvent("change_external_chat")
        eventXml.setChangeType("create")
        eventXml.setChatId("wrxdICMgAASZ0IK3jx6-m6cZpjJ4UxWg")
        expect:
        def chat = qyweixinGatewayInnerServiceImpl.repChangeExternalChat(eventXml)
        println chat
    }
}
