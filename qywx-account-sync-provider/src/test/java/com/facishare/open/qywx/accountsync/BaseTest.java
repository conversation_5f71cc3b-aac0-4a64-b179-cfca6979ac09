package com.facishare.open.qywx.accountsync;

import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-test/spring-common-test.xml"})
public class BaseTest {
    @BeforeClass
    public static void SetUp(){
        TraceUtil.initTrace(UUID.randomUUID().toString());
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name","qywx-account-sync-provider");
    }
}