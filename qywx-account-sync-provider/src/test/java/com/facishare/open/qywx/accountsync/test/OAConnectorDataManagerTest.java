package com.facishare.open.qywx.accountsync.test;

import com.facishare.open.qywx.accountsync.manager.OAConnectorDataManager;
import com.facishare.open.qywx.accountsync.manager.QYWeixinManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class OAConnectorDataManagerTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private OAConnectorDataManager oaConnectorDataManager;

    @Test
    public void sendTest() {
        oaConnectorDataManager.send(null, null, "qywx", "suite_ticket", null, null, null, null, "40082", "企微suite_ticket缓存失效");
    }

}
