package com.facishare.open.qywx.accountsync.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.model.EmployeeBindModel;
import com.facishare.open.feishu.syncapi.model.PageModel;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountinner.arg.QueryBindArg;
import com.facishare.open.qywx.accountinner.arg.QueryOutUnbindArg;
import com.facishare.open.qywx.accountinner.arg.QueryUnBindArg;
import com.facishare.open.qywx.accountinner.enums.SearchEmployeeInfoEnum;
import com.facishare.open.qywx.accountinner.model.ContactBindInfo;
import com.facishare.open.qywx.accountinner.model.SimpleContactBindInfo;
import com.facishare.open.qywx.accountinner.result.ExportDataResult;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.accountsync.excel.FileManager;
import com.facishare.open.qywx.accountsync.excel.ReadExcel;
import com.facishare.open.qywx.accountsync.manager.ExcelListener.BaseListener;
import com.facishare.open.qywx.accountsync.manager.FsManager;
import com.facishare.open.qywx.accountsync.model.GetFsUserIdsByRestResult;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinCorpIdInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinDeptAndEmpSyncInfo;
import com.facishare.open.qywx.accountsync.mongo.dao.OutUserInfoMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.OutUserInfoDoc;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.GetAllEmployeeIdsResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/09/28
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ContactBindInnerServiceTest extends AbstractJUnit4SpringContextTests {

//    String fsEa = "56569";
    //String fsEa = "74372";
    String fsEa = "84883";


    @Resource
    private ContactBindInnerService contactBindInnerService;

    @Autowired
    private QyweixinCorpBindDao qyweixinCorpBindDao;

    @Autowired
    private FsManager fsManager;

    @Autowired
    private FileManager fileManager;

    @Autowired
    private OutUserInfoMongoDao outUserInfoMongoDao;

//    @Test
//    public void getContactAppBind() {
//        Result<Object> contactAppBind = contactBindInnerService.getContactAppBind(fsEa,null);
//        System.out.println(contactAppBind);
//    }

    @Test
    public void getWXAccounts() {
//        Result<List<SimpleContactBindInfo>> wxAccounts = contactBindInnerService.getWXAccounts("82777", "1", null);
//        System.out.println(JSONObject.toJSONString(wxAccounts));
    }

    @Test
    public void automaticAccountMactch() {
//        Result<List<ContactBindInfo>> listResult = contactBindInnerService.automaticAccountMactch("82335", 1000,null);
//        System.out.println(JSONObject.toJSONString(listResult));
    }

    @Test
    public void saveAccountBind() {
        Result<String> stringResult = contactBindInnerService.saveAccountBind("90562",
                Lists.newArrayList("1000&wowx1mDAAAlEFUNW6yiM_J0y-FLmzobg"),
                null);
        System.out.println(JSONObject.toJSONString(stringResult));
    }

    @Test
    public void deleteAccountBind() {
        Result<String> stringResult = contactBindInnerService.deleteAccountBind("90892",
                Lists.newArrayList("1000"),
                "wpwx1mDAAAJexomNGFhECY68trViPz3A");
        System.out.println(stringResult);
    }

    @Test
    public void updateAccountBind() {
//        Result<String> stringResult = contactBindInnerService.updateAccountBind("81243",
//                Lists.newArrayList("1069&wowx1mDAAAMdB1vuVmKYTplHe-rEsDFQ"),null);
//        System.out.println(stringResult);
    }

    @Test
    public void getAccountBind() {
//        Result<List<ContactBindInfo>> accountBind = contactBindInnerService.getAccountBind(fsEa, 0, 1000,null,null);
//        System.out.println("未绑定：" + JSONObject.toJSONString(accountBind));
//
//        Result<List<ContactBindInfo>> accountUnBind = contactBindInnerService.getAccountBind(fsEa, 1, 1000,null,null);
//        System.out.println("已绑定：" + JSONObject.toJSONString(accountUnBind));
    }

    @Test
    public void deleteQyweixinCorpId(){
        // 0-普通删除绑定关系  1-该企业微信账号已绑定其它纷享账号 请纷享客服解绑   2-该纷享账号已绑定其它企业微信账号 请纷享客服解绑
        Map<String, String> requestMap = Maps.newHashMap();
        requestMap.put("deleteBindType", "0");
        requestMap.put("corpIds", "ww4ba39487c1f49493,ww4ba39487c1f49494");
        Result<Object> objectResult = contactBindInnerService.deleteQYWXBind(requestMap);
        System.out.println("清除结果：" + objectResult.isSuccess() +" :" + objectResult);
    }


    @Test
    public void deleteBindFsEa(){
        // 0-普通删除绑定关系  1-该企业微信账号已绑定其它纷享账号 请纷享客服解绑   2-该纷享账号已绑定其它企业微信账号 请纷享客服解绑
        Map<String, String> requestMap = Maps.newHashMap();
        requestMap.put("deleteBindType", "1");
        requestMap.put("corpIds", "ww4ba39487c1f49492");
        requestMap.put("fsEa", "99999");
        Result<Object> objectResult = contactBindInnerService.deleteQYWXBind(requestMap);
        System.out.println("清除结果：" + objectResult.isSuccess() +" :" + objectResult);
    }


    @Test
    public void deleteBindQYWX(){
        // 0-普通删除绑定关系  1-该企业微信账号已绑定其它纷享账号 请纷享客服解绑   2-该纷享账号已绑定其它企业微信账号 请纷享客服解绑
        Map<String, String> requestMap = Maps.newHashMap();
        requestMap.put("deleteBindType", "2");
        requestMap.put("corpIds", "wwbb224ca552b4f6ca");
        requestMap.put("fsEa", "99999");
        Result<Object> objectResult = contactBindInnerService.deleteQYWXBind(requestMap);
        System.out.println("清除结果：" + objectResult.isSuccess() +" :" + objectResult);
    }

    @Test
    public void testGetAttachments() {

        Result<Integer> attachments = contactBindInnerService.getAutoBind("74860","wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g");
        System.out.println(attachments.getData());
    }

    @Test
    public void testSaveAttachments() {
//        Result<Integer> attachments = contactBindInnerService.saveAutoBind("81243", null, 1);
//        System.out.println(attachments.getData());
//
//        Result<List<ContactBindInfo>> automaticAccountMactchResult = contactBindInnerService.automaticAccountMactch("83384", 1000,null);
//
//        List<String> bindList = new LinkedList<>();
//        automaticAccountMactchResult.getData().stream().forEach(item -> {
//            if(!StringUtils.isEmpty(item.getQywxEmployeeAccount())) {
//                bindList.add(item.getFsEmployeeAccount() + "&" + item.getQywxEmployeeAccount());
//            }
//        });
//        contactBindInnerService.saveAccountBind("83384", bindList, null);
    }

    @Test
    public void testEa() {
        List<String> ea = qyweixinCorpBindDao.getEaByContactBind(ConfigCenter.crm_domain);
        System.out.println(ea);
    }

    @Test
    public void testAutoSaveAttachments() {
        contactBindInnerService.getAutoContactBind();
    }

    @Test
    public void testAutoSaveAttachments1() {
        contactBindInnerService.saveAutoContactBind(fsEa, 1000, null);
    }

//    @Test
//    public void testAutoBind(){
//        AutoContactBindArg autoContactBindArg = new AutoContactBindArg();
//        autoContactBindArg.setFlag("1");
//        Result autoBind = contactBindInnerService.testAutoBind(fsEa, autoContactBindArg);
//        System.out.println(autoBind);
//        System.out.println(autoBind);
//    }

    @Test
    public void testCorpName() {
        Result<String> result = contactBindInnerService.queryCorpName2("82777","wpwx1mDAAAkLMNumW9-LpPSQ-mQpiASQ","1");
        System.out.println(result);
    }

    @Test
    public void testS() {
        contactBindInnerService.getJobId("74431");
    }

    @Test
    public void testContactBindCorp() {
        Result<QyweixinCorpIdInfo> result = contactBindInnerService.queryOutEaByFsEa("test3");
        System.out.println(result);
    }

    @Test
    public void testChangeContactBindCorp() {
        List<QyweixinAccountEmployeeMapping> employeeMappings = new LinkedList<>();
        QyweixinAccountEmployeeMapping mapping = new QyweixinAccountEmployeeMapping();
        mapping.setOutAccount("wowx1mDAAAIHxcX0WOO1y_lJo0NDSWyA");
        mapping.setOutEa("ww1dc427ba799b9ba61");
        employeeMappings.add(mapping);

        QyweixinAccountEmployeeMapping mapping1 = new QyweixinAccountEmployeeMapping();
        mapping1.setOutAccount("wowx1mDAAAaf7802XLXDUxEnLE_NJKoQ");
        mapping1.setOutEa("ww1dc427ba799b9ba61");
        employeeMappings.add(mapping1);

//        Result<Integer> result = contactBindInnerService.updateEmployeeAccountBind(employeeMappings, null);
//        System.out.println(result);
    }

    @Test
    public void testGetAllEmployees() {
        List<EmployeeDto> allEmployees = fsManager.getAllEmployees("83384",
                RunStatus.ACTIVE);
        System.out.println(allEmployees);
        GetFsUserIdsByRestResult fsUserIdsByRestService = fsManager.getFsUserIdsByRestService("83384", 1000);
        System.out.println(fsUserIdsByRestService);
    }

    @Test
    public void testGetAllEmployeeIds() {
        GetAllEmployeeIdsResult result = fsManager.getAllEmployeeIds("83384");
        System.out.println(result.getEmployeeIds().size());

    }

    @Test
    public void testUploadDepartmentsAndEmployeesFile() throws FileNotFoundException {
        String fileName = "D:\\fxiaoke\\WXWork\\testExcel\\DepqartmentAndEmployee.xlsx";
        File picFile = new File(fileName);
        FileInputStream fileInputStream = new FileInputStream(picFile);
        MultipartFile multipartFile = null;
        try {
            multipartFile = new MockMultipartFile(picFile.getName(), picFile.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        for (int i = 0; i < 2; i++) {
            byte[] bytes = new byte[0];
            try {
                bytes = multipartFile.getBytes();
                InputStream inputStream = new ByteArrayInputStream(bytes);
                ReadExcel.Arg<Map<Integer, String>> arg = new ReadExcel.Arg<>();
                BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
                };
                arg.setExcelListener(listen);
                arg.setInputStream(inputStream);
                arg.setSheetName(i == 0 ? "department" : "employee");
                fileManager.readExcelBySheetName(arg);
                if (CollectionUtils.isEmpty(listen.getDataList())) {
                    log.info("ControllerQYWeixinContactBind.uploadDepartmentsAndEmployeesFile,sheetName={},listen is employee.", i == 0 ? "department" : "employee");
                    continue;
                }
                List<QyweixinDeptAndEmpSyncInfo> deptAndEmpSyncInfos = listen.getDataList().stream().map(v -> {
                    QyweixinDeptAndEmpSyncInfo qyweixinDeptAndEmpSyncInfo = new QyweixinDeptAndEmpSyncInfo();
                    qyweixinDeptAndEmpSyncInfo.setEa(v.get(0));
                    qyweixinDeptAndEmpSyncInfo.setCorpId(v.get(1));
                    qyweixinDeptAndEmpSyncInfo.setFsId(Integer.parseInt(v.get(2)));
                    qyweixinDeptAndEmpSyncInfo.setOutId(v.get(4));
                    return qyweixinDeptAndEmpSyncInfo;
                }).collect(Collectors.toList());
                log.info("ControllerQYWeixinContactBind.uploadDepartmentsAndEmployeesFile,deptAndEmpSyncInfos={}.", deptAndEmpSyncInfos);
                contactBindInnerService.uploadDepartmentsAndEmployeesFile(deptAndEmpSyncInfos, i);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void testChangAuthEventToBindAccount() {
        Result<Void> result = contactBindInnerService.changAuthEventToBindAccount("84883", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
    }

    @Test
    public void testListenMqEventToBindAccount() {
        EmployeeDto employeeDto = new EmployeeDto();
        employeeDto.setName("陈宗鑫-nikename");
        employeeDto.setFullName("陈宗鑫-fullname");
        employeeDto.setEmployeeId(1021);
        Result<Void> result = contactBindInnerService.listenMqEventToBindAccount("84883", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", employeeDto);
    }

    @Test
    public void testAutoBindEmpAccount() {
        Result<Void> result = contactBindInnerService.autoBindEmpAccount("84883", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
    }

//    @Test
//    public void testhCeckContactAuth() {
//        contactBindInnerService.checkContactAuth("wx88a141937dd6f838", "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
//    }

    @Test
    public void queryFsUnbind() {
        QueryUnBindArg arg = new QueryUnBindArg();
        arg.setPageSize(10);
        arg.setEa("84883");
        Map<String, Object> searchContentMap = new HashMap<>();
        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName(), 2);
        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName(), "123");
        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName(), "a");
        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName(), "1000");
        arg.setSearchContentMap(searchContentMap);
        Result<PageModel<List<EmployeeBindModel.FsEmployee>>> pageModelResult = contactBindInnerService.queryFsUnbind(arg);
        System.out.println(pageModelResult);
    }

    @Test
    public void queryBind() {
        QueryBindArg arg = new QueryBindArg();
        arg.setPageSize(100);
        arg.setEa("88521");
        Map<String, Object> searchContentMap = new HashMap<>();
//        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_STATUS.getSearchName(), 1);
//        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_PHONE.getSearchName(), "13");
//        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName(), "陈");
//        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_DEPARTMENT.getSearchName(), "1001");
        arg.setSearchContentMap(searchContentMap);

        String json = "{\"connectParam\":{\"bindType\":1,\"dataCenterId\":\"662765ad1ea28e000156a59d\",\"dataCenterName\":\"一对多测试企业112（拨测用勿删除）\",\"fsEa\":\"88521\",\"outDepId\":\"3\",\"outEa\":\"wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w\",\"outEn\":\"一对多测试企业112（拨测用勿删除）-销售子公司edit\"},\"ea\":\"88521\",\"pageSize\":100,\"searchContentMap\":{\"employeeName\":\"\"}}";

        QueryBindArg arg2 = JSONObject.parseObject(json,QueryBindArg.class);
        Result<PageModel<List<EmployeeBindModel>>> pageModelResult = contactBindInnerService.queryBind(arg2);
        System.out.println(pageModelResult);
    }

    @Test
    public void queryOutUnbind() {
//        List<OutUserInfoDoc> docs = outUserInfoMongoDao.queryUserInfos("wpwx1mDAAAUf8zE0zlWW5t46SMWAmuEw");
//        DeleteResult result = outUserInfoMongoDao.deleteUserInfoByOutEa("wpwx1mDAAAUf8zE0zlWW5t46SMWAmuEw");
//        docs = outUserInfoMongoDao.queryUserInfos("wpwx1mDAAAUf8zE0zlWW5t46SMWAmuEw");
//        System.out.println(result);

        QueryOutUnbindArg arg = new QueryOutUnbindArg();
        arg.setPageSize(10);
        arg.setEa("91355");
//        Map<String, Object> searchContentMap = new HashMap<>();
//        searchContentMap.put(SearchEmployeeInfoEnum.EMPLOYEE_NAME.getSearchName(), "张");
//        arg.setSearchContentMap(searchContentMap);
        Result<List<EmployeeBindModel.OutEmployee>> pageModelResult = contactBindInnerService.queryOutUnbind(arg);
        System.out.println(pageModelResult);
    }

    @Test
    public void exportEmployeeBind() {
        Result<ExportDataResult> result = contactBindInnerService.exportEmployeeBind("81243",
                "wpwx1mDAAAsJuqgPFlcfGWtLOewfEiNw",
                1069,
                null);
        System.out.println(result);
    }
}
