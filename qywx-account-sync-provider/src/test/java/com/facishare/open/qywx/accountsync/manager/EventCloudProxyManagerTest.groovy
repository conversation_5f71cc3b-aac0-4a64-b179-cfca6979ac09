package com.facishare.open.qywx.accountsync.manager

import com.facishare.open.qywx.accountinner.model.OutEventDateChangeProto
import com.google.gson.Gson
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class EventCloudProxyManagerTest extends Specification {
    @Resource
    private EventCloudProxyManager eventCloudProxyManager;

    def "enterpriseBind"() {
        given:
        String pro = proCase;
        OutEventDateChangeProto proto = new Gson().fromJson(pro, OutEventDateChangeProto.class)
        expect:
        eventCloudProxyManager.handleEvent(proto)
        where:
        proCase || result
        "{\"appType\":\"dataPush\",\"eventType\":\"qyweixinAccountEmployeeBindDao\",\"domain\":\"https://crm.ceshi112.com\",\"content\":\"[{\\\"source\\\":\\\"qywx\\\",\\\"appId\\\":\\\"wx88a141937dd6f838\\\",\\\"fsAccount\\\":\\\"E.83838.10000\\\",\\\"outAccount\\\":\\\"wowx1mDAAAIHxcX0WOO1y_lJo0NDSWyA0\\\",\\\"outEa\\\":\\\"wpwx1mDAAA3fRMFTGuFvJa2LXoZ8U9_A\\\",\\\"status\\\":0,\\\"isvAccount\\\":\\\"wowx1mDAAAIHxcX0WOO1y_lJo0NDSWyA0\\\",\\\"gmtCreate\\\":\\\"Dec 16, 2021 5:06:19 AM\\\",\\\"gmtModified\\\":\\\"Mar 9, 2024 8:33:45 AM\\\",\\\"id\\\":********},{\\\"source\\\":\\\"qywx\\\",\\\"appId\\\":\\\"wx88a141937dd6f838\\\",\\\"fsAccount\\\":\\\"E.83838.1001\\\",\\\"outAccount\\\":\\\"xiaozhang001\\\",\\\"outEa\\\":\\\"ww1dc427ba799b9ba6wpwx1mDAAA3fRMFTGuFvJa2LXoZ8U9_A\\\",\\\"status\\\":0,\\\"isvAccount\\\":\\\"xiaozhang001\\\",\\\"gmtCreate\\\":\\\"Feb 19, 2022 1:27:13 AM\\\",\\\"gmtModified\\\":\\\"Oct 13, 2022 9:45:45 AM\\\",\\\"id\\\":********}]\"}" || null
        "{\"appType\":\"dataPush\",\"eventType\":\"accountEnterpriseBindDao\",\"domain\":\"https://crm.ceshi112.com\",\"content\":\"[{\\\"id\\\":742,\\\"source\\\":\\\"qywx\\\",\\\"fsEa\\\":\\\"83838\\\",\\\"outEa\\\":\\\"wpwx1mDAAA3fRMFTGuFvJa2LXoZ8U9_A\\\",\\\"depId\\\":\\\"1\\\",\\\"status\\\":0,\\\"bindType\\\":0,\\\"isvOutEa\\\":\\\"wpwx1mDAAA3fRMFTGuFvJa2LXoZ8U9_A\\\",\\\"autRetention\\\":1,\\\"openAuthorization\\\":1,\\\"autBind\\\":0,\\\"domain\\\":\\\"https://crm.ceshi112.com\\\",\\\"gmtCreate\\\":\\\"Dec 16, 2021 5:06:03 AM\\\",\\\"gmtModified\\\":\\\"Dec 16, 2021 5:06:03 AM\\\"}]\"}"|| null
        "{\"appId\":\"wx105357ca56c6db18\",\"eventType\":\"suite_ticket\",\"content\":\"DTHHyxYexlN1fQrnQaUTI5OnHG_v1KwKZjmWB7ndWHpj_Kb9zB4joRiNxYu8cmfkR048yuDAwa_4bkuO1i3U9RRD-HnIaPWC1P2mPdsy8fnQ88NSk98fCvZ7s7OU0L4r\"}" || null
        "{\"appId\":\"wx88a141937dd6f838\",\"appType\":\"tag_enterprise_wechat_cmd_event_4_third\",\"eventType\":\"change_external_contact\",\"outEa\":\"wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA\",\"content\":\"\\u003cxml\\u003e\\u003cSuiteId\\u003e\\u003c![CDATA[wx88a141937dd6f838]]\\u003e\\u003c/SuiteId\\u003e\\u003cAuthCorpId\\u003e\\u003c![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]\\u003e\\u003c/AuthCorpId\\u003e\\u003cInfoType\\u003e\\u003c![CDATA[change_external_contact]]\\u003e\\u003c/InfoType\\u003e\\u003cTimeStamp\\u003e1715570686\\u003c/TimeStamp\\u003e\\u003cChangeType\\u003e\\u003c![CDATA[edit_external_contact]]\\u003e\\u003c/ChangeType\\u003e\\u003cUserID\\u003e\\u003c![CDATA[wowx1mDAAArLI4aPJXTqJx_UJCABHILQ]]\\u003e\\u003c/UserID\\u003e\\u003cExternalUserID\\u003e\\u003c![CDATA[wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ]]\\u003e\\u003c/ExternalUserID\\u003e\\u003c/xml\\u003e\"}" || null
        "" || null
    }
}
