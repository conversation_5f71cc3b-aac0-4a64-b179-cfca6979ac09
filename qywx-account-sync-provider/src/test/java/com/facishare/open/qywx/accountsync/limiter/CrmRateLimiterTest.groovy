package com.facishare.open.qywx.accountsync.limiter

import org.apache.commons.lang3.StringUtils
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.util.concurrent.TimeUnit

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class CrmRateLimiterTest extends Specification {

    private static boolean isAllowed(String key) {
        long start = System.currentTimeMillis();
        boolean isAllowedByKey = CrmRateLimiter.isAllowed(key);
        println("thread= " + Thread.currentThread().getName() + " key= " + (StringUtils.isNotEmpty(key) ? key : "defaultRateLimit") + "isAllowedByKey=" + isAllowedByKey + " cost= " + (System.currentTimeMillis() - start) + " ms")
        return isAllowedByKey;
    }

    def "crmRateLimiterKeyTest"() {
        given:
        def key1 = key1Case
        def key2 = key2Case
        def key3 = key3Case
        def key4 = key4Case
        expect:
        for (int i = 0; i < 50; i++) {
            new Thread({ ->
                for (int j = 0; j < 10; j++) {
                    isAllowed(key1)
                }
            }).start()
            new Thread({ ->
                for (int j = 0; j < 10; j++) {
                    isAllowed(key2)
                }
            }).start()
            new Thread({ ->
                for (int j = 0; j < 10; j++) {
                    isAllowed(key3)
                }
            }).start()
            new Thread({ ->
                for (int j = 0; j < 10; j++) {
                    isAllowed(key4)
                }
            }).start()
        }
        TimeUnit.MINUTES.sleep(5);
        println null
        where:
        key1Case  |  key2Case  |  key3Case  |  key4Case
        null      |  null      |  null      |  null       //默认key
        null      |  "84883"   |  null      |  "84883"    //不同key
        "84883"   |  "84883"   |  "84883"   |  "84883"    //特定kdy
    }
}
