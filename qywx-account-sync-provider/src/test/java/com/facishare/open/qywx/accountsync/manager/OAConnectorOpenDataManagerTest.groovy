package com.facishare.open.qywx.accountsync.manager

import com.facishare.open.qywx.accountsync.handler.RepEventHandler
import com.facishare.open.qywx.accountsync.model.OAConnectorOpenDataModel
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class OAConnectorOpenDataManagerTest extends Specification {
    @Resource
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    def "send"() {
        given:
        OAConnectorOpenDataModel model = new OAConnectorOpenDataModel()
        model.setAppId("wx88a141937dd6f838")
        model.setOutUserId("wowx1mDAAAIHxcX0WOO1y_lJo0NDSWyA")
        model.setEa("83838")
        model.setChannelId("qywx")
        model.setCorpId("wpwx1mDAAA3fRMFTGuFvJa2LXoZ8U9_A")
        model.setDataTypeId("employeeLogin")
        model.setErrorCode("101")
        expect:
        oaConnectorOpenDataManager.send(model)

    }
}
