package com.facishare.open.qywx.accountsync.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.qywx.accountinner.model.FsAccountModel;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.utils.HttpHelper;
import com.facishare.open.qywx.accountsync.utils.RedisLockUtils;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.*;
import java.rmi.RemoteException;
import java.util.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ToolsServiceTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private ToolsService toolsService;
    @Autowired
    private RedisDataSource redisDataSource;

    @ReloadableProperty("crmAppId")
    private String crmAppId;
    @ReloadableProperty("repAppId")
    private String repAppId;

    @Test
    public void test() {
        System.out.println("hello");
        String env = System.getProperty("process.profile");
        System.out.println(env);
    }

    @Test
    public void refreshEmployeeTable() {
        Result<Integer> result = toolsService.refreshEmployeeTable("ww7611fdf9bbbd3c67");
        System.out.println(result);
    }

    @Test
    public void refreshEnterpriseTable() {
        Result<Integer> result = toolsService.refreshEnterpriseTable("ww3e0c16db26dde4af");
        System.out.println(result);
    }

    @Test
    public void refreshApplicationTable() {
        Result<Integer> result = toolsService.refreshApplicationTable("ww7611fdf9bbbd3c67");
        System.out.println(result);
    }

    @Test
    public void refreshEnterpriseAccount() {
        Result result = toolsService.refreshEnterpriseAccount("wwcd9be8e48c4d09ae", "");
        System.out.println(result);
    }

    @Test
    public void getEnterpriseAccountMigration() {
        Result result = toolsService.getEnterpriseAccountMigration("wwcd9be8e48c4d09ae", crmAppId);
        System.out.println(result);
    }

    @Test
    public void testRefreshOrderTable() {
        Result result = toolsService.refreshOrderTable("ww7611fdf9bbbd3c67");
        System.out.println(result);
    }

    @Test
    public void testRefreshApplicationInfoTable() {
        Result result = toolsService.refreshApplicationInfoTable("ww7611fdf9bbbd3c67");
        System.out.println(result);
    }

    @Test
    public void testRefreshAllEnterpriseAccount() {
        Result result = toolsService.refreshAllEnterpriseAccount(Lists.newArrayList("wwcd9be8e48c4d09ae"));
        System.out.println(result);
    }

    @Test
    public void testDeleteExternalContactTable() {
        Result result = toolsService.deleteExternalContactTable("ww7611fdf9bbbd3c67");
        System.out.println(result);
    }

    @Test
    public void testDeleteUserTable() {
        Result result = toolsService.deleteUserTable("ww7611fdf9bbbd3c67");
        System.out.println(result);
    }

    @Test
    public void testUpdateServiceProviderEmployeeBind() {
        Result result = toolsService.updateServiceProviderEmployeeBind("82778", null, null);
        System.out.println(result);
    }

    @Test
    public void readFile1() throws IOException {
        String fileName = "D:\\fxiaoke\\WXWork\\orderInfoUpdateEa\\order3\\enterprise.txt";
        File picFile = new File(fileName);
        FileInputStream fis = new FileInputStream(picFile);
        BufferedReader br = new BufferedReader(new InputStreamReader(fis));

        String line = null;
        List<QyweixinUpdateOrderInfo> orderInfoList = new LinkedList<>();
        while ((line = br.readLine()) != null) {
            QyweixinUpdateOrderInfo orderInfo = JSONObject.parseObject(line, QyweixinUpdateOrderInfo.class);
            String eid = orderInfo.getEid();
            Result<String> result = toolsService.corpId2OpenCorpId(eid);
            orderInfo.setEid2(result.getData());
            orderInfoList.add(orderInfo);
        }
        StringBuilder sb = new StringBuilder();
        for (QyweixinUpdateOrderInfo item : orderInfoList) {
            String json = JSON.toJSONString(item);
            sb.append(json + "\r\n");
        }
        FileWriter fw = new FileWriter("D:\\fxiaoke\\WXWork\\orderInfoUpdateEa\\order3\\enterprise2.txt", true);
        fw.write(sb.toString());
        fw.close();
        br.close();
        fis.close();
    }

    @Test
    public void readFile2() throws IOException {
        String fileName = "D:\\fxiaoke\\WXWork\\orderInfoUpdateEa\\order2\\wubb.txt";
        File picFile = new File(fileName);
        FileInputStream fis = new FileInputStream(picFile);
        BufferedReader br = new BufferedReader(new InputStreamReader(fis));

        String line = null;
        StringBuilder sb = new StringBuilder();
        while ((line = br.readLine()) != null) {
            List<String> result = Splitter.on(",").splitToList(line);
            String ea = result.get(2);
            Result<String> result1 = toolsService.corpId2OpenCorpId(ea);
            sb.append(line).append(",").append(result1.getData()).append("\r\n");
        }

        FileWriter fw = new FileWriter("D:\\fxiaoke\\WXWork\\orderInfoUpdateEa\\order2\\wubb2.txt", true);
        fw.write(sb.toString());
        fw.close();
        br.close();
        fis.close();
    }

    @Test
    public void readFile3() throws IOException {
        String fileName = "C:\\Users\\<USER>\\Desktop\\企微订单\\new-Enterprise.txt";
        File picFile = new File(fileName);
        FileInputStream fis = new FileInputStream(picFile);
        BufferedReader br = new BufferedReader(new InputStreamReader(fis));

        String line = null;
        StringBuilder sb = new StringBuilder();
        int sum = 0;
        while ((line = br.readLine()) != null) {
            try {
                QyweixinUpdateOrder2Info orderInfo = JSONObject.parseObject(line, QyweixinUpdateOrder2Info.class);
                String eid = orderInfo.getEid();
                if (eid.length() < 32) {
                    eid = toolsService.corpId2OpenCorpId(orderInfo.getEid()).getData();
                    orderInfo.setEid(eid);
                    sum++;
                }
                QyweixinEnterpriseOrder enterpriseInfo = JSONObject.parseObject(orderInfo.getEnterpriseInfo(), QyweixinEnterpriseOrder.class);
                //Result<String> result1 = toolsService.corpId2OpenCorpId(orderInfo1.getCorpId());
                enterpriseInfo.setCorpId(eid);
                orderInfo.setEnterpriseInfo(JSONObject.toJSONString(enterpriseInfo));

                sb.append(JSONObject.toJSONString(orderInfo)).append("\r\n");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        System.out.println("sum=" + sum);
        FileWriter fw = new FileWriter("C:\\Users\\<USER>\\Desktop\\企微订单\\new-Enterprise3.txt");
        fw.write(sb.toString());
        fw.close();
    }

    @Test
    public void genEnterpriseSql() throws IOException {
        String fileName = "C:\\Users\\<USER>\\Desktop\\企微订单\\new-Enterprise4.txt";
        File picFile = new File(fileName);
        FileInputStream fis = new FileInputStream(picFile);
        BufferedReader br = new BufferedReader(new InputStreamReader(fis));

        String line = null;
        StringBuilder sb = new StringBuilder();
        String tempSql = "db.Enterprise.updateOne({\"_id\" : ObjectId(\"{_id}\")},{$set:{\"eid\":\"{eid}\",\"enterpriseInfo\":{enterpriseInfo}}})";
        while ((line = br.readLine()) != null) {
            try {
                QyweixinUpdateOrder2Info orderInfo = JSONObject.parseObject(line, QyweixinUpdateOrder2Info.class);
                //QyweixinEnterpriseOrder enterpriseInfo =  JSONObject.parseObject(orderInfo.getEnterpriseInfo(), QyweixinEnterpriseOrder.class);
                String sql = tempSql.replace("{_id}", orderInfo.get_id().getOrderId2())
                        .replace("{eid}", orderInfo.getEid())
                        .replace("{enterpriseInfo}", orderInfo.getEnterpriseInfo());
                sb.append(sql).append("\r\n");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        FileWriter fw = new FileWriter("C:\\Users\\<USER>\\Desktop\\企微订单\\new-Enterprise7.txt");
        fw.write(sb.toString());
        fw.close();
    }

    @Test
    public void genAccountSql() throws IOException {
        String fileName = "C:\\Users\\<USER>\\Desktop\\企微订单\\biz_account.csv";
        File picFile = new File(fileName);
        FileInputStream fis = new FileInputStream(picFile);
        BufferedReader br = new BufferedReader(new InputStreamReader(fis));

        String line = null;
        StringBuilder sb = new StringBuilder();
        String tempSql = "update biz_account set value10='{value10}' where id='{id}';";
        while ((line = br.readLine()) != null) {
            List<String> result = Splitter.on(",").splitToList(line);
            String sql = tempSql.replace("{value10}", result.get(2))
                    .replace("{id}", result.get(0));
            sb.append(sql).append("\r\n");
        }

        FileWriter fw = new FileWriter("C:\\Users\\<USER>\\Desktop\\企微订单\\biz_account2.txt", true);
        fw.write(sb.toString());
        fw.close();
        br.close();
        fis.close();
    }

    @Test
    public void readFile4() throws IOException {
        String fileName = "D:\\fxiaoke\\WXWork\\orderInfoUpdateEa\\order2\\wubb2.txt";
        File picFile = new File(fileName);
        FileInputStream fis = new FileInputStream(picFile);
        BufferedReader br = new BufferedReader(new InputStreamReader(fis));

        String line = null;
        StringBuilder sb = new StringBuilder();
        while ((line = br.readLine()) != null) {
            List<String> result = Splitter.on(",").splitToList(line);
            if (result.get(2).equals(result.get(3)) && result.get(3).length() < 32) {
                continue;
            }
            sb.append(result.get(0)).append(",").append(result.get(1)).append(",").append(result.get(3)).append("\r\n");
        }

        FileWriter fw = new FileWriter("D:\\fxiaoke\\WXWork\\orderInfoUpdateEa\\order2\\wubb6.txt", true);
        fw.write(sb.toString());
        fw.close();
        br.close();
        fis.close();
    }

    @Test
    public void readFile5() throws IOException {
        String fileName = "C:\\Users\\<USER>\\Desktop\\企微订单\\new-Enterprise3.txt";
        File picFile = new File(fileName);
        FileInputStream fis = new FileInputStream(picFile);
        BufferedReader br = new BufferedReader(new InputStreamReader(fis));

        String line = null;
        StringBuilder sb = new StringBuilder();
        while ((line = br.readLine()) != null) {
            try {
                QyweixinUpdateOrder2Info orderInfo = JSONObject.parseObject(line, QyweixinUpdateOrder2Info.class);
                if (orderInfo.getEid().length() < 32) {
                    continue;
                }
                sb.append(JSONObject.toJSONString(orderInfo)).append("\r\n");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        FileWriter fw = new FileWriter("C:\\Users\\<USER>\\Desktop\\企微订单\\new-Enterprise4.txt");
        fw.write(sb.toString());
        fw.close();
        br.close();
        fis.close();
    }

    @Test
    public void getAccountInfo() {
        Result<List<FsAccountModel>> result = toolsService.getAccountInfo(1, Lists.newArrayList("1", "2"));
        System.out.println(result);
    }

    @Test
    public void queryAccountInfoList() {
        List<FsAccountModel> fsAccountModelList = toolsService.queryAccountInfoList(Lists.newArrayList("fs1000"));
        System.out.println(fsAccountModelList);
    }

    @Test
    public void switchFsAccontTest() throws Exception {
        File file = new File("D:\\fxiaoke\\WXWork\\fs\\fsde1.txt");
        Scanner sc = new Scanner(file);
        Boolean flag = Boolean.TRUE;
        List<String> userIds = new LinkedList<>();
        Map<String, List<String>> map = new HashMap<>();
        while (sc.hasNextLine()) {
            if (flag) {
                flag = Boolean.FALSE;
                continue;
            }
            System.out.println(sc.nextLine());
            List<String> accountList = Splitter.on(",").splitToList(sc.nextLine());
            userIds.add(accountList.get(1));
            map.put(accountList.get(1), accountList);
        }
        Map<String, Object> form = new HashMap<>();
        form.put("userid_list", userIds);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=CS8HhEskuMkgZeCIjlJUG-O8AJ-ckS7ywDRGfyRLJ_UdWUAMwvPiW3YtKsXBi-zth7RXzF5E7Y_M0tFq3bxXlu6V9A69ESRzReJ4xCUqcYeLDY-t7vq0LRZEyroLXiXVrpA7B9p2SNPTBwNhl2EVVzdOxRTsrwlJm59ZlfvoyZ3wCXGsy6CpPpQLDS1Wr74bbxMS4UPc9vyx7JTSJmDLuA";
        String result = null;
        HttpHelper httpHelper = new HttpHelper();
        result = httpHelper.postObjectData(url, form);
        System.out.println(result);
        List<QyweixinOpenUserIdInfo> resultLists = new LinkedList<>();
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.open_userid_list").toString());
            resultLists.addAll(jsonArray.toJavaList(QyweixinOpenUserIdInfo.class));
        }
        System.out.println(result);
        File file1 = new File("D:\\fxiaoke\\WXWork\\fs\\fsAccount.txt");
        if (!file1.exists())
        {
            file1.createNewFile();
        }
        FileWriter fw = new FileWriter(file1.getAbsoluteFile());
        BufferedWriter bw = new BufferedWriter(fw);
        for(QyweixinOpenUserIdInfo info : resultLists) {
            String account = map.get(info.getUserid()).get(0) + "," + map.get(info.getUserid()).get(1) + "," + info.getOpen_userid() + "\n";
            bw.write(account);
        }
        bw.close();
        System.out.println(bw);
    }

    @Test
    public void getQYWXAppBindInfoTest() {
        Result<QyweixinAppStatusInfo> result = toolsService.getQYWXAppBindInfo("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", repAppId);
        System.out.println(result);
    }

    @Test
    public void getQYWXAppAuthorityInfoTest() {
        Result<QyweixinAppAuthorityInfo> result = toolsService.getQYWXRepAppAuthorityInfo("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        System.out.println(result);
    }

    @Test
    public void queryCrmAccountObjStatusTest() {
        Result<AccountObjInfo> result = toolsService.queryCrmAccountObjStatus("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        System.out.println(result);
    }

    @Test
    public void queryCrmSalesOrderObjStatusTest() {
        Result<SalesOrderObjInfo> result = toolsService.queryCrmSalesOrderObjStatus("63314b5836a7260001bef0c2");
        System.out.println(result);
    }

    @Test
    public void getEnterpriseRunStatusTest() {
        Result<Integer> result = toolsService.getEnterpriseRunStatus("848831");
        System.out.println(result);
    }

    @Test
    public void unbindTest() {
        Result<Void> result = toolsService.unbind("*********", "123555", crmAppId);
        System.out.println(result);
    }

    @Test
    public void updateAllCorpBind() {
        Result<Void> result = toolsService.updateAllCorpBind();
        System.out.println(result);
    }

    @Test
    public void updateOpenIds() {
        List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos = new LinkedList<>();
            QyweixinIdToOpenidBo qyweixinIdToOpenidBo = new QyweixinIdToOpenidBo();
            qyweixinIdToOpenidBo.setCorpId("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
            qyweixinIdToOpenidBo.setOpenid("test");
            qyweixinIdToOpenidBo.setPlaintextId("test1");
            qyweixinIdToOpenidBos.add(qyweixinIdToOpenidBo);
        Result<Void> result = toolsService.updateOpenIds(qyweixinIdToOpenidBos);
        System.out.println(result);
    }

    @Test
    public void redis() {
        String outEa = "testtesttest";
        if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa, 60)) {
            try {
                System.out.println(1);
                boolean b = RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa, 60);
                System.out.println(b);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa);
            }
        }
        boolean b1 = RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa, 60);
        System.out.println(b1);
        if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa, 60)) {
            try {
                System.out.println(1);
                boolean b = RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa, 60);
                System.out.println(b);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa);
            }
        }


    }

    @Test
    public void updateAllCorpBindToCopy() {
        Result<Void> result = toolsService.updateAllCorpBindToCopy(0);
        System.out.println(result);
    }

    @Test
    public void deleteUserInfo() {
        Result<Long> result = toolsService.deleteUserInfo("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", true);
        System.out.println(result);
    }
}
