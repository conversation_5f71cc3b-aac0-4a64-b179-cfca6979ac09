package com.facishare.open.qywx.accountsync.test;

import com.facishare.open.qywx.accountsync.mongo.dao.OutDepartmentInfoMongoDao;
import com.facishare.open.qywx.accountsync.mongo.document.OutDepartmentInfoDoc;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.mongodb.client.result.DeleteResult;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class OutDepartmentInfoMongoDaoTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private OutDepartmentInfoMongoDao outDepartmentInfoMongoDao;

    @ReloadableProperty("crmAppId")
    private String crmAppId;
    @ReloadableProperty("repAppId")
    private String repAppId;

    @Test
    public void testMongoDao() {
        for (int i = 0; i < 10; i++) {
            OutDepartmentInfoDoc departmentInfoDoc = new OutDepartmentInfoDoc();
            //departmentInfoDoc.setId(ObjectId.get());
            departmentInfoDoc.setOutEa("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
            departmentInfoDoc.setOutDepartmentId(i + 1 + "");
            departmentInfoDoc.setOutDepartmentInfo("这个是jsonyyyy");
            departmentInfoDoc.setCreateTime(System.currentTimeMillis());
            departmentInfoDoc.setUpdateTime(System.currentTimeMillis());
            outDepartmentInfoMongoDao.batchReplace(Lists.newArrayList(departmentInfoDoc));
            //查询
//        OutDepartmentInfoDoc outDepartmentInfoDoc = outDepartmentInfoMongoDao.getById(departmentInfoDoc.getId().toString());
//        System.out.println(outDepartmentInfoDoc);
            //departmentInfoDoc.setId(ObjectId.get());
//        departmentInfoDoc.setOutDepartmentInfo("这个是json11111");
//        departmentInfoDoc.setCreateTime(System.currentTimeMillis());
//        departmentInfoDoc.setUpdateTime(System.currentTimeMillis());
//        outDepartmentInfoMongoDao.batchReplace(Lists.newArrayList(departmentInfoDoc));
            //查询
//        OutDepartmentInfoDoc outDepartmentInfoDoc1 = outDepartmentInfoMongoDao.getById(departmentInfoDoc.getId().toString());
//        System.out.println(outDepartmentInfoDoc1);

            List<OutDepartmentInfoDoc> outDepartmentInfoDocs = outDepartmentInfoMongoDao.queryDepartmentInfos("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
            System.out.println(outDepartmentInfoDocs);
        }
    }

    @Test
    public void deleteNotInCollectionDocs() {
        OutDepartmentInfoDoc departmentInfoDoc = new OutDepartmentInfoDoc();
        //departmentInfoDoc.setId(ObjectId.get());
        departmentInfoDoc.setOutEa("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
        departmentInfoDoc.setOutDepartmentId("100");
        departmentInfoDoc.setOutDepartmentInfo("这个是jsonyyyy");
        departmentInfoDoc.setCreateTime(System.currentTimeMillis());
        departmentInfoDoc.setUpdateTime(System.currentTimeMillis());

        OutDepartmentInfoDoc departmentInfoDoc1 = new OutDepartmentInfoDoc();
        //departmentInfoDoc1.setId(ObjectId.get());
        departmentInfoDoc1.setOutEa("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
        departmentInfoDoc1.setOutDepartmentId("300");
        departmentInfoDoc1.setOutDepartmentInfo("这个是jsonyyyy");
        departmentInfoDoc1.setCreateTime(System.currentTimeMillis());
        departmentInfoDoc1.setUpdateTime(System.currentTimeMillis());
        DeleteResult deleteResult = outDepartmentInfoMongoDao.deleteNotInCollectionDocs("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", Lists.newArrayList(departmentInfoDoc, departmentInfoDoc1));
        System.out.println(deleteResult);
    }
}
