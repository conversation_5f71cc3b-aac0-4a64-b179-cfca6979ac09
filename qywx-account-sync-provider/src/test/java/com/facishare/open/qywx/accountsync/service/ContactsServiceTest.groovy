package com.facishare.open.qywx.accountsync.service

import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinEnterpriseLicenseModel
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class ContactsServiceTest extends Specification {

    @Autowired
    private ContactsService contactsService;

    def "checkEnterpriseProductVersion"() {
        expect:
        for (int i = 0; i < 5; i ++) {
            new Thread({ ->
                for (int j = 0; j < 5; j++) {
                    contactsService.onAvailableRangeChanged("wx88a141937dd6f838", "wpwx1mDAAAdPdwoJsXU7EG90vG_FY2Cg")
                }
            }).start()
            new Thread({ ->
                for (int j = 0; j < 5; j++) {
                    contactsService.onAvailableRangeChanged("wx88a141937dd6f838", "wpwx1mDAAAdPdwoJsXU7EG90vG_FY2Cg")
                }
            }).start()
            new Thread({ ->
                for (int j = 0; j < 5; j++) {
                    contactsService.onAvailableRangeChanged("wx88a141937dd6f838", "wpwx1mDAAAdPdwoJsXU7EG90vG_FY2Cg")
                }
            }).start()
        }

        Thread.sleep(200000)
        def result = contactsService.onAvailableRangeChanged("wx88a141937dd6f838", "wpwx1mDAAAdPdwoJsXU7EG90vG_FY2Cg")
        println result
    }

    def "createEmployeeInfo"() {
        expect:
        def result = contactsService.createEmployeeInfo("wpwx1mDAAA3fRMFTGuFvJa2LXoZ8U9_A", "wx88a141937dd6f838", "wowx1mDAAATz-xun2ZUilgkIZhdu-9VQ", "83838")
        println result
    }

    def "addUser"() {
        expect:
        def result = contactsService.addUser("wx88a141937dd6f838", "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw", "wowx1mDAAAo920W0tmPtvkdlxM3njfdA")
        println result
    }
}
