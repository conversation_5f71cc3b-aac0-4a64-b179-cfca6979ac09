package com.facishare.open.qywx.accountsync.test;

import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountinner.service.ExternalContactsService;
import com.facishare.open.qywx.accountsync.result.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ExternalContactsServiceTest {
    @Autowired
    private ExternalContactsService externalContactsService;

    @Test
    public void getDetail2() {
        Result<QyweixinExternalContactRsp> result = externalContactsService.getDetail2("84883",
                "wmwx1mDAAAY8GeONLnAsySl13hkQ_E5A", null);
        System.out.println(result);
    }

    @Test
    public void getExternalContactList2() {
        Result<List<String>> result = externalContactsService.getExternalContactList2("84883",
                "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        System.out.println(result);
    }
}
