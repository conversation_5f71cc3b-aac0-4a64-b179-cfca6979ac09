package com.facishare.open.qywx.accountsync.manager


import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class CorpManagerTest extends Specification {
    @Resource
    private CorpManager corpManager;

    def "enterpriseBind"() {
        expect:
        corpManager.enterpriseBind("teeee", "fdfdf", 0)
    }
}
