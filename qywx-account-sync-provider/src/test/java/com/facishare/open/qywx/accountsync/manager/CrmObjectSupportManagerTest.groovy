package com.facishare.open.qywx.accountsync.manager

import cn.hutool.cache.CacheUtil
import com.fxiaoke.crmrestapi.common.data.HeaderObj
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit;

class CrmObjectSupportManagerTest extends Specification {

    def objectDescribeService = Mock(ObjectDescribeService)
    def existObjCache = CacheUtil.newTimedCache(TimeUnit.MINUTES.toMillis(24*60L));
    def yourClass = new CrmObjectSupportManager(objectDescribeService: objectDescribeService, existObjCache: existObjCache)
    HeaderObj headerObj = HeaderObj.newInstance(tenantId, -10000);
    String tenantId;

    @Unroll
    def "test isObjCreate with different scenarios"() {
        given:
        def tenantId = 123
        def objApiName = "TestObject"
        def cacheKey = tenantId + objApiName
        def headerObj = HeaderObj.newInstance(tenantId, -10000)
        def describeResult = new Result<ControllerGetDescribeResult>()

        when:
        if (scenario == "exists_in_cache") {
            existObjCache.put(cacheKey, "1")
        } else if (scenario == "describe_succeeds") {
            describeResult.setCode(0) // 假设成功时的代码为0
            objectDescribeService.getDescribe(headerObj, objApiName) >> describeResult
        } else if (scenario == "describe_fails") {
            describeResult.setCode(-1) // 假设失败时的代码为-1
            objectDescribeService.getDescribe(headerObj, objApiName) >> describeResult
        }

        and:
        def result = yourClass.isObjCreate(tenantId, objApiName)

        then:
        result == expectedResult
        if (scenario == "exists_in_cache") {
            0 * objectDescribeService.getDescribe(_, _)
        } else if (scenario == "describe_succeeds") {
            existObjCache.get(cacheKey) == "1"
        } else if (scenario == "describe_fails") {
            !existObjCache.containsKey(cacheKey)
        }

        where:
        scenario            | expectedResult
        "exists_in_cache"   | true
        "describe_succeeds" | true
        "describe_fails"    | false
    }

}

