package com.facishare.open.qywx.accountsync.test;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.arg.UploadFileArg;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.manager.FsFileManager;
import com.facishare.open.qywx.accountsync.network.ProxyOkHttpClient;
import com.facishare.open.qywx.accountsync.result.UploadFileResult;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinAccountSyncServiceImpl;
import com.facishare.open.qywx.accountsync.utils.HttpUrlUtils;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.BufferedInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class FsFileMangerTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private FsFileManager fsFileManager;
    @Autowired
    private ProxyOkHttpClient proxyOkHttpClient;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinAccountSyncServiceImpl qyweixinAccountSyncService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeService employeeService;

    @Test
    public void test() throws Exception{
        String tu = "http://wework.qpic.cn/bizmail/xkLxjFQ4ZSkMibfL7DPcia2s6AZuc7wyXb6FufYAeNHpv2DqzY7uat6g/0";
        String imageUrl = "http://wework.qpic.cn/bizmail/xkLxjFQ4ZSkMibfL7DPcia2s6AZuc7wyXb6FufYAeNHpv2DqzY7uat6g/0";
//        String outputFilePath = "D:\\picture/avatar.jpg"; // 替换为您要保存图片的路径
//
//        try {
//            downloadImage(imageUrl, outputFilePath);
//            System.out.println("图片下载完成！");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        URL url = new URL(tu);
        InputStream inputStream = url.openStream();
        URLConnection connection = url.openConnection();
        connection.connect();

        String contentLength = connection.getHeaderField("Content-Length");
        long length = Long.parseLong(contentLength);
        String path = url.getPath();
        System.out.println(path);
        String q = url.getUserInfo();
        System.out.println(q);
        UploadFileArg uploadFileArg = new UploadFileArg();
        uploadFileArg.setEa("84883");
        uploadFileArg.setFileExt("jpg");
        uploadFileArg.setFileName("avatar.jpg");
        uploadFileArg.setFileSize(length);
        uploadFileArg.setMessageType("image");
        UploadFileResult result = fsFileManager.uploadFile(inputStream, uploadFileArg);
        String url1 = "http://172.31.100.247:17263/API/v1/inner/object/PersonnelObj/action/Edit";
        Map<String, String> map = buildHeaderMap(84883, 1021, "OpenAPI-V2.0");

        Map<String, Object> form = new HashMap<>();
        Map<String, Object> form1 = new HashMap<>();
        Map<String, Object> form2 = new HashMap<>();
        form2.put("ext", "jpg");
        form2.put("filename", "avatar.jpg");
        form2.put("path", result.getNpath());//N_202309_12_27d6e7c1eaff46518c467efd35deff87
        form2.put("size", result.getFileSize());
        form1.put("profile_image", Lists.newArrayList(form2));
        form1.put("_id", "1021");
        form.put("object_data", form1);
        String httpRsp = proxyOkHttpClient.postUrl(url1, form, map);
        System.out.println(httpRsp);
    }

    public static Map<String, String> buildHeaderMap(int enterpriseId, int currentEmployeeId, String sourceHeader) {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-type", "application/json;charset=utf-8");
        headerMap.put("x-fs-userInfo", String.valueOf(currentEmployeeId));
        headerMap.put("x-fs-peer-name", sourceHeader);
        headerMap.put("x-fs-ei", String.valueOf(enterpriseId));
        return headerMap;
    }

    public static void downloadImage(String imageUrl, String outputFilePath) throws Exception {
        URL url = new URL(imageUrl);
        InputStream inputStream = url.openStream();
        BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);

        FileOutputStream fileOutputStream = new FileOutputStream(outputFilePath);
        byte[] buffer = new byte[1024];
        int bytesRead;

        while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
            fileOutputStream.write(buffer, 0, bytesRead);
        }

        fileOutputStream.close();
        bufferedInputStream.close();
    }

    @Test
    public void updateProfileImage() {
        String corpId = "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw";
        String outAccount = "wowx1mDAAAGSkoBYE8XgLqvIZEgspD-A";
        String profileImage = "http://wework.qpic.cn/bizmail/xkLxjFQ4ZSkMibfL7DPcia2s6AZuc7wyXb6FufYAeNHpv2DqzY7uat6g/0";
        try {
            List<QyweixinAccountEmployeeMapping> employeeMappingList = qyweixinAccountSyncService.getFsAccountByOutAccount(
                    corpId, outAccount,0,null);
            log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,employeeMappingList={}", employeeMappingList);
            if(CollectionUtils.isEmpty(employeeMappingList)) return;
            String userAccount = employeeMappingList.get(0).getFsAccount();
            log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,corpId={},outAccount={},userAccount={}",
                    corpId,outAccount,userAccount);

            List<String> accountList = Splitter.on(".").splitToList(userAccount);
            if(CollectionUtils.isEmpty(accountList) || accountList.size() < 3) {
                return;
            }
            String ea = accountList.get(1);
            //需要验证ea的正确性
            com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseResult = qyweixinAccountBindService.fsEaToOutEaResult(SourceTypeEnum.QYWX.getSourceType(), ea);
            log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,enterpriseResult={}", enterpriseResult);
            //反绑定企业不更新
            if(!enterpriseResult.isSuccess() || ObjectUtils.isEmpty(enterpriseResult) || enterpriseResult.getData().getBindType() != 0) {
                return;
            }
            String uid = accountList.get(2);
            int userId = 0;
            try {
                userId = Integer.parseInt(uid);
            } catch (Exception e) {
                log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,userAccount={},parseInt exception={}",userAccount,e.getMessage());
                return;
            }

            GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
            employeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            employeeDtoArg.setEmployeeId(userId);
            GetEmployeeDtoResult employeeDto = employeeService.getEmployeeDto(employeeDtoArg);
            log.info("updateProfileImage getEmployeeDto success. employeeDtoArg:{}, profileImage:{}, employeeDto:{}",
                    employeeDtoArg, profileImage, employeeDto);
            if (ObjectUtils.isNotEmpty(employeeDto.getEmployee()) && StringUtils.isEmpty(employeeDto.getEmployee().getProfileImage())) {
                //更新通讯录的方式去更新头像
                URL url = new URL(profileImage);
                InputStream inputStream = url.openStream();
                URLConnection connection = url.openConnection();
                connection.connect();
                String contentLength = connection.getHeaderField("Content-Length");
                long length = Long.parseLong(contentLength);
                UploadFileArg uploadFileArg = new UploadFileArg();
                uploadFileArg.setEa(ea);
                uploadFileArg.setFileExt("jpg");
                uploadFileArg.setFileName("avatar.jpg");
                uploadFileArg.setFileSize(length);
                uploadFileArg.setMessageType("image");
                UploadFileResult uploadFileResult = fsFileManager.uploadFile(inputStream, uploadFileArg);
                log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,uploadFileResult={}", uploadFileResult);
                if(ObjectUtils.isNotEmpty(uploadFileResult)) {
                    String restFulUrl = HttpUrlUtils.buildUpdateObjectDataUrl("PersonnelObj");
                    Map<String, String> headerMap =
                            HttpUrlUtils.buildHeaderMap(eieaConverter.enterpriseAccountToId(ea), -10000, HttpUrlUtils.FS_OPEN_QYWX);
                    Map<String, Object> form = new HashMap<>();
                    Map<String, Object> objectDataMqp = new HashMap<>();
                    Map<String, Object> profileImageMap = new HashMap<>();
                    profileImageMap.put("ext", "jpg");
                    profileImageMap.put("filename", "avatar.jpg");
                    profileImageMap.put("path", uploadFileResult.getNpath());//N_202309_12_27d6e7c1eaff46518c467efd35deff87
                    profileImageMap.put("size", uploadFileResult.getFileSize());
                    objectDataMqp.put("profile_image", Lists.newArrayList(profileImageMap));
                    objectDataMqp.put("_id", String.valueOf(userId));
                    form.put("object_data", objectDataMqp);
                    String httpRsp = proxyOkHttpClient.postUrl(restFulUrl, form, headerMap);
                    log.info("QyweixinGatewayInnerServiceImpl.updateProfileImage,restFulUrl={},headerMap={},form={},httpRsp={}", restFulUrl, headerMap, form, httpRsp);
                }
            }
        }catch (Exception e) {
            log.info("updateProfileImage failed,exception={}", e.getMessage());
        }
    }
}
