package com.facishare.open.qywx.accountsync.service

import com.facishare.open.order.contacts.proxy.api.service.FsEventService
import com.facishare.open.qywx.accountsync.service.impl.FsEventServiceImpl
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class FsEventServiceTest extends Specification {
    @Autowired
    private FsEventServiceImpl fsEventService

    def "repMsgEvent2"() {
        given:
        String ea = eaCase
        Integer ei = eiCase
        expect:
        def result = fsEventService.onEnterpriseOpened(ei, ea, "test")
        println result;
        where:
        eaCase  |  eiCase
        "y0668"  |  1177
    }
}
