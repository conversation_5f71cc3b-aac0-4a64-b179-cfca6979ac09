package com.facishare.open.qywx.save.enums;

/**
 * Created by fengyh on 2018/7/23.
 */
public enum ErrorRefer {
    DATABASE_RETURN_NULL(203, "数据库返回为空"),

    NULL_POINTER_EXCEPTION(202, "空指针异常"),

    BIND_ERROR(0, "绑定数量为0"),

    QUERRY_EMPTY(0, "查询数据为空"),

    PARAMS_ERROR(30, "参数错误"),

    ENTERPRISE_NOT_BING_ERROR(40, "企业未绑定"),

    ENTERPRISE_NOT_SAVE(50, "请联系管理人员在纷享进行相关企业会话存档设置"),

    SYSTEM_ERROR(400, "系统错误"),

    USER_NOT_LOGIN(220, "用户未登录"),

    ENTERPRISE_NO_SETTING(303, "请先在CRM的企业微信对接中进行授权"),

    TIME_ERROR(204, "时间不在范围内"),

    SECRET_ERROR(205, "自建应用secret不正确"),

    SWITCH_EXTERNAL_ERROR(206, "企业未同意转换external_userid"),

    SWITCH_OPENID_ERROR(207, "受企微限定，新授权会话存档能力的员工，需在第二天才可使用此能力。请您明天再试！"),

    FIELD(900, "Service返回失败"),

    STORAGE_NOT_SET_SALES_RECODE(208, "该企业未开启手动/自动存档至销售记录"),
    ;

    private int code;               //返回码
    private String message;        //返回描述

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    ErrorRefer(int code, String message){
        this.code = code;
        this.message = message;
    }
}
