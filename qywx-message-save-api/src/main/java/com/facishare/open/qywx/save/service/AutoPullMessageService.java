package com.facishare.open.qywx.save.service;

import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArg;
import com.facishare.open.qywx.save.result.QueryMessageIdResult;
import com.facishare.open.qywx.save.result.Result;

/**
 * <AUTHOR>
 * @Date 2021/3/29 19:18
 * @Version 1.0
 */
public interface AutoPullMessageService {
    //拉取聊天记录
    Result<Integer> getCorpIdMessage();

    //自动留存
    Result<Integer> getAutoSynchronizationMessage();

    //产生会话通知，企业微信通知专区，拿到notifyID,在去查询消息
    Result<Integer> getMessageByCallBackToken(String qywxCorpId,String appId,String notifyId);

    Result<QueryMessageIdResult> conditionQueryMessageData(String qywxCorpId, String appId, QywxQueryMessageArg queryMessageArg);

    //测试
    void test();
    //测试销售记录
    void test1();

}
