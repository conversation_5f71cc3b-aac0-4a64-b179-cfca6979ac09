package com.facishare.open.qywx.save.base;

/**
 * <pre>
 *  异常返回码枚举接口
 * </pre>
 * Create by max on 2019/07/16
 */
public interface IResponseEnum {
    /**
     * 获取返回码
     *
     * @return 返回码
     */
    int getCode();

    /**
     * 获取返回信息
     *
     * @return 返回信息
     */
    String getMessage();

    /**
     * 获取trace信息
     *
     * @return 返回信息
     */
    default String getTraceMessage() {
        return null;
    }
}
