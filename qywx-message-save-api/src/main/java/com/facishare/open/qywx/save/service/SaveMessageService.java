package com.facishare.open.qywx.save.service;


import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.save.result.FileMessageResult;
import com.facishare.open.qywx.save.result.Pager;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.vo.QywxMessageVo;
import com.mongodb.client.result.DeleteResult;
import org.bson.types.ObjectId;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/25 10:33
 * @Version 1.0
 */
public interface SaveMessageService {

    //保存解密后的数据
    Result<Integer> batchSaveMessage(List<QywxMessageVo> messageList);

    //更新数据
    Result<Integer> batchUpdateMessage(List<QywxMessageVo> messagePoList, String ea);

    //单独更新数据
    Result<Integer> updateMessageById(QywxMessageVo messagePo);

    //删除数据
    Result<Integer> batchDeleteMessage(List<QywxMessageVo> messagePoList);

    //根据消息id查询
    Result<QywxMessageVo>  getMessageById(String ea, String messageId);

    //条件返回消息记录
    Result<Pager<FileMessageResult>> conditionQueryMessage(QueryMessageArg arg);

    //返回消息记录
    Result<List<FileMessageResult>> getConditionQueryMessage(QueryMessageArg arg, List<QywxMessageVo> npathResult);

    //返回消息记录
    Result<List<FileMessageResult>> getConditionQueryMessageByRoom(QueryMessageArg arg, String roomId);

    //返回最新的seq
    Result<Long> queryLastSeq(String ea);

    Integer getIsRoom(QueryMessageArg queryMessageArg);

    List<String> getRoom(QueryMessageArg queryMessageArg);

    Result<List<FileMessageResult>> test(QueryMessageArg arg);

    String getName(String ea, String outEa, String userId);

    /**
     * 预置会话存档对象
     * @param ei
     * @return
     */
    Result<Boolean> preserveWeChatConversionObj(Integer ei);

    //迁移mongo
    Result<Void> saveMessageToMongo(String ea, Long startSeq, Long endSeq);

    //全量迁移mongo
    Result<Void> saveAllMessageToMongo();

    Result<Void> saveMessageAllIds();

    //删除
    Result<Long> deleteMongoTableData(String ea);

    //查询
    Result<QywxMessageVo> getMongoDataById(String ea, String id);

    //更新
    Result<Integer> updateMongoData(String ea, String id, QywxMessageVo vo);

    //插入
    Result<Integer> insertMongoData(String ea, QywxMessageVo vo);
}
