package com.facishare.open.qywx.save.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:29
 * @Version 1.0
 */
@Data
public class QywxActviceCodeArg implements Serializable {

    private String fsEa;
    private String activeCode;
    private String userId;

    @Data
    public static class QywxTransferUserIdArg implements Serializable {
        private String fsEa;
       private List<QywxTransferUserItem> transferList;

    }

    @Data
    public static class QywxTransferUserItem implements Serializable {

        private String handoverUserid;

        private String takeoverUserid;

    }

}
