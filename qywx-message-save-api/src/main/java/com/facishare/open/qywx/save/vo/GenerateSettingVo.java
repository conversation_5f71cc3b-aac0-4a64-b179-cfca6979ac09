package com.facishare.open.qywx.save.vo;

import com.facishare.open.qywx.save.arg.MessageStorageArg;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/22 19:51
 * @Version 1.0
 */
@Data
public class GenerateSettingVo implements Serializable {

    private Integer fsTenantId;
    private String ea;
    private String qywxCorpId;//企业微信id
    private String secret;//会话密钥
    private String publicKey;//公钥
    private String privateKey;//私钥
    private Integer version;//版本号
    private Integer seq;//记录沉淀的数据最大seq
    private List<String> ipList;
    private String corpSecret;//自建应用的secret
    private Integer autRetention;//会话留存
    private String agentId;//企业微信的应用id
    private MessageStorageArg storageLocation;//会话存档存储位置
}
