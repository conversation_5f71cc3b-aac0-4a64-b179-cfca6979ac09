package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/26
 */
@XStreamAlias("xml")
@Data
public class QyweixinAutoActiveMsgBaseXml implements Serializable {
    /**
     * 即appId。
     * e.g：wx88a141937dd6f838
     */
    @XStreamAlias("ServiceCorpId")
    private String serviceCorpId;

    @XStreamAlias("InfoType")
    private String infoType;

    @XStreamAlias("AuthCorpId")
    private String authCorpId;

    @XStreamAlias("Scene")
    private int scene;

    @XStreamAlias("TimeStamp")
    private long timeStamp;

    @XStreamAlias("AccountList")
    @XStreamImplicit(itemFieldName = "Account") // 指定列表中每个元素的标签名
    private List<Account> accountList;
    @Data
    @XStreamAlias("Account")
    public class Account {
        @XStreamAlias("ActiveCode")
        private String activeCode;

        @XStreamAlias("Type")
        private int type;

        @XStreamAlias("ExpireTime")
        private long expireTime;

        @XStreamAlias("UserId")
        private String userId;

        @XStreamAlias("PreviousStatus")
        private int previousStatus;

        // Getters and Setters
    }
}
