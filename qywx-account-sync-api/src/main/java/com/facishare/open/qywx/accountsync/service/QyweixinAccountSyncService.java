package com.facishare.open.qywx.accountsync.service;


import com.alibaba.fastjson.JSONObject;
import com.facishare.dubbo.plugin.annotation.RestAction;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.model.info.FsEmployeeDetailInfo;
import com.facishare.open.qywx.accountsync.model.IntelligentAppInfoResult;
import com.facishare.open.qywx.accountsync.model.OAConnectorOpenDataModel;
import com.facishare.open.qywx.accountsync.model.OaConnectorDataModel;
import com.facishare.open.qywx.accountsync.model.QyweixinQueryMessageInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinTagEmployeeList;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.save.arg.QywxActviceCodeArg;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArg;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArgByEa;
import com.facishare.open.qywx.save.result.QueryMessageIdResult;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.common.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 提供给北研调用的api
 * Created by liuwei on 2018/07/10
 */
public interface QyweixinAccountSyncService {

    /**
     * 获取第三方应用企业信息（企业信息，授权应用通讯录可见范围）
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @param appId  应用id
     * @return
     */
    @Deprecated
    Result<QyweixinCorpInfo> getCorpInfo(String fsEnterpriseAccount, String appId);
    Result<QyweixinCorpInfo> getCorpInfo2(String fsEnterpriseAccount, String appId, String outEa);

    /**
     * 获取应用管理员列表
     * @param appId  应用id
     * @param outEa
     * 应用的管理员列表（不包括外部管理员）
     * @return
     */
    Result<QyweixinGetAdminListRsp> getAdminList(String outEa, String appId, Integer agentId);

    /**
     * 获取部门列表
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @param appId  应用id
     * @param departmentId  部门id  非必填     获取指定部门及其下的子部门。 如果不填，默认获取全量组织架构
     * 只能拉取token对应的应用的权限范围内的部门列表
     * @param departmentId
     * @return
     */
    @Deprecated
    Result<List<QyweixinDepartmentInfo>> getDepartmentInfoList(String fsEnterpriseAccount, String appId, String departmentId);
    Result<List<QyweixinDepartmentInfo>> getDepartmentInfoList2(String fsEnterpriseAccount, String appId, String departmentId, String outEa);

    @Deprecated
    Result<List<QyweixinTag>> getTagInfoList(String fsEnterpriseAccount, String appId);
    Result<List<QyweixinTag>> getTagInfoList2(String fsEnterpriseAccount, String appId, String outEa);

    /**
     * 获取部门成员信息列表(企业信息带有应用可见范围部门)
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @param appId  应用id
     * @param departmentId  部门id
     * @return
     */
    @Deprecated
    Result<List<QyweixinEmployeeInfo>> getDepartmentEmployeeInfoList(String fsEnterpriseAccount, String appId, String departmentId);
    Result<List<QyweixinEmployeeInfo>> getDepartmentEmployeeInfoList3(String fsEnterpriseAccount, String appId, String departmentId, String outEa);

    /**
     * 获取部门成员信息列表(企业信息带有应用可见范围部门)。
     * 默认为CRM应用，获取其他应用信息请务调用此接口
     *
     * @param fsEnterpriseAccount 企业在纷享平台上的账号
     * @param departmentId        部门id
     * @return
     */
    @Deprecated
    @RestAction("getDepartmentEmployeeInfoList2")
    Result<List<QyweixinEmployeeInfo>> getDepartmentEmployeeInfoList(String fsEnterpriseAccount, String departmentId);

    /**
     * 获取标签成员信息列表(企业信息带有应用可见范围标签)
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @param appId 应用id
     * @param tagId 标签id
     * @return
     */
    @Deprecated
    Result<QyweixinTagEmployeeList> getTagEmployeeInfoList(String fsEnterpriseAccount, String appId, String tagId);
    Result<QyweixinTagEmployeeList> getTagEmployeeInfoList3(String fsEnterpriseAccount, String appId, String tagId, String outEa);

    @Deprecated
    @RestAction("getTagEmployeeInfoList2")
    Result<QyweixinTagEmployeeList> getTagEmployeeInfoList(String fsEnterpriseAccount, String tagId);


    /**
     * 批量获取获取员工信息(企业信息带有应用可见范围员工)
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @param appId 应用id
     * @param userIds 员工id
     * @return
     */
    @Deprecated
    Result<List<QyweixinEmployeeInfo>> getEmployeeInfoBatch(String fsEnterpriseAccount, String appId, List<String> userIds);
    Result<List<QyweixinEmployeeInfo>> getEmployeeInfoBatch3(String fsEnterpriseAccount, String appId, List<String> userIds, String outEa);

    /**
     * 默认为CRM应用
     */
    @Deprecated
    @RestAction("getEmployeeInfoBatch2")
    Result<List<QyweixinEmployeeInfo>> getEmployeeInfoBatch(String fsEnterpriseAccount, List<String> userIds);

    Result<List<QyweixinEmployeeInfo>> batchGetEmployeeInfo(String corpId, List<String> userIds);

    /**
     * 获取小程序当前用户信息
     * miniSuitID：企业微信分配的小程序的suitid
     * miniCode：小程序前端获取到的code
     */
    Result<QyweixinUserBind> getQyweixinMiniCurrentUser(Map paramMap);

    /**
     * 获取当前登录用户信息（授权应用登录，网站单点登录，管理后台单点登录, 企业微信工作台点击登录）
     * @param paramMap  ticket或者auth_code.
     * @return
     */
    Result<QyweixinEmployeeInfo> getQyweixinCurrentUser(Map paramMap);


    /**
     * 获取JS-SDK 权限签名
     * @param url      调用JS接口页面的完整URL
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @param appId  应用id
     * @return
     */
    Result<QyweixinJsapiSignature> createJsapiSignature(String url, String fsEnterpriseAccount, String appId);

    /**
     * 获取JS-SDK 权限签名
     * @param url      调用JS接口页面的完整URL
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @param appId  应用id
     * @param outEa 企微企业ID
     * @return
     */
    Result<QyweixinJsapiSignature> createJsapiSignature2(String url, String fsEnterpriseAccount, String appId, String outEa);

    /**
     * 获取开通的企业微信应用列表。
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @return : 添加过的企业微信应用列表。如果没有添加过企业微信应用，则返回空列表
     * */
    @Deprecated
    Result<List<QyweixinApp>> getAuthorizedAppInfo(String fsEnterpriseAccount);
    Result<List<QyweixinApp>> getAuthorizedAppInfo2(String fsEnterpriseAccount, String outEa);

    /**获取外部联系人详情
     *@return :  @see QyweixinExternalContact
     * @param corpID : 企业微信平台上企业账号。
     * @param userIDs : 企业微信外部联系人userID列表, 注意，这里不是企业内部员工*/
    Result<List<QyweixinExternalContactInfo>> getQyweixinExternalContacts(String appID,
                                                                          String corpID,
                                                                          String fsEa,
                                                                          String fsAccount,
                                                                          ArrayList<String> userIDs);

    /**
     * 自建应用获取外部联系人
     */
    Result<QyweixinExternalContactInfo> queryExternalContactsForSelf(String corpId, String appSecret, String externalUser);

    /**
     * 自建应用获取用户详情
     */
    Result<QyweixinEmployeeInfo> getUserInfoFromSelf(String corpSecret, String corpId, String userId);


    /**获取当前用户的手机号
     *
     * @param code : 当前用户的企业微信code
     * @param appId: 企业微信开平上创建的应用ID
     * @param qywxCorpId:企业微信平台上分配的企业账号
     *
     * @return : 用户的手机号。
     * */
    Result <Pair<String, String>> getUserPhone(String appId, String qywxCorpId, String code);


    /**
     * 提供给大数据组，计算活跃度使用。默认CRM应用
     *
     * 计算出可见范围内的员工数量
     * @param fsEa 企业在纷享平台上的账号
     * @return
     */
    @Deprecated
    Result<Integer> getCountQyweixinPrivilegeEmployee(String fsEa);
    Result<Integer> getCountQyweixinPrivilegeEmployee2(String fsEa, String outEa);

    /**
     * 查询企业是否已添加指定应用及应用相关状态
     *
     * @param ea    纷享侧企业账号
     * @param appId 企业微信侧应用ID
     */
    @Deprecated
    Result<QyweixinCorpBindInfo> getCorpBind(String ea, String appId);

    Result<QyweixinCorpBindInfo> getCorpBind2(String ea, String appId, String outEa);

    /**
     * 获取企业绑定信息
     * @param outEa
     * @param appId
     * @return
     */
    Result<QyweixinCorpBindInfo> getCorpBindInfo(String outEa, String appId);

    void testDatapersistorUserLogin();

    /**
     * 通过fsuserid换取外部联系人列表详情
     */
    Result<List<QyweixinExternalContactInfo>>  queryExternalContactList(GenerateSettingVo settingVo, Integer userId);

    /**
     * 开启关闭自动留存
     * @param fs_ea
     * @param flag
     * @return
     */
    @Deprecated
    Result autRetention(String fs_ea, int flag);
    Result autRetention2(String fs_ea, int flag, String outEa);


    /**
     * 开启关闭授权
     * @param fs_ea
     * @param flag
     * @return
     */
    @Deprecated
    Result openAuthorization(String fs_ea, int flag);
    Result openAuthorization2(String fs_ea, int flag, String outEa);

    /**
     * 获取授权信息
     * @return
     */
    @Deprecated
    Result getAuthorization(String fs_ea);
    Result getAuthorization2(String fs_ea, String outEa);

    /**
     * 查询开启了企业授权标识的企业ea
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<String> openAuthorizationByPage(int pageNum, int pageSize);

    List<String> getAutRetentionCorp();

    List<QyweixinExternalContactInfo> queryExternalContactListTwoScheme(String ea, String outAccount);
    /**
     * 获取外部联系人列表详情
     */
    @Deprecated
    QyweixinContactInfo  externalContact(String ea, List<String> userIds, String next_cursor, int limit);
    QyweixinContactInfo  externalContact2(String ea, List<String> userIds, String next_cursor, int limit, String outEa);

    @Deprecated
    Result<List<QyweixinExternalUserIdInfo>> switchExternalContactEmployeeId(String ea, List<String> externalUserIds);
    Result<List<QyweixinExternalUserIdInfo>> switchExternalContactEmployeeId2(String ea, List<String> externalUserIds, String outEa);

    @Deprecated
    Result<List<QyweixinOpenUserIdInfo>> switchEmployeeId(String ea, List<String> userIds);
    Result<List<QyweixinOpenUserIdInfo>> switchEmployeeId2(String ea, List<String> userIds, String outEa);

    Result<Void> finishExternalMigration(String ea);

    /**
     *自建应用和isv方案
     * @return
     */
    @Deprecated
    Map<String, String> externalContactEmployeeId(String ea);
    Map<String, String> externalContactEmployeeId2(String ea, String outEa);

    @Deprecated
    Result<List<QyweixinGetMessageUser>> AutoGetExternalContactEmployeeId(String ea);
    Result<List<QyweixinGetMessageUser>> AutoGetExternalContactEmployeeId2(String ea, String outEa);

    String getFsAccount(String ea, String externalContactEmployeeId, String outEa);

    Map<String,String> queryByPhone(Integer enterpriseId, String filed, String filedValue, String obj);

    int getEiByEa(String ea);

    String getOutEaByFsEa(String ea);

    @Deprecated
    QyweixinGroupChatDetail.GroupChat getRoomMessage(String ea, String roomId);
    QyweixinGroupChatDetail.GroupChat getRoomMessage2(String ea, String roomId, String outEa);

    String sendAutoMessage(AutoMessageArg autoMessageArg, Map<String, Object> attempt);

    String getToken(String outEa, String secret);

    int getCorpSecret(String ea);

    /**
     * 查看企业是否已经开通代开发自建应用，通过企业代开发自建应用的保存信息判断是否已经开通了代开发自建应用
     * @param corpId 企业微信id
     * @return
     */
    Integer queryEnterpriseReplaceApplication(String corpId);

    String queryEnterpriseIdByName(String corpName);

    QyweixinCorpRep getQYWXCorpBindInfo(String corpId);

    void plainToEncryption(String corpId, List<String> plainAccounts);

    /**
     * 分配成员的客户
     * @param customerInfo
     * @param isResigned true:离职成员，  false:在职成员
     * @return
     */
    Result<List<QyweixinTransferCustomerResult>> externalContactTransferCustomer(QyweixinTransferCustomerInfo customerInfo, Boolean isResigned);

    /**
     * 查询客户接替状态
     * @param customerStatusInfo
     * @param isResigned true:离职成员，  false:在职成员
     * @return
     */
    Result<QyweixinTransferCustomerStatusResult> externalContactTransferResult(QyweixinTransferCustomerStatusInfo customerStatusInfo, Boolean isResigned);

    /**
     * 分配成员的客户群
     * @param transferGroupChatInfo
     * @param isResigned  true:离职成员，  false:在职成员
     * @return
     */
    Result<List<QyweixinTransferGroupChatResult>> externalContactTransferGroupChat(QyweixinTransferGroupChatInfo transferGroupChatInfo, Boolean isResigned);

    /**
     * 获取待分配的离职成员列表
     * @param externalContactInfo
     * @return
     */
    Result<QyweixinUnassignedExternalContactResult> unassignedExternalContact(QyweixinUnassignedExternalContactInfo externalContactInfo);

//    /**
//     * 更新企业的信息，目前只更新了企业名字
//     * @param ea
//     * @return
//     */
//    Result<Void> updateCorpInfo(String ea);

    /**
     * 获取在职和离职继承的信息
     * @param transferInfo
     * @return
     */
    Result<List<QyweixinExternalContactTransferInfo>> getExternalContactTransferInfo(QyweixinQueryTransferInfo transferInfo);

    /**
     * 自动同步在职和离职继承的状态
     * @return
     */
    Result<Void> autoTransferStatus();

    /**
     * 获取在职和离职继承的相关字段
     * @return
     */
    Result<Map<String, String>> getTransferMappingFields();

    /**
     * 分配成员的客户
     * @param customerInfo
     * @return
     */
    Result<List<QyweixinTransferCustomerResult>> transferExternalContact(QyweixinTransferCustomerInfo customerInfo);

    Result<Integer> deleteExternalContactTransfers(List<Integer> deleteIds);

    Result<FsEmployeeBasicInfo> getEmployeeBasicInfoByMobile(String ea, String mobile);

    /**
     * 自动同步转换会话账号
     * @return
     */
    Result<Void> autoIdToOpenid();

    /**
     * 转换会话账号
     * @return
     */
    Result<Void> switchMessageUser(String ea);
    Result<Void> switchMessageUser2(String ea, String outEa);

    /**
     * 明文获取密文
     * @return
     */
    @Deprecated
    Result<List<QyweixinGetOpenIdResult>> getOpenIds(String ea, List<String> plaintextIds, List<String> openids);
    Result<List<QyweixinGetOpenIdResult>> getOpenIds2(String ea, List<String> plaintextIds, List<String> openids, String outEa);

    /**
     * 查询crm人员相关信息
     */
    Result<List<EmployeeDto>> getFsEmpUser(String fsEa, Integer employeeId, List<Integer> userList);

    /**
     * 监控上报
     */
    Result<Void> uploadOaConnectorData(OaConnectorDataModel oaConnectorDataModel);

    /**
     * 开通相关监控上报
     */
    Result<Void> uploadOaConnectorOpenData(OAConnectorOpenDataModel oaConnectorOpenDataModel);

    /**
     * 通过fsEa和userIds获取企微员工详情
     * @param fsEa
     * @param userIds
     * @return
     */
    @Deprecated
    Result<List<QyweixinEmployeeInfoResult>> batchGetEmployeeInfo2(String fsEa, List<String> userIds);
    Result<List<QyweixinEmployeeInfoResult>> batchGetEmployeeInfo21(String fsEa, List<String> userIds, String outEa);

    Result<EnterpriseTrialInfo> getEnterpriseTrialInfo(String fsEa);

    Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo(Integer ei, Integer userId);

    /**
     * 更新企业绑定拓展字段
     * @param fsEa
     * @param extendField
     * @param extendValue
     * @return
     */
    @Deprecated
    Result<Void> updateEnterpriseExtend(String fsEa, String extendField, Object extendValue);
    Result<Void> updateEnterpriseExtend2(String fsEa, String extendField, Object extendValue, String outEa);

    @Deprecated
    Result<QyweixinDepartmentInfo> getDepartmentInfo(String fsEa, String departmentId);
    Result<QyweixinDepartmentInfo> getDepartmentInfo2(String fsEa, String departmentId, String outEa);

    Result<Void> autoGetUserAndDepartmentInfo();

    Result<String> getAppAccessToken(String outEa, String appId);
    Result<String> getAppAccessTokenByFsEa(String fsEa);

    Result<String> getOutEaResultByFsEa(String fsEa);

    Result<List<String>> getOutEaResultListByFsEa(String fsEa);

    Result<List<String>> getFsEaResultByOutEa(String outEa);

    /**
     * 可以查询企业是否有相关的license，只要给企业符合一个license，都可以
     * @return Boolean  true:具有   false:不具有
     */
    Result<Boolean> checkEnterpriseProductVersion(QyweixinEnterpriseLicenseModel enterpriseLicenseModel);

    Result<Void> updateUserAndDepartmentInfo(String outEa);

    Result<Void> createEmployeeInfo(String outEa, String appId, String outUserId, String fsEa);

    Result<Boolean> isQywxDepExist(String appId, String corpId, String departmentId);
    Department getFsDepartment(String ea, Integer departmentId);

    Result<Void> batchInsertBusinessAppInfo(List<QyweixinBusinessBindInfo> qyweixinBusinessBindInfos);

    Result<QueryMessageIdResult> queryQywxSessionMessage(QywxQueryMessageArgByEa qywxQueryMessageArgByEa);

    Result<IntelligentAppInfoResult> queryIntelligentSessionInfo(String fsEa, String appId);

    Result<Void> triggerActiveCodes(String fsEa, String activeCode,String userId);

    Result<Void> transferUserIds(String fsEa, List<QywxActviceCodeArg.QywxTransferUserItem> transferList);
}
