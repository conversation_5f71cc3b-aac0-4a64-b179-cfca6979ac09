package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 企业微信可见范围变更时人员变更信息
 * <p>
 * Create by max on 2020/04/28
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CookedEventChange extends ProtoBase {

    private static final long serialVersionUID = -1299172286071088527L;

    /**
     * 移除事件的flag
     */
    public static int REMOVE_FLAG = 0;
    /**
     * 增加事件的flag
     */
    public static int ADD_FLAG = 1;

    /**
     * 企业ea
     */
    @Tag(1)
    private String ea;
    /**
     * 应用信息
     */
    @Tag(2)
    private String appId;
    /**
     * 变更的人员信息
     */
    @Tag(3)
    private List<Integer> userIds;
}
