package com.facishare.open.qywx.accountsync.utils.xml;

import com.google.common.base.MoreObjects;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

@XStreamAlias("xml")
@Data
public class EncryptXml {

    @XStreamAlias("Encrypt")
    private String encrypt;

    /**
     * 有可能为corpId，或者suiteId
     */
    @XStreamAlias("ToUserName")
    private String toUserName;

    @XStreamAlias("AgentID")
    private String agentId;

    @XStreamAlias("AgentType")
    private String agentType;

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("encrypt", encrypt)
                .add("toUserName", toUserName)
                .add("agentId", agentId)
                .add("agentType", agentType)
                .toString();
    }
}
