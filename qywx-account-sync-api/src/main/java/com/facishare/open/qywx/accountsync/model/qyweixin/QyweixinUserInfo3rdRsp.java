package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-07-23
 */
@Data
public class QyweixinUserInfo3rdRsp implements Serializable {
    private int errcode;
    private String errmsg;
    /**
     * 用户所属企业的corpid
     */
    private String corpid;
    /**
     * 企微企业的名称，虚拟字段，企微接口不返回这个字段，业务使用
     */
    private String corpName;
    /**
     * 用户在企业内的UserID，如果该企业与第三方应用没有授权关系时，返回密文UserId，有授权关系时，按照升级后的ID策略返回明文或密文
     */
    private String userid;
    /**
     * 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取
     */
    private String open_userid;

    public boolean isSuccess(){
        return errcode==0;
    }
}
