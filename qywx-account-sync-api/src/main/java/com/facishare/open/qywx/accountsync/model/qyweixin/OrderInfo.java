package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2019/2/15
 * 订单信息
 */
@Data
public class OrderInfo implements Serializable {
    private static final long serialVersionUID = 4516570241220646766L;

    /**
     * 订单号
     */
    private String orderid;

    /**
     * 订单状态。0-未支付(可能也表示已关闭、未支付且已过期、申请退款中)，1-已支付，2-已关闭， 3-未支付且已过期， 4-申请退款中， 5-申请退款成功
     */
    private int order_status;

    /**
     * 订单类型。0-普通订单，1-扩容订单，2-续期，3-版本变更
     */
    private int order_type;

    /**
     * 客户企业的corpid
     */
    private String paid_corpid;

    /**
     *  下单操作者userid
     */
    private String operator_id;

    /**
     * 应用id
     */
    private String suiteid;

    /**
     * 应用id。（仅旧套件有该字段）
     */
    private int appid;

    /**
     * 购买版本ID
     */
    private String edition_id;

    /**
     *  购买版本名字
     */
    private String edition_name;

    /**
     *  实付款金额，单位分
     */
    private long price;

    /**
     *  购买的人数
     */
    private int user_count;

    /**
     *  购买的时间，单位天
     */
    private int order_period;

    /**
     *  下单时间
     */
    private long order_time;

    /**
     *  付款时间
     */
    private long paid_time;

//    /**
//     *  订单处理状态，0：未推送处理，1：已推送处理，3：未推送已处理（企业开通失败并退款）
//     */
//    private int processingStatus;
}
