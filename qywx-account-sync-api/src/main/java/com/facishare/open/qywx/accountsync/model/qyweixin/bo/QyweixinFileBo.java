package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 企业微信文件上传记录表
 *
 * 上传的媒体文件限制
 * 所有文件size必须大于5个字节
 *
 * 图片（image）：10MB，支持JPG,PNG格式
 * 语音（voice） ：2MB，播放长度不超过60s，仅支持AMR格式
 * 视频（video） ：10MB，支持MP4格式
 * 普通文件（file）：20MB
 *
 * <AUTHOR>
 * @date ********
 */
@Data
@Table(name = "qyweixin_file")
public class QyweixinFileBo extends IdEntity implements Serializable {
    /**
     * 纷享EA
     */
    private String fsEa;
    /**
     * 纷享文件npath
     */
    private String npath;
    /**
     * 非纷享文件url
     */
    private String url;
    /**
     * 文件大小，单位字节
     */
    private Long fileSize;
    /**
     * 文件类型
     */
    private String type;
    /**
     * 企业微信上传文件接口返回的临时media_id字段，有效期3天
     */
    private String mediaId;
    /**
     * mediaId创建时间，从这个时间开始，有效期3天
     */
    private Timestamp createAt;
    /**
     * 上传失败消息
     */
    private String errMsg;
    private Timestamp createTime;
    private Timestamp updateTime;
}
