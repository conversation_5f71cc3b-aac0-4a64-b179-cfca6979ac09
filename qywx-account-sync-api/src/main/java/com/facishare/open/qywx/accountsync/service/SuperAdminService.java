package com.facishare.open.qywx.accountsync.service;

import com.facishare.open.qywx.accountsync.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 超级管理后台服务
 * <AUTHOR>
 * @date 2021/11/01
 */
public interface SuperAdminService {
    /**
     * 超级查询接口
     * @param sqlStr
     * @return
     */
    Result<List<Map<String, Object>>> superQuerySql(String sqlStr);

    /**
     * 超级插入接口
     * @param sqlStr
     * @return
     */
    Result<Integer> superInsertSql(String sqlStr);

    /**
     * 超级更新接口
     * @param sqlStr
     * @return
     */
    Result<Integer> superUpdateSql(String sqlStr);

    /**
     * 超级删除接口
     * @param sqlStr
     * @return
     */
    Result<Integer> superDeleteSql(String sqlStr);

    /**
     * 指定企业
     * @param
     * @return
     */
    Result<Integer> completeOrder(String orderId);
}
