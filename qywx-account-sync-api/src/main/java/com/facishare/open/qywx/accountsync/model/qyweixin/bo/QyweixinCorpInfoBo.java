package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/26
 */
@Data
@Table(name = "qyweixin_corp_info")
public class QyweixinCorpInfoBo extends IdEntity implements Serializable {

    //授权方企业微信id
    private String corpId;
    //授权方企业微信名称
    private String corpName;
    //所绑定的企业微信主体名称(仅认证过的企业有)
    private String corpFullName;
    //授权方企业微信类型，认证号：verified, 注册号：unverified
    private String corpType;
    //授权方企业微信方形头像
    private String corpSquareLogoUrl;
    //授权方企业微信用户规模
    private Integer corpUserMax;

    //认证到期时间
    private String verifiedEndTime;
    //企业类型，1. 企业; 2. 政府以及事业单位; 3. 其他组织, 4.团队号
    private Integer subjectType;
    //授权企业在微工作台（原企业号）的二维码，可用于关注微工作台
    private String corpWxqrcode;
    //企业规模。当企业未设置该属性时，值为空。如：1-50人
    private String corpScale;
    //企业所属行业。当企业未设置该属性时，值为空。 如：IT服务
    private String corpIndustry;
    //企业所属子行业。当企业未设置该属性时，值为空。如：互联网和相关服务
    private String corpSubIndustry;
    //企业所在地信息, 为空时表示未知
    private String location;

    //添加应用的管理员ID
    private String userId;

    /**
     * 1:通讯录基本信息只读
     * 2:通讯录全部信息只读
     * 3:通讯录全部信息读写
     * 4:单个基本信息只读
     * 5:通讯录全部信息只写
     */
    private String appLevel;

    /**
     付费状态。
     0-没有付费;
     1-限时试用;
     2-试用过期;
     3-购买期内;
     4-购买过期;
     5-不限时试用;
     6-购买期内，但是人数超标, 注意，超标后还可以用7天;
     7-购买期内，但是人数超标, 且已经超标试用7天
     **/
    private Integer appStatus;

    /**
     * 购买版本ID
     */
    private String editionId;

    /**
     * 购买版本名字
     */
    private String editionName;

    /**
     * 用户上限。特别注意，如果是固定总价的购买，该参数固定为 4294967295， 含义为不限用户数
     */
    private Long userLimit;

    /**
     * 版本到期时间（根据购买版本，可能是试用到期时间或付费使用到期时间）。特别注意，4294967295代表不限时间
     */
    private Long expiredTime;
}
