package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

@Data
public class GetAdminListModel implements Serializable {
    private Integer errcode;
    private String errmsg;
    private Admin admin;

    /**
     * 应用的管理员列表（不包括外部管理员）。成员授权模式下，不返回管理员列表
     */
    @Data
    public static class Admin implements Serializable {
        /**
         * 管理员的userid
         */
        private String userid;
        /**
         * 该管理员对应用的权限：0=发消息权限，1=管理权限
         */
        private Integer auth_type;
    }

    public boolean isSuccess(){
        return errcode==0;
    }
}
