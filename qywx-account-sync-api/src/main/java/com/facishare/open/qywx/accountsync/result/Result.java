package com.facishare.open.qywx.accountsync.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/11
 */
@Data
@AllArgsConstructor
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -1L;

    private T data;
    private String errorCode;
    private String errorMsg;
    @JsonIgnore
    @JSONField(serialize = false)
    private String i18nKey;
    @JsonIgnore
    @JSONField(serialize = false)
    private List<String> i18nExtra;

    public Result() {
        errorCode = "s120050000";
        this.i18nKey = I18NStringEnum.s1.getI18nKey();
    }
    public Result(String errorCode,String errorMsg, String i18nKey) {
        this.errorCode=errorCode;
        this.errorMsg=errorMsg;
        this.i18nKey= i18nKey;
    }


    public Result(T t) {
        data = t;
        errorCode = "s120050000";
        this.i18nKey= I18NStringEnum.s1.getI18nKey();
    }

    public boolean isSuccess() {
        return ("s120050000".equals(errorCode));
    }
    public Result<T> addError(String code) {
        return this.addError(code, null,null);
    }
    public Result<T> addError(String code ,String errorMsg, String i18nKey) {
        this.errorCode = code;
        this.errorMsg = errorMsg;
        this.i18nKey = i18nKey;
        return this;
    }
    public Result(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public Result<T> Result(String errorCode, String errorMsg, String i18nKey) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.i18nKey = i18nKey;
        return this;
    }

//    public Result<T> Result(String errorCode, String errorMsg, T data, String i18nKey) {
//        this.errorCode = errorCode;
//        this.errorMsg = errorMsg;
//        this.data = data;
//        this.i18nKey = i18nKey;
//        return this;
//    }

    public static <T> Result<T> newInstance(ErrorRefer errorRefer) {
        Result<T> result = new Result<>(errorRefer.getCode(),errorRefer.getQywxCode(),errorRefer.getI18nKey());
        return result;
    }

    public static <T> Result<T> newInstance(ErrorRefer errorRefer, String errorMsg, String i18nKey) {
        Result<T> result = new Result<>(errorRefer.getCode(),errorMsg,i18nKey);
        return result;
    }

    public static <T> Result<T> newInstance2(ErrorRefer errorRefer, T data) {
        Result<T> result = new Result<>(errorRefer.getCode(),errorRefer.getQywxCode(),errorRefer.getI18nKey());
        result.setData(data);
        return result;
    }
}
