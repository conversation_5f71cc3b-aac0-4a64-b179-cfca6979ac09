package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

@Data
public class QyweixinTransferCustomerStatusInfo implements Serializable {
    private String ea;
    private String outEa;//默认为空，1个CRM对多个企微必传
    private String handoverUserId;//原跟进成员的userid
    private String takeoverUserId;//接替成员的userid
    private String cursor;//分页查询的cursor，每个分页返回的数据不会超过1000条；不填或为空表示获取第一个分页
}