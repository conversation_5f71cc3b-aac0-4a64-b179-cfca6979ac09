package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppInfo implements Serializable {
    private Integer errcode;
    private String errmsg;
    private Integer agentid;
    private String name;
    private String square_logo_url;
    private String description;
    private AllowUserInfos allow_userinfos;
    private AllowPartys allow_partys;
    private AllowTags allow_tags;

    private Integer close;//企业应用是否被停用
    private String redirect_domain;//企业应用可信域名
    private Integer report_location_flag;//企业应用是否打开地理位置上报 0：不上报；1：进入会话上报；
    private Integer isreportenter;//是否上报用户进入应用事件。0：不接收；1：接收
    private String home_url;//应用主页url
    //代开发自建应用返回该字段，表示代开发发布状态。0：待开发（企业已授权，服务商未创建应用）；1：开发中（服务商已创建应用，未上线）；2：已上线（服务商已上线应用且不存在未上线版本）；3：存在未上线版本（服务商已上线应用但存在未上线版本）
    private Integer customized_publish_status;

    @Data
    public static class AllowUserInfos implements Serializable {
        private List<User> user;

        @Data
        public static class User implements Serializable {
            private String userid;
        }
    }

    @Data
    public static class AllowPartys implements Serializable {
        private List<String> partyid;
    }

    @Data
    public static class AllowTags implements Serializable {
        private List<String> tagid;
    }
}
