package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinTransferCustomerInfo implements Serializable {
    private String ea;
    private String outEa;//默认为空，1个CRM对多个企微必传
    private String handoverUserId;//原跟进成员的userid
    private String takeoverUserId;//接替成员的userid
    private List<String> externalUserId;//客户的external_userid列表，每次最多分配100个客户
    private String transferSuccessMsg;//转移成功后发给客户的消息，最多200个字符，不填则使用默认文案
    private String handoverDeptId;//原跟进成员的CRM部门id
    private String handoverDeptName;//原跟进成员的CRM部门名称
    private String takeoverDeptId;//接替成员的CRM部门id
    private String takeoverDeptName;//接替成员的CRM部门名称
    private String externalApiName;//CRM对象
    private String externalUserName;//CRM客户的名称
    private String externalNickname;//企微客户名称
}
