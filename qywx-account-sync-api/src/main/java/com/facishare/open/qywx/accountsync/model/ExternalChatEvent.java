package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;

@Data
public class ExternalChatEvent extends ProtoBase {
    @Tag(1)
    private String appId;

    @Tag(2)
    private String corpId;

    @Tag(3)
    private String fsEa;

    @Tag(4)
    private String changeType;

    @Tag(5)
    private String chatId;

    @Tag(6)
    private String updateDetail;

    @Tag(7)
    private String joinScene;

    @Tag(8)
    private String quitScene;

    @Tag(9)
    private String memChangeCnt;

    @Tag(10)
    private String externalChatDetail;  //从企业微信外部群详情接口拿回来的原始JSON数据
}
