package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class QyweixinExternalContactTransferInfo implements Serializable {
    private static final long serialVersionUID = -7776035853540812220L;

    private Long id;
    /**
     * 纷享ea
     */
    private String ea;
    /**
     * 外部联系人id
     */
    private String externalUserId;
    /**
     *外部联系人对象
     */
    private String externalApiName;
    /**
     *外部联系人企业昵称
     */
    private String externalNickname;
    /**
     *外部联系人名称
     */
    private String externalUserName;
    /**
     *原跟进成员的userid
     */
    private String handoverUserId;
    /**
     * 原跟进成员的名称
     */
    private String handoverUserName;
    /**
     * 原跟进成员的状态
     */
    private Integer handoverUserStatus;
    /**
     *原跟进人员的部门id
     */
    private String handoverDeptId;
    /**
     *原跟进人员的部门名称
     */
    private String handoverDeptName;
    /**
     *接替成员的userid
     */
    private String takeoverUserId;
    /**
     * 接替成员的名称
     */
    private String takeoverUserName;
    /**
     *接替成员的部门id
     */
    private String takeoverDeptId;
    /**
     *接替成员的部门名称
     */
    private String takeoverDeptName;
    /**
     *同步状态
     */
    private Integer syncStatus;
    /**
     *失败原因
     */
    private String errorMsg;
    /**
     *接替状态
     */
    private Integer transferResult;
    /**
     *接替时间
     */
    private Timestamp transferTime;
    /**
     *更新时间
     */
    private Timestamp updateTime;
    /**
     *创建时间
     */
    private Timestamp createTime;
}
