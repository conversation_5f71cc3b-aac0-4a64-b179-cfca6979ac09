package com.facishare.open.qywx.accountsync.service;

import com.facishare.open.order.contacts.proxy.api.model.contacts.OutDepModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.qywx.accountsync.model.ContactScopeModel;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.utils.xml.ContactsXml;

import java.io.Serializable;
import java.util.List;

/**
 * 纷享通讯录服务
 *
 * <AUTHOR>
 * @date ********
 */
public interface ContactsService extends Serializable {
    String qywxRootDepId = "0";
    /**
     * 初始化纷享通讯录
     *
     * @param appId
     * @param outEa
     * @param fsEa
     * @return
     */
    Result<Void> initContactsAsync(String appId,String outEa,String fsEa);

    /**
     * 应用可见范围就更事件处理逻辑
     * 对应企微的 change_auth 事件
     *
     * @param appId
     * @param outEa
     * @return
     */
    Result<Void> onAvailableRangeChanged(String appId,String outEa);

    /**
     * 通讯录变更事件
     * 对应企微的 change_contact 事件
     *
     * @return
     */
    Result<Void> onChangeContacts(ContactsXml contactsXml);

    /**
     * 获取CRM应用可见范围内的人员和部门信息
     * @return
     */
    Result<ContactScopeModel> getContactScopeData(String appId, String outEa);

    /**
     * 批量添加纷享员工
     *
     * @param appId
     * @param outEa
     * @param outUserIdList
     * @return
     */
    Result<Void> addUserList(String appId, String outEa, List<String> outUserIdList);

    /**
     * 批量停用纷享员工
     *
     * @param outUserIdList
     * @return
     */
    Result<Void> stopUserList(String appId, String outEa, List<String> outUserIdList);

    /**
     * 批量新增纷享部门
     *
     * @param appId
     * @param outEa
     * @param outDepIdList
     * @return
     */
    Result<Void> addDepList(String appId, String outEa, List<String> outDepIdList);

    /**
     * 批量更新纷享部门，只支持部门名称的更新
     *
     * @param appId
     * @param outEa
     * @param outDepIdList
     * @return
     */
    Result<Void> updateDepList(String appId, String outEa, List<String> outDepIdList);

    /**
     * 批量停用纷享部门
     *
     * @param appId
     * @param outEa
     * @param outDepIdList 需要停用的企微部门ID列表
     * @return
     */
    Result<Void> stopDepList(String appId, String outEa, List<String> outDepIdList);

//    /**
//     * 重置管理员角色
//     * @return
//     */
//    Result<Void> resetAdminRole(String appId, String outEa);

    /**
     * 新增员工，对应add_user事件
     * @param outEa
     * @param outEmpModel
     * @return
     */
    Result<Void> addUser(String outEa, OutEmpModel outEmpModel, String appId);

    /**
     * 更新员工，对应update_user事件
     * 目前只支持用户名称的更新，用户ID的更新，用户部门的更新
     * @param outEa
     * @param outEmpModel
     * @return
     */
    Result<Void> updateUser(String outEa, OutEmpModel outEmpModel, String appId);

    /**
     * 停用员工，对应于delete_user事件
     * @param outEa
     * @param outUserId
     * @return
     */
    Result<Void> stopUser(String outEa, String outUserId,String appId);

    /**
     * 添加纷享员工
     *
     * @param appId
     * @param outEa
     * @param outUserId
     * @return
     */
    Result<Void> addUser(String appId, String outEa, String outUserId);
}
