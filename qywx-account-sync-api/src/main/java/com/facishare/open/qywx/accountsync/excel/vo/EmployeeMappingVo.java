package com.facishare.open.qywx.accountsync.excel.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.facishare.open.qywx.i18n.I18NStringManager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(40)
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeMappingVo {
    /**
     * 纷享EA
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s106",index = 0)
    private String fsEa;

    /**
     * 企微企业ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s107",index = 1)
    private String outEa;

    /**
     * 纷享员工ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s113",index = 2)
    private Integer fsUserId;

    /**
     * 纷享员工名称
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s114",index = 3)
    private String fsUserName;

    /**
     * 企微员工ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s115",index = 4)
    private String outUserId;
    /**
     * 企微员工名称
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s116",index = 5)
    private String outUserName;

    public static EmployeeMappingVo getTempData(I18NStringManager i18NStringManager, String lang, String tenantId) {
        EmployeeMappingVo vo = new EmployeeMappingVo();
        vo.setFsEa(i18NStringManager.get(I18NStringEnum.s112.getI18nKey(),lang,tenantId,""));
        vo.setOutEa("");
        vo.setFsUserId(1000);
        vo.setFsUserName("");
        vo.setOutUserId("1");
        vo.setOutUserName("");
        return vo;
    }
}
