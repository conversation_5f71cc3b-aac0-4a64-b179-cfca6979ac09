package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/26
 */
@XStreamAlias("xml")
@Data
public class QyweixinMsgBaseXml implements Serializable {
    /**
     * 即appId。
     * e.g：wx88a141937dd6f838
     */
    @XStreamAlias("SuiteId")
    public String SuiteId;

    @XStreamAlias("InfoType")
    public String InfoType;

    @XStreamAlias("TimeStamp")
    public String TimeStamp;

    @XStreamAlias("AuthCorpId")
    public String AuthCorpId;

    @XStreamAlias("ToUserName")
    public String ToUserName;

    @XStreamAlias("ChangeType")
    public String ChangeType;
}
