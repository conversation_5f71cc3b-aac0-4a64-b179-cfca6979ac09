package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CreateCrmEnterpriseEventProto extends ProtoBase {
    @Tag(1)
    private String appId;   //应用appId

    @Tag(2)
    private String corpId;   //企微账号id

    @Tag(3)
    private String fsEa;    //纷享账号id

    @Tag(4)
    private String userId;    //企微安装应用员工账号id

    @Tag(5)
    private String fsUserId;    //纷享安装应用员工账号id
}


