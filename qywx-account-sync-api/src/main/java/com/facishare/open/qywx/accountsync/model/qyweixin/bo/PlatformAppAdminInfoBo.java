package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 第三方平台管理员信息
 * Created by <PERSON><PERSON><PERSON> on 2018/11/19
 */
@Table(name = "platform_app_admin_info")
@Data
public class PlatformAppAdminInfoBo extends IdEntity implements Serializable {

    //来源
    private String source;
    //appId
    private String appId;
    //外部企业id
    private String outEa;
    private String mobile;
    private String name;
}
