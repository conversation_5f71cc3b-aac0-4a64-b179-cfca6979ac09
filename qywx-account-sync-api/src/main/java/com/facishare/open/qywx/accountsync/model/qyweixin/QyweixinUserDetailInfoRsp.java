package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/24.
 */
@Data
@Builder
public class QyweixinUserDetailInfoRsp implements Serializable {
    private int errcode;
    private String errmsg;
    private String corpid; //
    private String userid; //成员UserID。对应管理端的帐号
    private String openid;

    private String name; //成员名称
    private Integer gender; //性别。0表示未定义，1表示男性，2表示女性
    private List<String> department;	//成员所属部门id列表，仅返回该应用有查看权限的部门id；成员授权模式下，固定返回根部门id，即固定为1
    private List<String> order;//	部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。值范围是[0, 2^32)。成员授权模式下不返回该字段
    private String position;//	职位信息；第三方仅通讯录应用可获取
    private Integer enable; //	成员启用状态。1表示启用的成员，0表示被禁用。注意，服务商调用接口不会返回此字段
    private Integer status;//	激活状态: 1=已激活，2=已禁用，4=未激活。已激活代表已激活企业微信或已关注微工作台（原企业号）。未激活代表既未激活企业微信又未关注微工作台（原企业号）。

    private String mobile; //手机号码，仅在用户同意snsapi_privateinfo授权时返回
    private String email;  //邮箱，仅在用户同意snsapi_privateinfo授权时返回
    private String avatar; //头像url。注：如果要获取小图将url最后的”/0”改成”/100”即可。仅在用户同意snsapi_privateinfo授权时返回
    private String qr_code;//员工个人二维码（扫描可添加为外部联系人），仅在用户同意snsapi_privateinfo授权时返回
    /**
     * 别名；第三方仅通讯录应用可获取
     */
    private String alias;
    /**
     * 表示在所在的部门内是否为上级。第三方仅通讯录应用可获取。
     */
    private List<String> is_leader_in_dept;
    private String main_department;//主部门，仅当应用对主部门有查看权限时返回。

    private Extattr extattr;

    private ExternalProfile external_profile;

    @Data
    public static class Extattr implements Serializable {
        private List<AttrsInfo> attrs;
    }

    @Data
    public static class ExternalProfile implements Serializable {
        private String external_corp_name;
        private WechatChannels wechat_channels;
        private List<AttrsInfo> external_attr;
    }
    @Data
    public static class WechatChannels implements Serializable {
        private String nickname;
        private Integer status;
    }

    @Data
    public static class AttrsInfo implements Serializable {
        private Integer type;
        private String name;
        private AttrsTextInfo text;
        private AttrsWebInfo web;
        private AttrsMiniprogramInfo miniprogram;
        @Data
        public static class AttrsTextInfo implements Serializable {
            private String value;
        }

        @Data
        public static class AttrsWebInfo implements Serializable {
            private String url;
            private String title;
        }

        @Data
        public static class AttrsMiniprogramInfo implements Serializable {
            private String appid;
            private String pagepath;
            private String title;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QyweixinUserDetailInfoRsp obj = (QyweixinUserDetailInfoRsp) o;
        return (StringUtils.equalsIgnoreCase(this.userid,obj.userid));
    }

    @Override
    public int hashCode() {
        return Objects.hash(userid);
    }
}
