package com.facishare.open.qywx.accountsync.model;

import com.facishare.open.qywx.save.arg.QywxQueryMessageArg;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinQueryMessageInfo implements Serializable {
    private String fsEa;
    private String queryWord;
    private Long startTime;
    private Long endTime;
    private Integer limit;
    private String cursor;
    private ChatInfo chatInfo;
    @Data
    public static class ChatInfo implements Serializable {
        private Integer chatType;
        private List<QywxQueryMessageArg.IdInfo> idList;
        private String chatId;
        private List<String> msgTypeList;
        private QywxQueryMessageArg.IdInfo sender;
    }
    @Data
    public static class IdInfo implements Serializable {
        private String openUserid;
        private String externalUserid;

    }
}
