package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 企业微信配置表
 * <AUTHOR>
 * @date 2024-01-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "qyweixin_config")
public class QyweixinConfigBo extends IdEntity implements Serializable {
    /**
     * 企业微信企业ID
     */
    private String outEa;
    /**
     * 配置类型
     */
    private String type;
    /**
     * 配置值
     */
    private String config;
    private Timestamp createTime;
    private Timestamp updateTime;
}
