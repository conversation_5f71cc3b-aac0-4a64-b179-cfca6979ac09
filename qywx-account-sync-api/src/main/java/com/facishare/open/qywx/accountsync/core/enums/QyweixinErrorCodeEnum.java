package com.facishare.open.qywx.accountsync.core.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/18
 */
public enum QyweixinErrorCodeEnum {

    SUCCESS(0, "成功"),
    CORP_ACCESS_TOKEN_INVALID(42001, "access_token已过期"),
    ACCESS_TOKEN_INVALID(40014, "不合法的access_token");

    /**
     * 40014	不合法的access_token
     * 42001	access_token已过期	access_token有时效性，需要重新获取一次
     * 42007	pre_auth_code已过期	pre_auth_code有时效性，需要重新获取一次
     * 42009	suite_access_token有时效性，需要重新获取一次
     */
    private Integer errCode;
    private String  errMsg;

    QyweixinErrorCodeEnum(Integer errCode, String errMsg) {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public Integer getErrCode() {
        return errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

}
