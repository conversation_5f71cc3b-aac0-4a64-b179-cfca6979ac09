package com.facishare.open.qywx.accountsync.model;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@NoArgsConstructor
public class ProductDefine {
    public static final ProductDefine STANDARDPRO;
    public static final ProductDefine STRENGTHEN;
    public static final ProductDefine PROMOTION_SALES;
    public static final ProductDefine ENTERPRISE;
    public static final ProductDefine OFFICE;
    public static final ProductDefine AGENT;
    public static final ProductDefine DEALER;
    public static final ProductDefine YUNZHIJIA;
    public static final ProductDefine KIS;
    public static final ProductDefine JDY;
    public static final ProductDefine TRAINING_ASSISTANT;
    public static final ProductDefine SMS_1000;
    public static final ProductDefine SMS_2500;
    public static final ProductDefine SMS_5000;
    public static final ProductDefine SMS_10000;
    public static final ProductDefine YXT_STAN;
    public static final ProductDefine FWT_STAN;
    public static final ProductDefine QYWX_TRY;
    public static final ProductDefine QYWX_BASE;
    public static final ProductDefine QYWX_PRO;
    public static final ProductDefine QYWX_ESERVICE_TRY;
    public static final ProductDefine QYWX_ESERVICE_BASE;
    public static final ProductDefine INTERCONNECT_APP_BASIC;
    public static final ProductDefine STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION;
    public static final ProductDefine LIVE;
    public static final ProductDefine ZHIBO_LIULIANG;

    public static final ProductDefine MANUFACTURE_DEALER;
    public static final ProductDefine ADVANCED_OUTWORK_APP;

    public static final ProductDefine QYWX_ORDER_PLUS_TRY;
    public static final ProductDefine QYWX_ORDER_PLUS_BASE;

    public static final ProductDefine PARTNER_USER_FIRM_LIMIT;
    public static final ProductDefine PARTNER_USER_FIRM_LIMIT_TRY;

    private static final List<ProductDefine> values;

    static {

        String env = System.getProperty("process.profile");
        boolean isOnlineEnv = StringUtils.containsIgnoreCase(env,"fstest")==false
                && StringUtils.containsIgnoreCase(env,"firstshare")==false;
        log.info("ProductDefine.static,env={},isOnlineEnv={}",env,isOnlineEnv);
        if (isOnlineEnv) {
            STANDARDPRO = new ProductDefine("5c9b6624833658000139824f1", "5c9b6624833658000139824f", "", "周期性主产品", "record_gt6rr__c");
            STRENGTHEN = new ProductDefine("5c9b66f5e20aec00013396f81", "5c9b66f5e20aec00013396f8", "", "周期性主产品", "record_gt6rr__c");
            PROMOTION_SALES = new ProductDefine("5c9b67940959b63966c537541", "5c9b67940959b63966c53754", "", "周期性主产品", "record_gt6rr__c");
            ENTERPRISE = new ProductDefine("5c9b6735675ef30001c2405a1", "5c9b6735675ef30001c2405a", "", "周期性主产品", "record_gt6rr__c");
            OFFICE = new ProductDefine("5c9b6585397e51000139b5fa1", "5c9b6585397e51000139b5fa", "", "周期性主产品", "record_gt6rr__c");
            AGENT = new ProductDefine("5ce7793af125ae4c83d92b781", "5ce7793af125ae4c83d92b78", "", "周期性主产品", "record_gt6rr__c");
            DEALER = new ProductDefine("5ce778cff125ae4c83d79ba51", "5ce778cff125ae4c83d79ba5", "", "周期性主产品", "record_gt6rr__c");
            YUNZHIJIA = new ProductDefine("5c9c61dc742c2f6d306ec65a1", "5c9c61dc742c2f6d306ec65a", "", "周期性主产品", "record_gt6rr__c");
            KIS = new ProductDefine("5c9c61bceb18ac6686d500fe1", "5c9c61bceb18ac6686d500fe", "是", "周期性主产品", "record_gt6rr__c");
            JDY = new ProductDefine("5db15d7d657abf0001b332241", "5db15d7d657abf0001b33224", "是", "周期性主产品", "record_gt6rr__c");
            TRAINING_ASSISTANT = new ProductDefine("5c9b6af3f125ae3bedcd0cba1", "5c9b6af3f125ae3bedcd0cba", "", "一次性产品", "record_yQ3a3__c");
            SMS_1000 = new ProductDefine("5d233310ea516c00019508e31", "5d233310ea516c00019508e3", "", "一次性产品", "record_yQ3a3__c");
            SMS_2500 = new ProductDefine("5d233310ea516c00019508e51", "5d233310ea516c00019508e5", "", "一次性产品", "record_yQ3a3__c");
            SMS_5000 = new ProductDefine("5d233310ea516c00019508e71", "5d233310ea516c00019508e7", "", "一次性产品", "record_yQ3a3__c");
            SMS_10000 = new ProductDefine("5d233310ea516c00019508e91", "5d233310ea516c00019508e9", "", "一次性产品", "record_yQ3a3__c");
            YXT_STAN = new ProductDefine("5c9b6bc3742c2f6d301d0ce61", "5c9b6bc3742c2f6d301d0ce6", "是", "周期性附产品", "record_HcAyv__c");
            FWT_STAN = new ProductDefine("5c9b6b85eb18ac668681f2a21", "5c9b6b85eb18ac668681f2a2", "是", "周期性附产品", "record_HcAyv__c");
            QYWX_TRY = new ProductDefine("5c9c634983365800015e74511", "5c9c634983365800015e7451", "是", "周期性主产品", "record_gt6rr__c");
            QYWX_BASE = new ProductDefine("5c9c634983365800015e74511", "5c9c634983365800015e7451", "", "周期性主产品", "record_gt6rr__c");
            QYWX_PRO = new ProductDefine("5c9b676e742c2f6d3019d0f01", "5c9b676e742c2f6d3019d0f0", "", "周期性主产品", "record_gt6rr__c");
            QYWX_ESERVICE_TRY = new ProductDefine("5e9e5ef6e7fb660001e736591", "5e9e5ef6e7fb660001e73659", "是", "周期性附产品", "record_HcAyv__c");
            QYWX_ESERVICE_BASE = new ProductDefine("5e9e5ef6e7fb660001e736591", "5e9e5ef6e7fb660001e73659", "", "周期性附产品", "record_HcAyv__c");
            INTERCONNECT_APP_BASIC = new ProductDefine("5c9b6c027adf610001d60a5b1", "5c9b6c027adf610001d60a5b", "", "周期性附产品", "record_HcAyv__c");
            STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION = new ProductDefine("5c9b68b317650c00011ba40b1", "5c9b68b317650c00011ba40b", "", "周期性附产品", "record_HcAyv__c");

            LIVE = new ProductDefine("5e50ac6ec9d1e700010141aa1", "5e50ac6ec9d1e700010141aa", "", "一次性产品", "record_yQ3a3__c");
            ZHIBO_LIULIANG = new ProductDefine("5e4e3a7b25ca9f00013c89f51", "5e4e3a7b25ca9f00013c89f5", "", null, null);

            ADVANCED_OUTWORK_APP = new ProductDefine("5c9b6b64742c2f6d301caa861", "5c9b6b64742c2f6d301caa86", "", "周期性附产品", "record_HcAyv__c");
            MANUFACTURE_DEALER = new ProductDefine("5ed9ee268d7868000149acd21", "5ed9ee268d7868000149acd2", "是", "周期性附产品", "record_HcAyv__c");

            QYWX_ORDER_PLUS_TRY = new ProductDefine("6380aa033ced340001b6fc4d1", "6380aa033ced340001b6fc4d", "是", "周期性主产品", "default__c");
            QYWX_ORDER_PLUS_BASE = new ProductDefine("6380aa033ced340001b6fc4d1", "6380aa033ced340001b6fc4d", "", "周期性主产品", "default__c");

            PARTNER_USER_FIRM_LIMIT_TRY = new ProductDefine("637c72b67f35f1000120851b1", "637c72b67f35f1000120851b", "是", "周期性附产品", "default__c");
            PARTNER_USER_FIRM_LIMIT = new ProductDefine("637c72b67f35f1000120851b1", "637c72b67f35f1000120851b", "", "周期性附产品", "default__c");
        } else {

            STANDARDPRO = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a07cfed910a04f8984", "", "周期性主产品", "record_81yqC__c");
            STRENGTHEN = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a77cfed910a04f94dd", "", "周期性主产品", "record_81yqC__c");
            PROMOTION_SALES = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a67cfed910a04f934d", "", "周期性主产品", "record_81yqC__c");
            ENTERPRISE = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a77cfed910a04f9541", "", "周期性主产品", "record_81yqC__c");
            OFFICE = new ProductDefine("5c90b2a87cfed910a04f966d1", "5c90b2a87cfed910a04f966d", "", "周期性主产品", "record_81yqC__c");
            AGENT = new ProductDefine("5a27f487a5423c95ee72b847", "5de8a84fcddbb400011adad3", "", "周期性主产品", "record_81yqC__c");
            DEALER = new ProductDefine("5a27f487a5423c95ee72b847", "5de8a84fcddbb400011adad3", "", "周期性主产品", "record_81yqC__c");
            YUNZHIJIA = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a57cfed910a04f921c", "", "周期性主产品", "record_81yqC__c");
            KIS = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a27cfed910a04f8d08", "是", "周期性主产品", "record_81yqC__c");
            JDY = new ProductDefine("5a27f487a5423c95ee72b847", "5db2bfcc7badd600012559b2", "是", "周期性主产品", "record_81yqC__c");
            TRAINING_ASSISTANT = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2ae7cfed910a04fa28a", "", "一次性产品", "record_GG8oY__c");
            SMS_1000 = new ProductDefine("5a27f487a5423c95ee72b847", "5d157beaa5083def23976952", "", "一次性产品", "record_GG8oY__c");
            SMS_2500 = new ProductDefine("5a27f487a5423c95ee72b847", "5d157beaa5083def23976954", "", "一次性产品", "record_GG8oY__c");
            SMS_5000 = new ProductDefine("5a27f487a5423c95ee72b847", "5d157beaa5083def23976956", "", "一次性产品", "record_GG8oY__c");
            SMS_10000 = new ProductDefine("5a27f487a5423c95ee72b847", "5d157beaa5083def23976958", "", "一次性产品", "record_GG8oY__c");
            YXT_STAN = new ProductDefine("5a27f487a5423c95ee72b847", "5ca5e6c5cbdbb8000161ee39", "是", "周期性附产品", "record_sw1oV__c");
            FWT_STAN = new ProductDefine("5a27f487a5423c95ee72b847", "5c9cc8991c3a230001d9338e", "是", "周期性附产品", "record_sw1oV__c");
            QYWX_TRY = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a57cfed910a04f92be", "是", "周期性主产品", "record_81yqC__c");
            QYWX_BASE = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a57cfed910a04f92be", "", "周期性主产品", "record_81yqC__c");
            QYWX_PRO = new ProductDefine("5a27f487a5423c95ee72b847", "5c90b2a67cfed910a04f93b1", "", "周期性主产品", "record_81yqC__c");

            QYWX_ESERVICE_TRY = new ProductDefine("5e731bb9530409000163ef161", "5e731bb9530409000163ef16", "是", "周期性附产品", "record_sw1oV__c");
            QYWX_ESERVICE_BASE = new ProductDefine("5e731bb9530409000163ef161", "5e731bb9530409000163ef16", "", "周期性附产品", "record_sw1oV__c");
            INTERCONNECT_APP_BASIC = new ProductDefine("5c90b2a07cfed910a04f89201", "5c90b2a07cfed910a04f8920", "", "周期性附产品", "record_sw1oV__c");
            STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION = new ProductDefine("5c90b2a27cfed910a04f8dd01", "5c90b2a27cfed910a04f8dd0", "", "周期性附产品", "record_sw1oV__c");

            LIVE = new ProductDefine(null, null, null, null, null);
            ZHIBO_LIULIANG = new ProductDefine("5e4e3a7b25ca9f00013c89f51", "5e4e3a7b25ca9f00013c89f5", "", "一次性产品", "record_GG8oY__c");

            ADVANCED_OUTWORK_APP = new ProductDefine("5c90b2aa7cfed910a04f9d231", "5c90b2aa7cfed910a04f9d23", "", "周期性附产品", "record_sw1oV__c");
            MANUFACTURE_DEALER = new ProductDefine("5ecba85b35bd940001eed29c1", "5ecba85b35bd940001eed29c", "是", "周期性附产品", "record_sw1oV__c");

            QYWX_ORDER_PLUS_TRY = new ProductDefine("6371de63166c2500019892d776517", "6371de63166c2500019892d7", "是", "周期性主产品", "default__c");
            QYWX_ORDER_PLUS_BASE = new ProductDefine("6371de63166c2500019892d776517", "6371de63166c2500019892d7", "", "周期性主产品", "default__c");

            PARTNER_USER_FIRM_LIMIT_TRY = new ProductDefine("63acf7a6e7bc45000109e0ac76517", "63acf7a6e7bc45000109e0ac", "是", "周期性附产品", "default__c");
            PARTNER_USER_FIRM_LIMIT = new ProductDefine("63acf7a6e7bc45000109e0ac76517", "63acf7a6e7bc45000109e0ac", "", "周期性附产品", "default__c");
        }

        values = Lists.newArrayList(
                STANDARDPRO,
                STRENGTHEN,
                PROMOTION_SALES,
                ENTERPRISE,
                OFFICE,
                AGENT,
                DEALER,
                YUNZHIJIA,
                KIS,
                JDY,
                TRAINING_ASSISTANT,
                SMS_1000,
                SMS_2500,
                SMS_5000,
                SMS_10000,
                YXT_STAN,
                FWT_STAN,
                QYWX_TRY,
                QYWX_BASE,
                QYWX_PRO,
                QYWX_ESERVICE_TRY,
                QYWX_ESERVICE_BASE,
                INTERCONNECT_APP_BASIC,
                STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION,
                LIVE,
                ADVANCED_OUTWORK_APP,
                MANUFACTURE_DEALER,
                QYWX_ORDER_PLUS_TRY,
                QYWX_ORDER_PLUS_BASE,
                PARTNER_USER_FIRM_LIMIT_TRY,
                PARTNER_USER_FIRM_LIMIT
        );

    }
    private String priceBookProductId;
    private String productId;
//    private String canTry;
//    private String productType;

    /**
     * 周期性主产品和周期性副产品的recordType
     */
    private String recordType;

    public ProductDefine(String priceBookProductId, String productId, String canTry, String productType, String recordType) {
        this.priceBookProductId = priceBookProductId;
        this.productId = productId;
//        this.canTry = canTry;
//        this.productType = productType;
        this.recordType = recordType;
    }

    public static ProductDefine fromId(String productId) {
        return values.stream().filter(item -> Objects.equals(productId, item.getProductId()))
                .findAny().orElseThrow(() -> new RuntimeException("Unknown product:" + productId));
    }
}
