package com.facishare.open.qywx.accountsync.model.qyweixin;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/25 17:35
 */
@Data
public class InnerSearchQueryInfo implements Serializable {
    private Integer limit = 0;
    private Integer offset = 100;
    private List<Filter> filters;
    private List<Order> orders;
    private List<String> fieldProjection;

    @Data
    public static class Filter {
        private String field_name;
        private List<Object> field_values;
        private String operator;
    }

    @Data
    public static class Order {
        private String fieldName;
        private boolean isAsc;
    }
}
