package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.common.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppLicenseInfo implements Serializable {
    private Integer errcode;
    private String errmsg;
    private Integer license_status;
    private TrailInfo trail_info;
    @Data
    private static class TrailInfo implements Serializable {
        @JSONField(serialize = false)
        private Long start_time;
        @JSONField(serialize = false)
        private Long end_time;
        public String getStartTime() {
            if(start_time==null) return "";
            return DateUtil.formatDate(new Date(start_time * 1000),"yyyy-MM-dd hh:mm:ss");
        }

        public String getEndTime() {
            if(end_time==null) return "";
            return DateUtil.formatDate(new Date(end_time * 1000),"yyyy-MM-dd hh:mm:ss");
        }
    }
}
