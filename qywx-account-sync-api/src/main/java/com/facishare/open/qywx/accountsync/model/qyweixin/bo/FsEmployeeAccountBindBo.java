package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/09/27
 */
@Data
@Table(name = "fs_employee_account_bind")
public class FsEmployeeAccountBindBo implements Serializable {

    private String fsEa;
    private String fsEmployeeName;
    private String fsEmployeeId;
    private String fsEmployeeMobile;
    private String fsEmployeeNamePinYin;
    private String fsDepartmentIds;
    private String qywxEa;
    private String qywxEmployeeName;
    private String qywxEmployeeId;
    private String qywxEmployeeMobile; //mobile/email二者不能同时为空
    private String qywxEmployeeEmail;  //mobile/email二者不能同时为空

    public FsEmployeeAccountBindBo(String fsEa, String fsEmployeeName, String fsEmployeeId, String fsEmployeeMobile, String fsEmployeeNamePinYin, String fsDepartmentIds, String qywxEa, String qywxEmployeeName, String qywxEmployeeId, String qywxEmployeeMobile, String qywxEmployeeEmail) {
        this.fsEa = fsEa;
        this.fsEmployeeName = fsEmployeeName;
        this.fsEmployeeId = fsEmployeeId;
        this.fsEmployeeMobile = fsEmployeeMobile;
        this.fsEmployeeNamePinYin = fsEmployeeNamePinYin;
        this.fsDepartmentIds = fsDepartmentIds;
        this.qywxEa = qywxEa;
        this.qywxEmployeeName = qywxEmployeeName;
        this.qywxEmployeeId = qywxEmployeeId;
        this.qywxEmployeeMobile = qywxEmployeeMobile;
        this.qywxEmployeeEmail = qywxEmployeeEmail;
    }

    public FsEmployeeAccountBindBo(String fsEa, String fsEmployeeName, String fsEmployeeAccount, String fsEmployeeMobile, String fsEmployeeNamePinYin, String fsDepartmentIds) {
        this.fsEa = this.fsEa;
        this.fsEmployeeName = this.fsEmployeeName;
        this.fsEmployeeId = fsEmployeeId;
        this.fsEmployeeMobile = this.fsEmployeeMobile;
        this.fsEmployeeNamePinYin = this.fsEmployeeNamePinYin;
        this.fsDepartmentIds = this.fsDepartmentIds;
    }
}
