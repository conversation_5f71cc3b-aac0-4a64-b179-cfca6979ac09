package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;


@XStreamAlias("xml")
@Data
public class SuiteAuthXml {

    @XStreamAlias("SuiteId")
    private String suiteId;

    @XStreamAlias("InfoType")
    private String infoType;

    @XStreamAlias("TimeStamp")
    private long timestamp;

    @XStreamAlias("SuiteTicket")
    private String suiteTicket;

    @Override
    public String toString() {
        return "SuiteAuthXml{" +
                "suiteId='" + suiteId + '\'' +
                ", infoType='" + infoType + '\'' +
                ", timestamp=" + timestamp +
                ", suiteTicket='" + suiteTicket + '\'' +
                '}';
    }
}
