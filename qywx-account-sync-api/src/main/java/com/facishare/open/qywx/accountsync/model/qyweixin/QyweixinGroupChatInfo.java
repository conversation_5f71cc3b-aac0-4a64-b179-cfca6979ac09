package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinGroupChatInfo implements Serializable {
    private String ea;//必填
    private String outEa;//默认为空，如果要支持一个CRM对多个企微，调用方不能传空值
    private String userId;//用于判断用户是否在该群聊
    private Integer statusFilter;//客户群跟进状态过滤。0 - 所有列表(即不过滤)，1 - 离职待继承，2 - 离职继承中，3 - 离职继承完成，默认为0
    private QyweixinOwnerFilter ownerFilter;//群主过滤。如果不填，表示获取应用可见范围内全部群主的数据（但是不建议这么用，如果可见范围人数超过1000人，为了防止数据包过大，会报错 81017）
    private String cursor;//用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填
    private Integer limit = 10;//分页，预期请求的数据量，取值范围 1 ~ 1000 必填

    @Data
    public static class QyweixinOwnerFilter implements Serializable {
        private List<String> userIdList;//用户ID列表。最多100个
    }
}
