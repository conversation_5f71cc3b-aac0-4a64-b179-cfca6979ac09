package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 获取自建或代开发应用用户敏感信息
 * <AUTHOR>
 * @date 2024-01-12
 */
@Data
@Builder
public class QyweixinRepUserDetailInfoRsp implements Serializable {
    private int errcode;
    private String errmsg;
    private String userid;
    private String gender;
    private String avatar;
    private String qr_code;
    private String mobile;
    private String email;
    private String biz_mail;
    private String address;
    public boolean isSuccess() {
        return Objects.equals(errcode, 0);
    }
}
