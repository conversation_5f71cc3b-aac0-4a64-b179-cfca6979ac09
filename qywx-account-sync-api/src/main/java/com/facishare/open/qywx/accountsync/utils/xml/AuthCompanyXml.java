package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * Created by fengyh on 2018/4/20.
 */

@XStreamAlias("xml")
@Data
public class AuthCompanyXml{

    @XStreamAlias("AuthCode")
    private String AuthCode;

    @XStreamAlias("SuiteId")
    private String SuiteId;

    @XStreamAlias("InfoType")
    private String InfoType;

    @XStreamAlias("TimeStamp")
    private String TimeStamp;

    @XStreamAlias("AuthCorpId")
    private String AuthCorpId;

    @Override
    public String toString() {
        return "AuthCompany{" +
                "SuiteId='" + SuiteId + '\'' +
                ", AuthCode='" + AuthCode + '\'' +
                ", InfoType='" + InfoType + '\'' +
                ", TimeStamp='" + TimeStamp + '\'' +
                ", AuthCorpId='" + AuthCorpId + '\'' +
                '}';
    }
}
