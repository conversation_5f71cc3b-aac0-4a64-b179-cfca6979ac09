package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

import java.io.Serializable;

//<xml>
//<ToUserName><![CDATA[toUser]]></ToUserName>
//<FromUserName><![CDATA[sys]]></FromUserName>
//<CreateTime>**********</CreateTime>
//<MsgType><![CDATA[event]]></MsgType>
//<Event><![CDATA[change_contact]]></Event>
//<ChangeType><![CDATA[update_tag]]></ChangeType>
//<TagId>1</TagId>
//<AddUserItems><![CDATA[zhangsan,lisi]]></AddUserItems>
//<DelUserItems><![CDATA[zhangsan1,lisi1]]></DelUserItems>
//<AddPartyItems><![CDATA[1,2]]></AddPartyItems>
//<DelPartyItems><![CDATA[3,4]]></DelPartyItems>
//</xml>

/**
 * 企业微信标签变更通知
 * <AUTHOR>
 * @date 2022-05-16
 */
@XStreamAlias("xml")
@Data
public class TagChangeEventXml implements Serializable {
    @XStreamAlias("ToUserName")
    public String ToUserName;//企业微信CorpID

    @XStreamAlias("FromUserName")
    public String FromUserName;//成员UserID

    @XStreamAlias("CreateTime")
    public String CreateTime;//消息创建时间（整型）

    @XStreamAlias("MsgType")
    public String MsgType;//消息类型，此时固定为：event

    @XStreamAlias("Event")
    public String Event;//事件类型

    @XStreamAlias("ChangeType")
    public String ChangeType;//固定为update_tag

    @XStreamAlias("TagId")
    public Integer TagId;//标签Id

    @XStreamAlias("AddUserItems")
    private String AddUserItems;//标签中新增的成员userid列表，用逗号分隔

    @XStreamAlias("DelUserItems")
    private String DelUserItems;//标签中删除的成员userid列表，用逗号分隔

    @XStreamAlias("AddPartyItems")
    private String AddPartyItems;//标签中新增的部门id列表，用逗号分隔

    @XStreamAlias("DelPartyItems")
    private String DelPartyItems;//标签中删除的部门id列表，用逗号分隔
}
