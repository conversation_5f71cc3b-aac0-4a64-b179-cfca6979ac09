package com.facishare.open.qywx.accountsync.utils;

/**
 * Created by fengyh on 2018/4/19.
 */

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.common.utils.AesException;
import com.facishare.open.qywx.accountsync.utils.xml.EncryptXml;
import com.facishare.open.qywx.accountsync.utils.xml.QYWeiXinMemberEventXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ObjectUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.security.AlgorithmParameters;
import java.util.Arrays;
import java.util.Random;


@Slf4j
public class QYWxCryptHelper {
    static Charset CHARSET = Charset.forName("utf-8");
    private static Base64 base64 = new Base64();

    // 生成4个字节的网络字节序
    private static byte[] getNetworkBytesOrder(int sourceNumber) {
        byte[] orderBytes = new byte[4];
        orderBytes[3] = (byte) (sourceNumber & 0xFF);
        orderBytes[2] = (byte) (sourceNumber >> 8 & 0xFF);
        orderBytes[1] = (byte) (sourceNumber >> 16 & 0xFF);
        orderBytes[0] = (byte) (sourceNumber >> 24 & 0xFF);
        return orderBytes;
    }

    // 还原4个字节的网络字节序
    private static int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        for (int i = 0; i < 4; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }

    // 随机生成16位字符串
    private static String getRandomStr() {
        String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 16; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }


    /**
     * 对密文进行解密.
     *
     * @param text 需要解密的密文
     * @return 解密得到的明文
     * @throws AesException aes解密失败
     */
    private static String decrypt(String text, byte[] aesKey) {
        log.info("QYWxCryptHelper.decrypt,text={}",text);
        byte[] original;
        try {
            // 设置解密模式为AES的CBC模式
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec key_spec = new SecretKeySpec(aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(aesKey, 0, 16));
            cipher.init(Cipher.DECRYPT_MODE, key_spec, iv);

            // 使用BASE64对密文进行解码
            byte[] encrypted = Base64.decodeBase64(text);

            // 解密
            original = cipher.doFinal(encrypted);
            log.info("trace decrypt,aesKey.length:{},  original.length:{} ", aesKey.length, original.length);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("QYWxCryptHelper.decrypt,exception={}",e.getMessage());
            log.info("trace decrypt, aesKey.length:{},  get exception, ", aesKey.length, e);
            return null;
        }

        String xmlContent, from_corpid;
        try {
            // 去除补位字符
            byte[] bytes = PKCS7Encoder.decode(original);

            // 分离16位随机字符串,网络字节序和corpId
            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);

            int xmlLength = recoverNetworkBytesOrder(networkOrder);

            if(xmlLength > 2 * 1024 * 1024) {
                //大于2m，失败
                log.info("QYWxCryptHelper.xmlLength:{}", xmlLength);
                return null;
            }

            xmlContent = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), CHARSET);
            log.info("trace decrypt, xmlLength:{}, xmlContent:{} ",xmlLength, xmlContent);
            from_corpid = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length),
                    CHARSET);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("trace decrypt, get exception, ", e);
            return null;
        } catch (Error e) {
            e.printStackTrace();
            log.info("trace decrypt, get error, ", e);
            return null;
        }
        log.info("trace decrypt, xmlContent:{} ", xmlContent);
        return xmlContent;
    }


    /**
     * 检验消息的真实性，并且获取解密后的明文.
     * <ol>
     * 	<li>利用收到的密文生成安全签名，进行签名验证</li>
     * 	<li>若验证通过，则提取xml中的加密消息</li>
     * 	<li>对消息进行解密</li>
     * </ol>
     *
     * @param msgSignature 签名串，对应URL参数的msg_signature
     * @param timeStamp 时间戳，对应URL参数的timestamp
     * @param nonce 随机串，对应URL参数的nonce
     * @param postData 密文，对应POST请求的数据
     * @param encryptXml xml由前面传过来
     *
     * @return 解密后的原文
     * @throws AesException 执行失败，请查看该异常的错误码和具体的错误信息
     */
    public static String DecryptMsg(String token, byte[] aesKey, String msgSignature, String timeStamp, String nonce, String postData, EncryptXml encryptXml)
            throws AesException {

        // 密钥，公众账号的app secret
        // 提取密文
        //Object[] encrypt = XMLParse.extract(postData);
        if(ObjectUtils.isEmpty(encryptXml)) {
            encryptXml = XStreamUtils.parseXml(postData, EncryptXml.class);
        }
        //EncryptXml encryptXml = XmlParser.fromXml(postData, EncryptXml.class);
        String encrypt = encryptXml.getEncrypt();

        // 验证安全签名
        String signature = SHA1.getSHA1(token, timeStamp, nonce, encrypt);

        log.info("QYWxCryptHelper.DecryptMsg,signature={}",signature);

        // 和URL中的签名比较是否相等
        // System.out.println("第三方收到URL中的签名：" + msg_sign);
        // System.out.println("第三方校验签名：" + signature);
        if (!signature.equals(msgSignature)) {
            log.info("QYWxCryptHelper.DecryptMsg,signature not match");
           return null;
        }

        log.info("QYWxCryptHelper.DecryptMsg,begin");
        // 解密
        String result = decrypt(encrypt, aesKey);
        log.info("QYWxCryptHelper.DecryptMsg,end,result={}",result);
        return result;
    }


    private static final String WATERMARK = "watermark";
    private static final String APPID = "appid";

    /**
     * 检验消息的真实性，appid校验 并且获取解密后的明文.
     * @param appId        敏感数据归属appid，开发者可校验此参数与自身appid是否一致
//     * @param msgSignature 签名串
//     * @param rawData      不包括敏感信息的原始数据字符串，用于计算签名
     * @param encryptedData 包括敏感数据在内的完整用户信息的加密数据
     * @param sessionKey    用户的会话密钥session_key。 开发者应该事先通过 wx.login 登录流程获取会话密钥 session_key 并保存在服务器
     * @param iv           加密算法的初始向量
     * @return
     */
    public static String DecryptMiniprogramMsg(String appId, String encryptedData, String sessionKey, String iv){

//        //签名验证
//        String signature = DigestUtils.sha1Hex(rawData+sessionKey);
//        if(! signature.equals(msgSignature)){
//            log.error("trace DecryptMiniprogramMsg signature error appId:{} sessionKey:{} msgSignature:{}, signature:{} ",appId, sessionKey, msgSignature, signature);
//            return null;
//        }

        byte[] original;
        try {
            // 设置解密模式为AES的CBC模式
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec key_spec = new SecretKeySpec(Base64.decodeBase64(sessionKey), "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(Base64.decodeBase64(iv)));
            cipher.init(Cipher.DECRYPT_MODE, key_spec, parameters);

            // 使用BASE64对密文进行解码
            byte[] encrypted = Base64.decodeBase64(encryptedData);

            // 解密
            original = cipher.doFinal(encrypted);
            log.info("trace DecryptMiniprogramMsg decrypt, original.length:{} ",  original.length);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("trace DecryptMiniprogramMsg decrypt error, get exception, ",  e);
            return null;
        }

        String result;
        try {
            // 去除补位字符
            byte[] bytes = PKCS7Encoder.decode(original);

            result = new String(bytes, CHARSET);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String decryptAppid = jsonObject.getJSONObject(WATERMARK).getString(APPID);

//            if(!appId.equals(decryptAppid)){
//                log.error("trace DecryptMiniprogramMsg error appId not equals decryptAppid:{} , appId:{}", decryptAppid, appId);
//                result = null;
//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("trace DecryptMiniprogramMsg PKCS7Encoder, get exception, ", e);
            return null;
        }
        log.info("trace DecryptMiniprogramMsg decrypt, result:{} ", result);
        return result;
    }

    /**
     * 验证URL
     * @param msgSignature 签名串，对应URL参数的msg_signature
     * @param timeStamp 时间戳，对应URL参数的timestamp
     * @param nonce 随机串，对应URL参数的nonce
     * @param echoStr 随机串，对应URL参数的echostr
     *
     * @return 解密之后的echostr
     * @throws AesException 执行失败，请查看该异常的错误码和具体的错误信息
     */
    public static String VerifyURL(String token, byte[] aesKey,  String msgSignature, String timeStamp, String nonce, String echoStr)
            throws AesException {
        String signature = SHA1.getSHA1(token, timeStamp, nonce, echoStr);

        if (!signature.equals(msgSignature)) {
            log.error("msgSignature:{}, signature:{} ", msgSignature, signature);
            return  null;
        }

        String result = decrypt(echoStr, aesKey);
        log.info("trace echoStr:{}, plain result:{}  ", echoStr, result);
        return result;
    }

    public static void main(String[] args) throws AesException {
        String msgSignature = "9a9f43d3da685fdce2313f5c89323d7df8a4c36d";
        String timeStamp = "1631621825";
        String nonce = "1632186440";
        String postData = "<xml><ToUserName><![CDATA[wwe9de3eff296c131d]]></ToUserName><Encrypt><![CDATA[P/tVU/OC9WemfN2tWxMqi4E3xUDS1V+Wd5aqQmyjDSR8Lk2bf8BH1q7xwLpG2j61BEUjMC0CM0bQYt7v1RmNRQ+GdfmUkMQB274minzxhnV1TA4mQeWA5zmY+Frl/e7Yyy6o/feCsHymqhFSB4dS+Mu8yG+J6jirXmKSxRlzCNTd6na2Nwl4grKaN+9nDDQmRP570DFhvP4ohA28J8tpY85QNdMOIHlF0x9AKGaZz6/hFZtRqVWSdvjPU4TNh9dEXF1+sNG5kEkR3fZanLoj5jjzcWQXrzIw5l7YOoAsRwkVnupF+fqS/moDTEHoGLSIHZ5UkMjNkuGZhBnJc68vesiCnmKM4kUy5vH214Lv+S45fw0fBsyeoX/bmmzsxP6/]]></Encrypt><AgentID><![CDATA[1000008]]></AgentID></xml>";

        String token="fxiaoke";
        byte[] aesKey= org.apache.commons.codec.binary.Base64.decodeBase64("jdRwH2fChYoOC5PWa2lmjKql2V9t5rnVWWQCcHV9GXs" + "=");
        EncryptXml encryptXml = XStreamUtils.parseXml(postData, EncryptXml.class);
        String result = DecryptMsg(token, aesKey, msgSignature, timeStamp, nonce, postData, encryptXml);
        System.out.println(result);
        //QYWeiXinDataEventBaseXml baseMsgXml = XmlParser.fromXml(result, QYWeiXinDataEventBaseXml.class);

        QYWeiXinMemberEventXml eventXml = XStreamUtils.parseXml(result, QYWeiXinMemberEventXml.class);
        System.out.println(eventXml);
        //小程序解密
        String appId = "wx4f4bc4dec97d474b";
        String encryptedData = "CiyLU1Aw2KjvrjMdj8YKliAjtP4gsMZMQmRzooG2xrDcvSnxIMXFufNstNGTyaGS9uT5geRa0W4oTOb1WT7fJlAC+oNPdbB+3hVbJSRgv+4lGOETKUQz6OYStslQ142dNCuabNPGBzlooOmB231qMM85d2/fV6ChevvXvQP8Hkue1poOFtnEtpyxVLW1zAo6/1Xx1COxFvrc2d7UL/lmHInNlxuacJXwu0fjpXfz/YqYzBIBzD6WUfTIF9GRHpOn/Hz7saL8xz+W//FRAUid1OksQaQx4CMs8LOddcQhULW4ucetDf96JcR3g0gfRK4PC7E/r7Z6xNrXd2UIeorGj5Ef7b1pJAYB6Y5anaHqZ9J6nKEBvB4DnNLIVWSgARns/8wR2SiRS7MNACwTyrGvt9ts8p12PKFdlqYTopNHR1Vf7XjfhQlVsAJdNiKdYmYVoKlaRv85IfVunYzO0IKXsyl7JCUjCpoG20f0a04COwfneQAGGwd5oa+T8yO5hzuyDb/XcxxmK01EpqOyuxINew==";
        String sessionKey = "tiihtNczf5v6AKRyjwEUhQ==";
        String iv = "r7BXXKkLb8qrSNn05n0qiA==";
        String signature = "e43ecb743a4d98ae8ed76b7ce6263010b8c0bb4b";
        String rawData = "{\"nickName\":\"Band\",\"gender\":1,\"language\":\"zh_CN\",\"city\":\"Guangzhou\",\"province\":\"Guangdong\",\"country\":\"CN\",\"avatarUrl\":\"http://wx.qlogo.cn/mmopen/vi_32/1vZvI39NWFQ9XM4LtQpFrQJ1xlgZxx3w7bQxKARol6503Iuswjjn6nIGBiaycAjAtpujxyzYsrztuuICqIM5ibXQ/0\"}";

        String miniprogram = DecryptMiniprogramMsg(appId, encryptedData, sessionKey, iv);
    }
}