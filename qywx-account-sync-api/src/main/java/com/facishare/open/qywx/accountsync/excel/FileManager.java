package com.facishare.open.qywx.accountsync.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileName;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2020/4/20
 **/
@Component
@Slf4j
public class FileManager {
    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private EIEAConverter eieaConverter;

    private final String ERP_SYNC_DATA_BUSINESS = "ERP_SYNC_DATA";

    public String uploadTnFile(String ea, Integer userId, byte[] bytes) {
        return uploadTnFile(ea, userId, null, bytes);
    }

    public String uploadTnFile(String ea, Integer userId, String fileName, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(ERP_SYNC_DATA_BUSINESS);
        if(!StringUtils.isEmpty(fileName)){
            arg.setOriginName(fileName);
        }
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        log.info("uploadTnFile,result={}",result);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        }
        return null;
    }

    public byte[] downTnFile(String ea, Integer userId, String nPath) {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setEa(ea);
        arg.setDownloadUser("E." + ea + "." + userId);
        arg.setnPath(nPath);
        NDownloadFile.Result result = storageService.nDownloadFile(arg, ea);
        if (result != null) {
            return result.getData();
        } else {
            log.error("downTnFile get result null");
            return null;
        }
    }

    public String getTnFileName(String tenantId, String ea, Integer userId, String nPath) {
        if (StringUtils.isEmpty(nPath)) {
            return null;
        }
        if (StringUtils.isEmpty(ea)) {
            ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        }
        if (userId == null) {
            userId = 1000;
        }
        NGetFileName.Arg arg = new NGetFileName.Arg();
        arg.setBusiness(ERP_SYNC_DATA_BUSINESS);
        arg.setEa(ea);
        arg.setSourceUser("E." + ea + "." + userId);
        arg.setPathList(Lists.newArrayList(nPath));
        NGetFileName.Result fileNames = storageService.getFileNames(arg, ea);
        log.info("getTnFileName,fileNames={}",fileNames);
        if (fileNames != null && fileNames.getPathOriginNames() != null) {
            return fileNames.getPathOriginNames().get(nPath);
        } else {
            return null;
        }
    }

    public <E> void readExcel(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        EasyExcel.read(inputStream, arg.getType(), listener).sheet().doRead();
    }

    public <E> void readExcelBySheetName(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        EasyExcel.read(inputStream, arg.getType(), listener)
                .sheet(arg.getSheetName())
                .doRead();
    }
}
