package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 纷享通讯录安装记录
 * Created by <PERSON><PERSON><PERSON> on 2018/09/26
 */
@Data
@Table(name = "qyweixin_contact_bind")
public class QyweixinContactBindBo extends IdEntity implements Serializable {

    private String corpId;
    private String isvCorpId;
    private String corpName;
    private String fsEa;
    private Integer status; //0：不存在绑定关系（引导安装通讯录应用）1：该企业微信账号已绑定其它纷享账号 请纷享客服 2：首次进入绑定 3：已绑定
}


