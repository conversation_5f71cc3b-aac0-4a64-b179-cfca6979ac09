package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2019/2/15
 */
@Data
public class QyweixinOrderInfoRsp implements Serializable {
    private static final long serialVersionUID = -8715184338572483887L;

    private int errcode;
    private String errmsg;

    /**
     * 订单号
     */
    private String orderid;

    /**
     * 订单状态。0-未支付(可能也表示已关闭、未支付且已过期、申请退款中)，1-已支付，2-已关闭， 3-未支付且已过期， 4-申请退款中， 5-申请退款成功
     */
    private Integer order_status;

    /**
     * 订单类型。0-普通订单，1-扩容订单，2-续期，3-版本变更
     */
    private Integer order_type;

    /**
     * 客户企业的corpid
     */
    private String paid_corpid;

    /**
     *  下单操作者userid
     */
    private String operator_id;

    /**
     * 应用id
     */
    private String suiteid;

    /**
     * 应用id。（仅旧套件有该字段）
     */
    private Integer appid;

    /**
     * 购买版本ID
     */
    private String edition_id;

    /**
     *  购买版本名字
     */
    private String edition_name;

    /**
     *  实付款金额，单位分
     */
    private Long price;

    /**
     *  购买的人数
     */
    private Integer user_count;

    /**
     *  购买的时间，单位天
     */
    private Integer order_period;

    /**
     *  下单时间
     */
    private Long order_time;

    /**
     *  付款时间
     */
    private Long paid_time;
    /**
     * 购买有效时间开始
     */
    private Long begin_time;
    /**
     * 购买有效时间结束
     */
    private Long end_time;

    //下单来源。0-客户下单；1-服务商代下单；2-代理商代下单
    private Integer order_from;
    //下单方corpid
    private String operator_corpid;

    public Boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }

}
