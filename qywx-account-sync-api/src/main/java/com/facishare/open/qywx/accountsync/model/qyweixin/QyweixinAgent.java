package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * 授权的应用信息
 * Created by <PERSON><PERSON><PERSON> on 2018/07/11
 */
@Data
public class QyweixinAgent implements Serializable {

    private static final long serialVersionUID = 5806494774738743337L;
    //授权方应用id
    private int agentId;
    //授权方应用名字
    private String name;
    //授权方应用方形头像
    private String roundLogoUrl;
    //授权方应用圆形头像
    private String squareLogoUrl;

    //旧的多应用套件中的对应应用id，新开发者请忽略
    private int appId;

    //应用对应的权限(应用可见范围 部门、标签、成员)
    private QyweixinAgentPrivilege privilege;
}
