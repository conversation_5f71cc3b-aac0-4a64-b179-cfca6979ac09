package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinGroupChatDetail implements Serializable {
    private Integer errcode;
    private String errmsg;
    private GroupChat group_chat;
    @Data
    public static class GroupChat implements Serializable {
        private String chat_id;
        private String name;
        private String owner;
        private Long create_time;
        private String notice;
        private List<MemberList> member_list;
        private List<UserIdModel> admin_list;
        @Data
        public static class MemberList implements Serializable {
            private String userid;
            private Integer type;
            private Long join_time;
            private Integer join_scene;
            private Invitor invitor;
            private String group_nickname;
            private String name;
            private String avatar;//群没有这个字段，手动添加进去
            @Data
            public static class Invitor implements Serializable {
                private String userid;
            }
        }
        @Data
        public static class UserIdModel implements Serializable {
            private String userid;
        }
    }

    public boolean isSuccess(){
        return errcode==0;
    }

    /**
     * 参数	说明
     * errcode	返回码
     * errmsg	对返回码的文本描述内容
     * group_chat	客户群详情
     * group_chat.chat_id	客户群ID
     * group_chat.name	群名
     * group_chat.owner	群主ID
     * group_chat.create_time	群的创建时间
     * group_chat.notice	群公告
     * group_chat.member_list	群成员列表
     * group_chat.member_list.userid	群成员id
     * group_chat.member_list.type	成员类型。
     * 1 - 企业成员
     * 2 - 外部联系人
     * group_chat.member_list.unionid	外部联系人在微信开放平台的唯一身份标识（微信unionid），通过此字段企业可将外部联系人与公众号/小程序用户关联起来。仅当群成员类型是微信用户（包括企业成员未添加好友），且企业绑定了微信开发者ID有此字段（查看绑定方法）。第三方不可获取，上游企业不可获取下游企业客户的unionid字段
     * group_chat.member_list.join_time	入群时间
     * group_chat.member_list.join_scene	入群方式。
     * 1 - 由群成员邀请入群（直接邀请入群）
     * 2 - 由群成员邀请入群（通过邀请链接入群）
     * 3 - 通过扫描群二维码入群
     * group_chat.member_list.invitor	邀请者。目前仅当是由本企业内部成员邀请入群时会返回该值
     * group_chat.member_list.invitor.userid	邀请者的userid
     * group_chat.member_list.group_nickname	在群里的昵称
     * group_chat.member_list.name	名字。仅当 need_name = 1 时返回
     * 如果是微信用户，则返回其在微信中设置的名字
     * 如果是企业微信联系人，则返回其设置对外展示的别名或实名
     * group_chat.admin_list	群管理员列表
     * group_chat.admin_list.userid	群管理员userid
     */

    public static void main(String[] args) {
        String json = "{\"errcode\":0,\"errmsg\":\"ok\",\"group_chat\":{\"chat_id\":\"wrwx1mDAAAbxOoU2StvuCsRlfBt-KIww\",\"name\":\"牧原&上张食品城报货沟通群\",\"create_time\":1667133412,\"member_list\":[{\"userid\":\"wowx1mDAAA1ZHF4JJtsVNgvobqR_4X0w\",\"type\":1,\"join_time\":1676432700,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAA3rGrNvYmVVB7ew8_lBQz6g\"},\"group_nickname\":\"\",\"name\":\"杨大成\"},{\"userid\":\"wowx1mDAAAyfW1Tx6QsgK7pKfyDEYVeQ\",\"type\":1,\"join_time\":1667133673,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\"},\"group_nickname\":\"\",\"name\":\"刘俊涠\"},{\"userid\":\"wowx1mDAAAittcgLomeG_ZnNFpuo_mAQ\",\"type\":1,\"join_time\":1667213124,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\"},\"group_nickname\":\"\",\"name\":\"张雪盎\"},{\"userid\":\"wowx1mDAAA3rGrNvYmVVB7ew8_lBQz6g\",\"type\":1,\"join_time\":1667133662,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\"},\"group_nickname\":\"\",\"name\":\"贺肖洋\"},{\"userid\":\"wowx1mDAAA5tz8nhafJF51GRqikx3Dyw\",\"type\":1,\"join_time\":1667213124,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\"},\"group_nickname\":\"\",\"name\":\"刘振林\"},{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\",\"type\":1,\"join_time\":1667133412,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\"},\"group_nickname\":\"\",\"name\":\"魏彭\"},{\"userid\":\"wmwx1mDAAA7xg3rpz1UL-JxbKeHgrD0w\",\"type\":2,\"join_time\":1667520082,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"燕子\"},{\"userid\":\"wmwx1mDAAAG0rmf_ZRL4LBGZyhHnU8Jw\",\"type\":2,\"join_time\":1667520082,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"囍\"},{\"userid\":\"wmwx1mDAAAo2QcqZogO-PMCnGNqmjBUQ\",\"type\":2,\"join_time\":1667471213,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"晴天\"},{\"userid\":\"wmwx1mDAAA_juNRPchjZNrddohm4hA4A\",\"type\":2,\"join_time\":1668826741,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"李爱琴18239766392\",\"name\":\"铿锵玫瑰\"},{\"userid\":\"wmwx1mDAAAKoZz9Bx4SAA9itAWhz3A1A\",\"type\":2,\"join_time\":1667471199,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"开心快乐\"},{\"userid\":\"wmwx1mDAAAOE2Sva0bdjQ6sbY8Cj3DMw\",\"type\":2,\"join_time\":1668080759,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAA3rGrNvYmVVB7ew8_lBQz6g\"},\"group_nickname\":\"\",\"name\":\"荷\"},{\"userid\":\"wmwx1mDAAABcsQXQES4yZxufh_EUmaXg\",\"type\":2,\"join_time\":1667471206,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"静待花开\"},{\"userid\":\"wmwx1mDAAADt7Xwta-24fg9se6uUdyOQ\",\"type\":2,\"join_time\":1667471223,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"～云～\"},{\"userid\":\"wmwx1mDAAAlWGid1NRzjyBffIqVieAvg\",\"type\":2,\"join_time\":1667471182,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"张师傅18337511835\",\"name\":\"张民政18337511835\"},{\"userid\":\"wmwx1mDAAA5-fwyddSC4ceoMLqA9RcnA\",\"type\":2,\"join_time\":1677054774,\"join_scene\":1,\"group_nickname\":\"王乐15637510003\",\"name\":\"你最珍贵、\"},{\"userid\":\"wmwx1mDAAAIvwC-79EtiXHV0f3rVE-1A\",\"type\":2,\"join_time\":1667134908,\"join_scene\":3,\"state\":\"\",\"invitor\":{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\"},\"group_nickname\":\"\",\"name\":\"赵丽萍\"},{\"userid\":\"wmwx1mDAAAKRjo4kNbRnz7w4HC2BlRJg\",\"type\":2,\"join_time\":1669674856,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"随心，\"},{\"userid\":\"wmwx1mDAAAYluRRoJV6Mfny4ps-vNlOw\",\"type\":2,\"join_time\":1667471191,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"随缘\"},{\"userid\":\"wmwx1mDAAABLbRH2IfH6hMCl5tmD3K7Q\",\"type\":2,\"join_time\":1667471162,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"赵师傅\",\"name\":\"苹果\"},{\"userid\":\"wmwx1mDAAAyYJPaob_dRvd8BwigOugew\",\"type\":2,\"join_time\":1667471172,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"高师傅\",\"name\":\"高师傅\"},{\"userid\":\"wmwx1mDAAAQ4OuLS1kqb38LJ3W3XILhg\",\"type\":2,\"join_time\":1667470182,\"join_scene\":1,\"invitor\":{\"userid\":\"wowx1mDAAA3rGrNvYmVVB7ew8_lBQz6g\"},\"group_nickname\":\"德客惠采购~赵磊\",\"name\":\"Z L\"},{\"userid\":\"wmwx1mDAAAccoc1WTLrYLzMDQPtHkjLw\",\"type\":2,\"join_time\":1676602081,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"天宏梁晓燕\",\"name\":\"\uD83C\uDF53平安喜乐\uD83C\uDF88\"},{\"userid\":\"wmwx1mDAAATdvPPKUynp26cT7eAyHiog\",\"type\":2,\"join_time\":1667520082,\"join_scene\":1,\"state\":\"\",\"group_nickname\":\"\",\"name\":\"春来花\uD83C\uDF3B自开\"}],\"admin_list\":[{\"userid\":\"wowx1mDAAAyfW1Tx6QsgK7pKfyDEYVeQ\"},{\"userid\":\"wowx1mDAAAwi8tDmDI2rf1h8e9cuC2Cw\"}],\"owner\":\"wowx1mDAAA3rGrNvYmVVB7ew8_lBQz6g\"}}";
        QyweixinGroupChatDetail result = JSONObject.parseObject(json,QyweixinGroupChatDetail.class);
        System.out.println(result);
    }
}
