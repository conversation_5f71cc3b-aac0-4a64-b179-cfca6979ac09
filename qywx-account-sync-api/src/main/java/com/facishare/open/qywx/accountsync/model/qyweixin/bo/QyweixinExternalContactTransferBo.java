package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "qyweixin_external_contact_transfer")
public class QyweixinExternalContactTransferBo extends IdEntity implements Serializable {
    /**
     * 纷享ea
     */
    private String ea;
    /**
     * 外部联系人id
     */
    private String externalUserId;
    /**
     *外部联系人对象
     */
    private String externalApiName;
    /**
     *外部联系人企业昵称
     */
    private String externalNickname;
    /**
     *外部联系人名字
     */
    private String externalUserName;
    /**
     *原跟进成员的userid
     */
    private String handoverUserId;
    /**
     *原跟进成员的名称
     */
    private String handoverUserName;
    /**
     *原跟进成员的状态
     */
    private Integer handoverUserStatus;
    /**
     *原跟进人员的部门id
     */
    private String handoverDeptId;
    /**
     *原跟进人员的部门名称
     */
    private String handoverDeptName;
    /**
     *接替成员的userid
     */
    private String takeoverUserId;
    /**
     *接替成员姓名
     */
    private String takeoverUserName;
    /**
     *接替成员的部门id
     */
    private String takeoverDeptId;
    /**
     *接替成员的部门名称
     */
    private String takeoverDeptName;
    /**
     *同步状态
     */
    private Integer syncStatus;
    /**
     * 同步失败原因
     */
    private String errorMsg;
    /**
     *接替状态
     */
    private Integer transferResult;
    /**
     *接替时间
     */
    private Timestamp transferTime;
    /**
     *更新时间
     */
    private Timestamp updateTime;
    /**
     *创建时间
     */
    private Timestamp createTime;

}
