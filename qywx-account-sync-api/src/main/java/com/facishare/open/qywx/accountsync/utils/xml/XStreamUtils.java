package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.security.AnyTypePermission;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class XStreamUtils {

    private static final ConcurrentHashMap xStream = new ConcurrentHashMap<String, XStream>();

    /**
     * 初始化
     * @param objName
     * @return
     */
    private static XStream getXStream(Class<?> objName) {
        String key = objName.getName();
        if (xStream.get(key) == null)
            xStream.put(key, new XStream());
        return (XStream) xStream.get(key);
    }

    /**
     * 序列化
     * @param xmlHead
     * @param reqObjName
     * @return
     */
    public String toXML(String xmlHead,Class<?> reqObjName) {
        XStream xstream = getXStream(reqObjName);
        xstream.processAnnotations(reqObjName);
        //不显示class属性
        xstream.aliasSystemAttribute(null, "class");
        return xmlHead + xstream.toXML(reqObjName);
    }

    /**
     * 反序列化
     * @param clazz
     * @param xml
     * @param <T>
     * @return
     */
    public static <T> T parseXml(String xml, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        XStream xStream = getXStream(clazz);
        xStream.processAnnotations(clazz);
        xStream.setMode(XStream.NO_REFERENCES);
        xStream.ignoreUnknownElements();
        //尽量限制所需的最低权限
        xStream.addPermission(AnyTypePermission.ANY);
        @SuppressWarnings("unchecked")
        T t = (T) xStream.fromXML(xml);
        log.info("XStreamUtils.parseXml,totalTime= {} ms", System.currentTimeMillis() - startTime);
        return t;
    }

    /**
     * 清除集合数据
     */
    public static void cleanXStreamAllData() {
        xStream.clear();
    }

    /**
     * 获取集合数据
     */
    public static ConcurrentHashMap getXStreamAllData() {
        return xStream;
    }

}
