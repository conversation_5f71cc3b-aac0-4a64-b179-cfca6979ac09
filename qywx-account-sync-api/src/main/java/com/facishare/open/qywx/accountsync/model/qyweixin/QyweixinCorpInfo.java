package com.facishare.open.qywx.accountsync.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QYWXBindTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/11
 */
@Data
public class QyweixinCorpInfo implements Serializable {

    private static final long serialVersionUID = -2502697640935026558L;
    //授权方企业微信id
    private String corpId;
    //授权方企业微信名称
    private String corpName;
    //授权方企业微信类型，认证号：verified, 注册号：unverified
    private String corpType;
    //授权方企业微信方形头像
    private String corpSquareLogoUrl;
    //授权方企业微信用户规模
    private int corpUserMax;
    //授权方企业微信应用规模（目前微信只给了0）
    private int corpAgentMax;
    //所绑定的企业微信主体名称(仅认证过的企业有)
    private String corpFullName;
    //认证到期时间
    private String verifiedEndTime;
    //企业类型，1. 企业; 2. 政府以及事业单位; 3. 其他组织, 4.团队号
    private int subjectType;
    //授权企业在微工作台（原企业号）的二维码，可用于关注微工作台
    private String corpWxqrcode;
    //企业规模。当企业未设置该属性时，值为空。如：1-50人
    private String corpScale;
    //企业所属行业。当企业未设置该属性时，值为空。 如：IT服务
    private String corpIndustry;
    //企业所属子行业。当企业未设置该属性时，值为空。如：互联网和相关服务
    private String corpSubIndustry;
    //企业所在地信息, 为空时表示未知
    private String location;

    //授权方应用id
    private int appAgentId;
    //授权方应用名字
    private String appName;
    //授权方应用方形头像
    private String appRoundLogoUrl;
    //授权方应用圆形头像
    private String appSquareLogoUrl;

    //应用对应的权限(应用可见范围 部门、标签、成员)
    private QyweixinAgentPrivilege appPrivilege;

    //企业当前生效的版本信息
    private EditionInfo editionInfo;
    /**
     * 企业微信绑定类型
     */
    private QYWXBindTypeEnum bindTypeEnum;

}
