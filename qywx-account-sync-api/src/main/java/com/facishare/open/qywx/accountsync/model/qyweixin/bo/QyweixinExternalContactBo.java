package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "qyweixin_external_contact")
public class QyweixinExternalContactBo extends IdEntity implements Serializable {
    /**
     * 企业微信的企业ID
     */
    private String outEa;
    /**
     * 企业微信的用户ID
     */
    private String outUserId;
    /**
     * 代开发应用或自建应用的外部联系人用户ID
     */
    private String externalUserId;
    /**
     * 第三方应用的外部联系人用户ID
     */
    private String isvExternalUserId;
    /**
     * 外部联系人用户名称
     */
    private String externalName;
    /**
     * 外部联系人头像
     * http://wx.qlogo.cn/mmhead/r48cSSlr7jgOYC2UV3qL7mhB7sKib5L8QSniaLxiafGwUWBicMibuOGbm0g/0
     */
    private String avatar;
    private Timestamp createTime;
    private Timestamp updateTime;
}
