package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

import java.io.Serializable;

/**
 * 外部联系人事件模型
 */

@XStreamAlias("xml")
@Data
public class ExternalContactEventXml implements Serializable {
    @XStreamAlias("SuiteId")
    public String SuiteId;

    @XStreamAlias("InfoType")
    public String InfoType;

    @XStreamAlias("TimeStamp")
    public String TimeStamp;

    @XStreamAlias("ToUserName")
    public String ToUserName;

    @XStreamAlias("FromUserName")
    public String FromUserName;

    @XStreamAlias("MsgType")
    public String MsgType;

    @XStreamAlias("Event")
    public String Event;

    @XStreamAlias("AuthCorpId")
    public String AuthCorpId;

    @XStreamAlias("ChangeType")
    public String ChangeType;

    @XStreamAlias("UserID")
    private String UserID;

    @XStreamAlias("ExternalUserID")
    private String ExternalUserID;

    @XStreamAlias("WelcomeCode")
    private String WelcomeCode;

    @XStreamAlias("Source")
    private String Source;
}
