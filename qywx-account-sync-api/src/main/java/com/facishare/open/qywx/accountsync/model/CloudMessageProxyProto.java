package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import io.protostuff.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.rocketmq.common.message.Message;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CloudMessageProxyProto extends ProtoBase {
    @Tag(1)
    private String type;   //事件类型

    @Tag(2)
    private String corpId;   //企微账号id

    @Tag(3)
    private String fsEa;    //纷享账号id

    @Tag(4)
    private Message message;    //需要通过跨云投递的消息

    @Tag(5)
    private List<MessageSaveDoc> addConversionMessages;    //新增会话数据列表

    @Tag(6)
    private List<MessageSaveDoc> updateConversionMessageIds;    //更新会话数据列表

}


