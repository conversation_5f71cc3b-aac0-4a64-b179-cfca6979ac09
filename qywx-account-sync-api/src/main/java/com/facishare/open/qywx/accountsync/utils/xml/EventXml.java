package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

import java.io.Serializable;

/**
 * 企微事件消息模板
 * <AUTHOR>
 * @date 2024-01-16
 */
@XStreamAlias("xml")
@Data
public class EventXml implements Serializable {
    @XStreamAlias("ToUserName")
    public String ToUserName;//企业微信CorpID

    @XStreamAlias("FromUserName")
    public String FromUserName;//成员UserID

    @XStreamAlias("CreateTime")
    public String CreateTime;//消息创建时间（整型）

    @XStreamAlias("MsgType")
    public String MsgType;//消息类型，此时固定为：event

    @XStreamAlias("AgentID")
    public String AgentID;//企微应用 agentId

    @XStreamAlias("Event")
    public String Event;//事件类型

    @XStreamAlias("EventKey")
    public String EventKey;//事件key,可为空
}
