package com.facishare.open.qywx.accountsync.utils.xml;


import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

@XStreamAlias("xml")
@Data
public class ResetPermanentCodeXml {

    @XStreamAlias("SuiteId")
    private String SuiteId;

    @XStreamAlias("AuthCode")
    private String AuthCode;

    @XStreamAlias("InfoType")
    private String InfoType;

    @XStreamAlias("TimeStamp")
    private String TimeStamp;

    @Override
    public String toString() {
        return "ResetPermanentCodeXml{" +
                "SuiteId='" + SuiteId + '\'' +
                ", AuthCode='" + AuthCode + '\'' +
                ", InfoType='" + InfoType + '\'' +
                ", TimeStamp='" + TimeStamp + '\'' +
                '}';
    }

}
