package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/26
 */
@XStreamAlias("xml")
@Data
public class QyweixinPaySuccessMsgBaseXml implements Serializable {
    /**
     * 即appId。
     * e.g：wx88a141937dd6f838
     */
    //	服务商CorpID
    @XStreamAlias("ServiceCorpId")
    public String SuiteId;
    //license_pay_success
    @XStreamAlias("InfoType")
    public String InfoType;
    //客户企业CorpID
    @XStreamAlias("AuthCorpId")
    public String AuthCorpId;

    @XStreamAlias("OrderId")
    public String OrderId;

    @XStreamAlias("BuyerUserId")
    public String BuyerUserId;

    @XStreamAlias("TimeStamp")
    public Long TimeStamp;
}
