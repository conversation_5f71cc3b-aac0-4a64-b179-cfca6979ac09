package com.facishare.open.qywx.accountsync.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OAConnectorOpenDataModel implements Serializable {
    private String tenantId;
    private String ea;
    private Long createTime;
    private String channelId;
    private String dataTypeId;
    private String corpId;
    private String outUserId;
    private String appId;
    private String errorCode;
    private String errorMsg;
}
