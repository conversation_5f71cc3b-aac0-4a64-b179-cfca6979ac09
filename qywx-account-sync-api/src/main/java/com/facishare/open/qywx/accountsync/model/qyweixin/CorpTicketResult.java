package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/8/2 14:54 根据企业corpid区分跳转路由环境
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CorpTicketResult implements Serializable {
    private String corpId;
    private String ticket;

}
