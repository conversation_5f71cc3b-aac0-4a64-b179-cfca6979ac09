package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2019/2/15
 * 企业当前生效的版本信息
 */
@Data
public class EditionInfo implements Serializable {

    private static final long serialVersionUID = 9065534974147248747L;

    /**
     付费状态。
     0-没有付费;
     1-限时试用;
     2-试用过期;
     3-购买期内;
     4-购买过期;
     5-不限时试用;
     6-购买期内，但是人数超标, 注意，超标后还可以用7天;
     7-购买期内，但是人数超标, 且已经超标试用7天
     **/
    private Integer appStatus;

    /**
     * 购买版本ID
     */
    private String editionId;

    /**
     * 购买版本名字
     */
    private String editionName;

    /**
     * 用户上限。特别注意，如果是固定总价的购买，该参数固定为 **********， 含义为不限用户数
     */
    private Long userLimit;

    /**
     * 版本到期时间（根据购买版本，可能是试用到期时间或付费使用到期时间）。特别注意，**********代表不限时间
     */
    private Long expiredTime;


}
