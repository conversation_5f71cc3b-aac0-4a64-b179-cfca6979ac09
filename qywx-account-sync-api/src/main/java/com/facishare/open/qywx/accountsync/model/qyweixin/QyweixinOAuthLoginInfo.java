package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/12
 */
@Data
public class QyweixinOAuthLoginInfo implements Serializable {

    private static final long serialVersionUID = -1595800253624581964L;
    //用户所属企业的corpid
    private String corpId;
    //用户在企业内的UserID，如果该企业与第三方应用有授权关系时，返回明文UserId，否则返回密文UserId
    private String userId;
    //	手机设备号(由企业微信在安装时随机生成，删除重装会改变，升级不受影响)
    private String deviceId;


    //scope为snsapi_userinfo或snsapi_privateinfo，且用户在应用可见范围之内时返回下面的参数。
    //	成员姓名
    private String name;
    //	性别。0表示未定义，1表示男性，2表示女性
    private String gender;

    //用户同意snsapi_privateinfo授权时返回
    //	成员手机号
    private String mobile;
    //	成员邮箱
    private String email;
    //	头像url。注：如果要获取小图将url最后的”/0”改成”/100”即可
    private String avatar;
    //	员工个人二维码（扫描可添加为外部联系人）
    private String qrCode;
}
