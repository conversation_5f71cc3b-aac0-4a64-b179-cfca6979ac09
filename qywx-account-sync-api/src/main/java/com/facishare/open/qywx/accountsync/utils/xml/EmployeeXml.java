package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/26
 */

@XStreamAlias("xml")
@Data
public class EmployeeXml extends QyweixinMsgBaseXml {

    @XStreamAlias("UserID")
    private String UserID;

    @XStreamAlias("Name")
    private String Name;

    @XStreamAlias("Department")
    private String Department;

    /**
     * 1表示男性，2表示女性
     */
    @XStreamAlias("Gender")
    private String Gender;

    /**
     * 激活状态：1=激活或关注， 2=禁用， 4=未激活 已激活代表已激活企业微信或已关注微工作台（原企业号）。未激活代表既未激活企业微信又未关注微工作台（原企业号）
     */
    @XStreamAlias("Status")
    private String Status;

    @XStreamAlias("EnglishName")
    private String EnglishName;

    @Override
    public String toString() {
        return "Employee{" +
                "SuiteId='" + SuiteId + '\'' +
                ", UserID='" + UserID + '\'' +
                ", Name='" + Name + '\'' +
                ", Department='" + Department + '\'' +
                ", Gender='" + Gender + '\'' +
                ", Status='" + Status + '\'' +
                ", EnglishName='" + EnglishName + '\'' +
                ", TimeStamp='" + TimeStamp + '\'' +
                '}';
    }
}
