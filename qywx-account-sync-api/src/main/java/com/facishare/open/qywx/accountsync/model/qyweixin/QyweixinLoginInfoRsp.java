package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/11
 */
@Data
public class QyweixinLoginInfoRsp implements Serializable {

    private int errcode;
    private String errmsg;
    private Integer usertype;//	登录用户的类型：1.创建者 2.内部系统管理员 3.外部系统管理员 4.分级管理员 5.成员
    private QyweixinAuthUserInfoRsp user_info;//	登录用户的信息
    private CorpInfo corp_info;
    private List<Agent> agent; //该管理员在该提供商中能使用的应用列表，当登录用户为管理员时返回
    private AuthInfo auth_info; //该管理员拥有的通讯录权限，当登录用户为管理员时返回


    @Data
    public class CorpInfo implements Serializable{
        private String corpid;	//授权方企业id
    }

    @Data
    public class Agent implements Serializable{
        private int agentid;	//应用id
        private int auth_type;	//该管理员对应用的权限：1.管理权限，0.使用权限
    }

    @Data
    public class AuthInfo implements  Serializable{
        private List<departmentInfo> department;

        @Data
        public class  departmentInfo implements  Serializable{
            private String id;
            private String writable;
        }
    }
}
