package com.facishare.open.qywx.accountsync.service;

import com.facishare.open.qywx.accountsync.core.enums.TemplateTypeEnum;
import com.facishare.open.qywx.accountsync.excel.BuildExcelFile;
import com.facishare.open.qywx.accountsync.excel.ImportExcelFile;
import com.facishare.open.qywx.accountsync.result.Result;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;

public interface ExcelFileService {
    /**
     * 构建excel表格，并上传到临时文件，返回路径
     * 仅支持一次传输数据，单个sheet
     *
     * @param <R>
     * @param buildExcelArg
     * @return
     */
    <R> Result<BuildExcelFile.Result> buildExcelFile(BuildExcelFile.Arg buildExcelArg);
    Result<BuildExcelFile.Result> buildExcelTemplate(@NotNull String ea,
                                                     @NotNull TemplateTypeEnum templateType);

    /**
     * 导入excel文件
     * @param arg
     * @return
     * @throws IOException
     */
    Result<ImportExcelFile.Result> importExcelFile(@Valid ImportExcelFile.MappingDataArg arg,
                                                   String lang) throws IOException;
}
