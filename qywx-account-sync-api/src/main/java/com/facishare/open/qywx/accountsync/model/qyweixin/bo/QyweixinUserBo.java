package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "qyweixin_user")
public class QyweixinUserBo extends IdEntity implements Serializable {
    /**
     * 企业微信的企业ID
     */
    private String outEa;
    /**
     * 企业微信的用户ID
     */
    private String userId;
    /**
     * 企业微信的用户名称
     */
    private String userName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业。
     */
    private Integer status;
    private Timestamp createTime;
    private Timestamp updateTime;
}
