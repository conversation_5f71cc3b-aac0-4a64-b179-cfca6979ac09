package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2019/2/15
 */
@XStreamAlias("xml")
@Data
public class OrderXml {
    @XStreamAlias("SuiteId")
    private String SuiteId;

    @XStreamAlias("PaidCorpId")
    private String PaidCorpId;

    @XStreamAlias("TimeStamp")
    private String TimeStamp;

    @XStreamAlias("OrderId")
    private String OrderId;

    @XStreamAlias("OperatorId")
    private String OperatorId;

    @XStreamAlias("OldOrderId")
    private String OldOrderId;

    @XStreamAlias("NewOrderId")
    private String NewOrderId;

    @Override
    public String toString() {
        return "OrderXml{" +
                "SuiteId='" + SuiteId + '\'' +
                ", PaidCorpId='" + PaidCorpId + '\'' +
                ", TimeStamp='" + TimeStamp + '\'' +
                ", OrderId='" + OrderId + '\'' +
                ", OperatorId='" + OperatorId + '\'' +
                ", OldOrderId='" + OldOrderId + '\'' +
                ", NewOrderId='" + NewOrderId + '\'' +
                '}';
    }
}
