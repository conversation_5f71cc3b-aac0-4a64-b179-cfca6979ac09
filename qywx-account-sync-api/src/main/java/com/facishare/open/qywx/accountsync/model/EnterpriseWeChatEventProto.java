package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAgentPrivilege;
import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * 企业微信事件模型
 * <AUTHOR>
 * @date 2022/12/20
 */
@Data
public class EnterpriseWeChatEventProto extends ProtoBase {

    @Tag(1)
    private String signature;
    @Tag(2)
    private String timestamp;
    @Tag(3)
    private String nonce;
    @Tag(4)
    private String echoStr;
    @Tag(5)
    private String appId;
    @Tag(6)
    private String data;
}
