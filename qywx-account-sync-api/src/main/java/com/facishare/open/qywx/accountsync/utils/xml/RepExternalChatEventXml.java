package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

import java.io.Serializable;

/**
 * 外部联系人事件模型
 */

@XStreamAlias("xml")
@Data
public class RepExternalChatEventXml implements Serializable {
    @XStreamAlias("ToUserName")
    public String ToUserName;//企业微信CorpID

    @XStreamAlias("FromUserName")
    public String FromUserName;//成员UserID

    @XStreamAlias("CreateTime")
    public String CreateTime;

    @XStreamAlias("MsgType")
    public String MsgType;

    @XStreamAlias("Event")
    public String Event;

    @XStreamAlias("ChangeType")
    public String ChangeType;

    @XStreamAlias("ChatId")
    public String ChatId;

    @XStreamAlias("UpdateDetail")
    public String UpdateDetail;

    @XStreamAlias("JoinScene")
    public String JoinScene;

    @XStreamAlias("QuitScene")
    public String QuitScene;

    @XStreamAlias("MemChangeCnt")
    public String MemChangeCnt;
}
