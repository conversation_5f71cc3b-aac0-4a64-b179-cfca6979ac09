package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2018/7/27.
 */
@Data
public class QyweixinFollowUser implements Serializable {

    private static final long serialVersionUID = 8263231023842232874L;
    private String userid;//":"rocky",F
    private String remark;//":"李部长",
    private String description;//":"对接采购事物",
    private Long createtime;//":**********
    private String state;//企业自定义的state参数，用于区分客户具体是通过哪个「联系我」添加，由企业通过创建「联系我」方式指定
    private String oper_userid;//发起添加的userid，如果成员主动添加，为成员的userid；如果是客户主动添加，则为客户的外部联系人userid；如果是内部成员共享/管理员分配，则为对应的成员/管理员userid
    private String remark_corp_name; // "腾讯科技"
    private List<String> remark_mobiles; //["13800000001","13000000002"]
    private Integer add_way; //该成员添加此客户的来源，具体含义详见https://developer.work.weixin.qq.com/document/path/92265#%E6%9D%A5%E6%BA%90%E5%AE%9A%E4%B9%89
}
