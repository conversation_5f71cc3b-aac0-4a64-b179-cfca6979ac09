package com.facishare.open.qywx.accountsync.model.qyweixin.ckBo;

import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/09/27
 */
@Data
@Table(name = "biz_log_oaconnectoropendata_dist")
public class BizLogOaconnectoropendataDistBo implements Serializable {
    private String appName;
    private String traceId;
    private String tenantId;
    private String ea; // 纷享租户ea，可以不填
    private long createTime;
    private String serverIp;
    private String channelId;
    private String dataTypeId;
    private String corpId;
    private String appId;
    private String outUserId;
    private String errorCode;
    private String errorMsg;
}
