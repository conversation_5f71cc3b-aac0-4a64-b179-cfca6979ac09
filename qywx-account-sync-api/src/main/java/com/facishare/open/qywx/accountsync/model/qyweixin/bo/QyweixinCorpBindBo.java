package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.facishare.open.qywx.accountsync.annotation.SecurityField;
import com.facishare.open.qywx.accountsync.annotation.SecurityObj;
import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/20
 */
@Data
@SecurityObj
@Table(name = "qyweixin_corp_bind")
public class QyweixinCorpBindBo extends IdEntity implements Serializable {

    private String corpId;

    /**
     * 专门用于代开发转换的第三方corpId
     */
    private String isvCorpId;

    private String corpName;

    private String appId;

    /**
     * 每次应用授权，会产生新的永久授权码（通过 corpId + suiteId 定位）
     */
    @SecurityField
    private String permanentCode;

    /**
     * 企业应用授权时分配的agentId (重新授权会更新)
     */
    private String agentId;

    private Integer status; //0-正常，1-停用
}
