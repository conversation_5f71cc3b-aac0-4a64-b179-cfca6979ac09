package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CheckSingleAgreeResult implements Serializable {
    private Integer errCode;
    private String errMsg;
    private List<AgreeInfo> agreeInfo;//同意情况

    @Data
    public static class AgreeInfo implements Serializable {
        private String agreeStatus;//同意:"Agree"，不同意:"Disagree"
        private Long statusChangeTime;//同意状态改变的具体时间，utc时间，单位秒
        private String userId;//用户的userid
        private String exteranalOpenId;//外部联系人的externalopenid
    }
}