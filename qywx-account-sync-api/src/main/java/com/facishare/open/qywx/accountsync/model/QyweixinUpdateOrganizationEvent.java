package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * Created by fengyh on 2018/3/6.
 */
@Data
public class QyweixinUpdateOrganizationEvent extends ProtoBase {

    /**
     * 接收通讯录变更和 人员的应用权限变化事件。 *** */

    @Tag(1)
    String  fsEa;      //纷享企业账号
    @Tag(2)
    String appId;      //应用id

    @Tag(3)
    //企业id
    private String corpId;
    /**
     * 根据eventtype, 这里的eventid含义不同。
     *
     * eventtype=employee_modify，  表示指定人员的账号信息修改变化，eventid是用户  userId（如修改了userid则为新的userid）
     * eventtype=employee，  表示指定人员的账号信息新增，eventid是用户 userId
     * eventtype=employee_delete, 表示删除人员账号
     * eventtype=tag,        表示指定标签的信息由变化，eventId是tagId 成员变更查看addAllowParty，reduceAllowParty，addAllowUser，reduceAllowUser
     * eventtype=department, 表示指定部门的信息由变化，eventid是departmentId.
     * eventtype=department_delete, 表示删除部门
     * eventtype=app_privilege 表示应用可见范围变更， 查看addAllowParty，reduceAllowParty，addAllowUser，reduceAllowUser，addAllowTag，reduceAllowTag
     * */
    @Tag(4)
    String  eventType;
    @Tag(5)
    String  eventId;
    @Tag(6)
    String  source;

    @Tag(7)
    //标签新增可见范围（部门）
    private List<String> addAllowParty;
    @Tag(8)
    //标签移除可见范围（部门）
    private List<String> reduceAllowParty;

    @Tag(9)
    //标签可见范围（员工）
    private List<String> addAllowUser;
    @Tag(10)
    //标签移除可见范围（员工）
    private List<String> reduceAllowUser;

    @Tag(11)
    //应用可见范围（部门）
    private List<String> allowParty;

    @Tag(12)
    //应用可见范围（员工）
    private List<String> allowUser;

    @Tag(13)
    //应用可见范围（标签）
    private List<String> allowTag;

    //fs_account 用于企业微信userId修改时通过修改绑定关系后查询发送mq
    @Tag(14)
    String  fsAccount;

}
