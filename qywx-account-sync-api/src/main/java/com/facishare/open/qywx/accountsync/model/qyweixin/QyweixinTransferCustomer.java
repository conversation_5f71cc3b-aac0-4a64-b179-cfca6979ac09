package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QyweixinTransferCustomer implements Serializable {
    private String handoverUserId;//原跟进成员的userid
    private String takeoverUserId;//接替成员的userid
    private List<String> externalUserId;//客户的external_userid列表，每次最多分配100个客户
    private String transferSuccessMsg;//转移成功后发给客户的消息，最多200个字符，不填则使用默认文案
}
