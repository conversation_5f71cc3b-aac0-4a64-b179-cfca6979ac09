package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

@Data
public class QyweixinUnassignedExternalContactInfo implements Serializable {
    private String ea;
    private String outEa;//默认为空，1个CRM对多个企微必传
    private Integer pageId = -1;//分页查询，要查询页号，从0开始
    private String cursor;//分页查询游标，字符串类型，适用于数据量较大的情况，如果使用该参数则无需填写page_id，该参数由上一次调用返回
    private Integer pageSize;//每次返回的最大记录数，默认为1000，最大值为1000
}
