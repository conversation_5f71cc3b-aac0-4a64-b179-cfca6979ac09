package com.facishare.open.qywx.accountsync.utils.xml;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.thoughtworks.xstream.XStream;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

/**
 * Author: Ansel <PERSON>ao
 * Create Time: 15/9/22
 */
//TODO 后面研究下Converter把xml的转化做的更简单一些
@Slf4j
public class XmlParser {

    @SuppressWarnings("unchecked")
    public static <T> T fromXml(String strXml, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        XStream xStream = new XStream();
        xStream.processAnnotations(clazz);
        xStream.setMode(XStream.NO_REFERENCES);
        xStream.ignoreUnknownElements();
        T xml = (T)xStream.fromXML(strXml);
        log.info("XmlParser.fromXml,totalTime={}", System.currentTimeMillis() - startTime);
        return xml;
    }

    public static <T> String toXml(T bean) {
        XStream xStream = new XStream();
        xStream.processAnnotations(bean.getClass());
        return xStream.toXML(bean);
    }

    public static boolean containTag(String xmltext, String tagName)       {
        Object[] result = new Object[3];
        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            DocumentBuilder db = dbf.newDocumentBuilder();
            StringReader sr = new StringReader(xmltext);
            InputSource is = new InputSource(sr);
            Document document = db.parse(is);

            Element root = document.getDocumentElement();
            NodeList nodelist = root.getElementsByTagName(tagName);
            if(null == nodelist || nodelist.getLength() == 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static void main(String[] args) {
        String plainMsg = "<xml><SuiteId><![CDATA[wx4c7edab730f4fdc9]]></SuiteId><AuthCorpId><![CDATA[ww4ba39487c1f49492]]></AuthCorpId><InfoType><![CDATA[change_contact]]></InfoType><TimeStamp>1532674596</TimeStamp><ChangeType><![CDATA[update_tag]]></ChangeType><TagId>1</TagId><DelUserItems><![CDATA[LiuWei3]]></DelUserItems></xml>";
        ContactsXml contactsInfo = XmlParser.fromXml(plainMsg, ContactsXml.class);
        System.out.println(JSONObject.toJSONString(contactsInfo));
    }
}
