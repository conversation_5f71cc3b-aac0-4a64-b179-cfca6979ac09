package com.facishare.open.qywx.accountsync.service;

import com.facishare.open.qywx.accountsync.arg.FileUploadArg;
import com.facishare.open.qywx.accountsync.model.FileUploadModel;
import com.facishare.open.qywx.accountsync.result.Result;

import java.util.List;

public interface FileUploadService {
    /**
     * 上传文件
     * @param arg
     * @return
     */
    Result<Void> upload(FileUploadArg arg);

    /**
     * 查询文件上传状态
     * @param npath
     * @return
     */
    Result<FileUploadModel> query(String fsEa, String npath, String url);
}
