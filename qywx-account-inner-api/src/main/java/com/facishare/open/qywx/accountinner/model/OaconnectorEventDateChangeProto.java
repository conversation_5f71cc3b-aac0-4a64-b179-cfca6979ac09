package com.facishare.open.qywx.accountinner.model;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OaconnectorEventDateChangeProto extends ProtoBase {
    @Tag(1)
    private String appId;            //应用id
    @Tag(2)
    private String appType;          //应用类型
    @Tag(3)
    private String eventType;        //事件类型
    @Tag(4)
    private String type;             //子类型
    @Tag(5)
    private String outEa;            //外部企业id
    @Tag(6)
    private String fsEa;             //纷享ea
    @Tag(7)
    private String fsEi;             //纷享ei
    @Tag(8)
    private String domain;           //域名
    @Tag(9)
    private String content;          //事件内容
}
