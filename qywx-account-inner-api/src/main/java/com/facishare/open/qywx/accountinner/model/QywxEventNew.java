package com.facishare.open.qywx.accountinner.model;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 开通企业事件
 * Created by fengyh on 2018/5/24.
 */
@Data
@Table(name = "enterprise_new_event")
public class QywxEventNew extends IdEntity implements Serializable  {
    /**两点提示：
     * 1.authorized一定要用包装类，不能用int. 因为非null的成员查询时都会作为查询条件。
     * 2.db中表的id不用再重新定义了，在基类IdEntity中已经定义了。
     * */

    @Column(name = "source")
    private String source;
    @Column(name = "out_ea_account")
    private String outEaAccount;
    @Column(name = "event_pb_data")
    private String eventPbData;
    @Column(name = "authorized")
    private Integer authorized; //0-没有收到授权通知， 1-已经收到授权通知
}
