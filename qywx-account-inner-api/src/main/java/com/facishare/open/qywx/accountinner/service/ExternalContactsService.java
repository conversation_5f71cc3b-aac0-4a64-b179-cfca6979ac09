package com.facishare.open.qywx.accountinner.service;

import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountsync.result.Result;

import java.util.List;

/**
 * 企业微信外部联系人服务类
 * <AUTHOR>
 * @date ********
 */
public interface ExternalContactsService {
    @Deprecated
    Result<QyweixinExternalContactRsp> getDetail(String fsEa,String externalUserId);
    Result<QyweixinExternalContactRsp> getDetail2(String fsEa,String externalUserId, String outEa);

    @Deprecated
    Result<List<String>> getExternalContactList(String fsEa, String fsUserId);
    Result<List<String>> getExternalContactListEx(String fsEa, String fsUserId, String outEa);

    @Deprecated
    Result<List<String>> getExternalContactList2(String fsEa, String outUserId);
    Result<List<String>> getExternalContactList21(String fsEa, String outUserId, String outEa);
}
