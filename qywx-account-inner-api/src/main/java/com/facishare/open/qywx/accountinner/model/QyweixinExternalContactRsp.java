package com.facishare.open.qywx.accountinner.model;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinFollowUser;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2018/7/26.
 */
@Data
public class QyweixinExternalContactRsp implements Serializable {
    private int errcode;
    private String errmsg;
    private QyweixinExternalContact external_contact;
    private List<QyweixinFollowUser> follow_user;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
