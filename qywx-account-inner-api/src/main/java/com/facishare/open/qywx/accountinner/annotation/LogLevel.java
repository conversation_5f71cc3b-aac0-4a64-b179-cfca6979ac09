package com.facishare.open.qywx.accountinner.annotation;


import com.facishare.open.qywx.accountinner.enums.LogLevelEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogLevel {
    LogLevelEnum value() default LogLevelEnum.DEBUG;
}
