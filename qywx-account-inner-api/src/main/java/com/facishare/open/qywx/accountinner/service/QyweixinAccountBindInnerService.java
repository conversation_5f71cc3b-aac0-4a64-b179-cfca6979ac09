package com.facishare.open.qywx.accountinner.service;


import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountinner.model.AccountSyncConfigModel;
import com.facishare.open.qywx.accountsync.model.EnterpriseModel;
import com.facishare.open.qywx.accountsync.result.Result;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by l<PERSON><PERSON> on 2018/7/18.
 *
 * 内部接口，不对外暴露。
 */
public interface QyweixinAccountBindInnerService {

    Result<String> outAccountToFsAccount(String sourceType, String corpId, String userId,String fsEa);

    Result<List<QyweixinAccountEmployeeMapping>> getEmployeeMapping(String corpId, String userId,int status,String fsEa);

    Result<List<QyweixinAccountEmployeeMapping>> outAccountToFsAccount(String sourceType, String corpId, List<String> fsAccountList);

    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBind(String sourceType, String fsEa, List<Integer> fsDepartmentIds);

    int deleteAccountBind(String fsAdminAccount,List<String> fsAccountList,String appId, String outEa);

    @Deprecated
    Result<List<QyweixinAccountEmployeeMapping>> queryAccountBind(List<String> fsAccountList,String appId);
    Result<List<QyweixinAccountEmployeeMapping>> queryAccountBind2(List<String> fsAccountList,String appId, String outEa);

    int updateQyweixinAccountEmployee(QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping);

    int updateQyweixinAccountEmployee(QyweixinAccountEmployeeMapping qyweixinAccountEmployeeMapping, String qwOldUserId);

    @Deprecated
    List<QyweixinAccountEmployeeMapping> queryAccountBindByFsEa(String fsEa, String appId);
    List<QyweixinAccountEmployeeMapping> queryAccountBindByFsEa2(String fsEa, String appId, String outEa);

    List<QyweixinAccountEmployeeMapping> queryAccountBindByOutEa(String outEa, String appId,int status);

    @Deprecated
    List<String> queryIsvAccountBindByFsEaOnlyOutAccount(String fsEa, String appId);
    List<String> queryIsvAccountBindByFsEaOnlyOutAccount2(String fsEa, String appId, String outEa);

    int countAccountBind(String fsEa, String appId);

    List<QyweixinAccountEnterpriseMapping> queryEnterpriseBindByOutEa(String corpId);

    List<QyweixinAccountEnterpriseMapping> queryEnterpriseAllBindByOutEa(String corpId);

    Result<QyweixinAccountEnterpriseMapping> queryCorpBindByOutEa(String corpId,String depId);

    int deleteQYWXBindByOutEa(List<String> outEaList, String appId);

    Result<Void> deleteQYWXAccountBind(String fsEa, String appId, String corpId);

    @Deprecated
    Result<QyweixinAccountEnterpriseMapping> getEnterpriseMapping(String fs_ea);
    Result<QyweixinAccountEnterpriseMapping> getEnterpriseMapping2(String fsEa, String outEa);
    @Deprecated
    Result updateAccountSyncConfig(String fs_ea,List<AccountSyncConfigModel> modelList);
    Result updateAccountSyncConfig2(String fs_ea,List<AccountSyncConfigModel> modelList, String outEa);

    /**
     * 获取和企业微信绑定的CRM企业EA列表
     * @param outEa
     * @param outUserId
     * @return
     */
    Result<List<EnterpriseModel>> getFsEaList(String outEa,String outUserId);

    /**
     * 通过企微corpId获取和企业微信绑定的CRM企业EA列表
     * @param outEa
     * @return
     */
    Result<List<String>> getFsEaList(String outEa);

    /**
     * 更新扩展字段
     * @param fsEa
     * @return
     */
    @Deprecated
    Result<Integer> updateExtend(String fsEa, String extend);
    Result<Integer> updateExtend2(String fsEa, String extend, String outEa);

    @Deprecated
    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindV2(String sourceType, String fsEa, String appId, Integer status, List<Integer> fsDepartmentIds);
    Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindV21(String sourceType,
                                                                          String fsEa,
                                                                          String appId,
                                                                          Integer status,
                                                                          List<Integer> fsDepartmentIds,
                                                                          String outEa);

    Result<Integer> updateEnterpriseOutInfo(int id, String outEa, String outDepId);

    Result<Integer> updateEnterpriseDomain(String fsEa, String domain);

    Result<List<QyweixinAccountEnterpriseMapping>> queryEnterpriseMappingListFromFsEa(String source,
                                                                                      String fsEa,
                                                                                      Integer status);

    Result<List<QyweixinAccountEmployeeMapping>> queryEmployeeMappingListFromFsUserId(String source,
                                                                                      String fsEa,
                                                                                      String fsUserId);
}
