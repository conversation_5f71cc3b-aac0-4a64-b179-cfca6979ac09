package com.facishare.open.qywx.accountinner.model;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountinner.enums.AccountSyncTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
public class AccountSyncConfigModel implements Serializable {
    private AccountSyncTypeEnum syncType; //客户同步类型
    private boolean autoSync; //是否开启自动同步
    private boolean allowSyncIfNoPhoneNumber;//企微客户无手机号不允许同步
    private CheckDuplicateSetting setting;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CheckDuplicateSetting implements Serializable {
        private boolean byPhone; //通过手机号查重
        private boolean byMembership; //通过团队成员查重
        private boolean byOrganization;//通过多组织查重
    }

    public static void main(String[] args) {
        AccountSyncConfigModel model = new AccountSyncConfigModel();
        model.setSyncType(AccountSyncTypeEnum.SYNC_TO_ACCOUNT);
        model.setAutoSync(true);
        model.setAllowSyncIfNoPhoneNumber(true);
        model.setting = new CheckDuplicateSetting();
        model.setting.setByPhone(true);
        model.setting.setByMembership(true);

        String json = JSONObject.toJSONString(model);
        System.out.println(json);
    }
}