package com.facishare.open.qywx.accountinner.model.qywx;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
public class EmployeeBindFile implements Serializable {
    /**
     * 纷享EA
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s106",index = 0)
    private String fsEa;
    /**
     * 企微企业ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s107",index = 1)
    private String outEa;
    /**
     * 纷享员工ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s113",index = 2)
    private Integer fsUserId;
    /**
     * 纷享员工名称
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s114",index = 3)
    private String fsUserName;
    /**
     * 企微员工ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s115",index = 4)
    private String outUserId;
    /**
     * 企微员工名称
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s116",index = 5)
    private String outUserName;
    /**
     * 纷享部门ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s108",index = 6)
    private Integer fsDeptId;
    /**
     * 纷享部门名称
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s109",index = 7)
    private String fsDeptName;
    /**
     * 纷享员工状态
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s200",index = 8)
    private String fsUserStatus;
    /**
     * 绑定状态
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s201",index = 9)
    private String bindStatus;
    /**
     * 绑定时间
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s202",index = 10)
    private Date gmtCreate;
}
