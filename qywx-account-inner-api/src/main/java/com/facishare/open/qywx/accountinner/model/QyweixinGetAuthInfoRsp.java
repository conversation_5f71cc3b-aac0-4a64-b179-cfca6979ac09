package com.facishare.open.qywx.accountinner.model;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAuthCorpInfoRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAuthInfoRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinEditionInfoRsp;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/19.
 */
@Data
public class QyweixinGetAuthInfoRsp implements Serializable {
    private int errcode;
    private String errmsg;
    QyweixinAuthCorpInfoRsp auth_corp_info;
    QyweixinAuthInfoRsp auth_info;

    QyweixinEditionInfoRsp edition_info;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
