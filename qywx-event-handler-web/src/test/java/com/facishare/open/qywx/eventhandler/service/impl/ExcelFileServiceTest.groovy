package com.facishare.open.qywx.eventhandler.service.impl

import com.facishare.open.qywx.accountbind.utils.TraceUtil
import com.facishare.open.qywx.accountsync.core.enums.TemplateTypeEnum
import com.facishare.open.qywx.accountsync.excel.BuildExcelFile
import com.facishare.open.qywx.accountsync.excel.FileManager
import com.facishare.open.qywx.accountsync.excel.ImportExcelFile
import com.facishare.open.qywx.accountsync.excel.vo.DepartmentMappingVo
import com.facishare.open.qywx.accountsync.excel.vo.EmployeeMappingVo
import com.facishare.open.qywx.accountsync.service.ExcelFileService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class ExcelFileServiceTest extends Specification {
    @Autowired
    private ExcelFileService excelFileService
    @Autowired
    private FileManager fileManager

    def "buildExcelFile"() {
        given:
        def map = new HashMap()
        map.put("department",Arrays.asList(DepartmentMappingVo.getTempData()))
        map.put("employee",Arrays.asList(EmployeeMappingVo.getTempData()))

        def arg = new BuildExcelFile.Arg()
        arg.setEa("81243")
        arg.setSheetDataList(map)
        arg.setTemplateType(TemplateTypeEnum.EMPLOYEE_BIND)
        expect:
        def excelFile = excelFileService.buildExcelFile(arg)
        println(excelFile)
    }

    def "buildExcelTemplate"() {
        given:
        expect:
        def excelFile = excelFileService.buildExcelTemplate("81243",TemplateTypeEnum.DEPARTMENT_EMPLOYEE_BIND)
        println(excelFile)
    }

    def "importExcelFile"() {
        given:
        def inputStream = new FileInputStream(new File("C:\\Users\\<USER>\\Downloads\\Enterprise WeChat Staff and Department Binding Table.xlsx"))
        byte[] bytes = new byte[inputStream.available()]
        inputStream.read(bytes)
        def tnPath = fileManager.uploadTnFile("81243", 1000, bytes)
        def arg = new ImportExcelFile.MappingDataArg()
        arg.setFsEa("81243")
        arg.setOutEa("wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w")
        arg.setFsUserId(1069)
        arg.setTemplateType(TemplateTypeEnum.DEPARTMENT_EMPLOYEE_BIND)
        arg.setNpath(tnPath)
        TraceUtil.setLocale("en")
        expect:
        def result = excelFileService.importExcelFile(arg)
        println(result)
    }
}
