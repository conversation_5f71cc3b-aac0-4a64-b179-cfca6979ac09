package com.facishare.open.qywx.eventhandler.controller

import com.facishare.open.qywx.eventhandler.controller.outer.ExternalContactsController
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class ExternalContactsControllerTest extends Specification {
    @Autowired
    private ExternalContactsController externalContactsController;

    def "detail"() {
        expect:
        def result = externalContactsController.detail("wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ");
        println result;
    }
}
