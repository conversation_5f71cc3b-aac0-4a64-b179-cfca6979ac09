package com.facishare.open.qywx.eventhandler.controller

import com.facishare.open.qywx.eventhandler.controller.outer.ControllerQYWeixinContactBind
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class ControllerQYWeixinContactBindTest extends Specification {
    @Autowired
    private ControllerQYWeixinContactBind controllerQYWeixinContactBind;

//    def "importEmployeeBind"() {
//        expect:
//        def result = controllerQYWeixinContactBind.importEmployeeBind();
//        println result;
//    }
}
