package com.facishare.open.qywx.eventhandler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountinner.arg.HttpArg;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.eventhandler.arg.QueryConnectInfoArg;
import com.facishare.open.qywx.eventhandler.controller.outer.ControllerQYWeixinContactBind;
import com.facishare.open.qywx.eventhandler.controller.outer.EnterpriseWeChatEventController;
import com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.eventhandler.utils.HttpProxyUtil;
import com.facishare.open.qywx.eventhandler.utils.SecurityUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class EnterpriseWeChatEventControllerTest extends BaseTest{
    @Autowired
    private EnterpriseWeChatEventController enterpriseWeChatEventController;
    @Autowired
    private ControllerQYWeixinContactBind controllerQYWeixinContactBind;
    @Autowired
    private AutoConfRocketMQProducer enterpriseWechatEventMQSender;
    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;
    @Autowired
    private HttpProxyUtil httpProxyUtil;

    @Test
    public void recvCmdEventTest() {
        sendMQ("test", "test", "tset", "tset", "tset", "test");
    }

    private void sendMQ(String tag, String msgSignature, String timeStamp, String nonce, String echostr, String postData) {
        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);

        log.info("EnterpriseWeChatEventController.sendMQ,eventProto={}", JSONObject.toJSONString(eventProto));

        Message msg = new Message();
        msg.setTags(tag);
        msg.setBody(eventProto.toProto());
        SendResult sendResult = enterpriseWechatEventMQSender.send(msg);
        log.info("EnterpriseWeChatEventController.sendMQ,sendResult={},eventProto={}", sendResult, JSONObject.toJSONString(eventProto));
    }

    @Test
    public void sendEventByDubbo() {

        String data = SecurityUtil.encryptStr("V1_"+"data");
        Pattern pattern = Pattern.compile("V1_"+"(.*)");
        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
        if (matcher.find()) {
            System.out.println(matcher.group(1));
        }

        enterpriseWeChatEventController.sendEventByDubbo(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD,
                "d969601511e2725ce93e1c09206c535d607ef7f9",
                "1677240442",
                "1676807632",
                null,
                "<xml><ToUserName><![CDATA[wx88a141937dd6f838]]></ToUserName><Encrypt><![CDATA[iG27QQSWMUKgNVPGaFmVDSb8iCukpPQgaY4eGQEtC3uxW2rs7Q/DhgLobgX4eFVZsaInQR51KYmVN0xpnp7MR6CxuBA6Bs+MnhpaAgZNlf5cyaEw0qUOkwThyVuKpzUvH920OGbYEvv7TBUNG3L0IIK6r7n7eif8eu5l4s5JlLA+PmcrXHFML/3vqJWtje/CHpV2phVgcn7qZPplfPNAj2dVOZIS/UiKLOCZJxHln7JToiKtmsSHCXCOdtSC+ssynY1Sba9MBHPgb7rsmfpmP/qDj1xxM1rDahUBBBc3J7Byq3upxOnwrp11EPPpAYMnq79VSsYEK7lI7thzal4viw==]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>",
                null);


    }

    @Test
    public void recvCmdEvent2() {
        String msgSignature = "4a1f39ffea5af6915afece2e4beb0c635bb5907d";
        String timeStamp = "1721702889";
        String nonce = "1721503456";
        String echoStr = "GBSs5Vb%2BH2X5ssd29tsMAOuC5JTQvY29AyQI9r8ytJX0GSUmXdfzFBvFfx%2FkrndRsXkuutxxoHw36ie8thC5kA%3D%3D";
        echoStr = URLDecoder.decode(echoStr);
        String appId = "wx88a141937dd6f838";
        String result = enterpriseWeChatEventController.recvCmdEvent2(msgSignature, timeStamp, nonce, echoStr, appId, null);
        System.out.println(result);
    }

    @Test
    public void queryBind() {
        Map<String, String> headers = new HashMap<>();
        headers.put("sec-fetch-mode", "cors");
        headers.put("referer", "https://crm.ceshi112.com/XV/UI/manage");
        headers.put("sec-fetch-site", "same-origin");
        headers.put("x-forwarded-proto", "https");
        headers.put("accept-language", "zh-CN,zh-TW;0.9,en;0.8");
        headers.put("cookie", "guid=9064cf49-0083-417f-b84f-939cea63baa0; fsRoutes=\"DS9ilc1shN7GMofpQTm5pOKnRNmP6Fjr/drI02Pwkqc=\"; lang=zh-CN; fs_token=DJauDM5YOpSjOcLcCIqqCpbYBJXYCp4jPZ1YC65XEJKqE35b; FSAuthX=0G60kBXJ0mC0003SQRobqUvswpdAy9j0EyJ71iNmbeqC5ZaKcgIMy9zctyu1zzhWAlyjxwDV7TVtAVzQvYC1ipQPSgpJ086PFnzJI2PQX6r9Qal888zOtyGaoTBtYncNMyPRrdrkMtNQBHzcdsaguIzNpjBGW53yTL425lPzAh5bldyT1dNqC6yKqThOeDfk3YtzKOm9mV8hW34mr7Q7WlSOV7fv4eMkDuG7hzun1DyZcuCsGj8DVtyNo3zj72; FSAuthXC=0G60kBXJ0mC0003SQRobqUvswpdAy9j0EyJ71iNmbeqC5ZaKcgIMy9zctyu1zzhWAlyjxwDV7TVtAVzQvYC1ipQPSgpJ086PFnzJI2PQX6r9Qal888zOtyGaoTBtYncNMyPRrdrkMtNQBHzcdsaguIzNpjBGW53yTL425lPzAh5bldyT1dNqC6yKqThOeDfk3YtzKOm9mV8hW34mr7Q7WlSOV7fv4eMkDuG7hzun1DyZcuCsGj8DVtyNo3zj72; JSESSIONID=F7D1574B84445CFDE9EBE04A8974061D");
        headers.put("x-forwarded-for", "*************, ************, ***********");
        headers.put("accept", "*/*");
        headers.put("x-real-ip", "*************");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"107\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("x-trace-id", "dds7101_1000_1735634927359:740");
        headers.put("sec-ch-ua-platform", "\"Windows\"");
        headers.put("x-requested-with", "XMLHttpRequest");
        headers.put("user-agent", "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.110 Safari/537.36 Language/zh ColorScheme/Light wxwork/4.1.31 (MicroMessenger/6.2) WindowsWechat MailPlugin_Electron WeMail embeddisk wwmver/3.26.14.632");
        headers.put("sec-fetch-dest", "empty");
        HttpArg arg = new HttpArg();
        arg.setMethod("GET");
        arg.setHeader(headers);
        arg.setUrl("http://************:21075/open/qyweixin/translate/jobId?_fs_token=DJauDM5YOpSjOcLcCIqqCpbYBJXYCp4jPZ1YC65XEJKqE35b&traceId=O-E.dds7101.1000-15864276");
        arg.setBody(null);
        Object result = httpProxyUtil.httpProxyRequest(arg);
        System.out.println(result);
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ObjectOutputStream out = new ObjectOutputStream(bos)) {
            out.writeObject(result);
            byte[] byteData = bos.toByteArray();
            System.out.println(byteData);
        } catch (IOException e) {

        }

//        QueryConnectInfoArg arg = new QueryConnectInfoArg();
//        arg.setDataCenterId("665553398973192f80fb46d5");
//        Result<QYWXConnectParam> result = controllerQYWeixinContactBind.queryConnectInfo(arg);
//        System.out.println(result);
    }
}
