package com.facishare.open.qywx.eventhandler.manager;

import com.facishare.open.qywx.eventhandler.BaseTest;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.facishare.open.qywx.i18n.I18NStringManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class I18NStringManagerTest extends BaseTest {
    @Resource
    private I18NStringManager i18NStringManager;

    @Test
    public void test() {
        String value = i18NStringManager.get2(I18NStringEnum.s104.getI18nKey(),
                "en",
                "82777",
                "员工",
                null);
        System.out.println(value);
    }
}
