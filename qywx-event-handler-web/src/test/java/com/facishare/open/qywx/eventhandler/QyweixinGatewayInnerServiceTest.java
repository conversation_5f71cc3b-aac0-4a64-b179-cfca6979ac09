package com.facishare.open.qywx.eventhandler;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinLoginInfoRsp;
import com.facishare.open.qywx.accountsync.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class QyweixinGatewayInnerServiceTest extends BaseTest {
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayInnerServiceNormal;

    @Resource
    private ProxyHttpClient proxyHttpClient;

    @Test
    public void test() {
        Result<QyweixinLoginInfoRsp> result = qyweixinGatewayInnerServiceNormal.code2WebLoginUserInfo("EVTKoqUGTvUx87AFgyqpfv-LGsWfFQsgpzzwPRxFwgZ-E1sTm6Wqhv0UT2GtePW2Ipsqh8rgcTe8WFX3hI5SxWuraEThXhCg82TIIvcl57I");
        System.out.println(result);
    }

    @Test
    public void post() {
        String url = "http://**************:36113/open/qyweixin/queryConnectInfo";
        String body = "{\"dataCenterId\":\"665553398973192f80fb46d5\"}";
        Map<String,String> headerMap = new HashMap<>();
        headerMap.put("cookie","hy_data_2020_id=184bd6de29f345-0beaa27dd5ac6f-786e7e59-2073600-184bd6de2a08c1; hy_data_2020_js_sdk=%7B%22distinct_id%22%3A%22184bd6de29f345-0beaa27dd5ac6f-786e7e59-2073600-184bd6de2a08c1%22%2C%22site_id%22%3A478%2C%22user_company%22%3A409%2C%22props%22%3A%7B%7D%2C%22device_id%22%3A%22184bd6de29f345-0beaa27dd5ac6f-786e7e59-2073600-184bd6de2a08c1%22%7D; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22184bd6ddc6c671-09d20b6e876bf2-786e7e59-2073600-184bd6ddc6d4c7%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%2C%22%24device_id%22%3A%22184bd6de1734b2-0d7c9c341ec70e-786e7e59-2073600-184bd6de1746ba%22%7D; _ga_9VQEQS45LV=GS1.2.**********.6.0.**********.0.0.0; _gcl_au=1.1.********.**********; guid=c934ac3a-8052-615b-eb60-719149cc3070; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=**********,**********; Hm_lpvt_06d5233541e92feb3cc8980700b1efa6=**********; HMACCOUNT=50B740B60973EFC1; _ga=GA1.2.**********.**********; _ga_0EEZ6V876K=GS1.1.**********.4.0.**********.58.0.0; _ga_212MF0G69T=GS1.1.**********.4.0.**********.58.0.*********; mirrorId=0000; EPXId=6084b5f3713a418fae3802e290823026; LoginId=LOGIN_ID_8fe4afa9-e874-4505-9d11-e5afd43b0719; lang=zh-CN; JSESSIONID=58AD19E81F011DD6D285F71EA474B1CC; fs_token=EJ4rP3SvCp4jP3CpOYqqCJ0tBJbZDZWjDpWvPJKvOcDYOMKm; FSAuthX=0G60pFtF00C00011Ra4OzK0VDve5V6BNssrok7yKEjepHfjoEkVTNQwJzoDLFRVq8pKhAat4v61WCwTVPOc0ayEHyAAz9LEg6pXYVyWVuFzyqhYO4YtyMXXi32NNshw41JGOlF4JOArtsUYD5aRGiUqsuOVWRzSWm9VENXNcaW21zFInA1BNCmsx5fi973c5WQUOEWtPMwbYgGibZR7doEtskdoCPZoAhaKuLPla9zd78yb4bep2xrFzIfE1; FSAuthXC=0G60pFtF00C00011Ra4OzK0VDve5V6BNssrok7yKEjepHfjoEkVTNQwJzoDLFRVq8pKhAat4v61WCwTVPOc0ayEHyAAz9LEg6pXYVyWVuFzyqhYO4YtyMXXi32NNshw41JGOlF4JOArtsUYD5aRGiUqsuOVWRzSWm9VENXNcaW21zFInA1BNCmsx5fi973c5WQUOEWtPMwbYgGibZR7doEtskdoCPZoAhaKuLPla9zd78yb4bep2xrFzIfE1; fsRoute=81243.1069");

        Result<QYWXConnectParam> response = proxyHttpClient.postUrl(url, body, headerMap, new TypeReference<Result<QYWXConnectParam>>() {

        });
        System.out.println(response);
    }
}
