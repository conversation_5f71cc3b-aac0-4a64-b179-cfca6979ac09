var vm = new Vue({
    el: '#app',
    data: {
        message:'hello message',
        corpId:'',
        appId:'',
        result:''
    },
    methods: {
        getAppLicenseInfo:function () {
            console.log("call getAppLicenseInfo begin");
            axios
                .get('/qyweixin/admin/tools/getAppLicenseInfo?corpId='+this.corpId)
                .then(function (res) {
                    this.result=res.data
                })
                .catch(function (res) {
                    alert(JSON.stringify(res))
            });
            console.log("call getAppLicenseInfo begin");
        }
    }
})