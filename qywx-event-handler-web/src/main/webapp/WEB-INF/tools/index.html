<!DOCTYPE html>
<html lang="en" xmlns:v-on="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <title>企业微信工具集</title>
    <script src="https://cdn.staticfile.org/vue/2.4.2/vue.min.js"></script>
    <script src="https://cdn.staticfile.org/axios/0.18.0/axios.min.js"></script>
    <style type="text/css">
        #app {
            margin-left:auto;
            margin-right:auto;
            width:1000px;
        }
        .div-group {
            display: block;
            background-color:lightgray;
            padding:5px 10px;
            margin-bottom:20px;
        }
        .input-text {
            width:50%;
            height:20px;
        }
        .btn-function {
            width:100px;
            height:30px;
            margin-top:10px;
            margin-left:100px;
        }
        .p-result {
            padding-left:10px;
            padding-right:10px;
        }
    </style>
</head>
<body>
<h1 style="text-align: center">企业微信工具集</h1>
<div id="app">
    <div class="div-group">
        <h2>查询应用的接口许可状态</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="appLicenseInfo.corpId" placeholder="请输入企业微信ID"><br/>
        <button class="btn-function" v-on:click="getAppLicenseInfo">执行</button>
        <p class="p-result">响应结果：{{appLicenseInfo.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>获取CRM应用可见范围内的员工ID</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="userListInAppVisibleRange.corpId" placeholder="请输入企业微信ID"><br/>
        <button class="btn-function" v-on:click="getUserListInAppVisibleRange">执行</button>
        <p class="p-result">响应结果：自动转换为附件下载</p>
    </div>

    <div class="div-group">
        <h2>查询企微人员绑定关系</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="userBindInfo.corpId" placeholder="请输入企业微信ID" required="required"><br/>
        <label style="display: inline-block;width:100px;" >企业微信人员ID：</label>
        <input class="input-text" type="text" v-model="userBindInfo.outUserId" placeholder="请输入企业微信人员ID" required="required"><br/>
        <label style="display: inline-block;width:100px;" >纷享ea：</label>
        <input class="input-text" type="text" v-model="userBindInfo.fsEa" placeholder="纷享ea，非必填"><br/>
        <button class="btn-function" v-on:click="getUserBindInfo">执行</button><br/>
        <label style="display: inline-block;width:1000px;" >结果说明：fsAccount：人员crm账号；status：正常绑定关系状态为0</label><br/>
        <p class="p-result">响应结果：{{userBindInfo.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>检查CRM用户是否停用</h2>
        <label style="display: inline-block;width:100px;" >纷享ea：</label>
        <input class="input-text" type="text" v-model="fsUserStatus.fsEa" placeholder="请输入纷享ea" required="required"><br/>
        <label style="display: inline-block;width:100px;" >纷享人员ID：</label>
        <input class="input-text" type="text" v-model="fsUserStatus.fsUserId" placeholder="请输入纷享人员ID" required="required"><br/>
        <button class="btn-function" v-on:click="getFsUserStatus">执行</button><br/>
        <label style="display: inline-block;width:1000px;" >结果说明：NORMAL为正常状态</label><br/>
        <p class="p-result">响应结果：{{fsUserStatus.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>查询企业微信应用信息（包括企业名称）</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="qywxAppStatus.corpId" placeholder="请输入企业微信ID" required="required"><br/>
        <label style="display: inline-block;width:100px;" >应用：</label>
        <input type="radio" name="appType" value="1" v-model="qywxAppStatus.appType" />纷享销客crm应用
        <input type="radio" name="appType" value="2" v-model="qywxAppStatus.appType" />纷享销客代开发应用<br/>
        <button class="btn-function" v-on:click="getQYWXAppStatus">执行</button><br/>
        <label style="display: inline-block;width:1000px;" >结果说明：status：应用绑定状态，为0是正常状态；appStatus：应用是否被删除，为0是应用正常状态；close：应用是否被停用，为0是应用未被停用；customizedPublishStatus：代开发发布状态，0为待开发（企业已授权，服务商未创建应用），1：开发中（服务商已创建应用，未上线）；2为已上线（服务商已上线应用且不存在未上线版本），3为存在未上线版本（服务商已上线应用但存在未上线版本）</label><br/>
        <p class="p-result">响应结果：{{qywxAppStatus.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>明文企微ID转换密文企微ID</h2>
        <label style="display: inline-block;width:100px;" >企业微信明文ID：</label>
        <input class="input-text" type="text" v-model="corpIdToEncryptionId.corpId" placeholder="请输入企业微信明文ID"><br/>
        <button class="btn-function" v-on:click="getCorpIdToEncryptionId">执行</button>
        <p class="p-result">响应结果：{{corpIdToEncryptionId.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>明文企微员工ID转换密文企微员工ID</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="userIdToEncryptionId.corpId" placeholder="请输入企业微信ID"><br/>
        <label style="display: inline-block;width:100px;" >企业微信员工ID：</label>
        <input class="input-text" type="text" v-model="userIdToEncryptionId.userId" placeholder="请输入企业微信员工ID"><br/>
        <button class="btn-function" v-on:click="getUserIdToEncryptionId">执行</button>
        <p class="p-result">响应结果：{{userIdToEncryptionId.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>检查企业微信代开发应用授权信息</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="repAppAuthorityInfo.corpId" placeholder="请输入企业微信ID"><br/>
        <button class="btn-function" v-on:click="getRepAppAuthorityInfo">执行</button><br/>
        <label style="display: inline-block;width:1000px;" >结果说明：userStatus：人员权限，0为权限正常状态；departmentStatus：部门权限，0为权限正常状态</label><br/>
        <p class="p-result">响应结果：{{repAppAuthorityInfo.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>检查crm客户是否已开通</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="crmAccountObjStatus.corpId" placeholder="请输入企业微信ID"><br/>
        <button class="btn-function" v-on:click="getCrmAccountObjStatus">执行</button><br/>
        <label style="display: inline-block;width:1000px;" >结果说明：channel：来源，可为空；lifeStatus：客户生命状态，normal为正常状态</label><br/>
        <p class="p-result">响应结果：{{crmAccountObjStatus.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>查看下crm订单是否成功</h2>
        <label style="display: inline-block;width:100px;" >crm订单ID：</label>
        <input class="input-text" type="text" v-model="crmSalesOrderObjStatus.orderId" placeholder="请输入crm订单ID"><br/>
        <button class="btn-function" v-on:click="getCrmSalesOrderObjStatus">执行</button><br/>
        <label style="display: inline-block;width:1000px;" >结果说明：salesOrderName：订单编号；salesOrderId：订单Id；orderStatus：订单状态；lifeStatus：订单生命状态，normal为正常状态</label><br/>
        <p class="p-result">响应结果：{{crmSalesOrderObjStatus.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>查看crm企业状态</h2>
        <label style="display: inline-block;width:100px;" >纷享ea：</label>
        <input class="input-text" type="text" v-model="enterpriseRunStatus.fsEa" placeholder="请输入纷享ea"><br/>
        <button class="btn-function" v-on:click="getEnterpriseRunStatus">执行</button><br/>
        <label style="display: inline-block;width:1000px;" >结果说明：2为正常状态</label><br/>
        <p class="p-result">响应结果：{{enterpriseRunStatus.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>企业微信和crm解绑（至少填一个）</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="unbind.corpId" placeholder="请输入企业微信ID"><br/>
        <label style="display: inline-block;width:100px;" >纷享ea：</label>
        <input class="input-text" type="text" v-model="unbind.fsEa" placeholder="请输入纷享ea"><br/>
        <button class="btn-function" v-on:click="getUnbind">执行</button>
        <p class="p-result">响应结果：{{unbind.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>企业微信创建纷享企业情况</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="fsEnterpriseOpen.outEa" placeholder="请输入企业微信ID"><br/>
        <button class="btn-function" v-on:click="getFsEnterpriseOpen">执行</button>
        <p class="p-result">响应结果：{{fsEnterpriseOpen.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>企业微信创建纷享人员情况</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="fsEmployeeOpen.outEa" placeholder="请输入企业微信ID"><br/>
        <label style="display: inline-block;width:100px;" >企业微信人员ID：</label>
        <input class="input-text" type="text" v-model="fsEmployeeOpen.outUserId" placeholder="请输入企业微信人员ID"><br/>
        <button class="btn-function" v-on:click="getFsEmployeeOpen">执行</button>
        <p class="p-result">响应结果：{{fsEmployeeOpen.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>纷享企业的绑定类型</h2>
        <label style="display: inline-block;width:100px;" >纷享ea：</label>
        <input class="input-text" type="text" v-model="enterpriseBindType.fsEa" placeholder="请输入纷享ea"><br/>
        <button class="btn-function" v-on:click="getEnterpriseBindType">执行</button>
        <p class="p-result">响应结果：{{enterpriseBindType.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>人员在纷享的状态</h2>
        <label style="display: inline-block;width:100px;" >企业微信ID：</label>
        <input class="input-text" type="text" v-model="fsEmployeeStatus.outEa" placeholder="请输入企业微信ID"><br/>
        <label style="display: inline-block;width:100px;" >企业微信人员ID：</label>
        <input class="input-text" type="text" v-model="fsEmployeeStatus.outUserId" placeholder="请输入企业微信人员ID"><br/>
        <button class="btn-function" v-on:click="getFsEmployeeStatus">执行</button>
        <p class="p-result">响应结果：{{fsEmployeeStatus.responseData}}</p>
    </div>

    <div class="div-group">
        <h2>处理重复人员数据（确认无误后再执行）</h2>
        <label style="display: inline-block;width:100px;" >纷享ei：</label>
        <input class="input-text" type="text" v-model="dealFsEmployeeData.ei" placeholder="请输入纷享ei"><br/>
        <label style="display: inline-block;width:100px;" >纷享人员ID(不需要保留的人员ID)：</label>
        <input class="input-text" type="text" v-model="dealFsEmployeeData.userId" placeholder="请输入纷享人员ID(不需要保留的人员ID)"><br/>
        <label style="display: inline-block;width:100px;" >纷享人员ID(需要保留的人员ID)：</label>
        <input class="input-text" type="text" v-model="dealFsEmployeeData.newUserId" placeholder="请输入纷享人员ID(需要保留的人员ID)"><br/>
        <button class="btn-function" v-on:click="getDealFsEmployeeData">执行</button>
        <p class="p-result">响应结果：{{dealFsEmployeeData.responseData}}</p>
    </div>
</div>

<!--<script src="../tools/app.js" type="text/javascript"></script>-->
<script type="text/javascript">
    var vm = new Vue({
        el: '#app',
        data: {
            appLicenseInfo:{
                corpId:'',
                responseData:''
            },
            userListInAppVisibleRange:{
                corpId:'',
                responseData:''
            },
            userBindInfo:{
                corpId:'',
                outUserId:'',
                fsEa:"",
                responseData:''
            },
            fsUserStatus:{
                fsEa:'',
                fsUserId:'',
                responseData:''
            },
            qywxAppStatus:{
                corpId:'',
                appType:'',
                responseData:''
            },
            corpIdToEncryptionId:{
                corpId:'',
                responseData:''
            },
            userIdToEncryptionId:{
                corpId:'',
                userId:'',
                responseData:''
            },
            repAppAuthorityInfo:{
                corpId:'',
                responseData:''
            },
            crmAccountObjStatus:{
                corpId:'',
                responseData:''
            },
            crmSalesOrderObjStatus:{
                orderId:'',
                responseData:''
            },
            enterpriseRunStatus:{
                fsEa:'',
                responseData:''
            },
            unbind:{
                corpId:'',
                fsEa:'',
                responseData:''
            },
            fsEnterpriseOpen:{
                outEa:'',
                responseData:''
            },
            fsEmployeeOpen:{
                outEa:'',
                outUserId:'',
                responseData:''
            },
            enterpriseBindType:{
                fsEa:'',
                responseData:''
            },
            fsEmployeeStatus:{
                outEa:'',
                outUserId:'',
                responseData:''
            },
            dealFsEmployeeData:{
                ei: '',
                userId:'',
                newUserId:'',
                responseData:''
            },
        },
        methods: {
            getAppLicenseInfo:function () {
                axios
                    .get('/qyweixin/admin/tools/getAppLicenseInfo?corpId='+this.appLicenseInfo.corpId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        vm.appLicenseInfo.responseData=res.data.data;
                        console.log("responseData="+vm.appLicenseInfo.responseData);
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getUserListInAppVisibleRange:function () {
                axios
                    .get('/qyweixin/admin/tools/getUserListInAppVisibleRange?corpId='+this.userListInAppVisibleRange.corpId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        //vm.userListInAppVisibleRange.responseData=res.data.data;
                        console.log("responseData="+vm.userListInAppVisibleRange.responseData);
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getUserBindInfo:function () {
                axios
                    .get('/qyweixin/admin/tools/getUserBindInfo?corpId='+this.userBindInfo.corpId+'&outUserId='+this.userBindInfo.outUserId+'&fsEa='+this.userBindInfo.fsEa,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        vm.userBindInfo.responseData=res.data.data;
                        console.log("responseData="+vm.userBindInfo.responseData);
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getFsUserStatus:function () {
                axios
                    .get('/qyweixin/admin/tools/getCRMUserStatus?fsEa='+this.fsUserStatus.fsEa+'&fsUserId='+this.fsUserStatus.fsUserId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        vm.fsUserStatus.responseData=res.data.data;
                        console.log("responseData="+vm.fsUserStatus.responseData);
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getQYWXAppStatus:function () {
                axios
                    .get('/qyweixin/admin/tools/getQYWXAppBindInfo?corpId='+this.qywxAppStatus.corpId+'&appType='+parseInt(this.qywxAppStatus.appType),{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.qywxAppStatus.responseData=res.data.data;
                            } else {
                                vm.qywxAppStatus.responseData='查询为空，该应用未安装或者未绑定成功';
                            }
                            console.log("responseData="+vm.qywxAppStatus.responseData);
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getCorpIdToEncryptionId:function () {
                axios
                    .get('/qyweixin/admin/tools/getOpenCorpId?corpId='+this.corpIdToEncryptionId.corpId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        vm.corpIdToEncryptionId.responseData=res.data.data;
                        console.log("responseData="+vm.corpIdToEncryptionId.responseData);
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getUserIdToEncryptionId:function () {
                axios
                    .get('/qyweixin/admin/tools/getOpenUserId?corpId='+this.userIdToEncryptionId.corpId+'&userId='+this.userIdToEncryptionId.userId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        vm.userIdToEncryptionId.responseData=res.data.data;
                        console.log("responseData="+vm.userIdToEncryptionId.responseData);
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getRepAppAuthorityInfo:function () {
                axios
                    .get('/qyweixin/admin/tools/getQYWXRepAppAuthorityInfo?corpId='+this.repAppAuthorityInfo.corpId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.repAppAuthorityInfo.responseData=res.data.data;
                            } else {
                                vm.repAppAuthorityInfo.responseData='查询为空，该应用未安装或者未绑定成功';
                            }
                            console.log("responseData="+vm.repAppAuthorityInfo.responseData);
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getCrmAccountObjStatus:function () {
                axios
                    .get('/qyweixin/admin/tools/queryCrmAccountObjStatus?corpId='+this.crmAccountObjStatus.corpId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.crmAccountObjStatus.responseData=res.data.data;
                                console.log("responseData="+vm.crmAccountObjStatus.responseData);
                            } else {
                                vm.crmAccountObjStatus.responseData='查询为空，该crm客户未创建成功';
                            }
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getCrmSalesOrderObjStatus:function () {
                axios
                    .get('/qyweixin/admin/tools/queryCrmSalesOrderObjStatus?orderId='+this.crmSalesOrderObjStatus.orderId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.crmSalesOrderObjStatus.responseData=res.data.data;
                                console.log("responseData="+vm.crmSalesOrderObjStatus.responseData);
                            } else {
                                vm.crmSalesOrderObjStatus.responseData='查询为空，该企业下订单未成功';
                            }
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getEnterpriseRunStatus:function () {
                axios
                    .get('/qyweixin/admin/tools/getEnterpriseRunStatus?fsEa='+this.enterpriseRunStatus.fsEa,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.enterpriseRunStatus.responseData=res.data.data;
                                console.log("responseData="+vm.enterpriseRunStatus.responseData);
                            } else {
                                vm.enterpriseRunStatus.responseData='查询为空，该企业未创建成功';
                            }
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getUnbind:function () {
                var corpId = this.unbind.corpId;
                var fsEa = this.unbind.fsEa;
                if((corpId != null || corpId !== '') && (fsEa == null || fsEa === '')) {
                    //提示有多少个绑定的crm信息
                    axios
                        .get('/qyweixin/admin/tools/getFsEaList?corpId='+corpId,{
                            headers: {
                                'fs-service': 'fs-qywx-web',
                                'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                                'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                            }
                        })
                        .then(function (res) {
                            console.log("res="+res);
                            if(res.data.success) {
                                if(res.data.data!=null) {
                                    var eaList = res.data.data;
                                    var flag = confirm("绑定的相关的纷享企业:" + eaList + "确定删除？");
                                    if(flag === true) {
                                        axios
                                            .get('/qyweixin/admin/tools/unbind?fsEa='+fsEa+'&corpId='+corpId)
                                            .then(function (res) {
                                                console.log("res="+res);
                                                if(res.data.success) {
                                                    vm.unbind.responseData='解绑成功';
                                                } else {
                                                    alert(JSON.stringify(res.data.errorMsg));
                                                }
                                            })
                                            .catch(function (res) {
                                                alert(JSON.stringify(res));
                                            });
                                    }
                                } else {
                                    vm.unbind.responseData='查询为空，未找到相关的绑定关系';
                                }
                            } else {
                                alert(JSON.stringify(res.data.errorMsg));
                            }
                        })
                        .catch(function (res) {
                            alert(JSON.stringify(res));
                        });
                } else {
                    axios
                        .get('/qyweixin/admin/tools/unbind?fsEa='+fsEa+'&corpId='+corpId,{
                            headers: {
                                'fs-service': 'fs-qywx-web',
                                'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                                'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                            }
                        })
                        .then(function (res) {
                            console.log("res="+res);
                            if(res.data.success) {
                                vm.unbind.responseData='解绑成功';
                            } else {
                                alert(JSON.stringify(res.data.errorMsg));
                            }
                        })
                        .catch(function (res) {
                            alert(JSON.stringify(res));
                        });
                }
            },
            getFsEnterpriseOpen:function () {
                axios
                    .get('/qyweixin/admin/tools/queryFsEnterpriseOpen?outEa='+this.fsEnterpriseOpen.outEa,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.fsEnterpriseOpen.responseData=res.data.data;
                            } else {
                                vm.fsEnterpriseOpen.responseData='查询为空，请重新输入正确的账号';
                            }
                            console.log("responseData="+vm.fsEnterpriseOpen.responseData);
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getFsEmployeeOpen:function () {
                axios
                    .get('/qyweixin/admin/tools/queryFsEmployeeOpen?outEa='+this.fsEmployeeOpen.outEa+'&outUserId='+this.fsEmployeeOpen.outUserId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.fsEmployeeOpen.responseData=res.data.data;
                            } else {
                                vm.fsEmployeeOpen.responseData='查询为空，请重新输入正确的账号';
                            }
                            console.log("responseData="+vm.fsEmployeeOpen.responseData);
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getEnterpriseBindType:function () {
                axios
                    .get('/qyweixin/admin/tools/queryEnterpriseBindType?fsEa='+this.enterpriseBindType.fsEa,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.enterpriseBindType.responseData=res.data.data;
                            } else {
                                vm.enterpriseBindType.responseData='查询为空，请重新输入正确的账号';
                            }
                            console.log("responseData="+vm.enterpriseBindType.responseData);
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
            getFsEmployeeStatus:function () {
                axios
                    .get('/qyweixin/admin/tools/queryFsEmployeeStatus?outEa='+this.fsEmployeeStatus.outEa+'&outUserId='+this.fsEmployeeStatus.outUserId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            if(res.data.data!=null) {
                                vm.fsEmployeeStatus.responseData=res.data.data;
                            } else {
                                vm.fsEmployeeStatus.responseData='查询为空，请重新输入正确的账号';
                            }
                            console.log("responseData="+vm.fsEmployeeStatus.responseData);
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },

            getDealFsEmployeeData:function () {
                axios
                    .get('/qyweixin/admin/tools/dealEmpData?ei='+this.dealFsEmployeeData.ei+'&userId='+this.dealFsEmployeeData.userId+'&newUserId='+this.dealFsEmployeeData.newUserId,{
                        headers: {
                            'fs-service': 'fs-qywx-web',
                            'fs-key': 'D9A78E4E3A6817397E975D3763C75',
                            'fs-secret': '8F2D5C72F893B7BB4CEFFB36753FA'
                        }
                    })
                    .then(function (res) {
                        console.log("res="+res);
                        if(res.data.success) {
                            vm.dealFsEmployeeData.responseData=res.data.data;
                            console.log("responseData="+vm.dealFsEmployeeData.responseData);
                        } else {
                            alert(JSON.stringify(res.data.errorMsg));
                        }
                    })
                    .catch(function (res) {
                        alert(JSON.stringify(res));
                    });
            },
        }
    })
</script>
</body>
</html>