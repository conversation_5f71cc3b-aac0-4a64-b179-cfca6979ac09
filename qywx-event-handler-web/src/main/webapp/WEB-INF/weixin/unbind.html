<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>解除企业微信绑定</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <script type="text/javascript" src="https://ajax.aspnetcdn.com/ajax/jQuery/jquery-3.1.1.min.js "></script>
</head>
<body>
<div class="container">
    <div class="row clearfix">
        <div class="col-md-4 column">
        </div>
        <div class="col-md-4 column">
            </br></br>
            <label for="corpIds">微信企业ID</label>
            <input type="text" class="form-control" id="corpIds" name="corpIds" placeholder="请输入微信企业ID">
            </br>
            <div id="check_Type" hidden="hidden">
                <label for="fsEa">纷享账号Ea</label>
                <input type="text" class="form-control" id="fsEa" name="fsEa"  placeholder="请输入新纷享账号">
                </br>
            </div>
            <div class="row clearfix">
                <div class="col-md-12">
                    <label for="simple">
                        <input type="radio" name="deleteBindType" id="simple"  value="0" checked="checked" class="bind_Radio">普通解绑
                    </label>
                    <label for="bind" hidden>
                        <input type="radio" name="deleteBindType" id="bind" value="3" class="bind_Radio">解绑并绑定新的纷享企业
                    </label>
                </div>
            </div>
            </br>
            <div class="row clearfix">
                <div class="col-md-6 column">
                    <button type="button" class="btn btn-default btn-primary" id="unbind-btn">解除绑定</button>
                </div>
                <div class="col-md-6 column">
                    <button type="button" class="btn btn-default" id="reset-btn">重置</button>
                </div>
            </div>
        </div>
        <div class="col-md-4 column">
        </div>
    </div>
</div>
</body>
</html>

<script type="text/javascript" >

    $(document).ready(function(){
        var url = location.href;
        $("#unbind-btn").click(function(){
            var params = {
                deleteBindType:$('input[name="deleteBindType"]:checked').val(),
                corpIds:$("#corpIds").val(),
                fsEa:$("#fsEa").val(),
                appId:"wxdeb7e0658a828754"
            }
            <!-- 这么写是因为appId在测试环境和线上是不一致的 -->
            if (url.includes("ceshi123") && url.includes("debug=true")){
                params.appId = "wx88a141937dd6f838";
            }
            $.ajax({
                url: '/open/qyweixin/deleteBind',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(params),
                success:function (data){
                    var d = data;
                    if(d.success) {
                        alert("解除绑定成功")
                        $("#corpIds").val("");
                    }else{
                        alert("解除绑定失败:【"+d.errorCode+":"+d.errorMsg+"】")
                    }
                },
                error:function(xhr) {
                    alert("请求失败..")
                }
            });
        });

        $(".bind_Radio").click(function() {
            var value = $('input[name="deleteBindType"]:checked ').val();
            if (value == 3) {
                $("#check_Type").show();
            }else {
                $("#check_Type").hide();
            }
        });

        $("#reset-btn").click(function(){
            $("#corpIds").val("");
            $("#fsEa").val("");
        });
    });
</script>