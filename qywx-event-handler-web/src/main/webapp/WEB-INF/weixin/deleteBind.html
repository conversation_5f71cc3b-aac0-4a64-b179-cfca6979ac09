<!DOCTYPE html>
<html lang="en">
<head>
    <title>微信企业绑定管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <script type="text/javascript" src="https://ajax.aspnetcdn.com/ajax/jQuery/jquery-3.1.1.min.js "></script>
</head>
<body>
</br></br></br></br>
<div class="container">

    删除微信企业绑定
    <form id="bindInfo" class="form-horizontal" >
        <!--username-->
        <div class="form-group">
            <label class="col-sm-3 control-label">环境</label>
            <label class="radio-inline">
                <input type="radio"  value="ceshi113" name="profile">ceshi113
            </label>
            <label class="radio-inline">
                <input type="radio"  value="fstest" name="profile" checked>fstest
            </label>
            <div class="col-sm-offset-3 col-sm-5 count"></div>
        </div>
        <!--pwd-->
        <div class="form-group">
            <label class="col-sm-3 control-label">微信企业ids</label>
            <div class="col-sm-5 control-div">
                <input type="text" class="form-control" name="corpIds" placeholder="外部企业账号eid，多个用逗号分隔">
            </div>
        </div>

        <!--buttons-->
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-2 control-div">
                <button type="button" class="btn btn-success btn-register" onclick="deleteBind0();">清除微信企业绑定</button>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-8 control-div" id="result"></div>
        </div>
    </form>

    该企业微信账号已绑定其它纷享账号 解绑操作
    <form id="bindInfo1" class="form-horizontal" >
        <!--username-->
        <div class="form-group">
            <label class="col-sm-3 control-label">环境</label>
            <label class="radio-inline">
                <input type="radio"  value="ceshi113" name="profile">ceshi113
            </label>
            <label class="radio-inline">
                <input type="radio"  value="fstest" name="profile" checked>fstest
            </label>
            <div class="col-sm-offset-3 col-sm-5 count"></div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">微信企业id</label>
            <div class="col-sm-5 control-div">
                <input type="text" class="form-control" name="corpIds" placeholder="微信企业id">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">绑定指定的纷享企业id</label>
            <div class="col-sm-5 control-div">
                <input type="text" class="form-control" name="fsEa" placeholder="绑定指定的纷享企业id">
            </div>
        </div>
        <!--buttons-->
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-2 control-div">
                <button type="button" class="btn btn-success btn-register" onclick="deleteBind1();">清除原绑定，并绑定指定纷享企业</button>
            </div>

        </div>
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-8 control-div" id="result1"></div>
        </div>
    </form>

    该纷享账号已绑定其它企业微信账号 解绑操作
    <form id="bindInfo2" class="form-horizontal" >
        <!--username-->
        <div class="form-group">
            <label class="col-sm-3 control-label">环境</label>
            <label class="radio-inline">
                <input type="radio"  value="ceshi113" name="profile">ceshi113
            </label>
            <label class="radio-inline">
                <input type="radio"  value="fstest" name="profile" checked>fstest
            </label>
            <div class="col-sm-offset-3 col-sm-5 count"></div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">纷享企业id</label>
            <div class="col-sm-5 control-div">
                <input type="text" class="form-control" name="fsEa" placeholder="纷享企业id">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">绑定指定的微信企业id</label>
            <div class="col-sm-5 control-div">
                <input type="text" class="form-control" name="corpIds" placeholder="绑定指定的微信企业id">
            </div>
        </div>
        <!--buttons-->
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-2 control-div">
                <button type="button" class="btn btn-success btn-register" onclick="deleteBind2();">清除原绑定，并绑定到指定微信企业</button>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-8 control-div" id="result2"></div>
        </div>
    </form>

</div>
</body>
</html>
<script type="text/javascript" >

    function deleteBind0() {
        var data=$('#bindInfo').serialize();
        var submitData=decodeURIComponent(data,true);
        submitData +="&deleteBindType=0";
        deleteBind(submitData, "result");
    }

    function deleteBind1() {
        var data=$('#bindInfo1').serialize();
        var submitData=decodeURIComponent(data,true);
        submitData +="&deleteBindType=1";
        deleteBind(submitData,"result1");
    }

    function deleteBind2() {
        var data=$('#bindInfo2').serialize();
        var submitData=decodeURIComponent(data,true);
        submitData +="&deleteBindType=2";
        deleteBind(submitData, "result2");
    }


    function deleteBind(submitData, resultText) {
        $("#"+resultText).text("请求中...");
        $.ajax({
            url: "/open/qyweixin/deleteBind?"+submitData,
            type: "get",
            success: function (msg, stat, req) {
                if("s120050000"==msg.errorCode){
                    $("#"+resultText).text("成功！");
                } else {
                    $("#"+resultText).text(msg.errorCode + ":" + msg.errorMsg +" data:" + msg.data);
                }
            },
            error: function (req, stat, err) {
                $("#"+resultText).text("请求失败！");
            }
        });
    }
</script>