<html>
<head>
    <title>外部联系人接口能力测试</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="http://cdn.static.runoob.com/libs/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
    <script type="text/javascript" src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-3.1.1.min.js "></script>
    <script type="text/javascript" src="http://libs.baidu.com/bootstrap/3.0.3/js/bootstrap.min.js"></script>
</head>

<body onload="getUserDetail()">
<form role="form">
    <fieldset>
        <legend>外部联系人详情纷享定制页面</legend>
    </fieldset>

    <fieldset>
        <h2>_________________________________________________________________________________________</h2>
        当前外部联系人详情：
        <textarea id="userDetail" rows="5" cols="40"></textarea>
    </fieldset>

    <fieldset>
        <h2>_________________________________________________________________________________________</h2>
        内部员工userid：
        <input type="text" id="userid" /><br><br><br><br>
        获取内部员工标记为企业客户的外部联系人列表：
        <textarea id="contactList" rows="5" cols="40"></textarea>
        <button type="button" class="btn btn-primary" id="queryBtn" onclick="getContacts()"> 查 询</button>
        <h2>_________________________________________________________________________________________</h2>
    </fieldset>
</form>
</body>

<script type="text/javascript" >
    function GetQueryString(name) {
        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if(r!=null)return  unescape(r[2]); return null;
    }

    function doGetUserDetail(url) {
        $.ajax({
            url: url,
            type: "get",
            success: function (msg, stat, req) {
                    $("#userDetail").val(msg);
            },
            error: function (req, stat, err) {
                alert("error");
            }
        });
    }
    function getUserDetail() {
        var code = GetQueryString("code");
        var url = "/webhook/qyweixin/priapp/getExternalContact?code="+code;
        doGetUserDetail(url);
    }

    function doGetContacts(url) {
        $.ajax({
            url: url,
            type: "get",
            success: function (msg, stat, req) {
                $("#contactList").val(msg);
            },
            error: function (req, stat, err) {
                alert("error");
            }
        });
    }
    function getContacts() {
        var userid = $("#userid").val();;
        var url = "/webhook/qyweixin/priapp/getExternalContactList?userid="+userid;
        doGetContacts(url);
    }
</script>

</html>
