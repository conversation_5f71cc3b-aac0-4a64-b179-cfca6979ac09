<%--
  Created by IntelliJ IDEA.
  User: chenzx9119
  Date: 2023/1/16
  Time: 10:39
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>
    <title><c:out value="${requestScope.errorPageTitle}"/></title>
    <script>
        // 检查jQuery是否已加载
        if (typeof jQuery === 'undefined') {
            var script = document.createElement('script');
            script.src = "https://a9.fspage.com/open/cdn/jquery/2.2.4/jquery.min.js";
            script.type = 'text/javascript';
            document.head.appendChild(script);
        }
    </script>
    <style>
        html, body {
            margin: 0;
            padding: 0;
        }

        .error-container {
            position: absolute;
            left: 50%;
            top: 10%;
            transform: translateX(-50%);
            font-size: 16px;
        }
        .err-img {
            width: 390px;
            height: 150px;
            background-image: url('https://a9.fspage.com/FSR/weex/erpdss/template_cover/erpdss_error.svg');
            background-size: contain;
            background-repeat: no-repeat;
        }

        .err-msg {
            padding: 0 40px;
        }

        .err-msg p {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            -webkit-line-clamp: 999;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
            word-break: break-word;
        }

        .error-tips p {
            margin: 0;
            text-align: start;
            font-size: 16px;
            color: #C1C5CE;
        }

        button.retry-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            font-size: 16px;
            color: white;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
    <script>
        // Your JavaScript functions
        function CreateEmployee(){
            // 获取按钮元素并禁用它
            var $retryButton = $('.retry-button');
            $retryButton.prop('disabled', true).text('同步中...'); // 更新按钮文本以反映状态

            var outEa = "<c:out value='${requestScope.errorPageOutEa}'/>"; // 从requestScope获取outEa
            var fsEa = "<c:out value='${requestScope.errorPageFsEa}'/>"; // 从requestScope获取fsEa
            var outUserId = "<c:out value='${requestScope.errorPageOutUserId}'/>"; // 从requestScope获取outUserId
            var token  = "<c:out value='${requestScope.errorPageToken}'/>"; // 从requestScope获取tokenerrorPage
            var appId  = "<c:out value='${requestScope.errorPageAppId}'/>"; // 从requestScope获取token
            var resyncEmp = <c:out value="${requestScope.resyncEmp}"/>;
            var createEmpSuccess = <c:out value="${requestScope.createEmpSuccess}"/>;
            var createEmpFailed = <c:out value="${requestScope.createEmpFailed}"/>;
                $.ajax({
                    type: 'POST',
                    url: '/open/qyweixin/employee/createEmployeeInfo',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        'outEa': outEa,
                        'fsEa': fsEa,
                        'outUserId': outUserId,
                        'token': token,
                        'appId': appId
                    }),
                    success: function (response) {
                        console.log("response=" + response);
                        if (response && response.errorCode === "s120050000") {
                            alert(createEmpSuccess);
                        } else {
                            alert(createEmpFailed);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error("Error: " + status + " " + error);
                        alert(createEmpFailed);
                    },
                    complete: function () {
                        // 请求完成后重新启用按钮，无论成功或失败
                        $retryButton.prop('disabled', false).text(resyncEmp); // 恢复按钮原始文本
                    }
                });
        }
    </script>
</head>
<body>
<div class="error-container">
    <div style="display: flex; justify-content: center; align-items: center;">
        <div class="err-img"></div>
    </div>
    <div class="err-msg">
        <p style="font-size: 20px; color: #181C25;"><c:out value="${requestScope.errorPageTip}"/></p>
        <p style="font-size: 16px; color: #545861;"><c:out value="${requestScope.errorPageTip2}"/></p> <br>
        <div class="error-tips">
            <p><c:out value="${requestScope.errorMsgTip}"/><c:out value="${requestScope.errorMsg}"/></p>
            <p><c:out value="${requestScope.proposeTip}"/><c:out value="${requestScope.propose}"/></p>
        </div>
        <%-- 根据requestScope.retry的值显示按钮 --%>
        <c:if test="${requestScope.createEmployee != null && requestScope.createEmployee}">
            <button class="retry-button" onclick="CreateEmployee()"><c:out value="${requestScope.resyncEmp}"/></button>
        </c:if>
    </div>
</div>
</body>
</html>