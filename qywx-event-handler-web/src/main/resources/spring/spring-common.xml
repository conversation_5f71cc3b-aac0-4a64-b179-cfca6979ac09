<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">


      <!-- 消费方应用名，用于计算依赖关系，不是匹配条件，不要与提供方一样 -->
    <dubbo:application id="fsOpenQywxEventHandler" name="fs-open-qywx-eventhandler" />
    <!-- 使用multicast广播注册中心暴露发现服务地址 -->
    <dubbo:registry id="fsOpenQywxEventHandlerRegistry" address="${dubbo.registry.address}" file="${dubbo.registry.file}" />
    <dubbo:protocol id="dubbo" name="dubbo" port="${duboo.port}"  />
    <dubbo:consumer id="fsOpenQywxEventHandlerConsumer"   registry="fsOpenQywxEventHandlerRegistry"
                    init="false"    check="false" timeout="300000"
                    retries="2"   filter="tracerpc" />

    <!--<dubbo:registry id="local" address="zookeeper://localhost:2181"/>-->

    <dubbo:reference id="employeeProviderService"
                     interface="com.facishare.organization.api.service.EmployeeProviderService"
                     version="5.7"/>

    <dubbo:reference id="accountBindService"
                     interface="com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />
    <dubbo:reference id="accountBindInnerService"
                     interface="com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />

    <dubbo:reference id="qyweixinMessageSendService"
                     interface="com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />

    <dubbo:reference id="qyweixinAccountSyncService"
                     interface="com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService"
                     version="2.0"
                     timeout="300000"
                     check="false"
                     />
    <dubbo:reference id="superAdminService"
                     interface="com.facishare.open.qywx.accountsync.service.SuperAdminService"
                     version="2.0"
                     timeout="300000"
                     check="false"
                     />
    <dubbo:reference id="qyweixinGatewayServiceNormal"
                     interface="com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />

    <dubbo:reference id="ContactBindInnerService"
                     interface="com.facishare.open.qywx.accountinner.service.ContactBindInnerService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />
    <!--企信存档接口-->
    <dubbo:reference id="messageGeneratingService"
                     interface="com.facishare.open.qywx.save.service.MessageGeneratingService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />

    <dubbo:reference id="saveMessageService"
                     interface="com.facishare.open.qywx.save.service.SaveMessageService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     />

    <dubbo:reference id="toolsService"
                     interface="com.facishare.open.qywx.accountinner.service.ToolsService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="30000"
                     check="false"
                     />

    <dubbo:reference id="fileUploadService"
                     interface="com.facishare.open.qywx.accountsync.service.FileUploadService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="30000"
                     check="false"
                     />

    <dubbo:reference id="externalContactsService"
                     interface="com.facishare.open.qywx.accountinner.service.ExternalContactsService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="30000"
                     check="false"
                     />

    <dubbo:reference id="notificationService"
                     interface="com.facishare.open.qywx.accountinner.service.NotificationService"
                     protocol="dubbo"
                     version="1.0"
                     check="false"
                     />

    <dubbo:reference id="fsEmployeeServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>

    <dubbo:reference id="activeSessionAuthorizeService"
                     interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService"/>

    <dubbo:reference id="enterpriseEditionService"
                     interface="com.facishare.uc.api.service.EnterpriseEditionService"
                     protocol="dubbo"
                     retries="0"/>

    <import resource="classpath:spring/qywx-i18n.xml"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!--aop打印log-->
    <bean id="logAspect" class="com.facishare.open.qywx.eventhandler.aop.LogAspect"/>
    <!--监控crm接口-->
    <bean id="crmRateLimiterAspect" class="com.facishare.open.qywx.eventhandler.aop.CrmRateLimiterAspect"/>
    <aop:config>
        <aop:aspect ref="controllerI18NAspect">
            <aop:pointcut id="controllerI18N"
                          expression="(execution(* com.facishare.open.qywx.eventhandler.controller.outer.*.*(..)))"/>
            <aop:around pointcut-ref="controllerI18N" method="around"/>
        </aop:aspect>
        <aop:aspect id="logMonitor" ref="logAspect">
            <aop:pointcut id="monitor"
                          expression="(execution(* com.facishare.open.qywx.eventhandler.controller.*.*(..))
                           or execution(* com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService.*.*(..)))"/>
            <aop:around pointcut-ref="monitor" method="around"/>
        </aop:aspect>
        <aop:aspect ref="crmRateLimiterAspect">
            <aop:pointcut id="crmRateLimiter"
                          expression="(execution(* com.facishare.converter.EIEAConverter.*(..)) or
            execution(* com.facishare.uc.api.service.EnterpriseEditionService.*(..)))"/>
            <aop:around pointcut-ref="crmRateLimiter" method="around"/>
        </aop:aspect>
        <aop:aspect ref="exceptionAspect">
            <aop:after-throwing method="onException"
                                throwing="e"
                                pointcut="(execution(* com.facishare.open.qywx..*.*(..)))"/>
        </aop:aspect>
    </aop:config>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxUploadSize" value="*********"/>
        <property name="maxInMemorySize" value="40960"/>
    </bean>

<!--    <bean id="enterpriseWechatEventMQSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg index="1" value="enterprise.wechat.event.name.server"/>-->
<!--        <constructor-arg index="2" value="enterprise.wechat.event.group.provider"/>-->
<!--        <constructor-arg index="3" value="enterprise.wechat.event.consume.topic"/>-->
<!--    </bean>-->

    <bean id="enterpriseWechatEventMQSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="fs-enterprise-wechat-web-provider-section"/>
    </bean>

    <!--发送企业自建应用的secret -->
    <bean id="secretMessageSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="CLOUD_UPGRADE_NOTICE_EVENT_SECTION"/>
    </bean>

    <bean id="conversionSettingMessageSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="CLOUD-CONVERSION-SETTING-EVENT-SECTION"/>
    </bean>

    <bean id="conversionChangeMessageSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="CLOUD-CONVERSION-CHANGE-EVENT-SECTION"/>
    </bean>

    <bean id="enterpriseAccountBindSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="FS_CLOUD_CORP_BIND_SECTION"/>
    </bean>

    <bean id="employeeAccountBindSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="CLOUD-EMPLOYEE-BIND-PROVIDER-SECTION"/>
    </bean>

    <bean id="qywxEventNotifyMQSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="fs-open-cloud-provider-section"/>
    </bean>

    <bean id="accountSyncDataSender" class="com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="FS_CLOUD_SYNC_DATA_SECTION"/>
    </bean>

<!--    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"-->
<!--          init-method="start" destroy-method="shutdown">-->
<!--        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg name="sectionNames" value="out-event-data-change-consume-section"/>-->
<!--        <constructor-arg name="messageListener" ref="outEventDataChangeListener"/>-->
<!--    </bean>-->


    <!-- message save mongo-->
    <bean id="saveCloudMessageMongoStore" name="saveCloudMessageMongoStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-open-qywx-app-config"/>
        <property name="sectionNames" value="qywxMongo"/>
    </bean>

    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiServiceProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>
    </bean>
    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>
    </bean>

    <bean id="fileManager" class="com.facishare.open.qywx.accountsync.excel.FileManager"/>

    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>
    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fs-open-qywx-redis-config"/>
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-open-qywx-app-config"/>

    <context:component-scan base-package="com.facishare.open.order.contacts.proxy.api" />
    <context:component-scan base-package="com.facishare.open.qywx.eventhandler,com.facishare.open.qywx.i18n" />

</beans>