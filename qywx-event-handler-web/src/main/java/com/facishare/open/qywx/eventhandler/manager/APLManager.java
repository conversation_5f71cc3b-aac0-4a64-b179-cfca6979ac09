package com.facishare.open.qywx.eventhandler.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.facishare.open.qywx.eventhandler.enums.SourceTypeEnum;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * APL函数工具类
 * <AUTHOR>
 * @date ********
 */

@Slf4j
@Component
public class APLManager {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Autowired
    private EmployeeProviderService employeeProviderService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    /**
     * 查找APL函数，获取APL函数的详情
     *
     * @param headerObj
     * @param arg
     * @return
     */
    public ObjectData findApl(HeaderObj headerObj, FunctionServiceFindArg arg) {
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(ConfigCenter.paasFunctionUrl, "/API/v1/inner/object/function/service/find");
        String json = JSON.toJSONString(arg);
        return proxyHttpClient.postUrlByJson(url, json, headerMap, new TypeReference<ObjectData>() {
        });
    }

    /**
     * 执行APL函数
     * @param headerObj
     * @param arg
     * @param serializeNull
     * @return
     */
    public ObjectData executeApl(HeaderObj headerObj, FunctionServiceExecuteArg arg, boolean serializeNull) {
        log.info("APLManager.executeApl,arg={}",arg);
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(ConfigCenter.paasFunctionUrl, "/v1/function/currencyFunction");
        if (serializeNull) {
            return proxyHttpClient.postUrlSerialNull(url, arg, headerMap, new TypeReference<ObjectData>() {
            });
        } else {
            return proxyHttpClient.postUrl(url, arg, headerMap, new TypeReference<ObjectData>() {
            });
        }
    }

    public FunctionResult executeApl2(String upstreamOutEa,String outEa, String outUserId, String fsEa, String fsUserId, String mobile) {
        log.info("APLManager.executeApl2,upstreamOutEa={},outEa={},outUserId={},fsEa={},fsUserId={},mobile={}",upstreamOutEa,outEa,outUserId,fsEa,fsUserId,mobile);
        FunctionResult functionResult = new FunctionResult();
        functionResult.setCode(0);

        Map<String,String> ssoAuthConfigMap = JSONObject.parseObject(ConfigCenter.ssoAuthConfig,Map.class);
        if(ssoAuthConfigMap==null || ssoAuthConfigMap.isEmpty()) {
            return functionResult;
        }
        String aplApiName = ssoAuthConfigMap.get(upstreamOutEa);
        log.info("APLManager.executeApl2,aplApiName={}",aplApiName);
        if(StringUtils.isEmpty(aplApiName)) {
            return functionResult;
        }
        Result<List<QyweixinAccountEnterpriseMapping>> enterpriseMappingResult = qyweixinAccountBindService.selectAllEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(),
                upstreamOutEa);
        if(CollectionUtils.isEmpty(enterpriseMappingResult.getData())) {
            log.info("APLManager.executeApl2,enterpriseMappingResult.data is empty");
            return functionResult;
        }
        List<QyweixinAccountEnterpriseMapping> normalEnterpriseMappingList = enterpriseMappingResult.getData().stream()
                .filter(v -> v.getStatus() == 0)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(normalEnterpriseMappingList)) {
            log.info("APLManager.executeApl2,normalEnterpriseMappingList is empty");
            return functionResult;
        }
        String upstreamFsEa = normalEnterpriseMappingList.get(0).getFsEa();
        log.info("APLManager.executeApl2,upstreamFsEa={}",upstreamFsEa);
        Integer upstreamTenantId = eieaConverter.enterpriseAccountToId(upstreamFsEa);
        HeaderObj headerObj = new HeaderObj(upstreamTenantId, SuperUserConstants.USER_ID);
        FunctionServiceExecuteArg functionServiceExecuteArg = new FunctionServiceExecuteArg();
        functionServiceExecuteArg.setApiName(aplApiName);
        functionServiceExecuteArg.setNameSpace("erpdss");
        functionServiceExecuteArg.setBindingObjectAPIName("NONE");

        if(org.apache.commons.lang.StringUtils.isEmpty(mobile)) {
            Integer fsEi = eieaConverter.enterpriseAccountToId(fsEa);
            GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
            getEmployeeDtoArg.setEnterpriseId(fsEi);
            getEmployeeDtoArg.setEmployeeId(Integer.valueOf(fsUserId));
            GetEmployeeDtoResult employeeDtoResult = employeeProviderService.getEmployeeDto(getEmployeeDtoArg);
            log.info("APLManager.executeApl2,employeeDtoResult={}",employeeDtoResult);
            if(employeeDtoResult!=null && employeeDtoResult.getEmployeeDto()!=null) {
                mobile = employeeDtoResult.getEmployeeDto().getMobile();
            }
        }

        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("upstreamOutEa", upstreamOutEa);
        paramMap.put("outEa", outEa);
        paramMap.put("outUserId", outUserId);
        paramMap.put("mobile", mobile);
        paramMap.put("fsEa", fsEa);
        paramMap.put("fsUserId", fsUserId);

        FunctionServiceParameterData<Map> parameterData = new FunctionServiceParameterData<>();
        parameterData.setName("syncArg");
        parameterData.setType("Map");
        parameterData.setValue(paramMap);

        functionServiceExecuteArg.setParameters(Lists.newArrayList(parameterData));
        log.info("APLManager.executeApl2,functionServiceExecuteArg={}",functionServiceExecuteArg);
        ObjectData objectData = executeApl(headerObj, functionServiceExecuteArg, true);
        log.info("APLManager.executeApl2,objectData={}",objectData);

        JSONObject resultJson = (JSONObject) objectData.get("result");
        if(resultJson!=null) {
            JSONObject functionResultJson = (JSONObject) resultJson.get("functionResult");
            if(functionResultJson!=null) {
                functionResult = functionResultJson.toJavaObject(FunctionResult.class);
            }
        }
        return functionResult;
    }

    public FunctionResult executeApl3(String upstreamOutEa,String outEa, String outUserId, String fsEa, String fsUserId, String mobile) {
        FunctionResult result = null;
        try {
            result = executeApl2(upstreamOutEa, outEa, outUserId, fsEa, fsUserId, mobile);
        } catch (Exception e) {
            result = new FunctionResult();
            result.setCode(0);
        }
        return result;
    }

    private Map<String, String> getHeader(HeaderObj headerObj) {
        Map<String, String> headerMap = new HashMap<>();
        headerObj.forEach((key, value) -> {
            headerMap.put(key, value == null ? "" : value.toString());
        });
        return headerMap;
    }

    @Data
    public static class FunctionResult implements Serializable {
        private Integer code;
        private String msg;
        private JSONObject data;
    }

}
