package com.facishare.open.qywx.eventhandler.utils;

import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountinner.arg.HttpArg;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class UrlParamsUtils {
    @ReloadableProperty("qywx_ip_url")
    private String qywxIpUrl;

    public HttpArg getUrlParams(HttpServletRequest request, HttpServletResponse response) {
        LogUtils.info("UrlParamsUtils.getUrlParams,request={},response={}", request, response);

        String uri = request.getRequestURI();

        //拼接参数，转到纷享云
        HttpArg arg = new HttpArg();
        String param = request.getQueryString();
        String method = request.getMethod();
        String url = qywxIpUrl + uri;

        LogUtils.info("UrlParamsUtils.getUrlParams,param={},method={},url={}", param, method, url);
        if (StringUtils.isNotEmpty(param)) {
            url = url + "?" + param;
        }

        // 获取到请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        LogUtils.info("UrlParamsUtils.getUrlParams,headerNames={}", headerNames);
        Map<String, String> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (StringUtils.equalsIgnoreCase(headerName, "host")
                    || StringUtils.equalsIgnoreCase(headerName, "Accept-Encoding")) continue;
            String header = request.getHeader(headerName);
            headerMap.put(headerName, header);
        }

        arg.setMethod(method);
        arg.setHeader(headerMap);
        arg.setUrl(url);
        arg.setBody(getParameters(request));
        LogUtils.info("UrlParamsUtils.getUrlParams,arg={}", arg);
        return arg;
    }

    public static String getParameters(HttpServletRequest request) {
        try {
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
            StringBuilder responseStrBuilder = new StringBuilder();
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null)
                responseStrBuilder.append(inputStr);
            if(ObjectUtils.isEmpty(responseStrBuilder)) {
                return null;
            }
            String bodyString = responseStrBuilder.toString();
            LogUtils.info("HttpProxyInterceptor.preHandle,bodyString={}", bodyString);
            return bodyString;
        } catch (Exception e) {
            LogUtils.error("获取json参数失败", e);
            return null;
        }
    }
}
