package com.facishare.open.qywx.eventhandler.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.excel.BaseListener;
import com.facishare.open.qywx.accountsync.excel.ImportExcelFile;
import com.facishare.open.qywx.accountsync.excel.vo.DepartmentMappingVo;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.facishare.open.qywx.i18n.I18NStringManager;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Getter
public class DepartmentDataMappingListener extends BaseListener<DepartmentMappingVo> {

    private final ImportExcelFile.Result importResult;
    private String fsEa;
    private String outEa;
    private Integer fsUserId;
    private QyweixinAccountBindService qyweixinAccountBindService;
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    private String crmAppId;
    private ToolsService toolsService;
    private Map<String,String> outEaMap = new HashMap<>();
    private I18NStringManager i18NStringManager;
    private String lang;

    public DepartmentDataMappingListener(String fsEa,
                                         String outEa,
                                         Integer fsUserId,
                                         QyweixinAccountBindService qyweixinAccountBindService,
                                         QyweixinAccountSyncService qyweixinAccountSyncService,
                                         String crmAppId,
                                         ToolsService toolsService,
                                         I18NStringManager i18NStringManager,
                                         String lang) {
        importResult = new ImportExcelFile.Result();
        this.fsEa = fsEa;
        this.outEa = outEa;
        this.fsUserId = fsUserId;
        this.qyweixinAccountBindService = qyweixinAccountBindService;
        this.qyweixinAccountSyncService = qyweixinAccountSyncService;
        this.crmAppId = crmAppId;
        this.toolsService =toolsService;
        this.i18NStringManager = i18NStringManager;
        this.lang = lang;
    }

    @Override
    public void invoke(DepartmentMappingVo data, AnalysisContext context) {
        super.invoke(data, context);
        log.info("DepartmentDataMappingListener.invoke,data={}", data);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();

        if(StringUtils.isEmpty(data.getFsEa())
                || StringUtils.isEmpty(data.getOutEa())
                || data.getFsDepId() == null
                || StringUtils.isEmpty(data.getOutDepId())) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s180,lang,null));
            return;
        }

        if(!outEaMap.containsKey(data.getOutEa())) {
            com.facishare.open.qywx.accountsync.result.Result<String> result = toolsService.corpId2OpenCorpId(data.getOutEa());
            outEaMap.put(data.getOutEa(),result.getData());
        }
        String outEa2 = outEaMap.get(data.getOutEa());
        log.info("DepartmentDataMappingListener.invoke,outEa2={}", outEa2);

        if(!StringUtils.equalsIgnoreCase(fsEa,data.getFsEa()) || !StringUtils.equalsIgnoreCase(outEa,outEa2)) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s171,lang,null));
            return;
        }

        Department fsDepartment = qyweixinAccountSyncService.getFsDepartment(fsEa, data.getFsDepId());
        log.info("DepartmentDataMappingListener.invoke,fsDepartment={}", fsDepartment);
        if(fsDepartment==null || fsDepartment.getDepartmentId()==0) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s181,lang,null));
            return;
        }

        com.facishare.open.qywx.accountsync.result.Result<Boolean> depExist = qyweixinAccountSyncService.isQywxDepExist(crmAppId,
                outEa,
                data.getOutDepId());
        log.info("DepartmentDataMappingListener.invoke,depExist={}", depExist);
        if(!depExist.getData()) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s182,lang,null));
            return;
        }

        Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(),
                outEa,
                crmAppId,
                -1,
                Lists.newArrayList(data.getOutDepId()));
        log.info("DepartmentDataMappingListener.invoke,queryDepartmentBindByOutDepartment={}", queryDepartmentBindByOutDepartment);
        if(CollectionUtils.isNotEmpty(queryDepartmentBindByOutDepartment.getData())) {
            for(QyweixinAccountDepartmentMapping mapping : queryDepartmentBindByOutDepartment.getData()) {
                if(StringUtils.equalsIgnoreCase(mapping.getOutEa(),outEa)) {
                    importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s183,lang,null)+mapping.getFsDepartmentId());
                    return;
                }
                if(!StringUtils.equalsIgnoreCase(mapping.getFsEa(),fsEa)) {
                    importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s184,lang,null)+mapping.getFsEa());
                    return;
                }
            }
        }

        Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByFsDepartment = qyweixinAccountBindService.queryDepartmentBindByFsDepartment(SourceTypeEnum.QYWX.getSourceType(),
                fsEa,
                Lists.newArrayList(data.getFsDepId()));
        log.info("DepartmentDataMappingListener.invoke,queryDepartmentBindByFsDepartment={}", queryDepartmentBindByFsDepartment);
        if(CollectionUtils.isNotEmpty(queryDepartmentBindByFsDepartment.getData())) {
            for(QyweixinAccountDepartmentMapping mapping : queryDepartmentBindByFsDepartment.getData()) {
                if(StringUtils.equalsIgnoreCase(mapping.getFsEa(),fsEa)) {
                    importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s185,lang,null)+mapping.getOutDepartmentId());
                    return;
                }
                if(!StringUtils.equalsIgnoreCase(mapping.getOutEa(),outEa)) {
                    importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s186,lang,null)+mapping.getOutEa());
                    return;
                }
            }
        }

        Result<QyweixinAccountDepartmentMapping> departmentMapping = qyweixinAccountBindService.getQywxDepartmentMapping(data.getFsEa(),
                data.getFsDepId()+"",
                outEa,
                data.getOutDepId());
        log.info("DepartmentDataMappingListener.invoke,departmentMapping={}", departmentMapping);
        if(departmentMapping.getData()==null) {
            List<QyweixinAccountDepartmentMapping> departmentMappingList = new ArrayList<>();

            QyweixinAccountDepartmentMapping mapping = new QyweixinAccountDepartmentMapping();
            mapping.setSource(SourceTypeEnum.QYWX.getSourceType());
            mapping.setFsEa(fsEa);
            mapping.setFsDepartmentId(Integer.valueOf(data.getFsDepId()));
            mapping.setOutEa(outEa);
            mapping.setOutDepartmentId(data.getOutDepId());
            mapping.setAppId(crmAppId);
            mapping.setStatus(0);

            departmentMappingList.add(mapping);

            log.info("DepartmentDataMappingListener.invoke,departmentMappingList={}", departmentMappingList);
            try {
                Result<Boolean> result = qyweixinAccountBindService.bindAccountDepartmentMapping(departmentMappingList);
                log.info("DepartmentDataMappingListener.invoke,result={}", result);
                if(result.isSuccess() && result.getData()) {
                    importResult.incrInsert(1);
                } else {
                    importResult.addImportError(rowNo,result.getErrorMsg());
                }
            } catch (Exception e) {
                log.info("DepartmentDataMappingListener.invoke,exception={}", e.getMessage(),e);
                importResult.addInvokeExceptionRow(rowNo,e.getMessage());
            }
        } else {
            importResult.incrUpdate(1);
        }
    }
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get(I18NStringEnum.s187,lang,null) + "\n");
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s176.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s176.getI18nValue(),importResult.getInvokedNum(),importResult.getInsertNum(),importResult.getUpdateNum()),
                        Lists.newArrayList(
                                importResult.getInvokedNum()+"",importResult.getInsertNum()+"",importResult.getUpdateNum()+""
                        )))
                .append("\n");

        printMsg.append(i18NStringManager.get2(I18NStringEnum.s177.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s177.getI18nValue(),importResult.getImportErrorRows().size()),
                        Lists.newArrayList(
                                importResult.getImportErrorRows().size()+""
                        )))
                .append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s178.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s178.getI18nValue(),Joiner.on(",").join(v), k),
                            Lists.newArrayList(
                                    Joiner.on(",").join(v), k
                            )))
                    .append("\n");
        });
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s179.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s179.getI18nValue(),importResult.getInvokeExceptionRows().size()),
                        Lists.newArrayList(
                                importResult.getInvokeExceptionRows().size()+""
                        )))
                .append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s178.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s178.getI18nValue(),Joiner.on(",").join(v), k),
                            Lists.newArrayList(
                                    Joiner.on(",").join(v), k
                            )))
                    .append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }
}
