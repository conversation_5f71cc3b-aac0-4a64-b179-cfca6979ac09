package com.facishare.open.qywx.eventhandler.exception;

import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @ResponseBody
    @ExceptionHandler({ Exception.class, RuntimeException.class })
    public Result<?> unknownErrorHandler(Exception e) {
        log.error("unknownErrorHandler", e);
        return new Result<>().addError(ErrorRefer.INTERNAL_ERROR.getCode(), e.getClass().getSimpleName(),null);
    }

}
