package com.facishare.open.qywx.eventhandler.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.eventhandler.arg.fsAuthInfo;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.facishare.open.qywx.eventhandler.enums.UserContextSingleton;
import com.facishare.open.qywx.eventhandler.exception.NoPermissionException;
import com.facishare.open.qywx.eventhandler.utils.MD5Helper;
import com.facishare.uc.api.model.usertoken.User;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 用户权限验证拦截器
 * <AUTHOR>
 * @date 2023.5.16
 */
@Component
@Slf4j
public class UserInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

    private void getUserFromFsCookie(String fsAuthXCCookie,String requestURI) {
        log.info("UserInterceptor.getUserFromFsCookie,fsAuthXCCookie={}",fsAuthXCCookie);
        CookieToAuth.Argument argument = new CookieToAuth.Argument();
        argument.setCookie(fsAuthXCCookie);
        CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
        if (!result.isSucceed() || result.getValidateStatus()!= ValidateStatus.NORMAL) {
            log.info("UserInterceptor.getUserFromFsCookie,failed,result={}", result);
            //跨云解析cookie会导致失败,但是可以正常返回body
            if(ObjectUtils.isNotEmpty(result.getBody())) {
                AuthXC authXC = result.getBody();
                log.info("UserInterceptor.getUserFromFsCookie,cookieToAuthXC is false,authXC={}", JSON.toJSONString(authXC));
                Integer ei = authXC.getEnterpriseId();
                String ea = authXC.getEnterpriseAccount();
                Integer employeeId = authXC.getEmployeeId();
                UserContextSingleton.INSTANCE.setUserContext(new User(ei, ea, employeeId, authXC.getDeviceId()));
            }
            return;
        }
        AuthXC authXC = result.getBody();
        log.info("UserInterceptor.getUserFromFsCookie,authXC={}", JSON.toJSONString(authXC));

        Integer ei = authXC.getEnterpriseId();
        String ea = authXC.getEnterpriseAccount();
        Integer employeeId = authXC.getEmployeeId();

        //管理工具特殊鉴权逻辑
        if(StringUtils.containsIgnoreCase(requestURI,"/qyweixin/admin/")) {
            Map<String, List<Integer>> useToolsAccountMap = new Gson().fromJson(ConfigCenter.USE_TOOLS_ACCOUNT, new TypeToken<Map<String, List<Integer>>>() {
            }.getType());
            log.info("UserInterceptor.getUserFromFsCookie,useToolsAccountMap={}", useToolsAccountMap);
            log.info("UserInterceptor.getUserFromFsCookie,ea={},employeeId={}", ea,employeeId);
            if(useToolsAccountMap.containsKey(ea) && useToolsAccountMap.get(ea).contains(employeeId)) {
                //管理工具特殊身份验证通过
                log.info("UserInterceptor.getUserFromFsCookie,requestURI={},pass", requestURI);
                UserContextSingleton.INSTANCE.setUserContext(new User(ei, ea, employeeId, authXC.getDeviceId()));
                return;
            } else {
                throw new NoPermissionException("无权使用管理工具相关接口");
            }
        }

        //需要鉴权的接口名单，这些接口是企微管理后台WEB调用的接口
        for(String uri : ConfigCenter.needAuthInterfaceList) {
            if(StringUtils.containsIgnoreCase(requestURI,uri)) {
                log.info("UserInterceptor.preHandle,requestURI={},need auth",requestURI);
                try {
                    Result<Boolean> roleCode = fsEmployeeServiceProxy.hasRoleCode(ei + "",
                            employeeId + "",
                            ConfigCenter.validRoleCodeList);
                    log.info("UserInterceptor.getUserFromFsCookie,roleCode={}", roleCode);
                    if(roleCode.getData()==false) {
                        throw new NoPermissionException("用户没有权限访问当前URL");
                    }
                } catch (Exception e) {
                    log.info("UserInterceptor.getUserFromFsCookie,hasRoleCode,exception={}", e.getMessage());
                }
            }
        }

        UserContextSingleton.INSTANCE.setUserContext(new User(ei, ea, employeeId, authXC.getDeviceId()));
        log.info("UserInterceptor.getUserFromFsCookie,requestURI={},passed", requestURI);
    }

    private void FillUserContext(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (Objects.isNull(cookies)) {
            if(StringUtils.containsIgnoreCase(request.getRequestURI(),"/qyweixin/admin/")) {
                throw new NoPermissionException("无权使用管理工具相关接口");
            }
            return;
        }
        log.info("UserInterceptor.preHandle,cookies={}",JSONObject.toJSONString(cookies));
        for (Cookie cookie : cookies) {
            if(StringUtils.equalsIgnoreCase("lang",cookie.getName())) {
                String lang = cookie.getValue();
                log.info("UserInterceptor.preHandle,lang={}",lang);
                TraceUtil.setLocale(lang);
            }
            if ("FSAuthXC".equalsIgnoreCase(cookie.getName())) {
                getUserFromFsCookie(cookie.getValue(),request.getRequestURI());
            }
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        String requestURI = request.getRequestURI();
        List<String> headers = Collections.list(request.getHeaderNames());
        log.info("UserInterceptor.preHandle,requestURI={},headers={}",requestURI,JSONObject.toJSONString(headers));

        //需要验证纷享用户身份的接口
        FillUserContext(request);

        //需要业务调用鉴权的接口
        verifyFsAuth(request);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        //每次请求结束，清除当前线程数据 （tomcat是使用线程池来处理业务的）
        UserContextSingleton.INSTANCE.removeUserContext();
    }

    private void verifyFsAuth(HttpServletRequest request) {
        //过滤掉不需要业务鉴权的接口
        boolean isNeedVerify = Boolean.FALSE;
        for(String uri : ConfigCenter.verifyFsAuthUrlList) {
            if (StringUtils.containsIgnoreCase(request.getRequestURI(), uri)) {
                isNeedVerify = Boolean.TRUE;
                break;
            }
        }
        log.info("UserInterceptor.verifyOutAuth,isNeedVerify={}", isNeedVerify);
        if(!isNeedVerify) {
            return;
        }
        //获取业务key和secret
        String fsService = request.getHeader("fs-service");
        String fsKey = request.getHeader("fs-key");
        String fsSecret = request.getHeader("fs-secret");
        if(StringUtils.isAnyEmpty(fsService, fsKey, fsSecret)) {
            throw new NoPermissionException("当前业务没有权限访问当前URL");
        }
        //验证
        Map<String, fsAuthInfo> verifyOutAuthMap = new Gson().fromJson(ConfigCenter.verifyFsAuth, new TypeToken<Map<String, fsAuthInfo>>() {
        });
        log.info("UserInterceptor.verifyOutAuth,verifyOutAuthMap={}", verifyOutAuthMap);
        if(ObjectUtils.isEmpty(verifyOutAuthMap)) {
            return;
        }
        //加密后校验
        String fsServiceEncryption;
        String fsKeyEncryption;
        String fsSecretEncryption;
        try {
            fsServiceEncryption = MD5Helper.getStringMD5(fsService);
            fsKeyEncryption = MD5Helper.getStringMD5(fsKey);
            fsSecretEncryption = MD5Helper.getStringMD5(fsSecret);
        } catch (NoSuchAlgorithmException e) {
            throw new NoPermissionException("权限校验失败");
        }
        log.info("UserInterceptor.verifyOutAuth,fsServiceEncryption={},fsKeyEncryption={},fsSecretEncryption={}", fsServiceEncryption, fsKeyEncryption, fsSecretEncryption);
        if(!verifyOutAuthMap.containsKey(fsServiceEncryption)) {
            throw new NoPermissionException("当前业务没有权限访问当前URL，请输入正确的服务名");
        }
        fsAuthInfo fsAuthInfo = verifyOutAuthMap.get(fsServiceEncryption);
        if(!(fsKeyEncryption.equals(fsAuthInfo.getFsKey()) && fsSecretEncryption.equals(fsAuthInfo.getFsSecret()))) {
            throw new NoPermissionException("当前业务没有权限访问当前URL，请输入正确的业务key和secret");
        }
    }
}

