package com.facishare.open.qywx.eventhandler.controller.outer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.model.qyweixin.AppConfigInfo;
import com.facishare.open.qywx.accountsync.utils.QYWxCryptHelper;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.facishare.open.qywx.eventhandler.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.eventhandler.utils.SecurityUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;

/**
 * 接收企业微信回调数据
 * Created by liuwei on 2018/07/18
 */
@Slf4j
@RestController
@RequestMapping(value ="qyweixin")
public class EnterpriseWeChatEventController {
    @Autowired
    private AutoConfRocketMQProducer enterpriseWechatEventMQSender;
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayServiceNormal;

    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;

    Map<String, AppConfigInfo> appMetaInfo = Maps.newHashMap();

    @PostConstruct
    public void initAppMeta() {
        //在cms上的格式配置格式 appMetaInfoStr=source,appid,appsecret   保存的时候key使用source_appid
        ArrayList<AppConfigInfo> appMetaComponent = new Gson().fromJson(appMetaInfoCmsStr, new TypeToken<ArrayList<AppConfigInfo>>() {
        }.getType());
        log.info("EnterpriseWeChatEventController.initAppMeta, appMetaInfoStr={}, appMetaComponent={} ", appMetaInfoCmsStr, appMetaComponent);
        appMetaComponent.stream().forEach(v -> {
            v.setEncodingAESKeyBase64(Base64.decodeBase64(v.getEncodingAESKey() + "="));
            v.setToken(SecurityUtil.decryptStr(v.getToken()));
            v.setSecret(SecurityUtil.decryptStr(v.getSecret()));
            appMetaInfo.put(v.getAppId(), v);
        });
    }

    /**
     * 接收企业微信消息事件。
     *
     * 这里有很多事件。包括ticket接收，授权成功/变更通知，通讯录变更通知
     * */
    @RequestMapping(value = "/recvCmdEvent")
    public String recvCmdEvent(@RequestParam(value = "msg_signature") String msgSignature,
                               @RequestParam(value = "timestamp") String timeStamp,
                               @RequestParam(value = "nonce") String nonce,
                               @RequestParam(value = "echostr", required = false) String echostr,
                               @RequestParam(required = false) String appID,
                               @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvCmdEvent,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{},appID:{}",
                msgSignature, timeStamp, nonce, postData, echostr,appID);

        //微信端配置接口时验证路由
        if(!StringUtils.isEmpty(echostr)) {
            if(StringUtils.isEmpty(appID)) {
                appID = ConfigCenter.crmAppId;
            }
            return verifyURL(msgSignature, timeStamp, nonce, echostr, appID);
        }

        sendEventByDubbo(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD,msgSignature,timeStamp,nonce,echostr,postData,appID);
        return "success";
    }

    /**
     * 企微登录授权回调URL
     *
     * @param msgSignature
     * @param timeStamp
     * @param nonce
     * @param echostr
     * @param appID
     * @param postData
     * @return
     */
    @RequestMapping(value = "/recvCmdEvent2")
    public String recvCmdEvent2(@RequestParam(value = "msg_signature") String msgSignature,
                               @RequestParam(value = "timestamp") String timeStamp,
                               @RequestParam(value = "nonce") String nonce,
                               @RequestParam(value = "echostr", required = false) String echostr,
                               @RequestParam(required = false) String appID,
                               @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvCmdEvent2,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{},appID:{}",
                msgSignature, timeStamp, nonce, postData, echostr, appID);

        //微信端配置接口时验证路由
        if(!StringUtils.isEmpty(echostr)) {
            if(StringUtils.isEmpty(appID)) {
                appID = ConfigCenter.crmAppId;
            }
            return verifyURL(msgSignature, timeStamp, nonce, echostr, appID);
        }

        sendEventByDubbo(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD,msgSignature,timeStamp,nonce,echostr,postData,appID);
        return "success";
    }

    /**
     * 接收企业微信用户从应用session上行的消息。
     * */
    @RequestMapping(value = "/recvDataEvent")
    public String recvDataEvent(@RequestParam(value = "msg_signature") String msgSignature,
                                @RequestParam(value = "timestamp") String timeStamp,
                                @RequestParam(value = "nonce") String nonce,
                                @RequestParam(value = "echostr", required = false) String echostr,
                                @RequestParam(required = false) String appID,
                                @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvDataEvent,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{}",
                msgSignature, timeStamp, nonce, postData, echostr);

        if(!StringUtils.isEmpty(echostr)) {
            return verifyURL(msgSignature, timeStamp, nonce, echostr, appID);
        }

        sendEventByDubbo(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_DATA_EVENT_4_THIRD,msgSignature,timeStamp,nonce,echostr,postData,appID);
        return "success";
    }

    /**
     * 接收企业微信代开发应用事件。
     *
     * 这里有很多事件。包括ticket接收，授权成功，更改secret
     * */
    @RequestMapping(value = "/repMsgEvent")
    public String repMsgEvent(@RequestParam(value = "msg_signature") String msgSignature,
                               @RequestParam(value = "timestamp") String timeStamp,
                               @RequestParam(value = "nonce") String nonce,
                               @RequestParam(value = "echostr", required = false) String echostr,
                               @RequestParam(required = false) String appID,
                               @RequestBody(required = false) String postData) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("ControllerQyweixinCallback.repMsgEvent,msgSignature={}, timeStamp={}, nonce={}, postData={}, echostr={},appID={}",
                msgSignature, timeStamp, nonce, postData, echostr,appID);

        //微信端配置接口时验证路由
        if(!StringUtils.isEmpty(echostr)) {
            return verifyURL(msgSignature, timeStamp, nonce, echostr, appID);
        }

        sendEventByDubbo(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP,msgSignature,timeStamp,nonce,echostr,postData,appID);
        return "success";
    }

    /**
     * 系统事件接收URL
     * 应用管理 -通用开发参数-系统事件接收URL
     * */
    @RequestMapping(value = "/recvSystemDataUrl")
    public String recvSystemDataUrl(@RequestParam(value = "msg_signature") String msgSignature,
                                @RequestParam(value = "timestamp") String timeStamp,
                                @RequestParam(value = "nonce") String nonce,
                                @RequestParam(value = "echostr", required = false) String echostr,
                                @RequestParam(required = false) String appID,
                                @RequestBody(required = false) String postData) {

        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace(traceId);

        log.info("EnterpriseWeChatEventController.recvDataEvent,msgSignature:{}, timeStamp:{}, nonce:{}, postData:{}, echostr:{}",
                msgSignature, timeStamp, nonce, postData, echostr);

        if(!StringUtils.isEmpty(echostr)) {
            return verifyURL(msgSignature, timeStamp, nonce, echostr, appID);
        }

        sendEventByDubbo(EnterpriseWeChatEventTag.TAG_SYSTEM_EVENT_NOTIFY,msgSignature,timeStamp,nonce,echostr,postData,appID);
        return "success";
    }


    /**
     * 接收企业微信验证URL事件。
     */
    private String verifyURL(String msgSignature, String timeStamp, String nonce, String echostr, String appID) {
        String result = null;
        try {
            result = QYWxCryptHelper.VerifyURL(appMetaInfo.get(appID).getToken(), appMetaInfo.get(appID).getEncodingAESKeyBase64(), msgSignature, timeStamp, nonce, echostr);
            log.info("EnterpriseWeChatEventController.verifyURL,result={}", result);
        } catch (Exception e) {
            log.error("EnterpriseWeChatEventController.verifyURL,exception", e);
        }
        return result;
    }

    private void sendMQ(String tag, String msgSignature, String timeStamp, String nonce, String echostr, String postData) {
        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);

        log.info("EnterpriseWeChatEventController.sendMQ,eventProto={}", JSONObject.toJSONString(eventProto));

        Message msg = new Message();
        msg.setTags(tag);
        msg.setBody(eventProto.toProto());
        SendResult sendResult = enterpriseWechatEventMQSender.send(msg);
        log.info("EnterpriseWeChatEventController.sendMQ,sendResult={},eventProto={}", sendResult, JSONObject.toJSONString(eventProto));
    }

    public void sendEventByDubbo(String tag, String msgSignature, String timeStamp, String nonce, String echostr, String postData, String appId) {
        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setSignature(msgSignature);
        eventProto.setTimestamp(timeStamp);
        eventProto.setNonce(nonce);
        eventProto.setEchoStr(echostr);
        eventProto.setData(postData);
        eventProto.setAppId(appId);

        log.info("EnterpriseWeChatEventController.sendEventByDubbo,eventProto={}", JSONObject.toJSONString(eventProto));

        String result = null;
        if(StringUtils.equalsIgnoreCase(tag,EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD)) {
            result=qyweixinGatewayServiceNormal.onMsgEvent(eventProto);
        } else if(StringUtils.equalsIgnoreCase(tag,EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_DATA_EVENT_4_THIRD)) {
            result=qyweixinGatewayServiceNormal.onDataEvent(eventProto);
        } else if(StringUtils.equalsIgnoreCase(tag,EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP)) {
            result=qyweixinGatewayServiceNormal.onRepEvent(eventProto);
        } else if(StringUtils.equalsIgnoreCase(tag,EnterpriseWeChatEventTag.TAG_SYSTEM_EVENT_NOTIFY)) {
            result=qyweixinGatewayServiceNormal.onSystemData(eventProto);
        }

        else {
            log.info("EnterpriseWeChatEventController.sendEventByDubbo,not support tag,tag={}",tag);
        }
        log.info("EnterpriseWeChatEventController.sendEventByDubbo,result={}", result);
    }

    /**
     * 接收企业微信代开发应用事件。
     *
     * 这里有很多事件。包括ticket接收，授权成功，更改secret
     * */
//    @Deprecated
//    @RequestMapping(value = "/batchProduceMQ",method = RequestMethod.POST)
//    public String batchProduceMQ(@RequestParam Integer batchSize,@RequestParam String tag,@RequestBody EnterpriseWeChatEventProto eventProto) {
//        log.info("EnterpriseWeChatEventController.batchProduceMQ,batchSize={},eventProto={}", batchSize,JSONObject.toJSONString(eventProto));
//        new Thread(()->{
//            for(int i=0;i<batchSize;i++) {
//                Message msg = new Message();
//                msg.setTags(tag);
//                msg.setBody(eventProto.toProto());
//                SendResult sendResult = enterpriseWechatEventMQSender.send(msg);
//                log.info("EnterpriseWeChatEventController.batchProduceMQ,sendResult={},eventProto={}", sendResult, JSONObject.toJSONString(eventProto));
//            }
//        }).start();
//        return "call success";
//    }
}
