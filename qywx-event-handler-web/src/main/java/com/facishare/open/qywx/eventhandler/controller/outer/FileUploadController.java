package com.facishare.open.qywx.eventhandler.controller.outer;

import com.facishare.open.qywx.accountsync.arg.FileUploadArg;
import com.facishare.open.qywx.accountsync.model.FileUploadModel;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.FileUploadService;
import com.facishare.open.qywx.eventhandler.enums.UserContextSingleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

//TODO 只保留cookie
@Deprecated
@RestController
@Slf4j
@RequestMapping("/qyweixin/file")
public class FileUploadController {
    @Resource
    private FileUploadService fileUploadService;

    @RequestMapping(value = "/upload")
    public Result<Void> upload(@RequestParam(required = false) String fsEa,
                               @RequestParam List<String> nPathList,
                               @RequestParam(required = false) String type) {
        if(StringUtils.isEmpty(fsEa) && UserContextSingleton.INSTANCE.getUserContext()!=null) {
            fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        }
        if(StringUtils.isEmpty(type)) {
            type = "file";
        }
        if(StringUtils.isEmpty(fsEa) || CollectionUtils.isEmpty(nPathList)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        FileUploadArg arg = new FileUploadArg();
        arg.setFsEa(fsEa);
        arg.setFileType(FileUploadArg.FileTypeEnum.valueOf(type));

        List<FileUploadArg.FileItem> fileItemList = new ArrayList<>();
        for(String npath : nPathList) {
            fileItemList.add(new FileUploadArg.FileItem(npath,null,null,null,null,null));
        }
        arg.setFileList(fileItemList);
        return fileUploadService.upload(arg);
    }

    @RequestMapping(value = "/batchUpload",method = RequestMethod.POST)
    public Result<Void> batchUpload(@RequestBody FileUploadArg arg) {
        if(StringUtils.isEmpty(arg.getFsEa()) && UserContextSingleton.INSTANCE.getUserContext()!=null) {
            arg.setFsEa(UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount());
        }
        if(arg.getFileType()==null) {
            arg.setFileType(FileUploadArg.FileTypeEnum.file);
        }
        if(StringUtils.isEmpty(arg.getFsEa()) || CollectionUtils.isEmpty(arg.getFileList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return fileUploadService.upload(arg);
    }

    @RequestMapping(value = "/query")
    public Result<FileUploadModel> query(@RequestParam(required = false) String fsEa,
                                         @RequestParam(required = false) String npath,
                                         @RequestParam(required = false) String url) {
        if(StringUtils.isEmpty(fsEa) && UserContextSingleton.INSTANCE.getUserContext()!=null) {
            fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        }
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        if(StringUtils.isEmpty(npath) && StringUtils.isEmpty(url)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return fileUploadService.query(fsEa,npath,url);
    }
}