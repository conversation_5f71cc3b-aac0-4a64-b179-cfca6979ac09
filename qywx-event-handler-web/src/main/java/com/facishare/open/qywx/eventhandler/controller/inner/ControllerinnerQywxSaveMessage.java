package com.facishare.open.qywx.eventhandler.controller.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountinner.enums.AccountSyncTypeEnum;
import com.facishare.open.qywx.accountinner.model.AccountSyncConfigModel;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinContact;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinContactInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinExternalUserIdInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinOldExternalUserIdInfo;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.save.enums.ErrorRefer;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/29 12:11
 * 企业微信会话存档
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping("/erpdss/API/v1/rest/inner/qyweixin/message")
public class ControllerinnerQywxSaveMessage {

    @Autowired
    private MessageGeneratingService generatingService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    private static  Integer LIMIT=20;

    private static Integer OFFSET=0;

    //返回批量可以自动同步的企业（分页）
    @RequestMapping(value = "/autRetentionCorpBatch",method =RequestMethod.GET)
    public Result<List<String>> autRetentionCorpBatch(@RequestParam(value = "pageNum", required = false) int pageNum, @RequestParam(value = "pageSize", required = false) int pageSize) {
        List<String> autRetentionCorpBatch = qyweixinAccountSyncService.openAuthorizationByPage(pageNum, pageSize);
        return new Result<>(autRetentionCorpBatch);
    }

    //查询开启了外部联系人功能的员工id
    @RequestMapping(value = "/externalContactEmployeeId",method =RequestMethod.GET)
    public Result<Map<String, String>> externalContactEmployeeId(@RequestParam("ea") String ea,
                                                                 @RequestHeader(value = "dcInfo", required = false) String dcInfo){
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("externalContactEmployeeId,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        Map<String, String> externalContactEmployeeId = qyweixinAccountSyncService.externalContactEmployeeId2(ea, outEa);
        return new Result<>(externalContactEmployeeId);
    }

    //查询外部联系人列表详情
    @RequestMapping(value = "/externalContact",method =RequestMethod.POST)
    public Result<QyweixinContactInfo> externalContact(@RequestBody QyweixinContact qyweixinContact,
                                                       @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("externalContact,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        if(ObjectUtils.isEmpty(qyweixinContact)){
            return new Result<>(ErrorRefer.PARAMS_ERROR);
        }
        QyweixinContactInfo contactResult = qyweixinAccountSyncService.externalContact2(qyweixinContact.getEa(),
                qyweixinContact.getUserIds(),
                qyweixinContact.getNext_cursor(),
                qyweixinContact.getLimit(),
                outEa);
        if(ObjectUtils.isEmpty(contactResult)){
            return new Result<>(ErrorRefer.QUERRY_EMPTY);
        }
        return new Result<>(contactResult);
    }

    //针对企微对external_userid的改造，通过fs_ea获取external_userid
    @RequestMapping(value = "/switchExternalContactEmployeeId",method =RequestMethod.POST)
    public Result<List<QyweixinExternalUserIdInfo>> switchExternalContactEmployeeId(@RequestBody QyweixinOldExternalUserIdInfo qyweixinOldExternalUserIdInfos){
        if(StringUtils.isEmpty(qyweixinOldExternalUserIdInfos.getEa())
                || ObjectUtils.isEmpty(qyweixinOldExternalUserIdInfos.getExternalUserIds())) {
            return new Result<>();
        }
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalUserIdInfo>> result = qyweixinAccountSyncService.switchExternalContactEmployeeId2(qyweixinOldExternalUserIdInfos.getEa(),
                qyweixinOldExternalUserIdInfos.getExternalUserIds(),
                qyweixinOldExternalUserIdInfos.getOutEa());
        if(result.getErrorCode().equals("s120050000")) {
            return new Result<>(result.getData());
        } if(result.getErrorCode().equals("48002") || result.getErrorCode().equals("48001")) {
            return new Result<>(ErrorRefer.SWITCH_EXTERNAL_ERROR);
        } else {
            return new Result<List<QyweixinExternalUserIdInfo>>().addError(Integer.parseInt(result.getErrorCode()), result.getErrorMsg());
        }

    }

    //针对企微对external_userid的改造，改造完成后，停止接口转换
    @RequestMapping(value = "/finishExternalMigration",method =RequestMethod.POST)
    public Result<Void> finishExternalMigration(@RequestParam("ea") String ea){
        com.facishare.open.qywx.accountsync.result.Result<Void> result = qyweixinAccountSyncService.finishExternalMigration(ea);
        if(result.getErrorCode().equals("s120050000")) {
            return new Result<>(result.getData());
        }
        return new Result<>(ErrorRefer.PARAMS_ERROR);
    }

    /**
     * 获取客户同步配置信息
     * @return
     */
    @RequestMapping(value = "/getAccountSyncConfig",method =RequestMethod.GET)
    public com.facishare.open.qywx.accountsync.result.Result<List<AccountSyncConfigModel>> getAccountSyncConfig(@RequestParam("fsEa") String fsEa,
                                                                                                                @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("getAccountSyncConfig,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        String fs_ea = fsEa;
//        if(StringUtils.isEmpty(fsEa)) {
//            fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
//        } else {
//            fs_ea = fsEa;
//        }
        if(StringUtils.isEmpty(fs_ea)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(com.facishare.open.qywx.accountsync.result.ErrorRefer.INVALID_CALL);
        }
        com.facishare.open.qywx.accountsync.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult
                = qyweixinAccountBindInnerService.getEnterpriseMapping2(fs_ea,outEa);
        log.info("ControllerOpenQywxSaveMessage.getAccountSyncConfig,enterpriseMappingResult={}",enterpriseMappingResult);
        if(enterpriseMappingResult.getData()==null) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(com.facishare.open.qywx.accountsync.result.ErrorRefer.CORP_ACCOUNT_NOT_BIND);
        }

        if(StringUtils.isNotEmpty(enterpriseMappingResult.getData().getAccountSyncConfig())) {
            List<AccountSyncConfigModel> list = JSONObject.parseArray(enterpriseMappingResult.getData().getAccountSyncConfig(),AccountSyncConfigModel.class);
            return new com.facishare.open.qywx.accountsync.result.Result<>(list);
        }

        //初始化默认的配置
        AccountSyncConfigModel model = new AccountSyncConfigModel();
        model.setSyncType(AccountSyncTypeEnum.SYNC_TO_LEADS);
        model.setAutoSync(enterpriseMappingResult.getData().getOpenAuthorization()!=0);
        model.setSetting(new AccountSyncConfigModel.CheckDuplicateSetting(true,true,false));

        AccountSyncConfigModel model2 = new AccountSyncConfigModel();
        model2.setSyncType(AccountSyncTypeEnum.SYNC_TO_CONTACT);
        model2.setSetting(new AccountSyncConfigModel.CheckDuplicateSetting(true,true,false));

        AccountSyncConfigModel model3 = new AccountSyncConfigModel();
        model3.setSyncType(AccountSyncTypeEnum.SYNC_TO_ACCOUNT);
        model3.setSetting(new AccountSyncConfigModel.CheckDuplicateSetting(true,true,false));

        return new com.facishare.open.qywx.accountsync.result.Result(Lists.newArrayList(model,model2,model3));
    }

    /**
     * 查询开启会话的企业
     * @return
     */
    @RequestMapping(value = "/queryAllSetting",method =RequestMethod.GET)
    @ResponseBody
    public Result<List<String>> queryAllSetting() {
        return generatingService.queryAllSetting();
    }

    @RequestMapping(value = "/getMessageStorageLocation",method =RequestMethod.POST)
    @ResponseBody
    public Result<MessageStorageArg> getMessageStorageLocation(@RequestParam String fsEa,
                                                               @RequestParam(required = false) Integer version,
                                                               @RequestParam String serviceKey,
                                                               @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        if (StringUtils.isAnyEmpty(fsEa, serviceKey)) {
            return new Result<>(ErrorRefer.PARAMS_ERROR);
        }
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("getMessageStorageLocation,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return generatingService.getMessageStorageLocation(fsEa, version, serviceKey, outEa);
    }
}
