package com.facishare.open.qywx.eventhandler.controller.outer;

import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountinner.service.ExternalContactsService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinExternalUserIdInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinFollowUser;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinOpenUserIdInfo;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.facishare.open.qywx.eventhandler.enums.UserContextSingleton;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 外部联系人接口集合
 * <AUTHOR>
 * @date ********
 */
@RestController
@Slf4j
@RequestMapping("/qyweixin/external/contacts")
public class ExternalContactsController {
    @Resource
    private ExternalContactsService externalContactsService;

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @RequestMapping(value = "/detail")
    public Result<QyweixinExternalContactRsp> detail(@RequestParam String externalUserId,
                                                     @RequestParam(value = "outEa", required = false) String outEa) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Result<QyweixinExternalContactRsp> detailResult = externalContactsService.getDetail2(fsEa, externalUserId, outEa);
        if(detailResult.isSuccess() && ObjectUtils.isNotEmpty(detailResult.getData()) && fsEa.equals(ConfigCenter.SERVICE_PROVIDER)) {
            Result<List<QyweixinExternalUserIdInfo>> externalContactEmployeeIdResult = qyweixinAccountSyncService.switchExternalContactEmployeeId2(fsEa,
                    Lists.newArrayList(detailResult.getData().getExternal_contact().getExternal_userid()),
                    outEa);
            if(externalContactEmployeeIdResult.isSuccess() && CollectionUtils.isNotEmpty(externalContactEmployeeIdResult.getData())) {
                detailResult.getData().getExternal_contact().setExternal_userid(externalContactEmployeeIdResult.getData().get(0).getNew_external_userid());
            }
            List<String> userIds = detailResult.getData().getFollow_user().stream().map(QyweixinFollowUser::getUserid).collect(Collectors.toList());
            List<QyweixinOpenUserIdInfo> userIdInfos = new LinkedList<>();
            for (int i = 0; i < userIds.size(); i += 900) {
                int end = Math.min(userIds.size(), i + 900);
                List<String> batch = userIds.subList(i, end);
                Result<List<QyweixinOpenUserIdInfo>> employeeIdResult = qyweixinAccountSyncService.switchEmployeeId2(fsEa, batch, outEa);
                if(employeeIdResult.isSuccess() && org.apache.commons.collections.CollectionUtils.isNotEmpty(employeeIdResult.getData())) {
                    userIdInfos.addAll(employeeIdResult.getData());
                }
            }
            log.info("ControllerOpenQYWeixin.getQyweixinExternalContact,,userIdInfos={}", userIdInfos);
            Map<String, QyweixinOpenUserIdInfo> userIdInfoMap = userIdInfos.stream()
                    .collect(Collectors.toMap(QyweixinOpenUserIdInfo::getUserid, Function.identity(), (v1, v2) -> v1));
            for(QyweixinFollowUser user : detailResult.getData().getFollow_user()) {
                if(userIdInfoMap.containsKey(user.getUserid())) {
                    user.setUserid(userIdInfoMap.get(user.getUserid()).getOpen_userid());
                }
            }
        }
        return detailResult;
    }


    /**
     * 企业可通过此接口获取指定成员添加的客户列表。客户是指配置了客户联系功能的成员所添加的外部联系人。
     * 没有配置客户联系功能的成员，所添加的外部联系人将不会作为客户返回。
     * @param fsUserId 纷享企业的员工ID，需要转换成企微的员工ID
     * @return 客户列表
     */
    @RequestMapping(value = "/getExternalContactList")
    public Result<List<String>> getExternalContactList(@RequestParam String fsUserId,
                                                       @RequestParam(value = "outEa", required = false) String outEa) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Result<List<String>> contactListResult = externalContactsService.getExternalContactListEx(fsEa, fsUserId, outEa);
        if(contactListResult.isSuccess() && CollectionUtils.isNotEmpty(contactListResult.getData()) && fsEa.equals(ConfigCenter.SERVICE_PROVIDER)) {
            List<QyweixinExternalUserIdInfo> externalContactEmployeeIds = new LinkedList<>();
            for (int i = 0; i < contactListResult.getData().size(); i += 900) {
                int end = Math.min(contactListResult.getData().size(), i + 900);
                List<String> batch = contactListResult.getData().subList(i, end);
                Result<List<QyweixinExternalUserIdInfo>> externalContactEmployeeIdResult = qyweixinAccountSyncService.switchExternalContactEmployeeId2(fsEa, batch, outEa);
                if(externalContactEmployeeIdResult.isSuccess() && CollectionUtils.isNotEmpty(externalContactEmployeeIdResult.getData())) {
                    externalContactEmployeeIds.addAll(externalContactEmployeeIdResult.getData());
                }
            }
            //剩下的是可以得，直接返回成功转换的
            List<String> externals = externalContactEmployeeIds.stream().map(QyweixinExternalUserIdInfo::getNew_external_userid).collect(Collectors.toList());
            log.info("ControllerOpenQYWeixin.getGroupChatDetail,externals={}", externals);
            return new Result<>(externals);
        }
        return contactListResult;
    }
}