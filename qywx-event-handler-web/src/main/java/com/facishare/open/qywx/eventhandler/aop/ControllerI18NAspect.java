package com.facishare.open.qywx.eventhandler.aop;

import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.eventhandler.enums.UserContextSingleton;
import com.facishare.open.qywx.i18n.I18NStringManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-10-12
 * 国际化专用AOP
 */
@Slf4j
//@Aspect
//@Order(1)
@Component
public class ControllerI18NAspect {
    @Autowired
    private I18NStringManager i18NStringManager;

    //@Around("execution(* com.facishare.open.qywx.eventhandler.controller.outer.*.*(..)) ")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        log.info("ControllerI18NAspect.around,begin");
        Object result = joinPoint.proceed();
        log.info("ControllerI18NAspect.around,result={}", result);

        //获取前端当前语言，只能使用这个key，不能从cookie里面获取，因为这个request的已经被CEP改变
        String lang = TraceUtil.getLocale();
        String tenantId = null;
        log.info("ControllerI18NAspect.around,userContext={}", UserContextSingleton.INSTANCE.getUserContext());
        if(UserContextSingleton.INSTANCE.getUserContext()!=null) {
            tenantId = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId()+"";
        }
        log.info("ControllerI18NAspect.around,lang={},tenantId={}", lang,tenantId);

        if (result != null) {
            Result result2 = null;
            if(result instanceof Result) {
                result2 = (Result) result;
            }
            log.info("ControllerI18NAspect.around,result2={}", result);
            if(result2!=null) {
                if(StringUtils.isNotEmpty(result2.getI18nKey())) {
                    result2.setErrorMsg(i18NStringManager.get2(result2.getI18nKey(), lang,tenantId, result2.getErrorMsg(),result2.getI18nExtra()));
                    result2.setI18nKey(null);
                    result2.setI18nExtra(null);
                }
                log.info("ControllerI18NAspect.around,end,result2={}", result2);
            }
        }
        log.info("ControllerI18NAspect.around,end,result={}", result);
        return result;
    }
}
