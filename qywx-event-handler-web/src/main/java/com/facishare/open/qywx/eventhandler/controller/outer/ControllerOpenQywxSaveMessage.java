package com.facishare.open.qywx.eventhandler.controller.outer;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.enums.AccountSyncTypeEnum;
import com.facishare.open.qywx.accountinner.model.AccountSyncConfigModel;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.eventhandler.enums.UserContextSingleton;
import com.facishare.open.qywx.eventhandler.utils.RSAUtil;
import com.facishare.open.qywx.save.arg.ConditionQueryMessageArg;
import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.facishare.open.qywx.save.enums.ErrorRefer;
import com.facishare.open.qywx.save.result.FileMessageResult;
import com.facishare.open.qywx.save.result.Pager;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.restful.common.StopWatch;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/29 12:11
 * 企业微信会话存档
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping("/open/qyweixin/message")
public class ControllerOpenQywxSaveMessage {

    @Autowired
    private MessageGeneratingService generatingService;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private SaveMessageService saveMessageService;

    private static  Integer LIMIT=20;

    private static Integer OFFSET=0;

    //保存会话设置
    @RequestMapping(value = "/saveSetting",method = RequestMethod.POST)
    public Result<Void> saveSetting(@RequestBody GenerateSettingVo generateSettingVo,
                                    @RequestHeader(value = "dcInfo", required = false) String dcInfo){
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("saveSetting,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        //判断是否在可操作范围内
        LocalTime now = LocalTime.now();
        LocalTime startTime = LocalTime.of(0, 0);
        LocalTime endTime = LocalTime.of(7, 0);
        if (startTime.isBefore(now) && now.isBefore(endTime)) {
            return new Result<>(ErrorRefer.TIME_ERROR);
        }
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer enterpriseId= UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId();
        log.info("ControllerOpenQywxSaveMessage.saveSetting,ea={},enterpriseId={}", ea, enterpriseId);
        generateSettingVo.setEa(ea);
        generateSettingVo.setFsTenantId(enterpriseId);
        //使用代开发进行授权，当客户删除代开发， corpSecret和agentId会出现空的现象，但是前端页面做了限制，只有授权了代开发才可以使用会话的功能，所以这里不用做限制
        if(StringUtils.isAnyEmpty(generateSettingVo.getSecret(),generateSettingVo.getPrivateKey(),generateSettingVo.getPublicKey())
                || ObjectUtils.isEmpty(generateSettingVo.getVersion())){
            return new Result<>(ErrorRefer.PARAMS_ERROR);
        }
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
                ea,
                outEa);
        if(ObjectUtils.isEmpty(result.getData()))return new Result<>(ErrorRefer.ENTERPRISE_NOT_BING_ERROR);
        generateSettingVo.setQywxCorpId(result.getData().getOutEa());
        QyweixinCorpRep qywxCorpBindInfo = qyweixinAccountSyncService.getQYWXCorpBindInfo(result.getData().getOutEa());
        log.info("ControllerOpenQywxSaveMessage.saveSetting,qywxCorpBindInfo={}", qywxCorpBindInfo);
        generateSettingVo.setCorpSecret(qywxCorpBindInfo.getPermanentCode());
        generateSettingVo.setAgentId(qywxCorpBindInfo.getAgentId());
        //插入corpSecret需要检验token
        String token = qyweixinAccountSyncService.getToken(generateSettingVo.getQywxCorpId(), generateSettingVo.getCorpSecret());
        if(token == null || token.length() == 0) {
            return new Result<>(ErrorRefer.SECRET_ERROR);
        }
        log.info("trace saveSetting info:{}",generateSettingVo);
        Result<Integer> saveSettingResult = generatingService.saveSetting(generateSettingVo);
//        if(!row.isSuccess())return new Result<>(ErrorRefer.SYSTEM_ERROR);
//        Result<Boolean> booleanResult = saveMessageService.preserveWeChatConversionObj(enterpriseId);
//        log.info("ControllerOpenQywxSaveMessage.saveSetting,booleanResult={}", booleanResult);
        if(!saveSettingResult.isSuccess()) {
            return new Result<Void>().addError(saveSettingResult.getErrorCode(), saveSettingResult.getErrorMsg());
        }
        return new Result<>();
    }

    //返回ssl公钥

    @RequestMapping(value = "/generatePublicKey",method = RequestMethod.GET)
    public Result<Map<String,String>> generatePublicKey(){
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        try {
            Map<String, String> keyMap = RSAUtil.initKey();
            return new Result<>(keyMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new Result<>();
    }

    //返回设置
    @RequestMapping("/querySetting")
    public Result<GenerateSettingVo> querySetting(@RequestHeader(value = "dcInfo", required = false) String dcInfo){
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("querySetting,dcInfo={},connectParam={}",dcInfo,connectParam);

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return generatingService.querySettingByAuto(fsEa, null, outEa);
    }


    //返回查询消息
    @RequestMapping(value="/queryMessage",method = RequestMethod.POST)
    public Result<Pager<FileMessageResult>> queryMessage(@RequestBody ConditionQueryMessageArg arg,
                                                         @RequestHeader(value = "dcInfo", required = false) String dcInfo){
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("queryMessage,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
        log.info("queryMessage,outEa={}",outEa);

        StopWatch messageWatch=StopWatch.create("querymessage");
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer ei = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        //如果没有设置会话存档的配置，直接返回提醒
        Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting(ea, null, outEa);
        if(ObjectUtils.isEmpty(generateSettingVoResult.getData())){
            return new Result<>(ErrorRefer.ENTERPRISE_NOT_SAVE);
        }
        //查询是否需要返回信息
        MessageStorageArg messageStorageArg = generateSettingVoResult.getData().getStorageLocation();
        if(messageStorageArg.getSalesRetentionType() == 0) {
            return new Result<>(ErrorRefer.STORAGE_NOT_SET_SALES_RECODE);
        }
        messageWatch.lap("querySetting");
        StringBuilder accountBuilder=new StringBuilder();
        String fsAccount= accountBuilder.append("E.").append(ea).append(".").append(userId).toString();
        List<String> accountList= Lists.newArrayList();
        accountList.add(fsAccount);
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult = qyweixinAccountBindService.fsAccountToOutAccountBatchV21(SourceTypeEnum.QYWX.getSourceType(),
                mainAppId,
                0,
                accountList,
                outEa);
        messageWatch.lap("batchAccount");
        String outAccount= accountResult.getData().get(0).getOutAccount();
//        if(StringUtils.isNotEmpty(isvAccount)){
//            //需要替换成明文的员工id。现在没有明确标识数据库哪个字段是明文员工id
//            com.facishare.open.qywx.accountsync.result.Result<QyweixinEmployeeInfo> userInfoFromSelf = qyweixinAccountSyncService.getUserInfoFromSelf(generateSettingVoResult.getData().getCorpSecret(), generateSettingVoResult.getData().getQywxCorpId(), isvAccount);
//            if(userInfoFromSelf.isSuccess()){
//                outAccount=isvAccount;
//            }
//        }
        outEa=accountResult.getData().get(0).getOutEa();
        log.info("queryMessage,outEa.2={}",outEa);
//        List<String> userIds=Lists.newArrayList(arg.getReceiveIds(),outAccount);
        QueryMessageArg queryMessageArg=new QueryMessageArg();
        queryMessageArg.setEmpId(userId);
        queryMessageArg.setFsEa(ea);
        queryMessageArg.setSenderIds(outAccount);
        queryMessageArg.setReceiveIds(arg.getReceiveIds());
        queryMessageArg.setPageSize(arg.getPageSize());
        queryMessageArg.setPageNum(arg.getPageNum());
        queryMessageArg.setLabelValue(arg.getLabelValue());
        queryMessageArg.setOutEa(outEa);
        queryMessageArg.setRoomId(arg.getRoomId());
        //得到对应的企业微信用户id
        Result<Pager<FileMessageResult>> pagerResult = saveMessageService.conditionQueryMessage(queryMessageArg);
        messageWatch.lap("queryMessage");
        messageWatch.log();
        return pagerResult;
    }

    //返回外部联系人列表
    @RequestMapping(value = "/queryContactList",method =RequestMethod.GET)
    public Result<List<QyweixinExternalContactInfo>> queryContactList(@RequestHeader(value = "dcInfo", required = false) String dcInfo){
        if(ObjectUtils.isEmpty(UserContextSingleton.INSTANCE)){
            return new Result<>(ErrorRefer.USER_NOT_LOGIN);
        }
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("queryContactList,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        //如果没有在授权设置，直接提醒没有授权
        Result<GenerateSettingVo> settingResult = generatingService.querySetting(ea,null, outEa);
        log.info("queryContactList settingResult:{}",settingResult);
        if(ObjectUtils.isEmpty(settingResult.getData())||ObjectUtils.isEmpty(settingResult.getData().getCorpSecret())){
            return new Result<>(ErrorRefer.ENTERPRISE_NO_SETTING);
        }
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalContactInfo>> contactResult = qyweixinAccountSyncService.queryExternalContactList(settingResult.getData(), userId);
        return new Result<>(contactResult.getData());
    }

    //自动留存开关
    @RequestMapping(value = "/autRetention",method =RequestMethod.GET)
    public Result autRetention(@RequestParam("flag") int flag,
                               @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("autRetention,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        String fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        if(flag != 1 && flag != 0) {
            return new Result(ErrorRefer.PARAMS_ERROR);
        }
        com.facishare.open.qywx.accountsync.result.Result aut = qyweixinAccountSyncService.autRetention2(fs_ea, flag, outEa);
        if(!"s120050000".equals(aut.getErrorCode())) {
            return new Result(ErrorRefer.DATABASE_RETURN_NULL);
        }
        return new Result();
    }

    //返回批量可以自动同步的企业（分页）
    @Deprecated
    @RequestMapping(value = "/autRetentionCorpBatch",method =RequestMethod.GET)
    public Result<List<String>> autRetentionCorpBatch(@RequestParam(value = "pageNum", required = false) int pageNum, @RequestParam(value = "pageSize", required = false) int pageSize) {
        List<String> autRetentionCorpBatch = qyweixinAccountSyncService.openAuthorizationByPage(pageNum, pageSize);
        return new Result<>(autRetentionCorpBatch);
    }

    //查询开启了外部联系人功能的员工id
    @Deprecated
    @RequestMapping(value = "/externalContactEmployeeId",method =RequestMethod.GET)
    public Result<Map<String, String>> externalContactEmployeeId(@RequestParam("ea") String ea,
                                                                 @RequestHeader(value = "dcInfo", required = false) String dcInfo){
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("externalContactEmployeeId,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        Map<String, String> externalContactEmployeeId = qyweixinAccountSyncService.externalContactEmployeeId2(ea, outEa);
        return new Result<>(externalContactEmployeeId);
    }

    //查询外部联系人列表详情
    @Deprecated
    @RequestMapping(value = "/externalContact",method =RequestMethod.POST)
    public Result<QyweixinContactInfo> externalContact(@RequestBody QyweixinContact qyweixinContact,
                                                       @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("externalContact,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        if(ObjectUtils.isEmpty(qyweixinContact)){
            return new Result<>(ErrorRefer.PARAMS_ERROR);
        }
        QyweixinContactInfo contactResult = qyweixinAccountSyncService.externalContact2(qyweixinContact.getEa(),
                qyweixinContact.getUserIds(),
                qyweixinContact.getNext_cursor(),
                qyweixinContact.getLimit(),
                outEa);
        if(ObjectUtils.isEmpty(contactResult)){
            return new Result<>(ErrorRefer.QUERRY_EMPTY);
        }
        return new Result<>(contactResult);
    }

    //针对企微对external_userid的改造，通过fs_ea获取external_userid
    @Deprecated
    @RequestMapping(value = "/switchExternalContactEmployeeId",method =RequestMethod.POST)
    public Result<List<QyweixinExternalUserIdInfo>> switchExternalContactEmployeeId(@RequestBody QyweixinOldExternalUserIdInfo qyweixinOldExternalUserIdInfos){
        if(StringUtils.isEmpty(qyweixinOldExternalUserIdInfos.getEa())
                || ObjectUtils.isEmpty(qyweixinOldExternalUserIdInfos.getExternalUserIds())) {
            return new Result<>();
        }
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalUserIdInfo>> result = qyweixinAccountSyncService.switchExternalContactEmployeeId2(qyweixinOldExternalUserIdInfos.getEa(),
                qyweixinOldExternalUserIdInfos.getExternalUserIds(),
                qyweixinOldExternalUserIdInfos.getOutEa());
        if(result.getErrorCode().equals("s120050000")) {
            return new Result<>(result.getData());
        } if(result.getErrorCode().equals("48002") || result.getErrorCode().equals("48001")) {
            return new Result<>(ErrorRefer.SWITCH_EXTERNAL_ERROR);
        } else {
            return new Result<List<QyweixinExternalUserIdInfo>>().addError(Integer.parseInt(result.getErrorCode()), result.getErrorMsg());
        }

    }

    //针对企微对external_userid的改造，改造完成后，停止接口转换
    @Deprecated
    @RequestMapping(value = "/finishExternalMigration",method =RequestMethod.POST)
    public Result<Void> finishExternalMigration(@RequestParam("ea") String ea){
        com.facishare.open.qywx.accountsync.result.Result<Void> result = qyweixinAccountSyncService.finishExternalMigration(ea);
        if(result.getErrorCode().equals("s120050000")) {
            return new Result<>(result.getData());
        }
        return new Result<>(ErrorRefer.PARAMS_ERROR);
    }

    //开启授权
    @Deprecated
    @RequestMapping(value = "/openAuthorization",method =RequestMethod.GET)
    public Result openAuthorization(@RequestParam("flag") int flag,
                                    @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("openAuthorization,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        SimpleDateFormat dateFormater = new SimpleDateFormat("HHmm");
        String date = dateFormater.format(new Date());
        System.out.println(date);
        int time = Integer.parseInt(date);
        if (time >= 2200 && time < 2400) {
            return new Result(ErrorRefer.TIME_ERROR);
        }
        String fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        if(flag != 1 && flag != 0) {
            return new Result(ErrorRefer.PARAMS_ERROR);
        }
        com.facishare.open.qywx.accountsync.result.Result aut = qyweixinAccountSyncService.openAuthorization2(fs_ea, flag, outEa);
        if(!"s120050000".equals(aut.getErrorCode())) {
            return new Result(ErrorRefer.DATABASE_RETURN_NULL);
        }
        return new Result();
    }

    //得到授权开关信息
    @RequestMapping(value = "/getAuthorization",method =RequestMethod.GET)
    @Deprecated
    public Result getAuthorization(@RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("getAuthorization,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        String fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        com.facishare.open.qywx.accountsync.result.Result aut = qyweixinAccountSyncService.getAuthorization2(fs_ea, outEa);
        if(!"s120050000".equals(aut.getErrorCode())) {
            return new Result(ErrorRefer.DATABASE_RETURN_NULL);
        }
        return new Result(aut.getData());
    }

    /**
     * 更新客户同步配置
     * @param modelList
     * @return
     */
    @RequestMapping(value = "/accountSyncConfig",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result accountSyncConfig(@RequestBody List<AccountSyncConfigModel> modelList,
                                                                               @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("accountSyncConfig,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        if(CollectionUtils.isEmpty(modelList)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(com.facishare.open.qywx.accountsync.result.ErrorRefer.PARAM_ERROR);
        }
        String fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        if(StringUtils.isEmpty(fs_ea)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(com.facishare.open.qywx.accountsync.result.ErrorRefer.INVALID_CALL);
        }
        return qyweixinAccountBindInnerService.updateAccountSyncConfig2(fs_ea,modelList,outEa);
    }

    /**
     * 获取客户同步配置信息
     * @return
     */

    @Deprecated
    @RequestMapping(value = "/getAccountSyncConfig",method =RequestMethod.GET)
    public com.facishare.open.qywx.accountsync.result.Result<List<AccountSyncConfigModel>> getAccountSyncConfig(@RequestParam("fsEa") String fsEa,
                                                                                                                @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("getAccountSyncConfig,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        String fs_ea = null;
        if(StringUtils.isEmpty(fsEa)) {
            fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        } else {
            fs_ea = fsEa;
        }
        if(StringUtils.isEmpty(fs_ea)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(com.facishare.open.qywx.accountsync.result.ErrorRefer.INVALID_CALL);
        }
        com.facishare.open.qywx.accountsync.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult
                = qyweixinAccountBindInnerService.getEnterpriseMapping2(fs_ea,outEa);
        log.info("ControllerOpenQywxSaveMessage.getAccountSyncConfig,enterpriseMappingResult={}",enterpriseMappingResult);
        if(enterpriseMappingResult.getData()==null) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(com.facishare.open.qywx.accountsync.result.ErrorRefer.CORP_ACCOUNT_NOT_BIND);
        }

        if(StringUtils.isNotEmpty(enterpriseMappingResult.getData().getAccountSyncConfig())) {
            List<AccountSyncConfigModel> list = JSONObject.parseArray(enterpriseMappingResult.getData().getAccountSyncConfig(),AccountSyncConfigModel.class);
            return new com.facishare.open.qywx.accountsync.result.Result<>(list);
        }

        //初始化默认的配置
        AccountSyncConfigModel model = new AccountSyncConfigModel();
        model.setSyncType(AccountSyncTypeEnum.SYNC_TO_LEADS);
        model.setAutoSync(enterpriseMappingResult.getData().getOpenAuthorization()!=0);
        model.setSetting(new AccountSyncConfigModel.CheckDuplicateSetting(true,true,false));

        AccountSyncConfigModel model2 = new AccountSyncConfigModel();
        model2.setSyncType(AccountSyncTypeEnum.SYNC_TO_CONTACT);
        model2.setSetting(new AccountSyncConfigModel.CheckDuplicateSetting(true,true,false));

        AccountSyncConfigModel model3 = new AccountSyncConfigModel();
        model3.setSyncType(AccountSyncTypeEnum.SYNC_TO_ACCOUNT);
        model3.setSetting(new AccountSyncConfigModel.CheckDuplicateSetting(true,true,false));

        return new com.facishare.open.qywx.accountsync.result.Result(Lists.newArrayList(model,model2,model3));
    }

    /**
     * 转换会话账号
     * @return
     */
    @RequestMapping(value = "/switchMessageUser",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.accountsync.result.Result switchMessageUser(@RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("switchMessageUser,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        String fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        if(StringUtils.isEmpty(fs_ea)) {
            return com.facishare.open.qywx.accountsync.result.Result.newInstance(com.facishare.open.qywx.accountsync.result.ErrorRefer.INVALID_CALL);
        }
        return qyweixinAccountSyncService.switchMessageUser2(fs_ea, outEa);
    }

    /**
     * 查询开启会话的企业
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/queryAllSetting",method =RequestMethod.GET)
    @ResponseBody
    public Result<List<String>> queryAllSetting() {
        return generatingService.queryAllSetting();
    }

    @RequestMapping(value = "/getMessageStorageLocation",method =RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Result<MessageStorageArg> getMessageStorageLocation(@RequestParam String fsEa,
                                                               @RequestParam(required = false) Integer version,
                                                               @RequestParam String serviceKey,
                                                               @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        if (StringUtils.isAnyEmpty(fsEa, serviceKey)) {
            return new Result<>(ErrorRefer.PARAMS_ERROR);
        }
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("getMessageStorageLocation,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return generatingService.getMessageStorageLocation(fsEa, version, serviceKey, outEa);
    }

    //保存会话设置
    @RequestMapping(value = "/eventcall",method = RequestMethod.POST)
    public Result<Void> saveSetting(HttpServletRequest httpServletRequest){
        log.info("http request message data:{}",httpServletRequest);
        try {
            String result = httpServletRequest.getInputStream().toString();
            log.info("event call result message:{}",result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new Result<>();
    }
}
