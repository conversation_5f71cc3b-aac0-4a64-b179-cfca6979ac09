package com.facishare.open.qywx.eventhandler.aop;

import com.alibaba.dubbo.common.URL;
import com.alibaba.fastjson.JSON;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.HttpArg;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.facishare.open.qywx.eventhandler.enums.UserContextSingleton;
import com.facishare.open.qywx.eventhandler.exception.NoPermissionException;
import com.facishare.open.qywx.eventhandler.manager.OANewBaseManager;
import com.facishare.open.qywx.eventhandler.utils.HttpProxyUtil;
import com.facishare.uc.api.model.usertoken.User;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class HttpProxyInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private HttpProxyUtil httpProxyUtil;
    @ReloadableProperty("qywx_ip_url")
    private String qywxIpUrl;

    @Resource
    private OANewBaseManager oANewBaseManager;
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        LogUtils.info("HttpProxyInterceptor.preHandle,request={},response={},handler={}", request, response, handler);

        String uri = request.getRequestURI();

        for(String notSupportUri : ConfigCenter.qywxNotSupportUrl) {
            if(StringUtils.containsIgnoreCase(uri, notSupportUri)) {
                LogUtils.info("HttpProxyInterceptor.preHandle,uri={}", uri);
                return true;
            }
        }


        //拼接参数，转到纷享云
        HttpArg arg = new HttpArg();
        String param = request.getQueryString();
        String method = request.getMethod();
        String url = qywxIpUrl + uri;
        //其他环境的contextPath为 feishu-web，需要替换为

        LogUtils.info("HttpProxyInterceptor.preHandle,param={},method={},url={}", param, method, url);
        if(StringUtils.isNotEmpty(param)) {
            url = url +  "?" + param;
        }

        // 获取到请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        LogUtils.info("HttpProxyInterceptor.preHandle,headerNames={}", headerNames);
        Map<String, String> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if(StringUtils.equalsIgnoreCase(headerName,"host")
                    || StringUtils.equalsIgnoreCase(headerName,"Accept-Encoding")) continue;
            String header = request.getHeader(headerName);
            headerMap.put(headerName, header);
        }

        arg.setMethod(method);
        arg.setHeader(headerMap);
        arg.setUrl(url);

        boolean isNewBase = false;
        try {
            isNewBase = isNewBase(request, arg);
        } catch (Exception e) {
            LogUtils.info("HttpProxyInterceptor.preHandle,error,arg={}", arg);
            return true;
        }
        log.info("HttpProxyInterceptor.preHandle,isNewBase={}",isNewBase);

        if(isNewBase) {
            try {
                arg.setBody(getParameters(request));
                String result = httpProxyUtil.httpProxyRequest(arg);
                LogUtils.info("HttpProxyInterceptor.preHandle,result={},arg={}", result, arg);

                //输出流
                response.setCharacterEncoding("UTF-8");
                ServletOutputStream outputStream = response.getOutputStream();
                outputStream.write(result.getBytes(StandardCharsets.UTF_8));
                outputStream.flush();
                return false;
            } catch (IOException e) {
                return true;
            }
        }
        return true;
    }

    /**
     *
     * @param request 入参
     * @return com.alibaba.fastjson.JSONObject
     * @Description: 作用:  获取参数
     */
    public static String getParameters(HttpServletRequest request) {
        try {
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
            StringBuilder responseStrBuilder = new StringBuilder();
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null)
                responseStrBuilder.append(inputStr);
            if(ObjectUtils.isEmpty(responseStrBuilder)) {
                return null;
            }
            String bodyString = responseStrBuilder.toString();
            LogUtils.info("HttpProxyInterceptor.preHandle,bodyString={}", bodyString);
            return bodyString;
        } catch (Exception e) {
            LogUtils.error("获取json参数失败", e);
            return null;
        }
    }

    private boolean isNewBase(HttpServletRequest request, HttpArg arg) {
        //param
        String fsEa = URL.valueOf(arg.getUrl()).getParameter("ea");
        if(StringUtils.isEmpty(fsEa)) {
            fsEa = URL.valueOf(arg.getUrl()).getParameter("fsEa");
        }

        if(StringUtils.isNotEmpty(fsEa)) {
            return oANewBaseManager.canRunInNewBaseByFsEa(fsEa);
        }

        String outEa = URL.valueOf(arg.getUrl()).getParameter("outEa");
        if(StringUtils.isEmpty(fsEa)) {
            outEa = URL.valueOf(arg.getUrl()).getParameter("corpId");
        }

        if (StringUtils.isNotEmpty(outEa)) {
            return oANewBaseManager.canRunInNewBaseByOutEa(outEa);
        }


        //cookie
        Cookie[] cookies = request.getCookies();
        if(ObjectUtils.isNotEmpty(cookies)) {
            for (Cookie cookie : cookies) {
                if ("FSAuthXC".equalsIgnoreCase(cookie.getName())) {
                    fsEa = getUserFromFsCookie(cookie.getValue());
                    if(StringUtils.isNotEmpty(fsEa)) {
                        return oANewBaseManager.canRunInNewBaseByFsEa(fsEa);
                    }
                }
            }
        }
        return false;
    }

    private String getUserFromFsCookie(String fsAuthXCCookie) {
        try {
            log.info("HttpProxyInterceptor.getUserFromFsCookie,fsAuthXCCookie={}",fsAuthXCCookie);
            CookieToAuth.Argument argument = new CookieToAuth.Argument();
            argument.setCookie(fsAuthXCCookie);
            CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
            AuthXC authXC = result.getBody();
            String fsEa = authXC.getEnterpriseAccount();
            log.info("HttpProxyInterceptor.getUserFromFsCookie,fsEa={}",fsEa);
            return fsEa;
        } catch (Exception e) {
            return null;
        }
    }
}
