package com.facishare.open.qywx.eventhandler.utils.xml;

import com.google.common.base.MoreObjects;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;


@XStreamAlias("xml")
@Data
public class SuiteAuthXml {

    @XStreamAlias("SuiteId")
    private String suiteId;

    @XStreamAlias("InfoType")
    private String infoType;

    @XStreamAlias("TimeStamp")
    private long timestamp;

    @XStreamAlias("SuiteTicket")
    private String suiteTicket;

    @Override
    public String toString() {
        return "SuiteAuthXml{" +
                "suiteId='" + suiteId + '\'' +
                ", infoType='" + infoType + '\'' +
                ", timestamp=" + timestamp +
                ", suiteTicket='" + suiteTicket + '\'' +
                '}';
    }
}
