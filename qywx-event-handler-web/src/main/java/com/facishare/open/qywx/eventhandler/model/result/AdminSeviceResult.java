package com.facishare.open.qywx.eventhandler.model.result;

import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import lombok.Data;

import java.io.Serializable;

/**
 * 纷享管理后台使用到
 * Created by <PERSON><PERSON><PERSON> on 2018/09/29
 */
@Data
public class AdminSeviceResult<T> implements Serializable {

    private String errorCode;
    private String errorMsg;
    private T value;

    public boolean isSuccess() {
        return ("s120050000".equals(errorCode));
    }


    public AdminSeviceResult<T> success(T value){
        this.value = value;
        this.errorCode = ErrorRefer.SUCC.getCode();
        return this;
    }

    public AdminSeviceResult<T> error(T value, String errorCode, String errorMsg){
        this.value = value;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        return this;
    }

    public AdminSeviceResult<T> result(T value, String errorCode, String errorMsg){
        this.value = value;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        return this;
    }

}
