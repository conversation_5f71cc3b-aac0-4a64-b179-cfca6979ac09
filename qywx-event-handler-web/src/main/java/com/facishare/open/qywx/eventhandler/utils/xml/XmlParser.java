package com.facishare.open.qywx.eventhandler.utils.xml;

import com.thoughtworks.xstream.XStream;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

/**
 * Author: Ansel <PERSON>ao
 * Create Time: 15/9/22
 */
//TODO 后面研究下Converter把xml的转化做的更简单一些
public class XmlParser {

    @SuppressWarnings("unchecked")
    public static <T> T fromXml(String strXml, Class<T> clazz) {
        XStream xStream = new XStream();
        xStream.processAnnotations(clazz);
        xStream.setMode(XStream.NO_REFERENCES);
        xStream.ignoreUnknownElements();
        return (T)xStream.fromXML(strXml);
    }

    public static <T> String toXml(T bean) {
        XStream xStream = new XStream();
        xStream.processAnnotations(bean.getClass());
        return xStream.toXML(bean);
    }

    public static boolean containTag(String xmltext, String tagName)       {
        Object[] result = new Object[3];
        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            DocumentBuilder db = dbf.newDocumentBuilder();
            StringReader sr = new StringReader(xmltext);
            InputSource is = new InputSource(sr);
            Document document = db.parse(is);

            Element root = document.getDocumentElement();
            NodeList nodelist = root.getElementsByTagName(tagName);
            if(null == nodelist || nodelist.getLength() == 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
