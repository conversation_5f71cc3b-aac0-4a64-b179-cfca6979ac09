package com.facishare.open.qywx.eventhandler.mongo.dao;

import com.facishare.open.qywx.accountsync.mongo.document.MessageSaveDoc;
import com.facishare.open.qywx.eventhandler.mongo.store.MessageSaveMongoStore;
import com.facishare.open.qywx.save.arg.QueryMessageArg;
import com.google.common.collect.Lists;
import com.mongodb.MongoException;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.currentDate;

/**
 * 会话存档操作mongo的dao类封装
 * <AUTHOR>
 * @date 2022/12/28
 */
@Slf4j
@Repository
public class MessageSaveMongoDao {
    public static final String f_id = "_id";
    public static final String f_ei = "ei";
    public static final String f_fsEa = "fsEa";
    public static final String f_messageId = "messageId";
    public static final String f_seq = "seq";
    public static final String f_keyVersion = "keyVersion";
    public static final String f_fromUser = "fromUser";
    public static final String f_toList = "toList";
    public static final String f_roomId = "roomId";
    public static final String f_messageTime = "messageTime";
    public static final String f_messageType = "messageType";
    public static final String f_content = "content";
    public static final String f_md5sum = "md5sum";
    public static final String f_sdkFileId = "sdkFileId";
    public static final String f_fileSize = "fileSize";
    public static final String f_npath = "npath";
    public static final String f_fileName = "fileName";
    public static final String f_fileExt = "fileExt";
    public static final String f_messageData = "messageData";
    public static final String f_createTime = "createTime";
    public static final String f_updateTime = "updateTime";

    private final MessageSaveMongoStore store;

    public MessageSaveMongoDao(MessageSaveMongoStore store) {
        this.store = store;
    }

    private List<ObjectId> convertObjectIds(Collection<String> ids) {
        List<ObjectId> objIds = ids.stream()
                .filter(ObjectId::isValid)
                .map(v -> new ObjectId(v)).collect(Collectors.toList());
        return objIds;
    }

    private static ObjectId getObjId(String id) {
        return new ObjectId(id);
    }

    private int update(Integer ei, List<Bson> filters, List<Bson> updates) {
        updates.add(currentDate(f_updateTime));
        Bson filter = and(filters);
        Bson update = combine(updates);
        UpdateResult updateResult = store.getOrCreateCollection(ei).updateOne(filter, update);
        return (int) updateResult.getModifiedCount();
    }


    public List<MessageSaveDoc> listByTenantId(Integer ei, Integer offset, Integer limit) {
        List<MessageSaveDoc> res = new ArrayList<>();
        store.getOrCreateCollection(ei).find().limit(limit).skip(offset).into(res);
        return res;
    }

    /**
     * 忽略结果
     *
     * @param messageSaveDoc
     * @return 1 成功 0 失败
     */

    public int insertIgnore(MessageSaveDoc messageSaveDoc) {
        MongoCollection<MessageSaveDoc> collection = store.getOrCreateCollection(messageSaveDoc.getEi());
        try {
            collection.insertOne(messageSaveDoc);
        } catch (MongoException mongoException) {
            log.error("sync data insert exception", mongoException);
            return 0;
        }
        return 1;
    }


    public MessageSaveDoc getById(Integer ei, @NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        MessageSaveDoc messageSaveDoc = store.getOrCreateCollection(ei).find(eq(new ObjectId(id))).limit(1).first();
        return messageSaveDoc;
    }


    public MessageSaveDoc getSimpleById(Integer ei, @NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        MessageSaveDoc messageSaveDoc = store.getOrCreateCollection(ei)
                .find(eq(new ObjectId(id)))
                .limit(1).first();
        return messageSaveDoc;
    }


    public List<MessageSaveDoc> listByIds(Integer ei, Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<MessageSaveDoc> res = new ArrayList<>();
        store.getOrCreateCollection(ei).find(filter).into(res);
        return res;
    }


    public List<MessageSaveDoc> listSimpleByIds(Integer ei, Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(f_id, objIds);
        List<MessageSaveDoc> res = new ArrayList<>();
        store.getOrCreateCollection(ei).find(filter).into(res);
        return res;
    }

    public void save(MessageSaveDoc messageSaveDoc) {
        log.info("MessageSaveMongoDao.save,messageSaveDoc={}",messageSaveDoc);
        Bson filter = and(eq(f_id, messageSaveDoc.getId()));
        store.getOrCreateCollection(messageSaveDoc.getEi()).replaceOne(filter, messageSaveDoc, new ReplaceOptions().upsert(true));
    }

    /**
     * 批量插入或替换记录
     *
     * @param ei
     * @param docList
     * @return
     */
    public BulkWriteResult batchReplace(Integer ei, Collection<MessageSaveDoc> docList) {
        log.info("MessageSaveMongoDao.batchReplace,docList.size={}", docList.size());
        List<WriteModel<MessageSaveDoc>> request = new ArrayList<>();
        for (MessageSaveDoc doc : docList) {
            Bson filter = and(eq(f_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection(ei)
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("MessageSaveMongoDao.batchReplace,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }


    /**
     * 批量更新，无则不插入。
     *
     * @param ei
     * @param docList
     * @return insert count
     */
    public BulkWriteResult batchUpdate(Integer ei, Collection<MessageSaveDoc> docList) {
        log.info("MessageSaveMongoDao.batchUpdate,docList.size={}", docList.size());
        List<WriteModel<MessageSaveDoc>> request = new ArrayList<>();
        for (MessageSaveDoc doc : docList) {
            Bson filter = and(eq(f_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection(ei)
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("MessageSaveMongoDao.batchUpdate,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }

    public List<MessageSaveDoc> pageByFilters(Integer ei, QueryMessageArg arg) {
        List<MessageSaveDoc> res = new ArrayList<>();
        Bson filter = buildFilter1(arg);
        int offset=(arg.getPageNum()-1)*arg.getPageSize();
        store.getOrCreateCollection(ei).find(filter)
                .sort(Sorts.descending(f_messageTime))
                .skip(offset)
                .limit(arg.getPageSize())
                .into(res);
        return res;
    }

    public List<MessageSaveDoc> queryMessages(Integer ei, QueryMessageArg arg) {
        List<MessageSaveDoc> res = new ArrayList<>();
        Bson filter = buildFilter1(arg);
        store.getOrCreateCollection(ei).find(filter)
                .sort(Sorts.descending(f_messageTime))
                .into(res);
        return res;
    }

    public FindIterable<MessageSaveDoc> queryRoomIds(Integer ei, QueryMessageArg arg) {
        Bson filter = buildFilter2(arg);
        Bson projections = Projections.fields(Projections.include(f_roomId));
        FindIterable<MessageSaveDoc> projection = store.getOrCreateCollection(ei).find(filter)
                .projection(projections);
        return projection;
    }

    public List<MessageSaveDoc> queryLastSeq(Integer ei, String ea) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_fsEa, ea));
        Bson filter = Filters.and(filters);
        List<MessageSaveDoc> res = new ArrayList<>();
        store.getOrCreateCollection(ei).find(filter)
                .sort(Sorts.descending(f_messageTime))
                .limit(1)
                .into(res);
        return res;
    }

    public List<MessageSaveDoc> queryPreMessageTime(Integer ei, QueryMessageArg arg) {
        Bson filter = buildFilter(arg);
        List<MessageSaveDoc> res = new ArrayList<>();
        store.getOrCreateCollection(ei)
                .find(filter)
                .limit(arg.getLimit())
                .sort(Sorts.descending(f_messageTime))
                .into(res);
        return res;
    }

    public DeleteResult deleteTableData(Integer ei, String ea) {
        log.info("MessageSaveMongoDao.deleteTableData,ei={},ea={}", ei,ea);
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_fsEa, ea));
        Bson filter = Filters.and(filters);
        DeleteResult deleteResult = store.getOrCreateCollection(ei).deleteMany(filter);
        log.info("MessageSaveMongoDao.deleteTableData,deleteResult={}", deleteResult);
        return deleteResult;
    }

    private Bson buildFilter(QueryMessageArg arg) {
        if(StringUtils.isEmpty(arg.getRoomId())) {
            arg.setRoomId(null);
        }
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_fsEa, arg.getFsEa()),
                Filters.eq(f_roomId, arg.getRoomId()));

        if(StringUtils.isNotEmpty(arg.getSenderIds()) && StringUtils.isNotEmpty(arg.getReceiveIds())) {
            List<Bson> filters1 = Lists.newArrayList(Filters.eq(f_fromUser, arg.getSenderIds()),
                    Filters.all(f_toList, arg.getReceiveIds()));
            Bson filter1 = Filters.and(filters1);

            List<Bson> filters2 = Lists.newArrayList(Filters.eq(f_fromUser, arg.getReceiveIds()),
                    Filters.all(f_toList, arg.getSenderIds()));
            Bson filter2 = Filters.and(filters2);

            filters.add(Filters.or(filter1, filter2));
        }
        Bson filter = Filters.and(filters);
        return filter;
    }

    private Bson buildFilter1(QueryMessageArg arg) {
        if(StringUtils.isEmpty(arg.getRoomId())) {
            arg.setRoomId(null);
        }
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_fsEa, arg.getFsEa()),
                Filters.eq(f_roomId, arg.getRoomId()));

        if(StringUtils.isNotEmpty(arg.getSenderIds()) && StringUtils.isNotEmpty(arg.getReceiveIds())) {
            List<Bson> filters1 = Lists.newArrayList(Filters.eq(f_fromUser, arg.getSenderIds()),
                    Filters.all(f_toList, arg.getReceiveIds()));
            Bson filter1 = Filters.and(filters1);

            List<Bson> filters2 = Lists.newArrayList(Filters.eq(f_fromUser, arg.getReceiveIds()),
                    Filters.all(f_toList, arg.getSenderIds()));
            Bson filter2 = Filters.and(filters2);

            filters.add(Filters.or(filter1, filter2));
        }

        if(ObjectUtils.isNotEmpty(arg.getLastMessageTime())) {
            filters.add(Filters.lte(f_messageTime, arg.getLastMessageTime()));
        }
        if(ObjectUtils.isNotEmpty(arg.getPreMessageTime())) {
            filters.add(Filters.gte(f_messageTime, arg.getPreMessageTime()));
        }
        Bson filter = Filters.and(filters);
        return filter;
    }

    private Bson buildFilter2(QueryMessageArg arg) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(f_fsEa, arg.getFsEa()),
                Filters.ne(f_roomId, null));

        List<Bson> filters1 = Lists.newArrayList(Filters.eq(f_fromUser, arg.getSenderIds()),
                Filters.all(f_toList, arg.getReceiveIds()));
        Bson filter1 = Filters.and(filters1);

        List<Bson> filters2 = Lists.newArrayList(Filters.eq(f_fromUser, arg.getReceiveIds()),
                Filters.all(f_toList, arg.getSenderIds()));
        Bson filter2 = Filters.and(filters2);

        filters.add(Filters.or(filter1, filter2));
        filters.add(Filters.lte(f_messageTime, arg.getLastMessageTime()));
        filters.add(Filters.gte(f_messageTime, arg.getPreMessageTime()));

        Bson filter = Filters.and(filters);
        return filter;
    }
}
