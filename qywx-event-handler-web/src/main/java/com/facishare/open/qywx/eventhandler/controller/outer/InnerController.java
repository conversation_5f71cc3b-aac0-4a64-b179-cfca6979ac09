package com.facishare.open.qywx.eventhandler.controller.outer;

import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 企微连接器内部服务，仅供纷享内部服务调用，比如集成平台调企微连接器，
 * 这里不要放其它服务
 * <AUTHOR>
 * @date 2024.05.11
 */
@Deprecated
@RestController
@Slf4j
@RequestMapping("/qyweixin/inner")
public class InnerController {
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    /**
     * 检查和更新企微连接器，用于历史企业自动刷企微连接器订单
     * @param fsEa
     * @return
     */
    @RequestMapping(value = "/checkAndInitConnector",method = RequestMethod.GET)
    public Result<Void> checkAndInitConnector(@RequestParam String fsEa,
                                              @RequestParam String dataCenterId){
        log.info("checkAndInitConnector,fsEa={},dataCenterId={}",fsEa,dataCenterId);
        return qyweixinGatewayInnerService.checkAndInitConnector(fsEa, dataCenterId);
    }

    @RequestMapping(value = "/checkAndInitConnector2",method = RequestMethod.GET)
    public Result<Void> checkAndInitConnector2(@RequestParam String fsEa){
        log.info("checkAndInitConnector2,fsEa={}",fsEa);
        return new Result<>();
    }
}
