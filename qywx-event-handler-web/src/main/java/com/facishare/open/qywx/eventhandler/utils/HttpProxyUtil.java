package com.facishare.open.qywx.eventhandler.utils;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.qywx.accountinner.arg.HttpArg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HttpProxyUtil {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    public String httpProxyRequest(HttpArg body) {
        if(body.getMethod().equals("GET")) {
            return proxyHttpClient.getUrl(body.getUrl(), body.getHeader());
        }
        return proxyHttpClient.postUrl(body.getUrl(), body.getBody(), body.getHeader());
    }

    public <T> T httpProxyRequest(HttpArg body, TypeReference<T> typeReference) {
        if(body.getMethod().equals("GET")) {
            return proxyHttpClient.getUrl(body.getUrl(), body.getHeader(), typeReference);
        }
        return proxyHttpClient.postUrl(body.getUrl(), body.getBody(), body.getHeader(), typeReference);
    }
}
