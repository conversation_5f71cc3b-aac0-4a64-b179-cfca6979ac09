package com.facishare.open.qywx.eventhandler.controller.inner;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.eventhandler.enums.SourceTypeEnum;
import com.facishare.open.qywx.eventhandler.model.qyweixin.QyweixinExchangeId;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 内部服务访问接口
 */
@RestController
@Slf4j
@RequestMapping("/erpdss/API/v1/rest/inner/qyweixin/")
public class ControllerInnerQYWeixin {

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayServiceNormal;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 计算出可见范围内的员工数量。默认CRM应用
     * @param fsEa
     * @return
     */
    @RequestMapping(value = "/getCountQyweixinPrivilegeEmployee", method = RequestMethod.GET)
    public Result<Integer> getCountQyweixinPrivilegeEmployee(@RequestParam String fsEa){
        return qyweixinAccountSyncService.getCountQyweixinPrivilegeEmployee(fsEa);
    }

    /**
     * 绑定老的纷享企业，出现同步待办消息异常
     * @param fsEa
     * @return
     */
    @RequestMapping(value = "/notifyHistoryToDoMessageGatewayBatch", method = RequestMethod.GET)
    public Result<Object> notifyHistoryToDoMessageGatewayBatch(@RequestParam String fsEa){
        if(StringUtils.isBlank(fsEa)){
            return new Result<>().addError(ErrorRefer.INTERNAL_ERROR.getCode(), ErrorRefer.INTERNAL_ERROR.name(), I18NStringEnum.s4.getI18nKey());
        }
        qyweixinGatewayServiceNormal.notifyHistoryToDoMessageGateway(Arrays.asList(fsEa.split(",")));
        return new Result<>();
    }

    @RequestMapping(value = "/getEmployeeBasicInfoByMobile", method = RequestMethod.GET)
    public Result<FsEmployeeBasicInfo> getEmployeeBasicInfoByMobile(@RequestParam(value = "ea") String ea,
                                                 @RequestParam(value = "mobile") String mobile){
        return qyweixinAccountSyncService.getEmployeeBasicInfoByMobile(ea, mobile);
    }

    /**
     * 更新企业绑定表拓展字段
     * 1、留资功能：函数调用此接口更新拓展字段的是否已留资的字段
     * @return
     */
    @RequestMapping(value = "/enterprise/updateEnterpriseExtend", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> updateEnterpriseExtend(@RequestParam String fsEa,
                                               @RequestParam String extendField,
                                               @RequestParam Object extendValue,
                                               @RequestParam(value = "outEa", required = false) String outEa) {
        if (StringUtils.isEmpty(fsEa) || StringUtils.isEmpty(extendField) || ObjectUtils.isEmpty(extendValue)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.updateEnterpriseExtend2(fsEa, extendField, extendValue, outEa);
    }

    /**
     * 通过纷享ea获取企业的绑定关系
     */

    @RequestMapping(value = "/enterprise/getOutEaByFsEa", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> getOutEaByFsEa(@RequestParam String fsEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getOutEaResultByFsEa(fsEa);
    }

    /**
     * 通过纷享ea获取企业的绑定关系，适用于一个CRM对多个企微的场景
     */
    @RequestMapping(value = "/enterprise/getOutEaListByFsEa", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<String>> getOutEaListByFsEa(@RequestParam String fsEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getOutEaResultListByFsEa(fsEa);
    }

    /**
     * 通过企微ea获取企业的绑定关系
     */
    @RequestMapping(value = "/enterprise/getFsEaByOutEa", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<String>> getFsEaByOutEa(@RequestParam String outEa) {
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getFsEaResultByOutEa(outEa);
    }

    @PostMapping("/fsIds2OpenIds")
    public Result<List<QyweixinExchangeId.Result>> getOutAccountsByFsAccounts(@RequestBody QyweixinExchangeId.Argument argument) {
        List<QyweixinExchangeId.Result> result = new ArrayList<>();
        String fsEa = argument.getEa();
        List<Integer> ids = argument.getFsIds();
        int type = argument.getType();
        if (StringUtils.isBlank(fsEa) || CollectionUtils.isEmpty(ids)) return new Result<>(result);
        log.info("ControllerQYWeixin.getOutAccountsByFsAccounts,argument={}", argument);
        ids = ids.stream().filter(id -> !"null".equalsIgnoreCase(String.valueOf(id))).collect(Collectors.toList());
        result = type == 1 ? processDepartment(fsEa, ids) : processEmployeeAccount(fsEa, ids);
        log.info("ControllerQYWeixin.getOutAccountsByFsAccounts,result={}", result);
        return new Result<>(result);
    }

    private List<QyweixinExchangeId.Result> processDepartment(String fsEa, List<Integer> departmentIds) {
        List<QyweixinExchangeId.Result> result = new ArrayList<>();
        Map<Integer, String> map = qyweixinAccountBindInnerService.queryDepartmentBind(SourceTypeEnum.QYWX.getSourceType(), fsEa, departmentIds)
                .getData().parallelStream()
                .collect(Collectors.toMap(QyweixinAccountDepartmentMapping::getFsDepartmentId,
                        QyweixinAccountDepartmentMapping::getOutDepartmentId,
                        (k1, k2) -> k2));
        departmentIds.forEach(departmentId -> {
            String outDepartmentId = map.get(departmentId);
            if (StringUtils.isNotBlank(outDepartmentId)){
                result.add(new QyweixinExchangeId.Result(departmentId, outDepartmentId));
            }
        });
        log.info("ControllerQYWeixin.processDepartment success. fsEa:{}, map:{}, departmentIds:{}, result:{}", fsEa, map, departmentIds, result);
        return result;
    }

    private List<QyweixinExchangeId.Result> processEmployeeAccount(String fsEa, List<Integer> userIds) {
        List<QyweixinExchangeId.Result> result = new ArrayList<>();
        List<String> fsAccounts = userIds.parallelStream().map(userId -> Joiner.on(".").join("E", fsEa, userId)).collect(Collectors.toList());
        String corpId = qyweixinAccountBindService.fsEaToOutEa(SourceTypeEnum.QYWX.getSourceType(), fsEa).getData();
        Map<String, String> map = qyweixinAccountBindInnerService.outAccountToFsAccount(SourceTypeEnum.QYWX.getSourceType(), corpId, fsAccounts).getData()
                .parallelStream()
                .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
                        QyweixinAccountEmployeeMapping::getOutAccount,
                        (key1, key2) -> key2));
        // 保持有序
        userIds.forEach(userId -> {
            String outAccount = map.get(Joiner.on(".").join("E", fsEa, userId));
            if (StringUtils.isNotBlank(outAccount)){
                result.add(new QyweixinExchangeId.Result(userId, outAccount));
            }
        });
        log.info("ControllerQYWeixin.processEmployeeAccount success. fsEa:{}, map:{}, userIds:{}, result:{}", fsEa, map, userIds, result);
        return result;
    }

    @RequestMapping(value = "/getDial", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    public Result<String> Dial(@RequestParam String callerEi,
                               @RequestParam String callerUid,
                               @RequestParam String calleeEa) throws IOException {
        String source="qywx";
        log.info("ControllerQYWeixin.Dial,callerEi={},callerUid={},calleeEa={}", callerEi,callerUid,calleeEa);
        Integer nCallerEi = null;
        try {
            nCallerEi=Integer.parseInt(callerEi);
        } catch (Exception e) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String callerEa = eieaConverter.enterpriseIdToAccount(nCallerEi);
        String fsCaller="E."+callerEa+"."+callerUid;
        log.info("ControllerQYWeixin.Dial,fsCaller={}", fsCaller);
        // 在db把fscaller换成userid,用下面接口实现
        com.facishare.open.qywx.accountbind.result.Result<Map<String, String>> userMap =
                qyweixinAccountBindService.fsAccountToOutAccountBatch(source, ConfigCenter.crmAppId, Lists.newArrayList(fsCaller));
        log.info("ControllerQYWeixin.Dial,userMap={}", userMap);
        String callerQywxUid=userMap.getData().get(fsCaller);
        log.info("ControllerQYWeixin.Dial,callerQywxUid={}", callerQywxUid);
        //企业内部换外部账号
        com.facishare.open.qywx.accountbind.result.Result<String> corpId = qyweixinAccountBindService.fsEaToOutEa(source, calleeEa);
        log.info("ControllerQYWeixin.Dial,corpId={}", corpId);
        Result<String> result = qyweixinGatewayServiceNormal.doDial(callerQywxUid, corpId.getData());
        log.info("ControllerQYWeixin.Dial,result={}", result);
        return result;
    }
}
