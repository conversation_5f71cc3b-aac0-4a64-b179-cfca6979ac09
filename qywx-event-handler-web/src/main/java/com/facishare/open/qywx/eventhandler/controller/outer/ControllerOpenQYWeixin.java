package com.facishare.open.qywx.eventhandler.controller.outer;

import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.model.info.FsEmployeeDetailInfo;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.model.QyweixinDepartmentBindModel;
import com.facishare.open.qywx.accountinner.model.QyweixinEmployeeBindModel;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinDepartmentBind;
import com.facishare.open.qywx.accountinner.model.qywx.QyweixinEmployeeBind;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.model.IntelligentAppInfoResult;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.eventhandler.config.ConfigCenter;
import com.facishare.open.qywx.eventhandler.enums.UserContextSingleton;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * www.xxxx.com/open/qyweixin 会转发到 open.xxx.com/open/qyweixin
 * 解决跨域的问题
 * Created by liuwei on 2018/10/18
 */
@RestController
@Slf4j
@RequestMapping("/open/qyweixin/")
public class ControllerOpenQYWeixin {

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayServiceNormal;

    @Autowired
    private ContactBindInnerService contactBindInnerService;

    @ReloadableProperty("repAppId")
    private String repAppId;

    /**
     * 批量获取外部联系人详情
     * @param request
     * @return
     */
    @RequestMapping(value = "/getQyweixinExternalContact", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<QyweixinExternalContactInfo>> getQyweixinExternalContact(@RequestBody Map<String, Object> request,
                                                                                @RequestParam(value = "appId", required = false) String appId,
                                                                                @RequestParam(value = "outEa", required = false) String outEa) {
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId()+"";
        String corpId=fsEaToOutEa(ea);
        ArrayList<String> userIDs = (ArrayList<String>) request.get("userIDs");
        if(null == userIDs || userIDs.isEmpty()){
            return new Result<List<QyweixinExternalContactInfo>>().Result(ErrorRefer.INTERNAL_ERROR.getCode(), "缺少参数userIDs",null);
        }

        //上架了多个应用到企业微信，如果业务方不传应用id, 默认就是用第一个上架的CRM应用的appid.
        String realAppId = ConfigCenter.crmAppId;
        if(null != appId) {
            realAppId = appId;
        }
        Result<QyweixinCorpBindInfo> corpBindInfo = qyweixinAccountSyncService.getCorpBindInfo(corpId, repAppId);
        if(corpBindInfo.isSuccess() && corpBindInfo.getData()!=null) {
            realAppId = repAppId;
        }
        Result<List<QyweixinExternalContactInfo>> externalContactsResult = qyweixinAccountSyncService.getQyweixinExternalContacts(realAppId,
                corpId,
                ea,
                "E."+ea+"."+userId,
                userIDs);
        if(externalContactsResult.isSuccess() && CollectionUtils.isNotEmpty(externalContactsResult.getData()) && ea.equals(ConfigCenter.SERVICE_PROVIDER)) {
            List<String> externalUserIds = externalContactsResult.getData().stream()
                    .map(QyweixinExternalContactInfo::getExternalUserid)
                    .collect(Collectors.toList());
            log.info("ControllerOpenQYWeixin.getQyweixinExternalContact,externalUserIds={}", externalUserIds);
            List<String> userIds = new LinkedList<>();
            for(QyweixinExternalContactInfo info : externalContactsResult.getData()) {
                userIds.addAll(info.getFollowUserList().stream().map(QyweixinFollowUser::getUserid).collect(Collectors.toList()));
            }
            log.info("ControllerOpenQYWeixin.getQyweixinExternalContact,userIds={}", userIds);
            List<QyweixinExternalUserIdInfo> externalContactEmployeeIds = new LinkedList<>();
            for (int i = 0; i < externalUserIds.size(); i += 900) {
                int end = Math.min(externalUserIds.size(), i + 900);
                List<String> batch = externalUserIds.subList(i, end);
                Result<List<QyweixinExternalUserIdInfo>> externalContactEmployeeIdResult = qyweixinAccountSyncService.switchExternalContactEmployeeId2(ea, batch, outEa);
                if(externalContactEmployeeIdResult.isSuccess() && CollectionUtils.isNotEmpty(externalContactEmployeeIdResult.getData())) {
                    externalContactEmployeeIds.addAll(externalContactEmployeeIdResult.getData());
                }
            }

            List<QyweixinOpenUserIdInfo> userIdInfos = new LinkedList<>();
            for (int i = 0; i < userIds.size(); i += 900) {
                int end = Math.min(userIds.size(), i + 900);
                List<String> batch = userIds.subList(i, end);
                Result<List<QyweixinOpenUserIdInfo>> employeeIdResult = qyweixinAccountSyncService.switchEmployeeId2(ea, batch, outEa);
                if(employeeIdResult.isSuccess() && CollectionUtils.isNotEmpty(employeeIdResult.getData())) {
                    userIdInfos.addAll(employeeIdResult.getData());
                }
            }
            log.info("ControllerOpenQYWeixin.getQyweixinExternalContact,externalContactEmployeeIds={},userIdInfos={}", externalContactEmployeeIds, userIdInfos);
            Map<String, QyweixinExternalUserIdInfo> externalUserIdInfoMap = externalContactEmployeeIds.stream()
                    .collect(Collectors.toMap(QyweixinExternalUserIdInfo::getExternal_userid, Function.identity(), (v1, v2) -> v1));
            Map<String, QyweixinOpenUserIdInfo> userIdInfoMap = userIdInfos.stream()
                    .collect(Collectors.toMap(QyweixinOpenUserIdInfo::getUserid, Function.identity(), (v1, v2) -> v1));
            for(QyweixinExternalContactInfo info : externalContactsResult.getData()) {
                if(externalUserIdInfoMap.containsKey(info.getExternalUserid())) {
                    info.setExternalUserid(externalUserIdInfoMap.get(info.getExternalUserid()).getNew_external_userid());
                }
                for(QyweixinFollowUser followUser : info.getFollowUserList()) {
                    if(userIdInfoMap.containsKey(followUser.getUserid())) {
                        followUser.setUserid(userIdInfoMap.get(followUser.getUserid()).getOpen_userid());
                    }
                }
            }
        }
        return externalContactsResult;
    }



    /**
     *
     * @param fsEa
     * @return
     */
    private String fsEaToOutEa(String fsEa) {
        return qyweixinAccountBindService.fsEaToOutEa("qywx", fsEa).getData();
    }

    /**
     * 计算出可见范围内的员工数量。默认CRM应用
     * @param fsEa
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/getCountQyweixinPrivilegeEmployee", method = RequestMethod.GET)
    public Result<Integer> getCountQyweixinPrivilegeEmployee(@RequestParam String fsEa){
        return qyweixinAccountSyncService.getCountQyweixinPrivilegeEmployee(fsEa);
    }

    /**
     * 绑定老的纷享企业，出现同步待办消息异常
     * @param fsEa
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/notifyHistoryToDoMessageGatewayBatch", method = RequestMethod.GET)
    public Result<Object> notifyHistoryToDoMessageGatewayBatch(@RequestParam String fsEa){
        if(StringUtils.isBlank(fsEa)){
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        qyweixinGatewayServiceNormal.notifyHistoryToDoMessageGateway(Arrays.asList(fsEa.split(",")));
        return new Result<>();
    }

    /**
     *查看企业是否已经开通代开发自建应用，企微连接器WEB接口
     */
    @RequestMapping(value = "/queryEnterpriseReplaceApplication", method = RequestMethod.GET)
    public Result<QyweixinAuthorizationBindInfo> queryEnterpriseReplaceApplication(@RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("queryEnterpriseReplaceApplication,dcInfo={},connectParam={}",dcInfo,connectParam);

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
        String outDepId =  QYWXConnectParam.isInvalid(connectParam) ? "1" : connectParam.getOutDepId();

        QyweixinAuthorizationBindInfo qyweixinAuthorizationBindInfo = new QyweixinAuthorizationBindInfo();

        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
                qyweixinAccountBindService.fsEaToOutEaResult2("qywx", fsEa, outEa);
        log.info("ControllerOpenQYWeixin.queryEnterpriseReplaceApplication,qyweixinCorpIDResult={}", qyweixinCorpIDResult);
        String corpName = null;

        Result<String> resultInfo = contactBindInnerService.queryCorpName2(fsEa, outEa, outDepId);
        if(!resultInfo.isSuccess()) {
            return new Result<>(resultInfo.getErrorCode(),resultInfo.getErrorMsg(),null);
        }
        log.info("ControllerOpenQYWeixin.queryEnterpriseReplaceApplication,resultInfo={}", resultInfo);
        if(StringUtils.isNotEmpty(resultInfo.getData())) {
            corpName = resultInfo.getData();
        }
        log.info("ControllerOpenQYWeixin.queryEnterpriseReplaceApplication,corpName={}", corpName);

        Integer result = 0;
        if(qyweixinCorpIDResult.getData()!=null) {
            if(StringUtils.isNotEmpty(qyweixinCorpIDResult.getData().getOutEa())) {
                result = qyweixinAccountSyncService.queryEnterpriseReplaceApplication(qyweixinCorpIDResult.getData().getOutEa());
                log.info("ControllerOpenQYWeixin.queryEnterpriseReplaceApplication,result={}", result);
            }
        }

        qyweixinAuthorizationBindInfo.setAuthorization(result > 0 ? 1 : 0);
        qyweixinAuthorizationBindInfo.setCorpName(corpName);
        if(qyweixinCorpIDResult.getData()!=null) {
            qyweixinAuthorizationBindInfo.setBindType(qyweixinCorpIDResult.getData().getBindType());
        } else {
            qyweixinAuthorizationBindInfo.setBindType(0);
        }
        log.info("ControllerOpenQYWeixin.queryEnterpriseReplaceApplication,qyweixinAuthorizationBindInfo={}.", qyweixinAuthorizationBindInfo);
        return new Result<>(qyweixinAuthorizationBindInfo);
    }

    /**
     * 获取外部联系人详情
     * @return
     */
    //todo 需要前后端一起改造
    @RequestMapping(value = "/getExternalContactDetail", method = RequestMethod.GET)
    public Result<QyweixinExternalContactRsp> getExternalContactDetail(@RequestParam String externalUserId,
                                                                       @RequestParam(value = "outEa", required = false) String outEa){
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("ControllerOpenQYWeixin.getExternalContactDetail,fsEa={}", fsEa);
        return qyweixinGatewayServiceNormal.getExternalContactDetail2(fsEa,externalUserId,outEa);
    }

    /**
     * 获取群详情
     * @return
     */
    //todo 需要前后端一起改造
    @RequestMapping(value = "/getGroupChatDetail", method = RequestMethod.GET)
    public Result<QyweixinGroupChatDetail> getGroupChatDetail(@RequestParam String chatId,
                                                              @RequestParam(required = false) String appId,
                                                              @RequestParam(value = "outEa", required = false) String outEa){
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("ControllerOpenQYWeixin.getGroupChatDetail,fsEa={},appId={}", fsEa,appId);
        if(StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        Result<QyweixinGroupChatDetail> chatDetailResult = qyweixinGatewayServiceNormal.getGroupChatDetail2(fsEa, chatId, appId, outEa);
        if(chatDetailResult.isSuccess() && ObjectUtils.isNotEmpty(chatDetailResult.getData()) && fsEa.equals(ConfigCenter.SERVICE_PROVIDER)) {
            List<String> userIds = new LinkedList<>();
            List<String> externalUserIds = new LinkedList<>();
            for(QyweixinGroupChatDetail.GroupChat.MemberList memberList : chatDetailResult.getData().getGroup_chat().getMember_list()) {
                if(memberList.getType() == 1) {
                    userIds.add(memberList.getUserid());
                } else {
                    externalUserIds.add(memberList.getUserid());
                }
            }
            for(QyweixinGroupChatDetail.GroupChat.UserIdModel userIdModel: chatDetailResult.getData().getGroup_chat().getAdmin_list()) {
                userIds.add(userIdModel.getUserid());
            }
            List<QyweixinExternalUserIdInfo> externalContactEmployeeIds = new LinkedList<>();
            for (int i = 0; i < externalUserIds.size(); i += 900) {
                int end = Math.min(externalUserIds.size(), i + 900);
                List<String> batch = externalUserIds.subList(i, end);
                Result<List<QyweixinExternalUserIdInfo>> externalContactEmployeeIdResult = qyweixinAccountSyncService.switchExternalContactEmployeeId2(fsEa, batch, outEa);
                if(externalContactEmployeeIdResult.isSuccess() && CollectionUtils.isNotEmpty(externalContactEmployeeIdResult.getData())) {
                    externalContactEmployeeIds.addAll(externalContactEmployeeIdResult.getData());
                }
            }

            List<QyweixinOpenUserIdInfo> userIdInfos = new LinkedList<>();
            for (int i = 0; i < userIds.size(); i += 900) {
                int end = Math.min(userIds.size(), i + 900);
                List<String> batch = userIds.subList(i, end);
                Result<List<QyweixinOpenUserIdInfo>> employeeIdResult = qyweixinAccountSyncService.switchEmployeeId2(fsEa, batch, outEa);
                if(employeeIdResult.isSuccess() && CollectionUtils.isNotEmpty(employeeIdResult.getData())) {
                    userIdInfos.addAll(employeeIdResult.getData());
                }
            }
            log.info("ControllerOpenQYWeixin.getQyweixinExternalContact,externalContactEmployeeIds={},userIdInfos={}", externalContactEmployeeIds, userIdInfos);
            Map<String, QyweixinExternalUserIdInfo> externalUserIdInfoMap = externalContactEmployeeIds.stream()
                    .collect(Collectors.toMap(QyweixinExternalUserIdInfo::getExternal_userid, Function.identity(), (v1, v2) -> v1));
            Map<String, QyweixinOpenUserIdInfo> userIdInfoMap = userIdInfos.stream()
                    .collect(Collectors.toMap(QyweixinOpenUserIdInfo::getUserid, Function.identity(), (v1, v2) -> v1));
            for(QyweixinGroupChatDetail.GroupChat.MemberList memberList : chatDetailResult.getData().getGroup_chat().getMember_list()) {
                if(memberList.getType() == 1) {
                    if(userIdInfoMap.containsKey(memberList.getUserid())) {
                        memberList.setUserid(userIdInfoMap.get(memberList.getUserid()).getOpen_userid());
                    }
                } else {
                    if(externalUserIdInfoMap.containsKey(memberList.getUserid())) {
                        memberList.setUserid(externalUserIdInfoMap.get(memberList.getUserid()).getNew_external_userid());
                    }
                }
            }
            for(QyweixinGroupChatDetail.GroupChat.UserIdModel userIdModel: chatDetailResult.getData().getGroup_chat().getAdmin_list()) {
                if(userIdInfoMap.containsKey(userIdModel.getUserid())) {
                    userIdModel.setUserid(userIdInfoMap.get(userIdModel.getUserid()).getOpen_userid());
                }
            }
        }
        return chatDetailResult;
    }

    @Deprecated
    @RequestMapping(value = "/getEmployeeBasicInfoByMobile", method = RequestMethod.GET)
    public Result<FsEmployeeBasicInfo> getEmployeeBasicInfoByMobile(@RequestParam(value = "ea") String ea,
                                                 @RequestParam(value = "mobile") String mobile){
        return qyweixinAccountSyncService.getEmployeeBasicInfoByMobile(ea, mobile);
    }

    /**
     * 留资功能使用的接口，得到企业的最新订单情况和绑定类型
     * @return
     */
    @RequestMapping(value = "/order/getEnterpriseTrialInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo() {
        if(ObjectUtils.isEmpty(UserContextSingleton.INSTANCE.getUserContext())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        return qyweixinAccountSyncService.getEnterpriseTrialInfo(fsEa);
    }

    /**
     * 留资功能使用的接口，得到员工在纷享crm的一些信息
     * @return
     */
    @RequestMapping(value = "/employee/getFsCurEmployeeDetailInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo() {
        if(ObjectUtils.isEmpty(UserContextSingleton.INSTANCE.getUserContext())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        Integer ei = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseId();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        return qyweixinAccountSyncService.getFsCurEmployeeDetailInfo(ei, userId);
    }

    /**
     * 更新企业绑定表拓展字段
     * 1、留资功能：函数调用此接口更新拓展字段的是否已留资的字段
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/enterprise/updateEnterpriseExtend", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> updateEnterpriseExtend(@RequestParam String fsEa,
                                               @RequestParam String extendField,
                                               @RequestParam Object extendValue,
                                               @RequestParam(value = "outEa", required = false) String outEa) {
        if (StringUtils.isEmpty(fsEa) || StringUtils.isEmpty(extendField) || ObjectUtils.isEmpty(extendValue)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.updateEnterpriseExtend2(fsEa, extendField, extendValue, outEa);
    }

    /**
     * 通过纷享ea获取企业的绑定关系
     */

    @Deprecated
    @RequestMapping(value = "/enterprise/getOutEaByFsEa", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> getOutEaByFsEa(@RequestParam String fsEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getOutEaResultByFsEa(fsEa);
    }

    /**
     * 通过纷享ea获取企业的绑定关系，适用于一个CRM对多个企微的场景
     */
    @Deprecated
    @RequestMapping(value = "/enterprise/getOutEaListByFsEa", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<String>> getOutEaListByFsEa(@RequestParam String fsEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getOutEaResultListByFsEa(fsEa);
    }

    /**
     * 通过企微ea获取企业的绑定关系
     */
    @Deprecated
    @RequestMapping(value = "/enterprise/getFsEaByOutEa", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<String>> getFsEaByOutEa(@RequestParam String outEa) {
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getFsEaResultByOutEa(outEa);
    }

    /**
     * 获取企微应用token
     * 1、建发客开
     */
    @RequestMapping(value = "/appInfo/getAppAccessToken", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> getAppAccessToken(@RequestParam String outEa,
                                               @RequestParam String appId) {
        if(StringUtils.isEmpty(outEa) || StringUtils.isEmpty(appId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.getAppAccessToken(outEa, appId);
    }

    /**
     *  查询员工绑定关系
     */
    @RequestMapping(value = "/employee/queryEmployeeBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<QyweixinEmployeeBindModel>> queryEmployeeBind(@RequestBody QyweixinEmployeeBind employeeBind) {
        if(ObjectUtils.isEmpty(employeeBind) || StringUtils.isEmpty(employeeBind.getFsEa()) || ObjectUtils.isEmpty(employeeBind.getEmployeeBinds())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return contactBindInnerService.queryEmployeeBind(employeeBind);
    }

    /**
     * 保存员工关系
     */
    @RequestMapping(value = "/employee/saveEmployeeBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Integer> saveEmployeeBind(@RequestBody QyweixinEmployeeBind employeeBind) {
        if(ObjectUtils.isEmpty(employeeBind) || StringUtils.isEmpty(employeeBind.getFsEa()) || CollectionUtils.isEmpty(employeeBind.getEmployeeBinds())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return contactBindInnerService.saveEmployeeBind(employeeBind);
    }

    /**
     *  更新员工关系
     */
    @RequestMapping(value = "/employee/updateEmployeeBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Integer> updateEmployeeBind(@RequestBody QyweixinEmployeeBind employeeBind) {
        if(ObjectUtils.isEmpty(employeeBind) || StringUtils.isEmpty(employeeBind.getFsEa()) || CollectionUtils.isEmpty(employeeBind.getEmployeeBinds())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return contactBindInnerService.updateEmployeeBind(employeeBind);
    }

    /**
     *
     */
    @RequestMapping(value = "/department/queryDepartmentBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<QyweixinDepartmentBindModel>> queryDepartmentBind(@RequestBody QyweixinDepartmentBind departmentBind) {
        if(ObjectUtils.isEmpty(departmentBind) || StringUtils.isEmpty(departmentBind.getFsEa()) || CollectionUtils.isEmpty(departmentBind.getDepartmentBinds())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return contactBindInnerService.queryDepartmentBind(departmentBind);
    }

    /**
     *
     */
    @RequestMapping(value = "/department/updateDepartmentBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Integer> updateDepartmentBind(@RequestBody QyweixinDepartmentBind departmentBind) {
        if(ObjectUtils.isEmpty(departmentBind) || StringUtils.isEmpty(departmentBind.getFsEa()) || CollectionUtils.isEmpty(departmentBind.getDepartmentBinds())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return contactBindInnerService.updateDepartmentBind(departmentBind);
    }

    /**
     *
     */
    @RequestMapping(value = "/department/saveDepartmentBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Integer> saveDepartmentBind(@RequestBody QyweixinDepartmentBind departmentBind) {
        if(ObjectUtils.isEmpty(departmentBind) || StringUtils.isEmpty(departmentBind.getFsEa()) || CollectionUtils.isEmpty(departmentBind.getDepartmentBinds())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return contactBindInnerService.saveDepartmentBind(departmentBind);
    }

    /**
     * 检查企业是否具有某类license
     * 1、crm企微连接器-->会话存档
     * @return
     */
    @RequestMapping(value = "/enterprise/checkEnterpriseProductVersion", method = RequestMethod.POST)
    @ResponseBody
    public Result<Boolean> checkEnterpriseProductVersion(@RequestBody QyweixinEnterpriseLicenseModel enterpriseLicenseModel,
                                                         @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("checkEnterpriseProductVersion,dcInfo={},connectParam={}",dcInfo,connectParam);

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        if(ObjectUtils.isEmpty(enterpriseLicenseModel) || StringUtils.isEmpty(enterpriseLicenseModel.getCheckType())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        enterpriseLicenseModel.setFsEa(fsEa);
        enterpriseLicenseModel.setOutEa(outEa);
        return qyweixinAccountSyncService.checkEnterpriseProductVersion(enterpriseLicenseModel);
    }

    /**
     * 创建员工接口
     * @return
     */
    @RequestMapping(value = "/employee/createEmployeeInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> createEmployeeInfo(@RequestBody QyweixinCreateEmployeeInfo createEmployeeInfo) {
        if(ObjectUtils.isEmpty(createEmployeeInfo) || StringUtils.isEmpty(createEmployeeInfo.getToken()) || !createEmployeeInfo.getToken().equals(ConfigCenter.ERROR_PAGE_TOKEN)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return qyweixinAccountSyncService.createEmployeeInfo(createEmployeeInfo.getOutEa(), createEmployeeInfo.getAppId(), createEmployeeInfo.getOutUserId(), createEmployeeInfo.getFsEa());
    }

    @RequestMapping(value = "/createJsapiSignature", method = RequestMethod.GET)
    @ResponseBody
    Result<QyweixinJsapiSignature> createJsapiSignature(@RequestParam("url")  String url,
                                                        @RequestParam("appId") String appId,
                                                        @RequestParam(value = "outEa", required = false) String outEa) {
        if(ObjectUtils.isEmpty(UserContextSingleton.INSTANCE.getUserContext())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        return qyweixinAccountSyncService.createJsapiSignature2(url, fsEa, appId, outEa);
    }

    @RequestMapping(value = "/queryIntelligentAppInfo", method = RequestMethod.GET)
    @ResponseBody
    Result<IntelligentAppInfoResult> queryIntelligentAppInfo(@RequestParam(value = "appId" ,required = false) String appId) {
        if(ObjectUtils.isEmpty(UserContextSingleton.INSTANCE.getUserContext())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Result<IntelligentAppInfoResult> intelligentAppInfoResultResult = qyweixinAccountSyncService.queryIntelligentSessionInfo(fsEa, appId);
        return intelligentAppInfoResultResult;
    }
}
