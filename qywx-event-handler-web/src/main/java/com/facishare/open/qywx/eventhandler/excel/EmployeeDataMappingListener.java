package com.facishare.open.qywx.eventhandler.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.excel.BaseListener;
import com.facishare.open.qywx.accountsync.excel.ImportExcelFile;
import com.facishare.open.qywx.accountsync.excel.vo.EmployeeMappingVo;
import com.facishare.open.qywx.i18n.I18NStringEnum;
import com.facishare.open.qywx.i18n.I18NStringManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Getter
public class EmployeeDataMappingListener extends BaseListener<EmployeeMappingVo> {

    private final ImportExcelFile.Result importResult;
    private String fsEa;
    private String outEa;
    private Integer fsUserId;
    private QyweixinAccountBindService qyweixinAccountBindService;
    private ToolsService toolsService;
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    private EIEAConverter eieaConverter;
    private Map<String,String> outEaMap = new HashMap<>();
    private I18NStringManager i18NStringManager;
    private String lang = null;

    public EmployeeDataMappingListener(String fsEa,
                                       String outEa,
                                       Integer fsUserId,
                                       QyweixinAccountBindService qyweixinAccountBindService,
                                       ToolsService toolsService,
                                       FsEmployeeServiceProxy fsEmployeeServiceProxy,
                                       EIEAConverter eieaConverter,
                                       I18NStringManager i18NStringManager,
                                       String lang) {
        importResult = new ImportExcelFile.Result();
        this.fsEa = fsEa;
        this.outEa = outEa;
        this.fsUserId = fsUserId;
        this.qyweixinAccountBindService = qyweixinAccountBindService;
        this.toolsService = toolsService;
        this.fsEmployeeServiceProxy = fsEmployeeServiceProxy;
        this.eieaConverter = eieaConverter;
        this.i18NStringManager = i18NStringManager;
        this.lang = lang;
    }

    @Override
    public void invoke(EmployeeMappingVo data, AnalysisContext context) {
        super.invoke(data, context);
        log.info("EmployeeDataMappingListener.invoke,data={}", data);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();

        if(StringUtils.isEmpty(data.getFsEa())
                || StringUtils.isEmpty(data.getOutEa())
                || data.getFsUserId() == null
                || StringUtils.isEmpty(data.getOutUserId())) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s169,lang,null));
            return;
        }

        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> detail = fsEmployeeServiceProxy.detail(ei + "",
                data.getFsUserId() + "");
        if(!detail.isSuccess() || detail.getData()==null) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s170,lang,null)+data.getFsUserId());
            return;
        }

        if(!outEaMap.containsKey(data.getOutEa())) {
            com.facishare.open.qywx.accountsync.result.Result<String> result = toolsService.corpId2OpenCorpId(data.getOutEa());
            outEaMap.put(data.getOutEa(),result.getData());
        }
        String outEa2 = outEaMap.get(data.getOutEa());
        log.info("EmployeeDataMappingListener.invoke,outEa2={}", outEa2);

        if(!StringUtils.equalsIgnoreCase(fsEa,data.getFsEa()) || !StringUtils.equalsIgnoreCase(outEa,outEa2)) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s171,lang,null));
            return;
        }

        com.facishare.open.qywx.accountsync.result.Result<String> getOpenUserId = toolsService.userId2OpenUserId3(outEa,
                null,
                data.getOutUserId());
        log.info("EmployeeDataMappingListener.invoke,getOpenUserId={}", getOpenUserId);
        if(!getOpenUserId.isSuccess()) {
            importResult.addImportError(rowNo,getOpenUserId.getErrorMsg());
            return;
        }
        String outUserId = getOpenUserId.getData();
        if(StringUtils.length(data.getOutUserId()) < 32 && StringUtils.equalsIgnoreCase(outUserId,data.getOutUserId())) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s172,lang,null) + data.getOutUserId());
            return;
        }

        Result<QyweixinAccountEmployeeMapping> employeeMapping2 = qyweixinAccountBindService.getQywxEmployeeMapping2(data.getFsEa(),
                data.getFsUserId()+"",
                outEa);
        log.info("EmployeeDataMappingListener.invoke,employeeMapping2={}", employeeMapping2);
        if(employeeMapping2.getData()!=null) {
            importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s173,lang,null)+data.getFsUserId());
            return;
        }

        Result<List<QyweixinAccountEmployeeMapping>> employeeMappingResult = qyweixinAccountBindService.getFsEmployeeMapping2(outEa,outUserId);
        log.info("EmployeeDataMappingListener.invoke,employeeMappingResult={}", employeeMappingResult);
        if(CollectionUtils.isNotEmpty(employeeMappingResult.getData())) {
            boolean hasBind = false;
            for(QyweixinAccountEmployeeMapping mapping : employeeMappingResult.getData()) {
                if(StringUtils.containsIgnoreCase(mapping.getFsAccount(),fsEa)) {
                    hasBind = true;
                    break;
                }
            }
            if(hasBind) {
                importResult.addImportError(rowNo,i18NStringManager.get(I18NStringEnum.s174,lang,null)+data.getOutUserId());
                return;
            }
        }

        employeeMapping2 = qyweixinAccountBindService.getQywxEmployeeMapping3(data.getFsEa(),
                data.getFsUserId()+"",
                outEa,
                outUserId);
        log.info("EmployeeDataMappingListener.invoke,employeeMapping2.2={}", employeeMapping2);
        if(employeeMapping2.getData()==null) {
            List<QyweixinAccountEmployeeMapping> employeeMappingList = new ArrayList<>();
            QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
            employeeMapping.setFsAccount("E." + fsEa + "." + data.getFsUserId());
            employeeMapping.setOutAccount(outUserId);
            employeeMapping.setIsvAccount(outUserId);
            employeeMapping.setOutEa(outEa);
            employeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
            employeeMappingList.add(employeeMapping);
            log.info("EmployeeDataMappingListener.invoke,employeeMappingList={}", employeeMappingList);
            try {
                Result<Boolean> result = qyweixinAccountBindService.bindAccountEmployeeMapping(employeeMappingList);
                log.info("EmployeeDataMappingListener.invoke,result={}", result);
                if(result.isSuccess() && result.getData()) {
                    importResult.incrInsert(1);
                } else {
                    importResult.addImportError(rowNo,result.getErrorMsg());
                }
            } catch (Exception e) {
                log.info("EmployeeDataMappingListener.invoke,exception={}", e.getMessage(),e);
                importResult.addInvokeExceptionRow(rowNo,e.getMessage());
            }
        } else {
            importResult.incrUpdate(1);
        }
    }
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get(I18NStringEnum.s175,lang,null)+"\n");
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s176.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s176.getI18nValue(),importResult.getInvokedNum(),importResult.getInsertNum(),importResult.getUpdateNum()),
                        Lists.newArrayList(
                                importResult.getInvokedNum()+"",importResult.getInsertNum()+"",importResult.getUpdateNum()+""
                        )))
                .append("\n");

        printMsg.append(i18NStringManager.get2(I18NStringEnum.s177.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s177.getI18nValue(),importResult.getImportErrorRows().size()),
                        Lists.newArrayList(
                                importResult.getImportErrorRows().size()+""
                        )))
                .append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s178.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s178.getI18nValue(),Joiner.on(",").join(v), k),
                            Lists.newArrayList(
                                    Joiner.on(",").join(v), k
                            )))
                    .append("\n");
        });
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s179.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s179.getI18nValue(),importResult.getInvokeExceptionRows().size()),
                        Lists.newArrayList(
                                importResult.getInvokeExceptionRows().size()+""
                        )))
                .append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s178.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s178.getI18nValue(),Joiner.on(",").join(v), k),
                            Lists.newArrayList(
                                    Joiner.on(",").join(v), k
                            )))
                    .append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }
}
