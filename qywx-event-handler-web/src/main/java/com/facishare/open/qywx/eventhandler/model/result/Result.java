package com.facishare.open.qywx.eventhandler.model.result;

/**
 * Created by fengyh on 2020/6/17.
 */
import lombok.Builder;
import lombok.Data;
import java.io.Serializable;
import lombok.Data;
import java.io.Serializable;
/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/24
 */
@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -1L;

    private T data;
    private String errorCode;
    private String errorMsg;

    public Result() {
        errorCode = "s120050000";
        errorMsg = "succ";
        data = null;
    }
    public Result(T t) {
        data = t;
        errorCode = "s120050000";
        errorMsg = "succ";
    }


    public Result(com.facishare.open.qywx.accountbind.result.Result<T> arg) {
        data = arg.getData();
        errorCode = arg.getErrorCode();
        errorMsg = arg.getErrorMsg();
    }

    public Result(com.facishare.open.qywx.accountsync.result.Result<T> arg) {
        data = arg.getData();
        errorCode = arg.getErrorCode();
        errorMsg = arg.getErrorMsg();
    }

    public Result(String code, String errorMsg, T data) {
        this.errorCode = code;
        this.errorMsg = errorMsg;
        this.data = data;
    }

    public boolean isSuccess() {
        return ("s120050000".equals(errorCode));
    }
    public Result<T> addError(String code) {
        return this.addErrorCode(code, null);
    }

    public Result<T> addErrorCode(String code , T data) {
        this.errorCode = code;
        this.errorMsg = "";
        this.data = data;
        return this;
    }

    public Result<T> addErrorMsg(String code, String errorMsg, T data) {
        this.errorCode = code;
        this.errorMsg = errorMsg;
        this.data = data;
        return this;
    }
}
