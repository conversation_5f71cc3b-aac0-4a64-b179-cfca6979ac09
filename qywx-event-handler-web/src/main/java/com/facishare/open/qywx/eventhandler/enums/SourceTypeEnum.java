package com.facishare.open.qywx.eventhandler.enums;

import com.google.common.base.MoreObjects;

/**
 * <p>source type</p>
 * @dateTime 2018/6/21 10:03
 * <AUTHOR> <EMAIL>
 * @version 1.0 
 */
public enum SourceTypeEnum {

    QYWX("qywx", "企业微信");

    /**
     * 类型
     */
    private String sourceType;

    /**
     * 描述
     */
    private String sourceDesc;

    private SourceTypeEnum(String sourceType, String sourceDesc) {
        this.sourceType = sourceType;
        this.sourceDesc = sourceDesc;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceDesc() {
        return sourceDesc;
    }

    public void setSourceDesc(String sourceDesc) {
        this.sourceDesc = sourceDesc;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("sourceType", sourceType).add("sourceDesc", sourceDesc)
                .toString();
    }

}
