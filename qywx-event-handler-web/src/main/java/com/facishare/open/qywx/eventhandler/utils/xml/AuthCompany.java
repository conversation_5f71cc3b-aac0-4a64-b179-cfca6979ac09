package com.facishare.open.qywx.eventhandler.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * Created by fengyh on 2018/4/20.
 */

@XStreamAlias("xml")
@Data
public class AuthCompany {

    @XStreamAlias("SuiteId")
    private String SuiteId;

    @XStreamAlias("AuthCode")
    private String AuthCode;

    @XStreamAlias("InfoType")
    private String InfoType;

    @XStreamAlias("TimeStamp")
    private String TimeStamp;

    @Override
    public String toString() {
        return "AuthCompany{" +
                "SuiteId='" + SuiteId + '\'' +
                ", AuthCode='" + AuthCode + '\'' +
                ", InfoType='" + InfoType + '\'' +
                ", TimeStamp='" + TimeStamp + '\'' +
                '}';
    }
}
