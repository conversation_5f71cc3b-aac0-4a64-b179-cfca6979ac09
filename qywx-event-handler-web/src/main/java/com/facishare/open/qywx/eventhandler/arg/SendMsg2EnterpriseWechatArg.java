package com.facishare.open.qywx.eventhandler.arg;

import com.facishare.open.qywx.messagesend.enums.QyWeixinMsgType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SendMsg2EnterpriseWechatArg implements Serializable {
    private String corpId;
    private String appId;
    private List<String> outUserIdList;
    private QyWeixinMsgType msgType;
    private Object msgContent;//取值 TextMsgContent ， TextCardMsgContent，NewsMsgContent，NewsArticle, MiniprogramNoticeMsgContent
}
