package com.facishare.open.qywx.eventhandler.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.rmi.RemoteException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public final class HttpHelper {

    private static final int DEFAULT_MAX_CONNECTION = 200;
    private static final int DEFAULT_SOCKET_TIMEOUT = 5000;
    private static final int DEFAULT_CONNECTION_TIMEOUT = 2000;

    //protected static Logger log = LoggerFactory.getLogger(HttpHelper.class);
    private HttpClient httpClient;

    public HttpHelper() {
        PoolingHttpClientConnectionManager connectionManager
                = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(DEFAULT_MAX_CONNECTION);
        connectionManager.setDefaultMaxPerRoute(DEFAULT_MAX_CONNECTION);

        SocketConfig.Builder sb = SocketConfig.custom();
        sb.setSoKeepAlive(true);
        sb.setTcpNoDelay(true);
        connectionManager.setDefaultSocketConfig(sb.build());

        HttpClientBuilder hb = HttpClientBuilder.create();
        hb.setConnectionManager(connectionManager);

        RequestConfig.Builder rb = RequestConfig.custom();
        rb.setSocketTimeout(DEFAULT_SOCKET_TIMEOUT);
        rb.setConnectTimeout(DEFAULT_CONNECTION_TIMEOUT);

        hb.setDefaultRequestConfig(rb.build());

        httpClient = hb.build();
    }

    private   String invoke(RequestBuilder builder,
                            String url,
                            Map<String, String> headers,
                            HttpEntity httpEntity,
                            ResponseHandler<String> handler) throws IOException {

        builder.setUri(url);
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                builder.addHeader(e.getKey(), e.getValue());
            }
        }

        builder.setEntity(httpEntity);

        try (CloseableHttpResponse response = (CloseableHttpResponse) httpClient.execute(builder.build())) {

            int httpCode = response.getStatusLine().getStatusCode();

            if (httpCode != HttpStatus.SC_OK) {
                String format = MessageFormat.format("HTTP Status Error {0} : {1}", httpCode,
                        EntityUtils.toString(response.getEntity()));
                throw new RemoteException(format);
            }

            return handler.handleResponse(response);

        }

    }

    /**
     * 发送 urlencode编码的form数据
     * @param url
     * @param form
     * @param headers
     * @return
     */
    public String postUrlEncodeData(String url, Map<String, String> form, Map<String, String> headers) throws RemoteException {

        List<NameValuePair> formParams = form.entrySet().stream()
                .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        UrlEncodedFormEntity entity;

        try {
            entity = new UrlEncodedFormEntity(formParams, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RemoteException("Encoding Error", e);
        }


        try {
            String rsp =  invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postUrlEncodeData param to yunzhijia: url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        } catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData2(String url , Map<String, Object> form) throws RemoteException {
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postJsonData param to yunzhijia: url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData(String url , Map<String, String> form) throws RemoteException {
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postJsonData param to yunzhijia: url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String doGet(String url) throws RemoteException {

        try {
            String rsp = invoke(RequestBuilder.get(), url, null, null, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postJsonData param  url:{}, rsp:{}", url, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public static void main(String[] args) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/set_session_info?suite_access_token=x6rGhe1dVnQMNfvfp6B7-VR3pQIQrDeXkkpB9Rv7oJ1qOvsNz7Ko30QTUxR9cXKMpBjpcGYURge5cfVCJNsobB8-tf1U0ZeMuX2Al65KMagmeRm6eLFx-W_7XHhluq8p";
        Map<String, Object> form = new HashMap<>();
        form.put("pre_auth_code", "c5bGVKENSBosOHmeY1Wq0n2PrJVmSVyK2QODsadMAnEo-38Az5A10F-EN4PDXBB-");
        Map<String, Integer> sessionInfo = ImmutableMap.<String, Integer>builder()
                .put("auth_type", 1).build();
        form.put("session_info", JSONObject.toJSONString(sessionInfo));
        try{
            log.info("trace setSessionInfo set:{}", form);
            String httpRsp = new HttpHelper().postJsonData2(url, form);
            log.info("trace setSessionInfo httpRsp:{}", httpRsp);
        } catch (Exception e) {
        }


    }
}
