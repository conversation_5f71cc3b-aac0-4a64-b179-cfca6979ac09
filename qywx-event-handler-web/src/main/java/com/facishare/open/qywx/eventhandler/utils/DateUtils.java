package com.facishare.open.qywx.eventhandler.utils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateUtils {
    private static DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    public static String FORMAT_yyyyMMddhhmmss = "yyyy-MM-dd hh:mm:ss";

    public static String nowDate() {
        return LocalDate.now().format(FORMATTER);
    }

    public static String nowDatePlusDays(int days) {
        return LocalDate.now().plusDays(days).format(FORMATTER);
    }

    public static Long date2Timestamp(String date) {
        LocalDate dt = LocalDate.parse(date, FORMATTER);
        return dt.atStartOfDay(ZoneId.of("+8")).toEpochSecond() * 1000;
    }

    public static Date parseDate(String date,String format) {
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(date,format);
        } catch (Exception e) {
            return null;
        }
    }

    public static void main(String[] args) {
        Date date = parseDate("2024-05-16 14:32:20", "yyyy-MM-dd hh:mm:ss");
        System.out.println(date);
    }
}
