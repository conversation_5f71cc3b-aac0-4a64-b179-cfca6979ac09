package com.facishare.open.qywx.eventhandler.controller.outer;

import com.facishare.open.kis.connector.common.result.ResultCode;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinCorpBindInfo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.accountsync.service.SuperAdminService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/qyweixin/admin/")
public class ControllerAdmin {
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayServiceNormal;

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Autowired
    private SuperAdminService superAdminService;

    @RequestMapping(value = "/reSendPayForAppSuccessEvent",method = RequestMethod.GET)
    public Result<String> reSendPayForAppSuccessEvent(String appId, String orderId,String outEa) {
        log.info("ControllerAdmin.reSendPayForAppSuccessEvent,appId={},orderId={},outEa={}");

        if(StringUtils.isEmpty(appId) || StringUtils.isEmpty(orderId) || StringUtils.isEmpty(outEa))
            return Result.newInstance(ErrorRefer.PARAM_ERROR);


        Result<QyweixinCorpBindInfo> qyweixinCorpBindInfoResult = qyweixinAccountSyncService.getCorpBindInfo(outEa, appId);
        if(!qyweixinCorpBindInfoResult.isSuccess() || qyweixinCorpBindInfoResult.getData().getStatus()==1)
            return new Result().addError(qyweixinCorpBindInfoResult.getErrorCode(),qyweixinCorpBindInfoResult.getErrorMsg(),null);

        qyweixinGatewayServiceNormal.payForAppSuccessEvent(appId, orderId);

        return new Result("send pay for app success event success");
    }
//
//    @RequestMapping(value = "/superQuery", method = RequestMethod.POST)
//    public Result<List<Map<String,Object>>> superQuery(@RequestBody String sql){
//        return superAdminService.superQuerySql(sql);
//    }
//
//    @RequestMapping(value = "/superInsert", method = RequestMethod.POST)
//    public Result<Integer> superInsert(@RequestBody String sql){
//        return superAdminService.superInsertSql(sql);
//    }
//
//    @RequestMapping(value = "/superUpdate", method = RequestMethod.POST)
//    public Result<Integer> superUpdate(@RequestBody String sql){
//        return superAdminService.superUpdateSql(sql);
//    }
//
//    @RequestMapping(value = "/superDelete", method = RequestMethod.POST)
//    public Result<Integer> superDelete(@RequestBody String sql){
//        return superAdminService.superDeleteSql(sql);
//    }
}
