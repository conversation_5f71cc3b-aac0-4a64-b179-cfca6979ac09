<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <dubbo:application id="fsOpenQywxMessageSend" name="fs-open-qywx-messagesend" />
    <dubbo:registry id="fsOpenQywxMessageSendRegistry" address="${dubbo.registry.address}" file="${dubbo.registry.file}" />
    <dubbo:protocol
            id="dubbo"
            name="dubbo"
            port="${duboo.port}"
            threadpool="limited"
            threads="300"
            accepts="1500"
    />
    <dubbo:provider id="fsOpenQywxMessageSendProvider" application="fsOpenQywxMessageSend" protocol="dubbo"  registry="fsOpenQywxMessageSendRegistry"  filter="tracerpc" />

    <bean id="qyWeixinMessageSendService" class="com.facishare.open.qywx.messagesend.service.impl.QYWeixinMessageSendServiceImpl"/>
    <dubbo:service interface="com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService"
                   ref="qyWeixinMessageSendService"
                   protocol="dubbo"
                   timeout="20000"
                   version="1.0"
                   retries="0"
                   />

    <dubbo:reference  id="qyweixinGatewayServiceNormal"
                      interface="com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService"
                      protocol="dubbo"
                      timeout="20000"
                      version="1.0"
                      check="false"
                      />
    <dubbo:reference  id="qyweixinAccountBindService"
                      interface="com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService"
                      protocol="dubbo"
                      timeout="20000"
                      version="1.0"
                      check="false"
                      />
    <dubbo:reference  id="ContactBindInnerService"
                      interface="com.facishare.open.qywx.accountinner.service.ContactBindInnerService"
                      protocol="dubbo"
                      timeout="20000"
                      version="1.0"
                      check="false"
                      />

    <dubbo:reference id="oaQYWeixinMessageSendService"
                     interface="com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     group="oaTemp"/>

    <import resource="classpath:spring/qywx-i18n.xml"/>

    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>
    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fsOpenWebhookRedisConfig"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!-- 启用@AspectJ注解 -->
    <aop:aspectj-autoproxy/>

    <!--aop打印log-->
    <bean id="logAspect" class="com.facishare.open.qywx.messagesend.aop.LogAspect"/>
    <aop:config>
        <aop:aspect id="logMonitor" ref="logAspect">
            <aop:pointcut id="monitor"
                          expression="(execution(* com.facishare.open.qywx.messagesend.service.impl.*.*(..)))"/>
            <aop:around pointcut-ref="monitor" method="around"/>
        </aop:aspect>
    </aop:config>

    <import resource="classpath:spring/ei-ea-converter.xml"/>

    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-open-qywx-messagesend-cms"/>

    <context:component-scan base-package="com.facishare.open.order.contacts.proxy,com.facishare.open.qywx.messagesend,com.facishare.open.qywx.i18n"/>

</beans>