package com.facishare.open.qywx.messagesend.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/31
 */
@Data
public class MPNewsArticle implements Serializable {

    //标题，不超过128个字节，超过会自动截断
    private String title;
    //图文消息缩略图的media_id, 可以通过素材管理接口获得。
    private String thumb_media_id;
    //作者
    private String author;
    //图文消息点击“阅读原文”之后的页面链接
    private String content_source_url;
    //图文消息的内容，支持html标签，不超过666 K个字节
    private String content;
    //图文消息的描述，不超过512个字节，超过会自动截断
    private String digest;
}
