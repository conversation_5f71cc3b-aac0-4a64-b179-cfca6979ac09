package com.facishare.open.qywx.messagesend.utils;

/**
 * Created by fengyh on 2018/3/6.
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.rmi.RemoteException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
public final class HttpHelper {

    private static final int DEFAULT_MAX_CONNECTION = 3;
    private static final int DEFAULT_SOCKET_TIMEOUT = 3000;
    private static final int DEFAULT_CONNECTION_TIMEOUT = 2000;

   // protected static Logger log = LoggerFactory.getLogger(HttpHelper.class);
    private HttpClient httpClient;

    public HttpHelper() {
        PoolingHttpClientConnectionManager connectionManager
                = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(DEFAULT_MAX_CONNECTION);
        connectionManager.setDefaultMaxPerRoute(DEFAULT_MAX_CONNECTION);

        SocketConfig.Builder sb = SocketConfig.custom();
        sb.setSoKeepAlive(true);
        sb.setTcpNoDelay(true);
        connectionManager.setDefaultSocketConfig(sb.build());

        HttpClientBuilder hb = HttpClientBuilder.create();
        hb.setConnectionManager(connectionManager);

        RequestConfig.Builder rb = RequestConfig.custom();
        rb.setSocketTimeout(DEFAULT_SOCKET_TIMEOUT);
        rb.setConnectTimeout(DEFAULT_CONNECTION_TIMEOUT);

        hb.setDefaultRequestConfig(rb.build());

        httpClient = hb.build();
    }

    private   String invoke(RequestBuilder builder,
                            String url,
                            Map<String, String> headers,
                            HttpEntity httpEntity,
                            ResponseHandler<String> handler) throws IOException {

        builder.setUri(url);
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                builder.addHeader(e.getKey(), e.getValue());
            }
        }

        builder.setEntity(httpEntity);

        try (CloseableHttpResponse response = (CloseableHttpResponse) httpClient.execute(builder.build())) {

            int httpCode = response.getStatusLine().getStatusCode();

            if (httpCode != HttpStatus.SC_OK) {
                String format = MessageFormat.format("HTTP Status Error {0} : {1}", httpCode,
                        EntityUtils.toString(response.getEntity()));
                log.info("trace invoke url:{}, get result :{} ,", url, format);
                return null;
            }
            return handler.handleResponse(response);

        }catch (Exception e) {
            log.info("trace invoke url:{}, get exception ,", url, e);
            return null;
        }
    }

    private Integer getStatusCode(RequestBuilder builder,
                                  String url,
                                  Map<String, String> headers,
                                  HttpEntity httpEntity) throws IOException {

        builder.setUri(url);
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> e : headers.entrySet()) {
                builder.addHeader(e.getKey(), e.getValue());
            }
        }
        builder.setEntity(httpEntity);

        try (CloseableHttpResponse response = (CloseableHttpResponse) httpClient.execute(builder.build())) {

            Integer httpCode = response.getStatusLine().getStatusCode();
            log.info("trace invoke url:{}, get response:{},  status code:{} ,", url, response, httpCode);
            return httpCode;
        }catch (Exception e) {
            log.error("trace invoke url:{}, get exception ,", url, e);
            return null;
        }
    }

    /**
     * 发送 urlencode编码的form数据
     * @param url
     * @param form
     * @param headers
     * @return
     */
    public String postUrlEncodeData(String url, Map<String, String> form, Map<String, String> headers) throws RemoteException {

        List<NameValuePair> formParams = form.entrySet().stream()
                .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        UrlEncodedFormEntity entity;

        try {
            entity = new UrlEncodedFormEntity(formParams, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RemoteException("Encoding Error", e);
        }


        try {
            String rsp =  invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postUrlEncodeData param to yunzhijia: url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        } catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData(String url , Map<String, String> form) throws RemoteException {
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postJsonData param to yunzhijia: url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData2(String url , Map<String, Object> form) throws RemoteException {
        log.info("trace postUrl body entity is:{}",form);
        String jsonContent = JSONArray.toJSONString(form, true);
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postJsonData param to url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData3(String url, Map<String, String> headers, Object params) throws RemoteException {
        String jsonArg = params instanceof String ? params.toString() : JSON.toJSONString(params);

        StringEntity entity = new StringEntity(jsonArg, "UTF-8");
        headers.put("Content-type", "application/json");

        try {
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postJsonData param to qywx: url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            throw new RemoteException("Encoding Error", e);
        }
    }

    public String postJsonData(String url , String jsonContent) throws RemoteException {
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        entity.setContentType("application/json");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");

        try {
            log.info("trace postJsonData param to yunzhijia: url:{}, headers:{}, entity:{}", url, headers, entity);
            String rsp = invoke(RequestBuilder.post(), url, headers, entity, response -> EntityUtils.toString(response.getEntity()));
            log.info("trace postJsonData result from yunzhijia: url:{}, headers:{}, entity:{}, rsp:{}", url, headers, entity, rsp);
            return rsp;
        }catch (Exception e) {
            log.info("trace postJsonData to url:{} get exception, ",url, e);
            return  null;
        }
    }


    public Integer postJsonDataAndRetStatusCode(String url , String jsonContent) throws RemoteException {
        StringEntity entity = new StringEntity(jsonContent, "UTF-8");
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-type", "application/json");

        try {
            Integer httpCode = getStatusCode(RequestBuilder.post(), url, headers, entity);
            log.info("trace postJsonData param to yunzhijia: url:{}, headers:{}, entity:{}, httpCode:{}", url, headers, entity, httpCode);
            return httpCode;
        }catch (Exception e) {
            log.info("trace postJsonDataAndRetStatusCode get exception, ", e);
            return null;
        }
    }
}
