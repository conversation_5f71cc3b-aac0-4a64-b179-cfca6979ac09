package com.facishare.open.qywx.messagesend.config;

import com.github.autoconf.ConfigFactory;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/11/01
 */
@Component
@Data
public class ConfigCenter {
    public static String FILTER_MESSAGES_EA = "{\"84883\":\"CstmCtrl_5FrKx__c\",\"74860\":\"test\"}";
    public static String PAAS_FUNCTION_URL = "http://172.31.100.247:4859";
    public static String crm_domain;
    public static String crmAppId;

    /**
     * 判断该环境是否是主环境
     */
    public static Boolean MAIN_ENV = true;
    static {
        ConfigFactory.getInstance().getConfig("fs-open-qywx-app-config", config -> {
            FILTER_MESSAGES_EA = config.get("FILTER_MESSAGES_EA", FILTER_MESSAGES_EA);
            PAAS_FUNCTION_URL = config.get("PAAS_FUNCTION_URL", PAAS_FUNCTION_URL);
            crm_domain = config.get("domainPrefix", crm_domain);
            MAIN_ENV = config.getBool("MAIN_ENV", MAIN_ENV);
            crmAppId = config.get("crmAppId");
        });
    }
}
