<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        ">
    <!--annotation configuration -->
    <import resource="classpath:spring/spring-common.xml" />
    <import resource="classpath:spring/spring-cms.xml" />
    <context:annotation-config />
    <context:component-scan base-package="com.facishare.open.qywx.messagesend"/>

</beans>