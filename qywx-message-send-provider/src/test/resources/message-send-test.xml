<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       http://www.springframework.org/schema/context
	   http://www.springframework.org/schema/context/spring-context.xsd">

    <!-- 使用multicast广播注册中心暴露发现服务地址 -->
    <dubbo:application name="message-send-consumer-test" />

    <dubbo:registry id="remote113" address="zookeeper://***********:2181?backup=***********:2181,***********:2181" protocol="dubbo"/>

    <dubbo:registry id="remote112" address="zookeeper://***********:2181?backup=***********:2181,***********:2181" protocol="dubbo"/>

    <!--<dubbo:registry id="dubbo-registry-local" address="zookeeper://localhost:2181"/>-->

    <!--<dubbo:reference registry="remote112" id="qyWeixinMessageSendService" interface="QYWeixinMessageSendService" timeout="30000" protocol="dubbo" version="1.0"/>-->

    <dubbo:reference registry="remote112" id="qyWeixinMessageSendService"
                     interface="com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService" timeout="30000" protocol="dubbo" version="1.0"/>

    <dubbo:reference registry="remote112" id="oaQYWeixinMessageSendService"
                     interface="com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService"
                     version="1.0"
                     timeout="300000"
                     check="false"
                     group="oaTemp"/>

    <import resource="classpath:spring/ei-ea-converter.xml"/>
</beans>