package com.facishare.open.qywx.messagesend.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.messagesend.enums.QyWeixinMsgType;
import com.facishare.open.qywx.messagesend.model.*;
import com.facishare.open.qywx.messagesend.result.Result;
import com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by fengyh on 2018/3/7.
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:/message-send-test.xml")
public class MessageSendServiceTest extends AbstractJUnit4SpringContextTests {

    @Resource
    private QYWeixinMessageSendService qyWeixinMessageSendService;
    @Resource
    @Qualifier("oaQYWeixinMessageSendService")
    private QYWeixinMessageSendService oaQyWeixinMessageSendService;

    @Test
    public void textSendMsg(){
        TextMsgContent textMsgContent = new TextMsgContent();
        textMsgContent.setContent("你好，让我们开始幸运的开发之旅！U-FSQYWX-wowx1mDAAADxO-7I5TTy3US9kXDrHkjA  fsdf U-FSQYWX-wowx1mDAAADxO-7I5TTy3US9kXDrHkjA   fdsf D-FSQYWX-1   sdf  U-FSQYWX-wowx1mDAAAj0eqolavzZeD1HEkdNo-BA");
        SendQyWeixinMsgReq sendQyWeixinMsgReq = new SendQyWeixinMsgReq<TextMsgContent>();
        sendQyWeixinMsgReq.setAppId(appId);
        sendQyWeixinMsgReq.setFsEnterpriseAccount("84883");
        sendQyWeixinMsgReq.setToUserList(Lists.newArrayList("1021"));
        sendQyWeixinMsgReq.setMsgContent(textMsgContent);
        sendQyWeixinMsgReq.setType(QyWeixinMsgType.MSG_TEXT);
        String s = replaceMsg(JSONObject.toJSONString(sendQyWeixinMsgReq.getMsgContent()));
        System.out.println(s);
        TextMsgContent content = new Gson().fromJson(s, TextMsgContent.class);
        System.out.println(content);
        Result<SendQyWeixinMsgRsp> result = oaQyWeixinMessageSendService.sendQyWeixinMsg(sendQyWeixinMsgReq);
        System.out.println("trace textSendMsg:" + result);
    }

    @Test
    public void sendMsg(){
        TextMsgContent textMsgContent = new TextMsgContent();
        textMsgContent.setContent("代理通代开发应用卡片消息跳转测试"+System.currentTimeMillis());

        TextCardMsgContent textCardMsgContent = new TextCardMsgContent();
        textCardMsgContent.setTitle("代理通代开发应用卡片消息跳转测试");
        textCardMsgContent.setDescription("<br> <br>流程名称 : 测试11<br>名称 : 1<br>待处理人 : XiongTao<br>关联对象 : 流流程程测试<br>关联数据 : 21<br>主属性 : 21<br>");
        textCardMsgContent.setUrl("http://www.ceshi112.com/qyweixin/doRepFunction?corpId=wpwx1mDAAAFtBrQCfiHtmRaUHt5hRo7A&appId=dk34d6bacd61e33d99&param=aHR0cHM6Ly93d3cuY2VzaGkxMTIuY29t");
        textCardMsgContent.setBtntxt("查看详情");

        Result<SendQyWeixinMsgRsp> result = qyWeixinMessageSendService.sendMsg("wpwx1mDAAAFtBrQCfiHtmRaUHt5hRo7A",
                "dk34d6bacd61e33d99",
                QyWeixinMsgType.MSG_TEXT_CARD,
                textCardMsgContent,
                Lists.newArrayList("wowx1mDAAA4ZIEKKwkyK-R8sc0g-yS0w"));
        System.out.println("trace textSendMsg:" + result);
    }

    @Test
    public void sendMiniprogramMsg(){
        MiniprogramNoticeMsgContent content = new MiniprogramNoticeMsgContent();
        content.setTitle("代理通小程序卡片消息测试");
        content.setDescription("测试1\n测试2\n测试3\n");
        content.setAppid("wx8e4d8c7a7f0efdaf");
        content.setPage("pages/index/index");

        Result<SendQyWeixinMsgRsp> result = qyWeixinMessageSendService.sendMsg("wpwx1mDAAAsJuqgPFlcfGWtLOewfEiNw",
                "dk34d6bacd61e33d99",
                QyWeixinMsgType.MINIPROGRAM_NOTICE,
                content,
                Lists.newArrayList("wowx1mDAAA0Ou5xip4toV7W4cvRmepcw"));
        System.out.println("trace textSendMsg:" + result);
    }

//    String appId = "wx88a141937dd6f838";
//    String fsEa = "74362";

    String appId = "wx88a141937dd6f838";
    String fsEa = "78052";
    @Test
    public void textCardMsg(){
        TextCardMsgContent textCardMsgContent = new TextCardMsgContent();
        textCardMsgContent.setTitle("待办通知 : 待处理的CRM审批流程");
        textCardMsgContent.setDescription("<br>客户审批(2024-10-25 11:43)<br> <br>流程主题 : 客户审批(2024-10-25 11:43)<br>客户名称 : 贝贝测试客户1016.2<br>1级行业 : <br>客户级别 : 重要客户<br>成交状态 : 未成交<br>负责人 : 杨贤杰Summer<br>");
        textCardMsgContent.setUrl("https://open.ceshi112.com/qyweixin/doFunction?param=aHR0cHM6Ly93d3cuY2VzaGkxMTIuY29tL2hjcm0vd2VjaGF0L2Z1bmN0aW9uL3RvZG8/YXBpbmFtZT1BY2NvdW50T2JqJmlkPTY3MWIxM2NiNGM0NjgzMDAwMWM2Y2U2OCZlYT04MTI0Mw==&appID=dk3ff8a65e707ca3c2");
        textCardMsgContent.setBtntxt("查看详情");
        SendQyWeixinMsgReq sendQyWeixinMsgReq = new SendQyWeixinMsgReq<TextMsgContent>();
        sendQyWeixinMsgReq.setAppId("wx88a141937dd6f838");
        sendQyWeixinMsgReq.setFsEnterpriseAccount("81243");
        sendQyWeixinMsgReq.setToUserList(Lists.newArrayList("1069"));
        sendQyWeixinMsgReq.setMsgContent(textCardMsgContent);
        sendQyWeixinMsgReq.setType(QyWeixinMsgType.MSG_TEXT_CARD);
        sendQyWeixinMsgReq.setSourceMsg("{\"actionRange\":0,\"bizType\":\"452\",\"createTime\":*************,\"dealSummary\":\"来自系统的客户审批已批复。所属数据：贝贝测试客户1016.2\",\"employeeIds\":[1069],\"hasSyncBizData\":false,\"lastDealSummaryInfo\":{\"M1\":\"qx_srv.deal.summary.452\",\"M2\":[\"#I18N#qx_srv.common.default.system\",\"客户\",\"贝贝测试客户1016.2\"]},\"lastSummaryInfo\":{\"M1\":\"qx_srv.todo.summary.452\",\"M2\":[\"#I18N#qx_srv.common.default.system\",\"客户\",\"贝贝测试客户1016.2\"]},\"objectApiName\":\"AccountObj\",\"objectBizType\":\"default__c\",\"objectId\":\"671b13cb4c46830001c6ce68\",\"senderId\":\"-10000\",\"sourceId\":\"671b13cff2d17358f158f0ae\",\"sourceName\":\"\",\"status\":0,\"summary\":\"来自系统的客户审批待您批复。所属数据：贝贝测试客户1016.2\",\"tenantId\":81243,\"todoId\":\"452_671b13cff2d17358f158f0ae\",\"todoType\":\"452\",\"urlParameters\":{\"activityId\":\"1\",\"objectApiName\":\"AccountObj\",\"activityInstanceId\":\"\",\"applicantId\":\"1069\",\"workflowInstanceId\":\"671b13ceb44c2f18a54e0047\",\"taskId\":\"671b13cff2d17358f158f0ae\",\"objectId\":\"671b13cb4c46830001c6ce68\"}}");
        Result<SendQyWeixinMsgRsp> result = qyWeixinMessageSendService.sendQyWeixinMsg(sendQyWeixinMsgReq);
        System.out.println("trace textCardMsg:" + result);
    }


    @Test
    public void newsMsg(){
        NewsMsgContent newsMsgContent = new NewsMsgContent();
        NewsArticle newsArticle = new NewsArticle();
        newsArticle.setTitle("欢迎开通纷享销客");
        newsArticle.setDescription("欢迎开通纷享销客");
        newsArticle.setPicurl("http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png");
        newsArticle.setUrl("http://open.ceshi113.com/qyweixin/doMessageFunction?param=aHR0cHM6Ly93d3cuY2VzaGkxMTMuY29tL2hjcm0vd2VjaGF0L2Z1bmN0aW9uL3RvZG8/aWQ9ZmRlNzBkZjc3NTEwNGRmZWE5YTgzMWE2YzBiYWMwOGImYXBpbmFtZT1NYXJrZXRpbmdFdmVudE9iag==&appID=wx4c7edab730f4fdc9");
        newsArticle.setBtntxt("更多");

        NewsArticle newsArticle2 = new NewsArticle();
        BeanUtils.copyProperties(newsArticle, newsArticle2);
        NewsArticle newsArticle3 = new NewsArticle();
        BeanUtils.copyProperties(newsArticle, newsArticle3);
        newsMsgContent.setArticles(Lists.newArrayList(newsArticle, newsArticle2, newsArticle3));

        SendQyWeixinMsgReq sendQyWeixinMsgReq = new SendQyWeixinMsgReq<TextMsgContent>();
        sendQyWeixinMsgReq.setAppId(appId);
        sendQyWeixinMsgReq.setFsEnterpriseAccount(fsEa);
        sendQyWeixinMsgReq.setToUserList(Lists.newArrayList("1000"));
        sendQyWeixinMsgReq.setMsgContent(newsMsgContent);
        sendQyWeixinMsgReq.setType(QyWeixinMsgType.MSG_NEWS_CARD);
        Result<SendQyWeixinMsgRsp> result = qyWeixinMessageSendService.sendQyWeixinMsg(sendQyWeixinMsgReq);
        System.out.println("trace textCardMsg:" + result);
    }

    @Test
    public void mpnewsMsg(){
        MPNewsMsgContent mpNewsMsgContent =new MPNewsMsgContent();
        MPNewsArticle mpNewsArticle = new MPNewsArticle();
        mpNewsArticle.setTitle("mpnewsMsg msg title");
        mpNewsArticle.setContent_source_url("https://www.fxiaoke.com");
        mpNewsArticle.setContent("Content");
        mpNewsArticle.setDigest("Digest description");
        mpNewsArticle.setAuthor("路过");
        mpNewsArticle.setThumb_media_id("3RRuYZ68z1sISbZufDwmTVkgrdDKg8OxPJel-hiAUApK9brRxQiiVd7HBzAxqs7LX");


        mpNewsMsgContent.setArticles(Lists.newArrayList(mpNewsArticle));

        SendQyWeixinMsgReq sendQyWeixinMsgReq = new SendQyWeixinMsgReq<TextMsgContent>();
        sendQyWeixinMsgReq.setAppId(appId);
        sendQyWeixinMsgReq.setFsEnterpriseAccount("58491");
        sendQyWeixinMsgReq.setToUserList(Lists.newArrayList("1000","nihao","43353"));
        sendQyWeixinMsgReq.setMsgContent(mpNewsMsgContent);
//        sendQyWeixinMsgReq.setType("mpnews");
        Result<SendQyWeixinMsgRsp> result = qyWeixinMessageSendService.sendQyWeixinMsg(sendQyWeixinMsgReq);
        System.out.println("trace textCardMsg:" + result);
    }

//    3dftxAtcbA8LdJkC4sTkyByTiduaABD5zSCUPZ8lhNDE
//    3RRuYZ68z1sISbZufDwmTVkgrdDKg8OxPJel-hiAUApK9brRxQiiVd7HBzAxqs7LX
//    3wS4OFQpNSOZd27Vi9hDNC_xge2vDWzAZ54z2A_sQkmEoxYnJZAsEfl8mMhj_PDee

    private String replaceMsg(String msg) {
        String userNamePrefix = "U-FSQYWX-";
        Pattern pattern = Pattern.compile(userNamePrefix);
        Matcher matcher = pattern.matcher(msg);
        Set<String> userAccountSet = new HashSet<>();
        Set<String> deptAccountSet = new HashSet<>();
        while (matcher.find()){
            System.out.println(matcher.start());
            //字串的索引
            String account = msg.substring(matcher.start(), matcher.end() + 32);
            System.out.println(account);
            userAccountSet.add(account);
        }
        System.out.println("-----------------------------------------");
        String deptNamePrefix = "D-FSQYWX-";
        Pattern pattern1 = Pattern.compile(deptNamePrefix);
        Matcher matcher1 = pattern1.matcher(msg);
        while (matcher1.find()){
            System.out.println(matcher1.start());
            //字串的索引
            int sum = 0;
            int index = matcher1.end();
            char c = msg.charAt(index);
            while (StringUtils.isNumeric(String.valueOf(c))){
                sum ++;
                c = msg.charAt(index ++);
            }
            String account = msg.substring(matcher1.start(), matcher1.end() + sum - 1);
            System.out.println(account);
            deptAccountSet.add(account);
        }
        if(CollectionUtils.isNotEmpty(userAccountSet)) {
            for(String account : userAccountSet) {
                msg = msg.replace(account, "$userName="+ account.replace(userNamePrefix, "") +"$");
            }
        }
        if(CollectionUtils.isNotEmpty(deptAccountSet)) {
            for(String account : deptAccountSet) {
                msg = msg.replace(account, "$departmentName="+ account.replace(deptNamePrefix, "") +"$");
            }
        }
        return msg;
    }
}
