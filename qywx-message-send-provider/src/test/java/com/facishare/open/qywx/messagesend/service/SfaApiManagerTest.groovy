package com.facishare.open.qywx.messagesend.service

import com.facishare.open.qywx.messagesend.manager.SfaApiManager
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData
import com.fxiaoke.otherrestapi.function.data.HeaderObj
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:message-send-test.xml")
class SfaApiManagerTest extends Specification {

    @Autowired
    private SfaApiManager sfaApiManager;

    def "executeCustomFunction"() {
        given:
        HeaderObj headerObj = new HeaderObj(84883,-10000)
        FunctionServiceExecuteArg arg = new FunctionServiceExecuteArg()
        arg.setApiName("CstmCtrl_eTRZZ__c")
        arg.setBindingObjectAPIName("NONE")
        arg.setNameSpace("controller")
        Map<String, Object> form = new HashMap<>()
        form.put("touser", "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ|")
        form.put("toparty", "")
        form.put("totag", "")
        form.put("msgtype", "text")
        //form.put("agentid", "1000048")
        form.put("enable_id_trans", 1)
        Map<String, Object> text = new HashMap<>()
        text.put("content", "再看一眼就会爆炸")
        form.put("text", text)
        List<FunctionServiceParameterData> parameters = new LinkedList<>()
        FunctionServiceParameterData<Map> data = new FunctionServiceParameterData()
        data.setName("msgMap")
        data.setType("Map")
        data.setValue(form)
        parameters.add(data)
        arg.setParameters(parameters)
        expect:
        def result = sfaApiManager.executeCustomFunction(headerObj, arg)
        println result;
    }
}
