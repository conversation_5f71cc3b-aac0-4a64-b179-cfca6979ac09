package com.facishare.open.qywx.messagesend.service;

import com.facishare.open.qywx.messagesend.enums.QyWeixinMsgType;
import com.facishare.open.qywx.messagesend.model.SendQyWeixinMsgReq;
import com.facishare.open.qywx.messagesend.model.SendQyWeixinMsgRsp;
import com.facishare.open.qywx.messagesend.result.Result;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/11
 */
public interface QYWeixinMessageSendService {
    /**
     * 根据appId发送消息到企业微信的应用session.
     * 发送格式见：https://work.weixin.qq.com/api/doc/90001/90143/90372
     *
     * @param req : @see SendQyWeixinMsgReq
     * @return @see SendQyWeixinMsgRsp.
     */
    Result<SendQyWeixinMsgRsp> sendQyWeixinMsg(SendQyWeixinMsgReq<?> req);

    Result<SendQyWeixinMsgRsp> sendMsg(String corpId,
                                       String appId,
                                       QyWeixinMsgType msgType,
                                       Object msgContent,
                                       List<String> outUserIdList);
}
