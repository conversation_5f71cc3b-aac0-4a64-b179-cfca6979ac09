package com.facishare.open.qywx.messagesend.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/31
 *
 * 文本卡片消息
 */
@Data
public class TextCardMsgContent implements Serializable {

    private static final long serialVersionUID = 4355074295312862109L;
    //标题，不超过128个字节，超过会自动截断
    private String title;
    //描述，不超过512个字节，超过会自动截断  如：<div class=\"gray\">2016年9月26日</div> <div class=\"normal\">恭喜你抽中iPhone 7一台，领奖码：xxxx</div><div class=\"highlight\">请于2016年10月10日前联系行政同事领取</div>
    private String description;
    //点击后跳转的链接。
    private String url;
    //按钮文字。 默认为“详情”， 不超过4个文字，超过自动截断。
    private String btntxt;
}
