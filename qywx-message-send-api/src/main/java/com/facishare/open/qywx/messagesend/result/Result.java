package com.facishare.open.qywx.messagesend.result;

import com.facishare.open.qywx.messagesend.enums.ErrorRefer;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by fengyh on 2018/7/23.
 */
@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -1L;

    private T data;
    //0-成功， other-失败
    private Integer errorCode;
    private String errorMsg;

    public Result() {
        errorCode = 0;
        errorMsg = "success";
    }

    public Result(ErrorRefer errorRefer) {
        errorCode = errorRefer.getCode();
        errorMsg = errorRefer.getMessage();
    }

    public Result(T t) {
        data = t;
        errorCode = 0;
        errorMsg = "success";
    }

    public boolean isSuccess() {
        return (0 == errorCode);
    }

    public Result<T> addError(String msg){
        return this.addError(900, msg);
    }

    public Result<T> addError(int code ,String msg) {
        return this.addError(code, msg, null);
    }


    public Result<T> addError(int code ,String msg,T data) {
        this.errorMsg = msg;
        this.errorCode = code;
        this.data = data;
        return this;
    }
}

